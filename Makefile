# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.28

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /data/mec_channel

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /data/mec_channel

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /data/mec_channel/CMakeFiles /data/mec_channel//CMakeFiles/progress.marks
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start /data/mec_channel/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named mec_channel

# Build rule for target.
mec_channel: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 mec_channel
.PHONY : mec_channel

# fast build rule for target.
mec_channel/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/build
.PHONY : mec_channel/fast

#=============================================================================
# Target rules for targets named git_version.h

# Build rule for target.
git_version.h: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 git_version.h
.PHONY : git_version.h

# fast build rule for target.
git_version.h/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/git_version.h.dir/build.make CMakeFiles/git_version.h.dir/build
.PHONY : git_version.h/fast

#=============================================================================
# Target rules for targets named send110

# Build rule for target.
send110: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 send110
.PHONY : send110

# fast build rule for target.
send110/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/send110.dir/build.make CMakeFiles/send110.dir/build
.PHONY : send110/fast

#=============================================================================
# Target rules for targets named send113

# Build rule for target.
send113: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 send113
.PHONY : send113

# fast build rule for target.
send113/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/send113.dir/build.make CMakeFiles/send113.dir/build
.PHONY : send113/fast

#=============================================================================
# Target rules for targets named send144

# Build rule for target.
send144: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 send144
.PHONY : send144

# fast build rule for target.
send144/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/send144.dir/build.make CMakeFiles/send144.dir/build
.PHONY : send144/fast

#=============================================================================
# Target rules for targets named send146

# Build rule for target.
send146: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 send146
.PHONY : send146

# fast build rule for target.
send146/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/send146.dir/build.make CMakeFiles/send146.dir/build
.PHONY : send146/fast

#=============================================================================
# Target rules for targets named send247

# Build rule for target.
send247: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 send247
.PHONY : send247

# fast build rule for target.
send247/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/send247.dir/build.make CMakeFiles/send247.dir/build
.PHONY : send247/fast

app/config.o: app/config.cpp.o
.PHONY : app/config.o

# target to build an object file
app/config.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/app/config.cpp.o
.PHONY : app/config.cpp.o

app/config.i: app/config.cpp.i
.PHONY : app/config.i

# target to preprocess a source file
app/config.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/app/config.cpp.i
.PHONY : app/config.cpp.i

app/config.s: app/config.cpp.s
.PHONY : app/config.s

# target to generate assembly for a file
app/config.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/app/config.cpp.s
.PHONY : app/config.cpp.s

app/main.o: app/main.cpp.o
.PHONY : app/main.o

# target to build an object file
app/main.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/app/main.cpp.o
.PHONY : app/main.cpp.o

app/main.i: app/main.cpp.i
.PHONY : app/main.i

# target to preprocess a source file
app/main.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/app/main.cpp.i
.PHONY : app/main.cpp.i

app/main.s: app/main.cpp.s
.PHONY : app/main.s

# target to generate assembly for a file
app/main.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/app/main.cpp.s
.PHONY : app/main.cpp.s

app/service.o: app/service.cpp.o
.PHONY : app/service.o

# target to build an object file
app/service.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/app/service.cpp.o
.PHONY : app/service.cpp.o

app/service.i: app/service.cpp.i
.PHONY : app/service.i

# target to preprocess a source file
app/service.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/app/service.cpp.i
.PHONY : app/service.cpp.i

app/service.s: app/service.cpp.s
.PHONY : app/service.s

# target to generate assembly for a file
app/service.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/app/service.cpp.s
.PHONY : app/service.cpp.s

dvin_client/dvin_debug_client.o: dvin_client/dvin_debug_client.cpp.o
.PHONY : dvin_client/dvin_debug_client.o

# target to build an object file
dvin_client/dvin_debug_client.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/dvin_client/dvin_debug_client.cpp.o
.PHONY : dvin_client/dvin_debug_client.cpp.o

dvin_client/dvin_debug_client.i: dvin_client/dvin_debug_client.cpp.i
.PHONY : dvin_client/dvin_debug_client.i

# target to preprocess a source file
dvin_client/dvin_debug_client.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/dvin_client/dvin_debug_client.cpp.i
.PHONY : dvin_client/dvin_debug_client.cpp.i

dvin_client/dvin_debug_client.s: dvin_client/dvin_debug_client.cpp.s
.PHONY : dvin_client/dvin_debug_client.s

# target to generate assembly for a file
dvin_client/dvin_debug_client.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/dvin_client/dvin_debug_client.cpp.s
.PHONY : dvin_client/dvin_debug_client.cpp.s

engine_module/config/config_base.o: engine_module/config/config_base.cpp.o
.PHONY : engine_module/config/config_base.o

# target to build an object file
engine_module/config/config_base.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/engine_module/config/config_base.cpp.o
.PHONY : engine_module/config/config_base.cpp.o

engine_module/config/config_base.i: engine_module/config/config_base.cpp.i
.PHONY : engine_module/config/config_base.i

# target to preprocess a source file
engine_module/config/config_base.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/engine_module/config/config_base.cpp.i
.PHONY : engine_module/config/config_base.cpp.i

engine_module/config/config_base.s: engine_module/config/config_base.cpp.s
.PHONY : engine_module/config/config_base.s

# target to generate assembly for a file
engine_module/config/config_base.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/engine_module/config/config_base.cpp.s
.PHONY : engine_module/config/config_base.cpp.s

engine_module/engine/epoll_engine.o: engine_module/engine/epoll_engine.cpp.o
.PHONY : engine_module/engine/epoll_engine.o

# target to build an object file
engine_module/engine/epoll_engine.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/engine_module/engine/epoll_engine.cpp.o
.PHONY : engine_module/engine/epoll_engine.cpp.o

engine_module/engine/epoll_engine.i: engine_module/engine/epoll_engine.cpp.i
.PHONY : engine_module/engine/epoll_engine.i

# target to preprocess a source file
engine_module/engine/epoll_engine.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/engine_module/engine/epoll_engine.cpp.i
.PHONY : engine_module/engine/epoll_engine.cpp.i

engine_module/engine/epoll_engine.s: engine_module/engine/epoll_engine.cpp.s
.PHONY : engine_module/engine/epoll_engine.s

# target to generate assembly for a file
engine_module/engine/epoll_engine.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/engine_module/engine/epoll_engine.cpp.s
.PHONY : engine_module/engine/epoll_engine.cpp.s

engine_module/engine/event_engine.o: engine_module/engine/event_engine.cpp.o
.PHONY : engine_module/engine/event_engine.o

# target to build an object file
engine_module/engine/event_engine.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/engine_module/engine/event_engine.cpp.o
.PHONY : engine_module/engine/event_engine.cpp.o

engine_module/engine/event_engine.i: engine_module/engine/event_engine.cpp.i
.PHONY : engine_module/engine/event_engine.i

# target to preprocess a source file
engine_module/engine/event_engine.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/engine_module/engine/event_engine.cpp.i
.PHONY : engine_module/engine/event_engine.cpp.i

engine_module/engine/event_engine.s: engine_module/engine/event_engine.cpp.s
.PHONY : engine_module/engine/event_engine.s

# target to generate assembly for a file
engine_module/engine/event_engine.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/engine_module/engine/event_engine.cpp.s
.PHONY : engine_module/engine/event_engine.cpp.s

engine_module/engine/http_client_mgr.o: engine_module/engine/http_client_mgr.cpp.o
.PHONY : engine_module/engine/http_client_mgr.o

# target to build an object file
engine_module/engine/http_client_mgr.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/engine_module/engine/http_client_mgr.cpp.o
.PHONY : engine_module/engine/http_client_mgr.cpp.o

engine_module/engine/http_client_mgr.i: engine_module/engine/http_client_mgr.cpp.i
.PHONY : engine_module/engine/http_client_mgr.i

# target to preprocess a source file
engine_module/engine/http_client_mgr.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/engine_module/engine/http_client_mgr.cpp.i
.PHONY : engine_module/engine/http_client_mgr.cpp.i

engine_module/engine/http_client_mgr.s: engine_module/engine/http_client_mgr.cpp.s
.PHONY : engine_module/engine/http_client_mgr.s

# target to generate assembly for a file
engine_module/engine/http_client_mgr.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/engine_module/engine/http_client_mgr.cpp.s
.PHONY : engine_module/engine/http_client_mgr.cpp.s

engine_module/engine/http_service_mgr.o: engine_module/engine/http_service_mgr.cpp.o
.PHONY : engine_module/engine/http_service_mgr.o

# target to build an object file
engine_module/engine/http_service_mgr.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/engine_module/engine/http_service_mgr.cpp.o
.PHONY : engine_module/engine/http_service_mgr.cpp.o

engine_module/engine/http_service_mgr.i: engine_module/engine/http_service_mgr.cpp.i
.PHONY : engine_module/engine/http_service_mgr.i

# target to preprocess a source file
engine_module/engine/http_service_mgr.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/engine_module/engine/http_service_mgr.cpp.i
.PHONY : engine_module/engine/http_service_mgr.cpp.i

engine_module/engine/http_service_mgr.s: engine_module/engine/http_service_mgr.cpp.s
.PHONY : engine_module/engine/http_service_mgr.s

# target to generate assembly for a file
engine_module/engine/http_service_mgr.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/engine_module/engine/http_service_mgr.cpp.s
.PHONY : engine_module/engine/http_service_mgr.cpp.s

engine_module/engine/mqtt_client_mgr.o: engine_module/engine/mqtt_client_mgr.cpp.o
.PHONY : engine_module/engine/mqtt_client_mgr.o

# target to build an object file
engine_module/engine/mqtt_client_mgr.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/engine_module/engine/mqtt_client_mgr.cpp.o
.PHONY : engine_module/engine/mqtt_client_mgr.cpp.o

engine_module/engine/mqtt_client_mgr.i: engine_module/engine/mqtt_client_mgr.cpp.i
.PHONY : engine_module/engine/mqtt_client_mgr.i

# target to preprocess a source file
engine_module/engine/mqtt_client_mgr.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/engine_module/engine/mqtt_client_mgr.cpp.i
.PHONY : engine_module/engine/mqtt_client_mgr.cpp.i

engine_module/engine/mqtt_client_mgr.s: engine_module/engine/mqtt_client_mgr.cpp.s
.PHONY : engine_module/engine/mqtt_client_mgr.s

# target to generate assembly for a file
engine_module/engine/mqtt_client_mgr.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/engine_module/engine/mqtt_client_mgr.cpp.s
.PHONY : engine_module/engine/mqtt_client_mgr.cpp.s

engine_module/engine/tcp_mgr.o: engine_module/engine/tcp_mgr.cpp.o
.PHONY : engine_module/engine/tcp_mgr.o

# target to build an object file
engine_module/engine/tcp_mgr.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/engine_module/engine/tcp_mgr.cpp.o
.PHONY : engine_module/engine/tcp_mgr.cpp.o

engine_module/engine/tcp_mgr.i: engine_module/engine/tcp_mgr.cpp.i
.PHONY : engine_module/engine/tcp_mgr.i

# target to preprocess a source file
engine_module/engine/tcp_mgr.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/engine_module/engine/tcp_mgr.cpp.i
.PHONY : engine_module/engine/tcp_mgr.cpp.i

engine_module/engine/tcp_mgr.s: engine_module/engine/tcp_mgr.cpp.s
.PHONY : engine_module/engine/tcp_mgr.s

# target to generate assembly for a file
engine_module/engine/tcp_mgr.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/engine_module/engine/tcp_mgr.cpp.s
.PHONY : engine_module/engine/tcp_mgr.cpp.s

engine_module/engine/udp_mgr.o: engine_module/engine/udp_mgr.cpp.o
.PHONY : engine_module/engine/udp_mgr.o

# target to build an object file
engine_module/engine/udp_mgr.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/engine_module/engine/udp_mgr.cpp.o
.PHONY : engine_module/engine/udp_mgr.cpp.o

engine_module/engine/udp_mgr.i: engine_module/engine/udp_mgr.cpp.i
.PHONY : engine_module/engine/udp_mgr.i

# target to preprocess a source file
engine_module/engine/udp_mgr.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/engine_module/engine/udp_mgr.cpp.i
.PHONY : engine_module/engine/udp_mgr.cpp.i

engine_module/engine/udp_mgr.s: engine_module/engine/udp_mgr.cpp.s
.PHONY : engine_module/engine/udp_mgr.s

# target to generate assembly for a file
engine_module/engine/udp_mgr.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/engine_module/engine/udp_mgr.cpp.s
.PHONY : engine_module/engine/udp_mgr.cpp.s

engine_module/log/log.o: engine_module/log/log.cpp.o
.PHONY : engine_module/log/log.o

# target to build an object file
engine_module/log/log.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/engine_module/log/log.cpp.o
.PHONY : engine_module/log/log.cpp.o

engine_module/log/log.i: engine_module/log/log.cpp.i
.PHONY : engine_module/log/log.i

# target to preprocess a source file
engine_module/log/log.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/engine_module/log/log.cpp.i
.PHONY : engine_module/log/log.cpp.i

engine_module/log/log.s: engine_module/log/log.cpp.s
.PHONY : engine_module/log/log.s

# target to generate assembly for a file
engine_module/log/log.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/engine_module/log/log.cpp.s
.PHONY : engine_module/log/log.cpp.s

engine_module/mem/frame_buffer.o: engine_module/mem/frame_buffer.cpp.o
.PHONY : engine_module/mem/frame_buffer.o

# target to build an object file
engine_module/mem/frame_buffer.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/engine_module/mem/frame_buffer.cpp.o
.PHONY : engine_module/mem/frame_buffer.cpp.o

engine_module/mem/frame_buffer.i: engine_module/mem/frame_buffer.cpp.i
.PHONY : engine_module/mem/frame_buffer.i

# target to preprocess a source file
engine_module/mem/frame_buffer.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/engine_module/mem/frame_buffer.cpp.i
.PHONY : engine_module/mem/frame_buffer.cpp.i

engine_module/mem/frame_buffer.s: engine_module/mem/frame_buffer.cpp.s
.PHONY : engine_module/mem/frame_buffer.s

# target to generate assembly for a file
engine_module/mem/frame_buffer.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/engine_module/mem/frame_buffer.cpp.s
.PHONY : engine_module/mem/frame_buffer.cpp.s

engine_module/signal/signal_handler.o: engine_module/signal/signal_handler.cpp.o
.PHONY : engine_module/signal/signal_handler.o

# target to build an object file
engine_module/signal/signal_handler.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/engine_module/signal/signal_handler.cpp.o
.PHONY : engine_module/signal/signal_handler.cpp.o

engine_module/signal/signal_handler.i: engine_module/signal/signal_handler.cpp.i
.PHONY : engine_module/signal/signal_handler.i

# target to preprocess a source file
engine_module/signal/signal_handler.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/engine_module/signal/signal_handler.cpp.i
.PHONY : engine_module/signal/signal_handler.cpp.i

engine_module/signal/signal_handler.s: engine_module/signal/signal_handler.cpp.s
.PHONY : engine_module/signal/signal_handler.s

# target to generate assembly for a file
engine_module/signal/signal_handler.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/engine_module/signal/signal_handler.cpp.s
.PHONY : engine_module/signal/signal_handler.cpp.s

engine_module/thread_resource/tr_center.o: engine_module/thread_resource/tr_center.cpp.o
.PHONY : engine_module/thread_resource/tr_center.o

# target to build an object file
engine_module/thread_resource/tr_center.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/engine_module/thread_resource/tr_center.cpp.o
.PHONY : engine_module/thread_resource/tr_center.cpp.o

engine_module/thread_resource/tr_center.i: engine_module/thread_resource/tr_center.cpp.i
.PHONY : engine_module/thread_resource/tr_center.i

# target to preprocess a source file
engine_module/thread_resource/tr_center.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/engine_module/thread_resource/tr_center.cpp.i
.PHONY : engine_module/thread_resource/tr_center.cpp.i

engine_module/thread_resource/tr_center.s: engine_module/thread_resource/tr_center.cpp.s
.PHONY : engine_module/thread_resource/tr_center.s

# target to generate assembly for a file
engine_module/thread_resource/tr_center.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/engine_module/thread_resource/tr_center.cpp.s
.PHONY : engine_module/thread_resource/tr_center.cpp.s

engine_module/utility/app_clock.o: engine_module/utility/app_clock.cpp.o
.PHONY : engine_module/utility/app_clock.o

# target to build an object file
engine_module/utility/app_clock.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/engine_module/utility/app_clock.cpp.o
.PHONY : engine_module/utility/app_clock.cpp.o

engine_module/utility/app_clock.i: engine_module/utility/app_clock.cpp.i
.PHONY : engine_module/utility/app_clock.i

# target to preprocess a source file
engine_module/utility/app_clock.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/engine_module/utility/app_clock.cpp.i
.PHONY : engine_module/utility/app_clock.cpp.i

engine_module/utility/app_clock.s: engine_module/utility/app_clock.cpp.s
.PHONY : engine_module/utility/app_clock.s

# target to generate assembly for a file
engine_module/utility/app_clock.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/engine_module/utility/app_clock.cpp.s
.PHONY : engine_module/utility/app_clock.cpp.s

engine_module/utility/common_utility.o: engine_module/utility/common_utility.cpp.o
.PHONY : engine_module/utility/common_utility.o

# target to build an object file
engine_module/utility/common_utility.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/engine_module/utility/common_utility.cpp.o
.PHONY : engine_module/utility/common_utility.cpp.o

engine_module/utility/common_utility.i: engine_module/utility/common_utility.cpp.i
.PHONY : engine_module/utility/common_utility.i

# target to preprocess a source file
engine_module/utility/common_utility.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/engine_module/utility/common_utility.cpp.i
.PHONY : engine_module/utility/common_utility.cpp.i

engine_module/utility/common_utility.s: engine_module/utility/common_utility.cpp.s
.PHONY : engine_module/utility/common_utility.s

# target to generate assembly for a file
engine_module/utility/common_utility.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/engine_module/utility/common_utility.cpp.s
.PHONY : engine_module/utility/common_utility.cpp.s

engine_module/utility/coordinate_convert.o: engine_module/utility/coordinate_convert.cpp.o
.PHONY : engine_module/utility/coordinate_convert.o

# target to build an object file
engine_module/utility/coordinate_convert.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/engine_module/utility/coordinate_convert.cpp.o
.PHONY : engine_module/utility/coordinate_convert.cpp.o

engine_module/utility/coordinate_convert.i: engine_module/utility/coordinate_convert.cpp.i
.PHONY : engine_module/utility/coordinate_convert.i

# target to preprocess a source file
engine_module/utility/coordinate_convert.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/engine_module/utility/coordinate_convert.cpp.i
.PHONY : engine_module/utility/coordinate_convert.cpp.i

engine_module/utility/coordinate_convert.s: engine_module/utility/coordinate_convert.cpp.s
.PHONY : engine_module/utility/coordinate_convert.s

# target to generate assembly for a file
engine_module/utility/coordinate_convert.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/engine_module/utility/coordinate_convert.cpp.s
.PHONY : engine_module/utility/coordinate_convert.cpp.s

engine_module/utility/crc16.o: engine_module/utility/crc16.cpp.o
.PHONY : engine_module/utility/crc16.o

# target to build an object file
engine_module/utility/crc16.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/engine_module/utility/crc16.cpp.o
.PHONY : engine_module/utility/crc16.cpp.o

engine_module/utility/crc16.i: engine_module/utility/crc16.cpp.i
.PHONY : engine_module/utility/crc16.i

# target to preprocess a source file
engine_module/utility/crc16.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/engine_module/utility/crc16.cpp.i
.PHONY : engine_module/utility/crc16.cpp.i

engine_module/utility/crc16.s: engine_module/utility/crc16.cpp.s
.PHONY : engine_module/utility/crc16.s

# target to generate assembly for a file
engine_module/utility/crc16.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/engine_module/utility/crc16.cpp.s
.PHONY : engine_module/utility/crc16.cpp.s

engine_module/utility/dev_performance.o: engine_module/utility/dev_performance.cpp.o
.PHONY : engine_module/utility/dev_performance.o

# target to build an object file
engine_module/utility/dev_performance.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/engine_module/utility/dev_performance.cpp.o
.PHONY : engine_module/utility/dev_performance.cpp.o

engine_module/utility/dev_performance.i: engine_module/utility/dev_performance.cpp.i
.PHONY : engine_module/utility/dev_performance.i

# target to preprocess a source file
engine_module/utility/dev_performance.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/engine_module/utility/dev_performance.cpp.i
.PHONY : engine_module/utility/dev_performance.cpp.i

engine_module/utility/dev_performance.s: engine_module/utility/dev_performance.cpp.s
.PHONY : engine_module/utility/dev_performance.s

# target to generate assembly for a file
engine_module/utility/dev_performance.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/engine_module/utility/dev_performance.cpp.s
.PHONY : engine_module/utility/dev_performance.cpp.s

engine_module/utility/dir_op.o: engine_module/utility/dir_op.cpp.o
.PHONY : engine_module/utility/dir_op.o

# target to build an object file
engine_module/utility/dir_op.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/engine_module/utility/dir_op.cpp.o
.PHONY : engine_module/utility/dir_op.cpp.o

engine_module/utility/dir_op.i: engine_module/utility/dir_op.cpp.i
.PHONY : engine_module/utility/dir_op.i

# target to preprocess a source file
engine_module/utility/dir_op.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/engine_module/utility/dir_op.cpp.i
.PHONY : engine_module/utility/dir_op.cpp.i

engine_module/utility/dir_op.s: engine_module/utility/dir_op.cpp.s
.PHONY : engine_module/utility/dir_op.s

# target to generate assembly for a file
engine_module/utility/dir_op.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/engine_module/utility/dir_op.cpp.s
.PHONY : engine_module/utility/dir_op.cpp.s

engine_module/utility/hex_str.o: engine_module/utility/hex_str.cpp.o
.PHONY : engine_module/utility/hex_str.o

# target to build an object file
engine_module/utility/hex_str.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/engine_module/utility/hex_str.cpp.o
.PHONY : engine_module/utility/hex_str.cpp.o

engine_module/utility/hex_str.i: engine_module/utility/hex_str.cpp.i
.PHONY : engine_module/utility/hex_str.i

# target to preprocess a source file
engine_module/utility/hex_str.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/engine_module/utility/hex_str.cpp.i
.PHONY : engine_module/utility/hex_str.cpp.i

engine_module/utility/hex_str.s: engine_module/utility/hex_str.cpp.s
.PHONY : engine_module/utility/hex_str.s

# target to generate assembly for a file
engine_module/utility/hex_str.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/engine_module/utility/hex_str.cpp.s
.PHONY : engine_module/utility/hex_str.cpp.s

engine_module/utility/hmac_code.o: engine_module/utility/hmac_code.cpp.o
.PHONY : engine_module/utility/hmac_code.o

# target to build an object file
engine_module/utility/hmac_code.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/engine_module/utility/hmac_code.cpp.o
.PHONY : engine_module/utility/hmac_code.cpp.o

engine_module/utility/hmac_code.i: engine_module/utility/hmac_code.cpp.i
.PHONY : engine_module/utility/hmac_code.i

# target to preprocess a source file
engine_module/utility/hmac_code.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/engine_module/utility/hmac_code.cpp.i
.PHONY : engine_module/utility/hmac_code.cpp.i

engine_module/utility/hmac_code.s: engine_module/utility/hmac_code.cpp.s
.PHONY : engine_module/utility/hmac_code.s

# target to generate assembly for a file
engine_module/utility/hmac_code.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/engine_module/utility/hmac_code.cpp.s
.PHONY : engine_module/utility/hmac_code.cpp.s

engine_module/utility/ota_upgade.o: engine_module/utility/ota_upgade.cpp.o
.PHONY : engine_module/utility/ota_upgade.o

# target to build an object file
engine_module/utility/ota_upgade.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/engine_module/utility/ota_upgade.cpp.o
.PHONY : engine_module/utility/ota_upgade.cpp.o

engine_module/utility/ota_upgade.i: engine_module/utility/ota_upgade.cpp.i
.PHONY : engine_module/utility/ota_upgade.i

# target to preprocess a source file
engine_module/utility/ota_upgade.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/engine_module/utility/ota_upgade.cpp.i
.PHONY : engine_module/utility/ota_upgade.cpp.i

engine_module/utility/ota_upgade.s: engine_module/utility/ota_upgade.cpp.s
.PHONY : engine_module/utility/ota_upgade.s

# target to generate assembly for a file
engine_module/utility/ota_upgade.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/engine_module/utility/ota_upgade.cpp.s
.PHONY : engine_module/utility/ota_upgade.cpp.s

engine_module/utility/rsa_crypt.o: engine_module/utility/rsa_crypt.cpp.o
.PHONY : engine_module/utility/rsa_crypt.o

# target to build an object file
engine_module/utility/rsa_crypt.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/engine_module/utility/rsa_crypt.cpp.o
.PHONY : engine_module/utility/rsa_crypt.cpp.o

engine_module/utility/rsa_crypt.i: engine_module/utility/rsa_crypt.cpp.i
.PHONY : engine_module/utility/rsa_crypt.i

# target to preprocess a source file
engine_module/utility/rsa_crypt.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/engine_module/utility/rsa_crypt.cpp.i
.PHONY : engine_module/utility/rsa_crypt.cpp.i

engine_module/utility/rsa_crypt.s: engine_module/utility/rsa_crypt.cpp.s
.PHONY : engine_module/utility/rsa_crypt.s

# target to generate assembly for a file
engine_module/utility/rsa_crypt.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/engine_module/utility/rsa_crypt.cpp.s
.PHONY : engine_module/utility/rsa_crypt.cpp.s

engine_module/utility/str_op.o: engine_module/utility/str_op.cpp.o
.PHONY : engine_module/utility/str_op.o

# target to build an object file
engine_module/utility/str_op.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/engine_module/utility/str_op.cpp.o
.PHONY : engine_module/utility/str_op.cpp.o

engine_module/utility/str_op.i: engine_module/utility/str_op.cpp.i
.PHONY : engine_module/utility/str_op.i

# target to preprocess a source file
engine_module/utility/str_op.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/engine_module/utility/str_op.cpp.i
.PHONY : engine_module/utility/str_op.cpp.i

engine_module/utility/str_op.s: engine_module/utility/str_op.cpp.s
.PHONY : engine_module/utility/str_op.s

# target to generate assembly for a file
engine_module/utility/str_op.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/engine_module/utility/str_op.cpp.s
.PHONY : engine_module/utility/str_op.cpp.s

engine_module/utility/time_task.o: engine_module/utility/time_task.cpp.o
.PHONY : engine_module/utility/time_task.o

# target to build an object file
engine_module/utility/time_task.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/engine_module/utility/time_task.cpp.o
.PHONY : engine_module/utility/time_task.cpp.o

engine_module/utility/time_task.i: engine_module/utility/time_task.cpp.i
.PHONY : engine_module/utility/time_task.i

# target to preprocess a source file
engine_module/utility/time_task.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/engine_module/utility/time_task.cpp.i
.PHONY : engine_module/utility/time_task.cpp.i

engine_module/utility/time_task.s: engine_module/utility/time_task.cpp.s
.PHONY : engine_module/utility/time_task.s

# target to generate assembly for a file
engine_module/utility/time_task.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/engine_module/utility/time_task.cpp.s
.PHONY : engine_module/utility/time_task.cpp.s

platform/baidu/baidu_mec_client.o: platform/baidu/baidu_mec_client.cpp.o
.PHONY : platform/baidu/baidu_mec_client.o

# target to build an object file
platform/baidu/baidu_mec_client.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/platform/baidu/baidu_mec_client.cpp.o
.PHONY : platform/baidu/baidu_mec_client.cpp.o

platform/baidu/baidu_mec_client.i: platform/baidu/baidu_mec_client.cpp.i
.PHONY : platform/baidu/baidu_mec_client.i

# target to preprocess a source file
platform/baidu/baidu_mec_client.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/platform/baidu/baidu_mec_client.cpp.i
.PHONY : platform/baidu/baidu_mec_client.cpp.i

platform/baidu/baidu_mec_client.s: platform/baidu/baidu_mec_client.cpp.s
.PHONY : platform/baidu/baidu_mec_client.s

# target to generate assembly for a file
platform/baidu/baidu_mec_client.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/platform/baidu/baidu_mec_client.cpp.s
.PHONY : platform/baidu/baidu_mec_client.cpp.s

platform/baidu/baidu_mec_protocol.o: platform/baidu/baidu_mec_protocol.cpp.o
.PHONY : platform/baidu/baidu_mec_protocol.o

# target to build an object file
platform/baidu/baidu_mec_protocol.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/platform/baidu/baidu_mec_protocol.cpp.o
.PHONY : platform/baidu/baidu_mec_protocol.cpp.o

platform/baidu/baidu_mec_protocol.i: platform/baidu/baidu_mec_protocol.cpp.i
.PHONY : platform/baidu/baidu_mec_protocol.i

# target to preprocess a source file
platform/baidu/baidu_mec_protocol.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/platform/baidu/baidu_mec_protocol.cpp.i
.PHONY : platform/baidu/baidu_mec_protocol.cpp.i

platform/baidu/baidu_mec_protocol.s: platform/baidu/baidu_mec_protocol.cpp.s
.PHONY : platform/baidu/baidu_mec_protocol.s

# target to generate assembly for a file
platform/baidu/baidu_mec_protocol.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/platform/baidu/baidu_mec_protocol.cpp.s
.PHONY : platform/baidu/baidu_mec_protocol.cpp.s

platform/boyuan/boyuan_mqtt_protocol.o: platform/boyuan/boyuan_mqtt_protocol.cpp.o
.PHONY : platform/boyuan/boyuan_mqtt_protocol.o

# target to build an object file
platform/boyuan/boyuan_mqtt_protocol.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/platform/boyuan/boyuan_mqtt_protocol.cpp.o
.PHONY : platform/boyuan/boyuan_mqtt_protocol.cpp.o

platform/boyuan/boyuan_mqtt_protocol.i: platform/boyuan/boyuan_mqtt_protocol.cpp.i
.PHONY : platform/boyuan/boyuan_mqtt_protocol.i

# target to preprocess a source file
platform/boyuan/boyuan_mqtt_protocol.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/platform/boyuan/boyuan_mqtt_protocol.cpp.i
.PHONY : platform/boyuan/boyuan_mqtt_protocol.cpp.i

platform/boyuan/boyuan_mqtt_protocol.s: platform/boyuan/boyuan_mqtt_protocol.cpp.s
.PHONY : platform/boyuan/boyuan_mqtt_protocol.s

# target to generate assembly for a file
platform/boyuan/boyuan_mqtt_protocol.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/platform/boyuan/boyuan_mqtt_protocol.cpp.s
.PHONY : platform/boyuan/boyuan_mqtt_protocol.cpp.s

platform/boyuan/boyuan_platform_client.o: platform/boyuan/boyuan_platform_client.cpp.o
.PHONY : platform/boyuan/boyuan_platform_client.o

# target to build an object file
platform/boyuan/boyuan_platform_client.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/platform/boyuan/boyuan_platform_client.cpp.o
.PHONY : platform/boyuan/boyuan_platform_client.cpp.o

platform/boyuan/boyuan_platform_client.i: platform/boyuan/boyuan_platform_client.cpp.i
.PHONY : platform/boyuan/boyuan_platform_client.i

# target to preprocess a source file
platform/boyuan/boyuan_platform_client.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/platform/boyuan/boyuan_platform_client.cpp.i
.PHONY : platform/boyuan/boyuan_platform_client.cpp.i

platform/boyuan/boyuan_platform_client.s: platform/boyuan/boyuan_platform_client.cpp.s
.PHONY : platform/boyuan/boyuan_platform_client.s

# target to generate assembly for a file
platform/boyuan/boyuan_platform_client.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/platform/boyuan/boyuan_platform_client.cpp.s
.PHONY : platform/boyuan/boyuan_platform_client.cpp.s

platform/debug/debug_mec_client.o: platform/debug/debug_mec_client.cpp.o
.PHONY : platform/debug/debug_mec_client.o

# target to build an object file
platform/debug/debug_mec_client.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/platform/debug/debug_mec_client.cpp.o
.PHONY : platform/debug/debug_mec_client.cpp.o

platform/debug/debug_mec_client.i: platform/debug/debug_mec_client.cpp.i
.PHONY : platform/debug/debug_mec_client.i

# target to preprocess a source file
platform/debug/debug_mec_client.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/platform/debug/debug_mec_client.cpp.i
.PHONY : platform/debug/debug_mec_client.cpp.i

platform/debug/debug_mec_client.s: platform/debug/debug_mec_client.cpp.s
.PHONY : platform/debug/debug_mec_client.s

# target to generate assembly for a file
platform/debug/debug_mec_client.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/platform/debug/debug_mec_client.cpp.s
.PHONY : platform/debug/debug_mec_client.cpp.s

platform/debug/debug_platform_client.o: platform/debug/debug_platform_client.cpp.o
.PHONY : platform/debug/debug_platform_client.o

# target to build an object file
platform/debug/debug_platform_client.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/platform/debug/debug_platform_client.cpp.o
.PHONY : platform/debug/debug_platform_client.cpp.o

platform/debug/debug_platform_client.i: platform/debug/debug_platform_client.cpp.i
.PHONY : platform/debug/debug_platform_client.i

# target to preprocess a source file
platform/debug/debug_platform_client.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/platform/debug/debug_platform_client.cpp.i
.PHONY : platform/debug/debug_platform_client.cpp.i

platform/debug/debug_platform_client.s: platform/debug/debug_platform_client.cpp.s
.PHONY : platform/debug/debug_platform_client.s

# target to generate assembly for a file
platform/debug/debug_platform_client.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/platform/debug/debug_platform_client.cpp.s
.PHONY : platform/debug/debug_platform_client.cpp.s

platform/empty/empty_mec_client.o: platform/empty/empty_mec_client.cpp.o
.PHONY : platform/empty/empty_mec_client.o

# target to build an object file
platform/empty/empty_mec_client.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/platform/empty/empty_mec_client.cpp.o
.PHONY : platform/empty/empty_mec_client.cpp.o

platform/empty/empty_mec_client.i: platform/empty/empty_mec_client.cpp.i
.PHONY : platform/empty/empty_mec_client.i

# target to preprocess a source file
platform/empty/empty_mec_client.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/platform/empty/empty_mec_client.cpp.i
.PHONY : platform/empty/empty_mec_client.cpp.i

platform/empty/empty_mec_client.s: platform/empty/empty_mec_client.cpp.s
.PHONY : platform/empty/empty_mec_client.s

# target to generate assembly for a file
platform/empty/empty_mec_client.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/platform/empty/empty_mec_client.cpp.s
.PHONY : platform/empty/empty_mec_client.cpp.s

platform/empty/empty_platform_client.o: platform/empty/empty_platform_client.cpp.o
.PHONY : platform/empty/empty_platform_client.o

# target to build an object file
platform/empty/empty_platform_client.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/platform/empty/empty_platform_client.cpp.o
.PHONY : platform/empty/empty_platform_client.cpp.o

platform/empty/empty_platform_client.i: platform/empty/empty_platform_client.cpp.i
.PHONY : platform/empty/empty_platform_client.i

# target to preprocess a source file
platform/empty/empty_platform_client.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/platform/empty/empty_platform_client.cpp.i
.PHONY : platform/empty/empty_platform_client.cpp.i

platform/empty/empty_platform_client.s: platform/empty/empty_platform_client.cpp.s
.PHONY : platform/empty/empty_platform_client.s

# target to generate assembly for a file
platform/empty/empty_platform_client.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/platform/empty/empty_platform_client.cpp.s
.PHONY : platform/empty/empty_platform_client.cpp.s

platform/genvict/gv_http_client.o: platform/genvict/gv_http_client.cpp.o
.PHONY : platform/genvict/gv_http_client.o

# target to build an object file
platform/genvict/gv_http_client.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/platform/genvict/gv_http_client.cpp.o
.PHONY : platform/genvict/gv_http_client.cpp.o

platform/genvict/gv_http_client.i: platform/genvict/gv_http_client.cpp.i
.PHONY : platform/genvict/gv_http_client.i

# target to preprocess a source file
platform/genvict/gv_http_client.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/platform/genvict/gv_http_client.cpp.i
.PHONY : platform/genvict/gv_http_client.cpp.i

platform/genvict/gv_http_client.s: platform/genvict/gv_http_client.cpp.s
.PHONY : platform/genvict/gv_http_client.s

# target to generate assembly for a file
platform/genvict/gv_http_client.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/platform/genvict/gv_http_client.cpp.s
.PHONY : platform/genvict/gv_http_client.cpp.s

platform/genvict/gv_mec_client.o: platform/genvict/gv_mec_client.cpp.o
.PHONY : platform/genvict/gv_mec_client.o

# target to build an object file
platform/genvict/gv_mec_client.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/platform/genvict/gv_mec_client.cpp.o
.PHONY : platform/genvict/gv_mec_client.cpp.o

platform/genvict/gv_mec_client.i: platform/genvict/gv_mec_client.cpp.i
.PHONY : platform/genvict/gv_mec_client.i

# target to preprocess a source file
platform/genvict/gv_mec_client.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/platform/genvict/gv_mec_client.cpp.i
.PHONY : platform/genvict/gv_mec_client.cpp.i

platform/genvict/gv_mec_client.s: platform/genvict/gv_mec_client.cpp.s
.PHONY : platform/genvict/gv_mec_client.s

# target to generate assembly for a file
platform/genvict/gv_mec_client.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/platform/genvict/gv_mec_client.cpp.s
.PHONY : platform/genvict/gv_mec_client.cpp.s

platform/genvict/gv_mec_protocol.o: platform/genvict/gv_mec_protocol.cpp.o
.PHONY : platform/genvict/gv_mec_protocol.o

# target to build an object file
platform/genvict/gv_mec_protocol.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/platform/genvict/gv_mec_protocol.cpp.o
.PHONY : platform/genvict/gv_mec_protocol.cpp.o

platform/genvict/gv_mec_protocol.i: platform/genvict/gv_mec_protocol.cpp.i
.PHONY : platform/genvict/gv_mec_protocol.i

# target to preprocess a source file
platform/genvict/gv_mec_protocol.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/platform/genvict/gv_mec_protocol.cpp.i
.PHONY : platform/genvict/gv_mec_protocol.cpp.i

platform/genvict/gv_mec_protocol.s: platform/genvict/gv_mec_protocol.cpp.s
.PHONY : platform/genvict/gv_mec_protocol.s

# target to generate assembly for a file
platform/genvict/gv_mec_protocol.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/platform/genvict/gv_mec_protocol.cpp.s
.PHONY : platform/genvict/gv_mec_protocol.cpp.s

platform/genvict/gv_mqtt_client.o: platform/genvict/gv_mqtt_client.cpp.o
.PHONY : platform/genvict/gv_mqtt_client.o

# target to build an object file
platform/genvict/gv_mqtt_client.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/platform/genvict/gv_mqtt_client.cpp.o
.PHONY : platform/genvict/gv_mqtt_client.cpp.o

platform/genvict/gv_mqtt_client.i: platform/genvict/gv_mqtt_client.cpp.i
.PHONY : platform/genvict/gv_mqtt_client.i

# target to preprocess a source file
platform/genvict/gv_mqtt_client.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/platform/genvict/gv_mqtt_client.cpp.i
.PHONY : platform/genvict/gv_mqtt_client.cpp.i

platform/genvict/gv_mqtt_client.s: platform/genvict/gv_mqtt_client.cpp.s
.PHONY : platform/genvict/gv_mqtt_client.s

# target to generate assembly for a file
platform/genvict/gv_mqtt_client.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/platform/genvict/gv_mqtt_client.cpp.s
.PHONY : platform/genvict/gv_mqtt_client.cpp.s

platform/genvict/gv_mqtt_protocol.o: platform/genvict/gv_mqtt_protocol.cpp.o
.PHONY : platform/genvict/gv_mqtt_protocol.o

# target to build an object file
platform/genvict/gv_mqtt_protocol.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/platform/genvict/gv_mqtt_protocol.cpp.o
.PHONY : platform/genvict/gv_mqtt_protocol.cpp.o

platform/genvict/gv_mqtt_protocol.i: platform/genvict/gv_mqtt_protocol.cpp.i
.PHONY : platform/genvict/gv_mqtt_protocol.i

# target to preprocess a source file
platform/genvict/gv_mqtt_protocol.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/platform/genvict/gv_mqtt_protocol.cpp.i
.PHONY : platform/genvict/gv_mqtt_protocol.cpp.i

platform/genvict/gv_mqtt_protocol.s: platform/genvict/gv_mqtt_protocol.cpp.s
.PHONY : platform/genvict/gv_mqtt_protocol.s

# target to generate assembly for a file
platform/genvict/gv_mqtt_protocol.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/platform/genvict/gv_mqtt_protocol.cpp.s
.PHONY : platform/genvict/gv_mqtt_protocol.cpp.s

platform/genvict/gv_platform_client.o: platform/genvict/gv_platform_client.cpp.o
.PHONY : platform/genvict/gv_platform_client.o

# target to build an object file
platform/genvict/gv_platform_client.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/platform/genvict/gv_platform_client.cpp.o
.PHONY : platform/genvict/gv_platform_client.cpp.o

platform/genvict/gv_platform_client.i: platform/genvict/gv_platform_client.cpp.i
.PHONY : platform/genvict/gv_platform_client.i

# target to preprocess a source file
platform/genvict/gv_platform_client.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/platform/genvict/gv_platform_client.cpp.i
.PHONY : platform/genvict/gv_platform_client.cpp.i

platform/genvict/gv_platform_client.s: platform/genvict/gv_platform_client.cpp.s
.PHONY : platform/genvict/gv_platform_client.s

# target to generate assembly for a file
platform/genvict/gv_platform_client.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/platform/genvict/gv_platform_client.cpp.s
.PHONY : platform/genvict/gv_platform_client.cpp.s

platform/genvict_sdk/gv_sdk_protocol.o: platform/genvict_sdk/gv_sdk_protocol.cpp.o
.PHONY : platform/genvict_sdk/gv_sdk_protocol.o

# target to build an object file
platform/genvict_sdk/gv_sdk_protocol.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/platform/genvict_sdk/gv_sdk_protocol.cpp.o
.PHONY : platform/genvict_sdk/gv_sdk_protocol.cpp.o

platform/genvict_sdk/gv_sdk_protocol.i: platform/genvict_sdk/gv_sdk_protocol.cpp.i
.PHONY : platform/genvict_sdk/gv_sdk_protocol.i

# target to preprocess a source file
platform/genvict_sdk/gv_sdk_protocol.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/platform/genvict_sdk/gv_sdk_protocol.cpp.i
.PHONY : platform/genvict_sdk/gv_sdk_protocol.cpp.i

platform/genvict_sdk/gv_sdk_protocol.s: platform/genvict_sdk/gv_sdk_protocol.cpp.s
.PHONY : platform/genvict_sdk/gv_sdk_protocol.s

# target to generate assembly for a file
platform/genvict_sdk/gv_sdk_protocol.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/platform/genvict_sdk/gv_sdk_protocol.cpp.s
.PHONY : platform/genvict_sdk/gv_sdk_protocol.cpp.s

platform/genvict_sdk/gv_sdk_service.o: platform/genvict_sdk/gv_sdk_service.cpp.o
.PHONY : platform/genvict_sdk/gv_sdk_service.o

# target to build an object file
platform/genvict_sdk/gv_sdk_service.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/platform/genvict_sdk/gv_sdk_service.cpp.o
.PHONY : platform/genvict_sdk/gv_sdk_service.cpp.o

platform/genvict_sdk/gv_sdk_service.i: platform/genvict_sdk/gv_sdk_service.cpp.i
.PHONY : platform/genvict_sdk/gv_sdk_service.i

# target to preprocess a source file
platform/genvict_sdk/gv_sdk_service.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/platform/genvict_sdk/gv_sdk_service.cpp.i
.PHONY : platform/genvict_sdk/gv_sdk_service.cpp.i

platform/genvict_sdk/gv_sdk_service.s: platform/genvict_sdk/gv_sdk_service.cpp.s
.PHONY : platform/genvict_sdk/gv_sdk_service.s

# target to generate assembly for a file
platform/genvict_sdk/gv_sdk_service.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/platform/genvict_sdk/gv_sdk_service.cpp.s
.PHONY : platform/genvict_sdk/gv_sdk_service.cpp.s

platform/gosuncn/gxx_mec_client.o: platform/gosuncn/gxx_mec_client.cpp.o
.PHONY : platform/gosuncn/gxx_mec_client.o

# target to build an object file
platform/gosuncn/gxx_mec_client.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/platform/gosuncn/gxx_mec_client.cpp.o
.PHONY : platform/gosuncn/gxx_mec_client.cpp.o

platform/gosuncn/gxx_mec_client.i: platform/gosuncn/gxx_mec_client.cpp.i
.PHONY : platform/gosuncn/gxx_mec_client.i

# target to preprocess a source file
platform/gosuncn/gxx_mec_client.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/platform/gosuncn/gxx_mec_client.cpp.i
.PHONY : platform/gosuncn/gxx_mec_client.cpp.i

platform/gosuncn/gxx_mec_client.s: platform/gosuncn/gxx_mec_client.cpp.s
.PHONY : platform/gosuncn/gxx_mec_client.s

# target to generate assembly for a file
platform/gosuncn/gxx_mec_client.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/platform/gosuncn/gxx_mec_client.cpp.s
.PHONY : platform/gosuncn/gxx_mec_client.cpp.s

platform/gosuncn/gxx_mec_protocol.o: platform/gosuncn/gxx_mec_protocol.cpp.o
.PHONY : platform/gosuncn/gxx_mec_protocol.o

# target to build an object file
platform/gosuncn/gxx_mec_protocol.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/platform/gosuncn/gxx_mec_protocol.cpp.o
.PHONY : platform/gosuncn/gxx_mec_protocol.cpp.o

platform/gosuncn/gxx_mec_protocol.i: platform/gosuncn/gxx_mec_protocol.cpp.i
.PHONY : platform/gosuncn/gxx_mec_protocol.i

# target to preprocess a source file
platform/gosuncn/gxx_mec_protocol.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/platform/gosuncn/gxx_mec_protocol.cpp.i
.PHONY : platform/gosuncn/gxx_mec_protocol.cpp.i

platform/gosuncn/gxx_mec_protocol.s: platform/gosuncn/gxx_mec_protocol.cpp.s
.PHONY : platform/gosuncn/gxx_mec_protocol.s

# target to generate assembly for a file
platform/gosuncn/gxx_mec_protocol.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/platform/gosuncn/gxx_mec_protocol.cpp.s
.PHONY : platform/gosuncn/gxx_mec_protocol.cpp.s

platform/huawei/huawei_mec_client.o: platform/huawei/huawei_mec_client.cpp.o
.PHONY : platform/huawei/huawei_mec_client.o

# target to build an object file
platform/huawei/huawei_mec_client.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/platform/huawei/huawei_mec_client.cpp.o
.PHONY : platform/huawei/huawei_mec_client.cpp.o

platform/huawei/huawei_mec_client.i: platform/huawei/huawei_mec_client.cpp.i
.PHONY : platform/huawei/huawei_mec_client.i

# target to preprocess a source file
platform/huawei/huawei_mec_client.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/platform/huawei/huawei_mec_client.cpp.i
.PHONY : platform/huawei/huawei_mec_client.cpp.i

platform/huawei/huawei_mec_client.s: platform/huawei/huawei_mec_client.cpp.s
.PHONY : platform/huawei/huawei_mec_client.s

# target to generate assembly for a file
platform/huawei/huawei_mec_client.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/platform/huawei/huawei_mec_client.cpp.s
.PHONY : platform/huawei/huawei_mec_client.cpp.s

platform/huawei/huawei_mec_protocol.o: platform/huawei/huawei_mec_protocol.cpp.o
.PHONY : platform/huawei/huawei_mec_protocol.o

# target to build an object file
platform/huawei/huawei_mec_protocol.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/platform/huawei/huawei_mec_protocol.cpp.o
.PHONY : platform/huawei/huawei_mec_protocol.cpp.o

platform/huawei/huawei_mec_protocol.i: platform/huawei/huawei_mec_protocol.cpp.i
.PHONY : platform/huawei/huawei_mec_protocol.i

# target to preprocess a source file
platform/huawei/huawei_mec_protocol.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/platform/huawei/huawei_mec_protocol.cpp.i
.PHONY : platform/huawei/huawei_mec_protocol.cpp.i

platform/huawei/huawei_mec_protocol.s: platform/huawei/huawei_mec_protocol.cpp.s
.PHONY : platform/huawei/huawei_mec_protocol.s

# target to generate assembly for a file
platform/huawei/huawei_mec_protocol.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/platform/huawei/huawei_mec_protocol.cpp.s
.PHONY : platform/huawei/huawei_mec_protocol.cpp.s

platform/icloud_platform_client.o: platform/icloud_platform_client.cpp.o
.PHONY : platform/icloud_platform_client.o

# target to build an object file
platform/icloud_platform_client.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/platform/icloud_platform_client.cpp.o
.PHONY : platform/icloud_platform_client.cpp.o

platform/icloud_platform_client.i: platform/icloud_platform_client.cpp.i
.PHONY : platform/icloud_platform_client.i

# target to preprocess a source file
platform/icloud_platform_client.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/platform/icloud_platform_client.cpp.i
.PHONY : platform/icloud_platform_client.cpp.i

platform/icloud_platform_client.s: platform/icloud_platform_client.cpp.s
.PHONY : platform/icloud_platform_client.s

# target to generate assembly for a file
platform/icloud_platform_client.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/platform/icloud_platform_client.cpp.s
.PHONY : platform/icloud_platform_client.cpp.s

platform/imec_client.o: platform/imec_client.cpp.o
.PHONY : platform/imec_client.o

# target to build an object file
platform/imec_client.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/platform/imec_client.cpp.o
.PHONY : platform/imec_client.cpp.o

platform/imec_client.i: platform/imec_client.cpp.i
.PHONY : platform/imec_client.i

# target to preprocess a source file
platform/imec_client.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/platform/imec_client.cpp.i
.PHONY : platform/imec_client.cpp.i

platform/imec_client.s: platform/imec_client.cpp.s
.PHONY : platform/imec_client.s

# target to generate assembly for a file
platform/imec_client.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/platform/imec_client.cpp.s
.PHONY : platform/imec_client.cpp.s

platform/multi/multi_mec_client.o: platform/multi/multi_mec_client.cpp.o
.PHONY : platform/multi/multi_mec_client.o

# target to build an object file
platform/multi/multi_mec_client.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/platform/multi/multi_mec_client.cpp.o
.PHONY : platform/multi/multi_mec_client.cpp.o

platform/multi/multi_mec_client.i: platform/multi/multi_mec_client.cpp.i
.PHONY : platform/multi/multi_mec_client.i

# target to preprocess a source file
platform/multi/multi_mec_client.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/platform/multi/multi_mec_client.cpp.i
.PHONY : platform/multi/multi_mec_client.cpp.i

platform/multi/multi_mec_client.s: platform/multi/multi_mec_client.cpp.s
.PHONY : platform/multi/multi_mec_client.s

# target to generate assembly for a file
platform/multi/multi_mec_client.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/platform/multi/multi_mec_client.cpp.s
.PHONY : platform/multi/multi_mec_client.cpp.s

platform/multi/multi_platform_client.o: platform/multi/multi_platform_client.cpp.o
.PHONY : platform/multi/multi_platform_client.o

# target to build an object file
platform/multi/multi_platform_client.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/platform/multi/multi_platform_client.cpp.o
.PHONY : platform/multi/multi_platform_client.cpp.o

platform/multi/multi_platform_client.i: platform/multi/multi_platform_client.cpp.i
.PHONY : platform/multi/multi_platform_client.i

# target to preprocess a source file
platform/multi/multi_platform_client.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/platform/multi/multi_platform_client.cpp.i
.PHONY : platform/multi/multi_platform_client.cpp.i

platform/multi/multi_platform_client.s: platform/multi/multi_platform_client.cpp.s
.PHONY : platform/multi/multi_platform_client.s

# target to generate assembly for a file
platform/multi/multi_platform_client.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/platform/multi/multi_platform_client.cpp.s
.PHONY : platform/multi/multi_platform_client.cpp.s

platform/tianan/mec_protocol/perception.pb.o: platform/tianan/mec_protocol/perception.pb.cc.o
.PHONY : platform/tianan/mec_protocol/perception.pb.o

# target to build an object file
platform/tianan/mec_protocol/perception.pb.cc.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/platform/tianan/mec_protocol/perception.pb.cc.o
.PHONY : platform/tianan/mec_protocol/perception.pb.cc.o

platform/tianan/mec_protocol/perception.pb.i: platform/tianan/mec_protocol/perception.pb.cc.i
.PHONY : platform/tianan/mec_protocol/perception.pb.i

# target to preprocess a source file
platform/tianan/mec_protocol/perception.pb.cc.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/platform/tianan/mec_protocol/perception.pb.cc.i
.PHONY : platform/tianan/mec_protocol/perception.pb.cc.i

platform/tianan/mec_protocol/perception.pb.s: platform/tianan/mec_protocol/perception.pb.cc.s
.PHONY : platform/tianan/mec_protocol/perception.pb.s

# target to generate assembly for a file
platform/tianan/mec_protocol/perception.pb.cc.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/platform/tianan/mec_protocol/perception.pb.cc.s
.PHONY : platform/tianan/mec_protocol/perception.pb.cc.s

platform/tianan/tianan_http_client.o: platform/tianan/tianan_http_client.cpp.o
.PHONY : platform/tianan/tianan_http_client.o

# target to build an object file
platform/tianan/tianan_http_client.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/platform/tianan/tianan_http_client.cpp.o
.PHONY : platform/tianan/tianan_http_client.cpp.o

platform/tianan/tianan_http_client.i: platform/tianan/tianan_http_client.cpp.i
.PHONY : platform/tianan/tianan_http_client.i

# target to preprocess a source file
platform/tianan/tianan_http_client.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/platform/tianan/tianan_http_client.cpp.i
.PHONY : platform/tianan/tianan_http_client.cpp.i

platform/tianan/tianan_http_client.s: platform/tianan/tianan_http_client.cpp.s
.PHONY : platform/tianan/tianan_http_client.s

# target to generate assembly for a file
platform/tianan/tianan_http_client.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/platform/tianan/tianan_http_client.cpp.s
.PHONY : platform/tianan/tianan_http_client.cpp.s

platform/tianan/tianan_mec_client.o: platform/tianan/tianan_mec_client.cpp.o
.PHONY : platform/tianan/tianan_mec_client.o

# target to build an object file
platform/tianan/tianan_mec_client.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/platform/tianan/tianan_mec_client.cpp.o
.PHONY : platform/tianan/tianan_mec_client.cpp.o

platform/tianan/tianan_mec_client.i: platform/tianan/tianan_mec_client.cpp.i
.PHONY : platform/tianan/tianan_mec_client.i

# target to preprocess a source file
platform/tianan/tianan_mec_client.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/platform/tianan/tianan_mec_client.cpp.i
.PHONY : platform/tianan/tianan_mec_client.cpp.i

platform/tianan/tianan_mec_client.s: platform/tianan/tianan_mec_client.cpp.s
.PHONY : platform/tianan/tianan_mec_client.s

# target to generate assembly for a file
platform/tianan/tianan_mec_client.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/platform/tianan/tianan_mec_client.cpp.s
.PHONY : platform/tianan/tianan_mec_client.cpp.s

platform/tianan/tianan_mec_protocol.o: platform/tianan/tianan_mec_protocol.cpp.o
.PHONY : platform/tianan/tianan_mec_protocol.o

# target to build an object file
platform/tianan/tianan_mec_protocol.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/platform/tianan/tianan_mec_protocol.cpp.o
.PHONY : platform/tianan/tianan_mec_protocol.cpp.o

platform/tianan/tianan_mec_protocol.i: platform/tianan/tianan_mec_protocol.cpp.i
.PHONY : platform/tianan/tianan_mec_protocol.i

# target to preprocess a source file
platform/tianan/tianan_mec_protocol.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/platform/tianan/tianan_mec_protocol.cpp.i
.PHONY : platform/tianan/tianan_mec_protocol.cpp.i

platform/tianan/tianan_mec_protocol.s: platform/tianan/tianan_mec_protocol.cpp.s
.PHONY : platform/tianan/tianan_mec_protocol.s

# target to generate assembly for a file
platform/tianan/tianan_mec_protocol.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/platform/tianan/tianan_mec_protocol.cpp.s
.PHONY : platform/tianan/tianan_mec_protocol.cpp.s

platform/tianan/tianan_mqtt_client.o: platform/tianan/tianan_mqtt_client.cpp.o
.PHONY : platform/tianan/tianan_mqtt_client.o

# target to build an object file
platform/tianan/tianan_mqtt_client.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/platform/tianan/tianan_mqtt_client.cpp.o
.PHONY : platform/tianan/tianan_mqtt_client.cpp.o

platform/tianan/tianan_mqtt_client.i: platform/tianan/tianan_mqtt_client.cpp.i
.PHONY : platform/tianan/tianan_mqtt_client.i

# target to preprocess a source file
platform/tianan/tianan_mqtt_client.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/platform/tianan/tianan_mqtt_client.cpp.i
.PHONY : platform/tianan/tianan_mqtt_client.cpp.i

platform/tianan/tianan_mqtt_client.s: platform/tianan/tianan_mqtt_client.cpp.s
.PHONY : platform/tianan/tianan_mqtt_client.s

# target to generate assembly for a file
platform/tianan/tianan_mqtt_client.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/platform/tianan/tianan_mqtt_client.cpp.s
.PHONY : platform/tianan/tianan_mqtt_client.cpp.s

platform/tianan/tianan_mqtt_protocol.o: platform/tianan/tianan_mqtt_protocol.cpp.o
.PHONY : platform/tianan/tianan_mqtt_protocol.o

# target to build an object file
platform/tianan/tianan_mqtt_protocol.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/platform/tianan/tianan_mqtt_protocol.cpp.o
.PHONY : platform/tianan/tianan_mqtt_protocol.cpp.o

platform/tianan/tianan_mqtt_protocol.i: platform/tianan/tianan_mqtt_protocol.cpp.i
.PHONY : platform/tianan/tianan_mqtt_protocol.i

# target to preprocess a source file
platform/tianan/tianan_mqtt_protocol.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/platform/tianan/tianan_mqtt_protocol.cpp.i
.PHONY : platform/tianan/tianan_mqtt_protocol.cpp.i

platform/tianan/tianan_mqtt_protocol.s: platform/tianan/tianan_mqtt_protocol.cpp.s
.PHONY : platform/tianan/tianan_mqtt_protocol.s

# target to generate assembly for a file
platform/tianan/tianan_mqtt_protocol.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/platform/tianan/tianan_mqtt_protocol.cpp.s
.PHONY : platform/tianan/tianan_mqtt_protocol.cpp.s

platform/tianan/tianan_platform_client.o: platform/tianan/tianan_platform_client.cpp.o
.PHONY : platform/tianan/tianan_platform_client.o

# target to build an object file
platform/tianan/tianan_platform_client.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/platform/tianan/tianan_platform_client.cpp.o
.PHONY : platform/tianan/tianan_platform_client.cpp.o

platform/tianan/tianan_platform_client.i: platform/tianan/tianan_platform_client.cpp.i
.PHONY : platform/tianan/tianan_platform_client.i

# target to preprocess a source file
platform/tianan/tianan_platform_client.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/platform/tianan/tianan_platform_client.cpp.i
.PHONY : platform/tianan/tianan_platform_client.cpp.i

platform/tianan/tianan_platform_client.s: platform/tianan/tianan_platform_client.cpp.s
.PHONY : platform/tianan/tianan_platform_client.s

# target to generate assembly for a file
platform/tianan/tianan_platform_client.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/platform/tianan/tianan_platform_client.cpp.s
.PHONY : platform/tianan/tianan_platform_client.cpp.s

platform/tianan/tianan_spat_client.o: platform/tianan/tianan_spat_client.cpp.o
.PHONY : platform/tianan/tianan_spat_client.o

# target to build an object file
platform/tianan/tianan_spat_client.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/platform/tianan/tianan_spat_client.cpp.o
.PHONY : platform/tianan/tianan_spat_client.cpp.o

platform/tianan/tianan_spat_client.i: platform/tianan/tianan_spat_client.cpp.i
.PHONY : platform/tianan/tianan_spat_client.i

# target to preprocess a source file
platform/tianan/tianan_spat_client.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/platform/tianan/tianan_spat_client.cpp.i
.PHONY : platform/tianan/tianan_spat_client.cpp.i

platform/tianan/tianan_spat_client.s: platform/tianan/tianan_spat_client.cpp.s
.PHONY : platform/tianan/tianan_spat_client.s

# target to generate assembly for a file
platform/tianan/tianan_spat_client.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/platform/tianan/tianan_spat_client.cpp.s
.PHONY : platform/tianan/tianan_spat_client.cpp.s

platform/tianan/tianan_spat_protocol.o: platform/tianan/tianan_spat_protocol.cpp.o
.PHONY : platform/tianan/tianan_spat_protocol.o

# target to build an object file
platform/tianan/tianan_spat_protocol.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/platform/tianan/tianan_spat_protocol.cpp.o
.PHONY : platform/tianan/tianan_spat_protocol.cpp.o

platform/tianan/tianan_spat_protocol.i: platform/tianan/tianan_spat_protocol.cpp.i
.PHONY : platform/tianan/tianan_spat_protocol.i

# target to preprocess a source file
platform/tianan/tianan_spat_protocol.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/platform/tianan/tianan_spat_protocol.cpp.i
.PHONY : platform/tianan/tianan_spat_protocol.cpp.i

platform/tianan/tianan_spat_protocol.s: platform/tianan/tianan_spat_protocol.cpp.s
.PHONY : platform/tianan/tianan_spat_protocol.s

# target to generate assembly for a file
platform/tianan/tianan_spat_protocol.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/platform/tianan/tianan_spat_protocol.cpp.s
.PHONY : platform/tianan/tianan_spat_protocol.cpp.s

platform/tianan_obu/tianan_obu_http_client.o: platform/tianan_obu/tianan_obu_http_client.cpp.o
.PHONY : platform/tianan_obu/tianan_obu_http_client.o

# target to build an object file
platform/tianan_obu/tianan_obu_http_client.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/platform/tianan_obu/tianan_obu_http_client.cpp.o
.PHONY : platform/tianan_obu/tianan_obu_http_client.cpp.o

platform/tianan_obu/tianan_obu_http_client.i: platform/tianan_obu/tianan_obu_http_client.cpp.i
.PHONY : platform/tianan_obu/tianan_obu_http_client.i

# target to preprocess a source file
platform/tianan_obu/tianan_obu_http_client.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/platform/tianan_obu/tianan_obu_http_client.cpp.i
.PHONY : platform/tianan_obu/tianan_obu_http_client.cpp.i

platform/tianan_obu/tianan_obu_http_client.s: platform/tianan_obu/tianan_obu_http_client.cpp.s
.PHONY : platform/tianan_obu/tianan_obu_http_client.s

# target to generate assembly for a file
platform/tianan_obu/tianan_obu_http_client.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/platform/tianan_obu/tianan_obu_http_client.cpp.s
.PHONY : platform/tianan_obu/tianan_obu_http_client.cpp.s

platform/tianan_obu/tianan_obu_platform_client.o: platform/tianan_obu/tianan_obu_platform_client.cpp.o
.PHONY : platform/tianan_obu/tianan_obu_platform_client.o

# target to build an object file
platform/tianan_obu/tianan_obu_platform_client.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/platform/tianan_obu/tianan_obu_platform_client.cpp.o
.PHONY : platform/tianan_obu/tianan_obu_platform_client.cpp.o

platform/tianan_obu/tianan_obu_platform_client.i: platform/tianan_obu/tianan_obu_platform_client.cpp.i
.PHONY : platform/tianan_obu/tianan_obu_platform_client.i

# target to preprocess a source file
platform/tianan_obu/tianan_obu_platform_client.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/platform/tianan_obu/tianan_obu_platform_client.cpp.i
.PHONY : platform/tianan_obu/tianan_obu_platform_client.cpp.i

platform/tianan_obu/tianan_obu_platform_client.s: platform/tianan_obu/tianan_obu_platform_client.cpp.s
.PHONY : platform/tianan_obu/tianan_obu_platform_client.s

# target to generate assembly for a file
platform/tianan_obu/tianan_obu_platform_client.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/platform/tianan_obu/tianan_obu_platform_client.cpp.s
.PHONY : platform/tianan_obu/tianan_obu_platform_client.cpp.s

platform/tianan_obu/tianan_obu_protocal.o: platform/tianan_obu/tianan_obu_protocal.cpp.o
.PHONY : platform/tianan_obu/tianan_obu_protocal.o

# target to build an object file
platform/tianan_obu/tianan_obu_protocal.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/platform/tianan_obu/tianan_obu_protocal.cpp.o
.PHONY : platform/tianan_obu/tianan_obu_protocal.cpp.o

platform/tianan_obu/tianan_obu_protocal.i: platform/tianan_obu/tianan_obu_protocal.cpp.i
.PHONY : platform/tianan_obu/tianan_obu_protocal.i

# target to preprocess a source file
platform/tianan_obu/tianan_obu_protocal.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/platform/tianan_obu/tianan_obu_protocal.cpp.i
.PHONY : platform/tianan_obu/tianan_obu_protocal.cpp.i

platform/tianan_obu/tianan_obu_protocal.s: platform/tianan_obu/tianan_obu_protocal.cpp.s
.PHONY : platform/tianan_obu/tianan_obu_protocal.s

# target to generate assembly for a file
platform/tianan_obu/tianan_obu_protocal.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/platform/tianan_obu/tianan_obu_protocal.cpp.s
.PHONY : platform/tianan_obu/tianan_obu_protocal.cpp.s

platform/tianan_obu/tiannan_obu_mqtt_client.o: platform/tianan_obu/tiannan_obu_mqtt_client.cpp.o
.PHONY : platform/tianan_obu/tiannan_obu_mqtt_client.o

# target to build an object file
platform/tianan_obu/tiannan_obu_mqtt_client.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/platform/tianan_obu/tiannan_obu_mqtt_client.cpp.o
.PHONY : platform/tianan_obu/tiannan_obu_mqtt_client.cpp.o

platform/tianan_obu/tiannan_obu_mqtt_client.i: platform/tianan_obu/tiannan_obu_mqtt_client.cpp.i
.PHONY : platform/tianan_obu/tiannan_obu_mqtt_client.i

# target to preprocess a source file
platform/tianan_obu/tiannan_obu_mqtt_client.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/platform/tianan_obu/tiannan_obu_mqtt_client.cpp.i
.PHONY : platform/tianan_obu/tiannan_obu_mqtt_client.cpp.i

platform/tianan_obu/tiannan_obu_mqtt_client.s: platform/tianan_obu/tiannan_obu_mqtt_client.cpp.s
.PHONY : platform/tianan_obu/tiannan_obu_mqtt_client.s

# target to generate assembly for a file
platform/tianan_obu/tiannan_obu_mqtt_client.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/platform/tianan_obu/tiannan_obu_mqtt_client.cpp.s
.PHONY : platform/tianan_obu/tiannan_obu_mqtt_client.cpp.s

test/http_client_tool.o: test/http_client_tool.cpp.o
.PHONY : test/http_client_tool.o

# target to build an object file
test/http_client_tool.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/test/http_client_tool.cpp.o
.PHONY : test/http_client_tool.cpp.o

test/http_client_tool.i: test/http_client_tool.cpp.i
.PHONY : test/http_client_tool.i

# target to preprocess a source file
test/http_client_tool.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/test/http_client_tool.cpp.i
.PHONY : test/http_client_tool.cpp.i

test/http_client_tool.s: test/http_client_tool.cpp.s
.PHONY : test/http_client_tool.s

# target to generate assembly for a file
test/http_client_tool.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/test/http_client_tool.cpp.s
.PHONY : test/http_client_tool.cpp.s

test/tcp_client_tool.o: test/tcp_client_tool.cpp.o
.PHONY : test/tcp_client_tool.o

# target to build an object file
test/tcp_client_tool.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/test/tcp_client_tool.cpp.o
.PHONY : test/tcp_client_tool.cpp.o

test/tcp_client_tool.i: test/tcp_client_tool.cpp.i
.PHONY : test/tcp_client_tool.i

# target to preprocess a source file
test/tcp_client_tool.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/test/tcp_client_tool.cpp.i
.PHONY : test/tcp_client_tool.cpp.i

test/tcp_client_tool.s: test/tcp_client_tool.cpp.s
.PHONY : test/tcp_client_tool.s

# target to generate assembly for a file
test/tcp_client_tool.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/test/tcp_client_tool.cpp.s
.PHONY : test/tcp_client_tool.cpp.s

test/tcp_service_tool.o: test/tcp_service_tool.cpp.o
.PHONY : test/tcp_service_tool.o

# target to build an object file
test/tcp_service_tool.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/test/tcp_service_tool.cpp.o
.PHONY : test/tcp_service_tool.cpp.o

test/tcp_service_tool.i: test/tcp_service_tool.cpp.i
.PHONY : test/tcp_service_tool.i

# target to preprocess a source file
test/tcp_service_tool.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/test/tcp_service_tool.cpp.i
.PHONY : test/tcp_service_tool.cpp.i

test/tcp_service_tool.s: test/tcp_service_tool.cpp.s
.PHONY : test/tcp_service_tool.s

# target to generate assembly for a file
test/tcp_service_tool.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/test/tcp_service_tool.cpp.s
.PHONY : test/tcp_service_tool.cpp.s

test/test_code.o: test/test_code.cpp.o
.PHONY : test/test_code.o

# target to build an object file
test/test_code.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/test/test_code.cpp.o
.PHONY : test/test_code.cpp.o

test/test_code.i: test/test_code.cpp.i
.PHONY : test/test_code.i

# target to preprocess a source file
test/test_code.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/test/test_code.cpp.i
.PHONY : test/test_code.cpp.i

test/test_code.s: test/test_code.cpp.s
.PHONY : test/test_code.s

# target to generate assembly for a file
test/test_code.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/test/test_code.cpp.s
.PHONY : test/test_code.cpp.s

test/test_tianan_mec_protocal.o: test/test_tianan_mec_protocal.cpp.o
.PHONY : test/test_tianan_mec_protocal.o

# target to build an object file
test/test_tianan_mec_protocal.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/test/test_tianan_mec_protocal.cpp.o
.PHONY : test/test_tianan_mec_protocal.cpp.o

test/test_tianan_mec_protocal.i: test/test_tianan_mec_protocal.cpp.i
.PHONY : test/test_tianan_mec_protocal.i

# target to preprocess a source file
test/test_tianan_mec_protocal.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/test/test_tianan_mec_protocal.cpp.i
.PHONY : test/test_tianan_mec_protocal.cpp.i

test/test_tianan_mec_protocal.s: test/test_tianan_mec_protocal.cpp.s
.PHONY : test/test_tianan_mec_protocal.s

# target to generate assembly for a file
test/test_tianan_mec_protocal.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/test/test_tianan_mec_protocal.cpp.s
.PHONY : test/test_tianan_mec_protocal.cpp.s

v2x_client/v2x_client.o: v2x_client/v2x_client.cpp.o
.PHONY : v2x_client/v2x_client.o

# target to build an object file
v2x_client/v2x_client.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/v2x_client/v2x_client.cpp.o
.PHONY : v2x_client/v2x_client.cpp.o

v2x_client/v2x_client.i: v2x_client/v2x_client.cpp.i
.PHONY : v2x_client/v2x_client.i

# target to preprocess a source file
v2x_client/v2x_client.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/v2x_client/v2x_client.cpp.i
.PHONY : v2x_client/v2x_client.cpp.i

v2x_client/v2x_client.s: v2x_client/v2x_client.cpp.s
.PHONY : v2x_client/v2x_client.s

# target to generate assembly for a file
v2x_client/v2x_client.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/v2x_client/v2x_client.cpp.s
.PHONY : v2x_client/v2x_client.cpp.s

v2x_client/v2x_protocol.o: v2x_client/v2x_protocol.cpp.o
.PHONY : v2x_client/v2x_protocol.o

# target to build an object file
v2x_client/v2x_protocol.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/v2x_client/v2x_protocol.cpp.o
.PHONY : v2x_client/v2x_protocol.cpp.o

v2x_client/v2x_protocol.i: v2x_client/v2x_protocol.cpp.i
.PHONY : v2x_client/v2x_protocol.i

# target to preprocess a source file
v2x_client/v2x_protocol.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/v2x_client/v2x_protocol.cpp.i
.PHONY : v2x_client/v2x_protocol.cpp.i

v2x_client/v2x_protocol.s: v2x_client/v2x_protocol.cpp.s
.PHONY : v2x_client/v2x_protocol.s

# target to generate assembly for a file
v2x_client/v2x_protocol.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mec_channel.dir/build.make CMakeFiles/mec_channel.dir/v2x_client/v2x_protocol.cpp.s
.PHONY : v2x_client/v2x_protocol.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... rebuild_cache"
	@echo "... git_version.h"
	@echo "... send110"
	@echo "... send113"
	@echo "... send144"
	@echo "... send146"
	@echo "... send247"
	@echo "... mec_channel"
	@echo "... app/config.o"
	@echo "... app/config.i"
	@echo "... app/config.s"
	@echo "... app/main.o"
	@echo "... app/main.i"
	@echo "... app/main.s"
	@echo "... app/service.o"
	@echo "... app/service.i"
	@echo "... app/service.s"
	@echo "... dvin_client/dvin_debug_client.o"
	@echo "... dvin_client/dvin_debug_client.i"
	@echo "... dvin_client/dvin_debug_client.s"
	@echo "... engine_module/config/config_base.o"
	@echo "... engine_module/config/config_base.i"
	@echo "... engine_module/config/config_base.s"
	@echo "... engine_module/engine/epoll_engine.o"
	@echo "... engine_module/engine/epoll_engine.i"
	@echo "... engine_module/engine/epoll_engine.s"
	@echo "... engine_module/engine/event_engine.o"
	@echo "... engine_module/engine/event_engine.i"
	@echo "... engine_module/engine/event_engine.s"
	@echo "... engine_module/engine/http_client_mgr.o"
	@echo "... engine_module/engine/http_client_mgr.i"
	@echo "... engine_module/engine/http_client_mgr.s"
	@echo "... engine_module/engine/http_service_mgr.o"
	@echo "... engine_module/engine/http_service_mgr.i"
	@echo "... engine_module/engine/http_service_mgr.s"
	@echo "... engine_module/engine/mqtt_client_mgr.o"
	@echo "... engine_module/engine/mqtt_client_mgr.i"
	@echo "... engine_module/engine/mqtt_client_mgr.s"
	@echo "... engine_module/engine/tcp_mgr.o"
	@echo "... engine_module/engine/tcp_mgr.i"
	@echo "... engine_module/engine/tcp_mgr.s"
	@echo "... engine_module/engine/udp_mgr.o"
	@echo "... engine_module/engine/udp_mgr.i"
	@echo "... engine_module/engine/udp_mgr.s"
	@echo "... engine_module/log/log.o"
	@echo "... engine_module/log/log.i"
	@echo "... engine_module/log/log.s"
	@echo "... engine_module/mem/frame_buffer.o"
	@echo "... engine_module/mem/frame_buffer.i"
	@echo "... engine_module/mem/frame_buffer.s"
	@echo "... engine_module/signal/signal_handler.o"
	@echo "... engine_module/signal/signal_handler.i"
	@echo "... engine_module/signal/signal_handler.s"
	@echo "... engine_module/thread_resource/tr_center.o"
	@echo "... engine_module/thread_resource/tr_center.i"
	@echo "... engine_module/thread_resource/tr_center.s"
	@echo "... engine_module/utility/app_clock.o"
	@echo "... engine_module/utility/app_clock.i"
	@echo "... engine_module/utility/app_clock.s"
	@echo "... engine_module/utility/common_utility.o"
	@echo "... engine_module/utility/common_utility.i"
	@echo "... engine_module/utility/common_utility.s"
	@echo "... engine_module/utility/coordinate_convert.o"
	@echo "... engine_module/utility/coordinate_convert.i"
	@echo "... engine_module/utility/coordinate_convert.s"
	@echo "... engine_module/utility/crc16.o"
	@echo "... engine_module/utility/crc16.i"
	@echo "... engine_module/utility/crc16.s"
	@echo "... engine_module/utility/dev_performance.o"
	@echo "... engine_module/utility/dev_performance.i"
	@echo "... engine_module/utility/dev_performance.s"
	@echo "... engine_module/utility/dir_op.o"
	@echo "... engine_module/utility/dir_op.i"
	@echo "... engine_module/utility/dir_op.s"
	@echo "... engine_module/utility/hex_str.o"
	@echo "... engine_module/utility/hex_str.i"
	@echo "... engine_module/utility/hex_str.s"
	@echo "... engine_module/utility/hmac_code.o"
	@echo "... engine_module/utility/hmac_code.i"
	@echo "... engine_module/utility/hmac_code.s"
	@echo "... engine_module/utility/ota_upgade.o"
	@echo "... engine_module/utility/ota_upgade.i"
	@echo "... engine_module/utility/ota_upgade.s"
	@echo "... engine_module/utility/rsa_crypt.o"
	@echo "... engine_module/utility/rsa_crypt.i"
	@echo "... engine_module/utility/rsa_crypt.s"
	@echo "... engine_module/utility/str_op.o"
	@echo "... engine_module/utility/str_op.i"
	@echo "... engine_module/utility/str_op.s"
	@echo "... engine_module/utility/time_task.o"
	@echo "... engine_module/utility/time_task.i"
	@echo "... engine_module/utility/time_task.s"
	@echo "... platform/baidu/baidu_mec_client.o"
	@echo "... platform/baidu/baidu_mec_client.i"
	@echo "... platform/baidu/baidu_mec_client.s"
	@echo "... platform/baidu/baidu_mec_protocol.o"
	@echo "... platform/baidu/baidu_mec_protocol.i"
	@echo "... platform/baidu/baidu_mec_protocol.s"
	@echo "... platform/boyuan/boyuan_mqtt_protocol.o"
	@echo "... platform/boyuan/boyuan_mqtt_protocol.i"
	@echo "... platform/boyuan/boyuan_mqtt_protocol.s"
	@echo "... platform/boyuan/boyuan_platform_client.o"
	@echo "... platform/boyuan/boyuan_platform_client.i"
	@echo "... platform/boyuan/boyuan_platform_client.s"
	@echo "... platform/debug/debug_mec_client.o"
	@echo "... platform/debug/debug_mec_client.i"
	@echo "... platform/debug/debug_mec_client.s"
	@echo "... platform/debug/debug_platform_client.o"
	@echo "... platform/debug/debug_platform_client.i"
	@echo "... platform/debug/debug_platform_client.s"
	@echo "... platform/empty/empty_mec_client.o"
	@echo "... platform/empty/empty_mec_client.i"
	@echo "... platform/empty/empty_mec_client.s"
	@echo "... platform/empty/empty_platform_client.o"
	@echo "... platform/empty/empty_platform_client.i"
	@echo "... platform/empty/empty_platform_client.s"
	@echo "... platform/genvict/gv_http_client.o"
	@echo "... platform/genvict/gv_http_client.i"
	@echo "... platform/genvict/gv_http_client.s"
	@echo "... platform/genvict/gv_mec_client.o"
	@echo "... platform/genvict/gv_mec_client.i"
	@echo "... platform/genvict/gv_mec_client.s"
	@echo "... platform/genvict/gv_mec_protocol.o"
	@echo "... platform/genvict/gv_mec_protocol.i"
	@echo "... platform/genvict/gv_mec_protocol.s"
	@echo "... platform/genvict/gv_mqtt_client.o"
	@echo "... platform/genvict/gv_mqtt_client.i"
	@echo "... platform/genvict/gv_mqtt_client.s"
	@echo "... platform/genvict/gv_mqtt_protocol.o"
	@echo "... platform/genvict/gv_mqtt_protocol.i"
	@echo "... platform/genvict/gv_mqtt_protocol.s"
	@echo "... platform/genvict/gv_platform_client.o"
	@echo "... platform/genvict/gv_platform_client.i"
	@echo "... platform/genvict/gv_platform_client.s"
	@echo "... platform/genvict_sdk/gv_sdk_protocol.o"
	@echo "... platform/genvict_sdk/gv_sdk_protocol.i"
	@echo "... platform/genvict_sdk/gv_sdk_protocol.s"
	@echo "... platform/genvict_sdk/gv_sdk_service.o"
	@echo "... platform/genvict_sdk/gv_sdk_service.i"
	@echo "... platform/genvict_sdk/gv_sdk_service.s"
	@echo "... platform/gosuncn/gxx_mec_client.o"
	@echo "... platform/gosuncn/gxx_mec_client.i"
	@echo "... platform/gosuncn/gxx_mec_client.s"
	@echo "... platform/gosuncn/gxx_mec_protocol.o"
	@echo "... platform/gosuncn/gxx_mec_protocol.i"
	@echo "... platform/gosuncn/gxx_mec_protocol.s"
	@echo "... platform/huawei/huawei_mec_client.o"
	@echo "... platform/huawei/huawei_mec_client.i"
	@echo "... platform/huawei/huawei_mec_client.s"
	@echo "... platform/huawei/huawei_mec_protocol.o"
	@echo "... platform/huawei/huawei_mec_protocol.i"
	@echo "... platform/huawei/huawei_mec_protocol.s"
	@echo "... platform/icloud_platform_client.o"
	@echo "... platform/icloud_platform_client.i"
	@echo "... platform/icloud_platform_client.s"
	@echo "... platform/imec_client.o"
	@echo "... platform/imec_client.i"
	@echo "... platform/imec_client.s"
	@echo "... platform/multi/multi_mec_client.o"
	@echo "... platform/multi/multi_mec_client.i"
	@echo "... platform/multi/multi_mec_client.s"
	@echo "... platform/multi/multi_platform_client.o"
	@echo "... platform/multi/multi_platform_client.i"
	@echo "... platform/multi/multi_platform_client.s"
	@echo "... platform/tianan/mec_protocol/perception.pb.o"
	@echo "... platform/tianan/mec_protocol/perception.pb.i"
	@echo "... platform/tianan/mec_protocol/perception.pb.s"
	@echo "... platform/tianan/tianan_http_client.o"
	@echo "... platform/tianan/tianan_http_client.i"
	@echo "... platform/tianan/tianan_http_client.s"
	@echo "... platform/tianan/tianan_mec_client.o"
	@echo "... platform/tianan/tianan_mec_client.i"
	@echo "... platform/tianan/tianan_mec_client.s"
	@echo "... platform/tianan/tianan_mec_protocol.o"
	@echo "... platform/tianan/tianan_mec_protocol.i"
	@echo "... platform/tianan/tianan_mec_protocol.s"
	@echo "... platform/tianan/tianan_mqtt_client.o"
	@echo "... platform/tianan/tianan_mqtt_client.i"
	@echo "... platform/tianan/tianan_mqtt_client.s"
	@echo "... platform/tianan/tianan_mqtt_protocol.o"
	@echo "... platform/tianan/tianan_mqtt_protocol.i"
	@echo "... platform/tianan/tianan_mqtt_protocol.s"
	@echo "... platform/tianan/tianan_platform_client.o"
	@echo "... platform/tianan/tianan_platform_client.i"
	@echo "... platform/tianan/tianan_platform_client.s"
	@echo "... platform/tianan/tianan_spat_client.o"
	@echo "... platform/tianan/tianan_spat_client.i"
	@echo "... platform/tianan/tianan_spat_client.s"
	@echo "... platform/tianan/tianan_spat_protocol.o"
	@echo "... platform/tianan/tianan_spat_protocol.i"
	@echo "... platform/tianan/tianan_spat_protocol.s"
	@echo "... platform/tianan_obu/tianan_obu_http_client.o"
	@echo "... platform/tianan_obu/tianan_obu_http_client.i"
	@echo "... platform/tianan_obu/tianan_obu_http_client.s"
	@echo "... platform/tianan_obu/tianan_obu_platform_client.o"
	@echo "... platform/tianan_obu/tianan_obu_platform_client.i"
	@echo "... platform/tianan_obu/tianan_obu_platform_client.s"
	@echo "... platform/tianan_obu/tianan_obu_protocal.o"
	@echo "... platform/tianan_obu/tianan_obu_protocal.i"
	@echo "... platform/tianan_obu/tianan_obu_protocal.s"
	@echo "... platform/tianan_obu/tiannan_obu_mqtt_client.o"
	@echo "... platform/tianan_obu/tiannan_obu_mqtt_client.i"
	@echo "... platform/tianan_obu/tiannan_obu_mqtt_client.s"
	@echo "... test/http_client_tool.o"
	@echo "... test/http_client_tool.i"
	@echo "... test/http_client_tool.s"
	@echo "... test/tcp_client_tool.o"
	@echo "... test/tcp_client_tool.i"
	@echo "... test/tcp_client_tool.s"
	@echo "... test/tcp_service_tool.o"
	@echo "... test/tcp_service_tool.i"
	@echo "... test/tcp_service_tool.s"
	@echo "... test/test_code.o"
	@echo "... test/test_code.i"
	@echo "... test/test_code.s"
	@echo "... test/test_tianan_mec_protocal.o"
	@echo "... test/test_tianan_mec_protocal.i"
	@echo "... test/test_tianan_mec_protocal.s"
	@echo "... v2x_client/v2x_client.o"
	@echo "... v2x_client/v2x_client.i"
	@echo "... v2x_client/v2x_client.s"
	@echo "... v2x_client/v2x_protocol.o"
	@echo "... v2x_client/v2x_protocol.i"
	@echo "... v2x_client/v2x_protocol.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

