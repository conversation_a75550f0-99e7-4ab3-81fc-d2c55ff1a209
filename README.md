# mec_channel
实验性项目-mec_channel

## 代码下载
1. git clone  **********************:gvv2x/vlink/mec_channel.git 
2. git git pull --force origin dev_master:dev_master
3. git co dev_master
4. git submodule init 
5. git submodule update --init --recursive

## 编译
1. 执行 cmake . 生成 makefile

编译选项 TARGET_MACHINE 指定机器架构
TARGET_MACHINE=X86/ARM32/ARM64

编译选项 MEC_TYPE 指定支持的MEC类型
MEC_TYPE=BAIDU_MEC/GXX_MEC/DEBUG_MEC

cmake -D TARGET_MACHINE=ARM64 -D MEC_TYPE=BAIDU_MEC .

2. 执行 make 进行编译

## 设备部署情况 10.1
### 1 创建相关目录
mkdir -p /home/<USER>/mec_channel/log/

### 2 system 配置文件路径 
/etc/systemd/system/mec_channel.service
    EnvironmentFile=/etc/ubus_env.conf

### 3 程序路径及配置文件路径
ExecStart=/genvict/bin/mec_channel --config_file=/genvict/etc/mec_channel.cfg

/genvict/bin/mec_sevice_test --config_file=/genvict/etc/mec_sevice_test.cfg

### 4 日志路径 
log_file=/home/<USER>/mem_channel/log/mec_channel_

### 5 服务启动
systemctl start mec_channel.service
systemctl start mec_sevice_test.service

### 6 服务停止
systemctl stop mec_channel.service

### 7 服务设置开机自启动
systemctl enable mec_channel.service
systemctl enable mec_sevice_test.service

### 8 服务日志查看
journalctl -f -u mec_channel.service

## 五所现场环境 
### 网络配置
DNS1 *************
DNS2 ************

5G OBU
wifi gvcustomer                  gvpublic   gv0200759
ssh gvpublic


5G RSU
wifi gv0200759
ssh gv0200759

web 
user
GV.user123


gateway:*************
netmask:*************

#### 1号设备 --route add ok   -- 升级  mec 已升级
ipaddress:*************    
mec_ip *************

gofr *************

#### 2号设备 --
rsu_ip:*************  
mec_ip ************* 

gofr *************

#### 3号设备移动
rsu:*************  
mec_ip *************

gofr *************


#### 4号设备移动
rsu:*************
mec_ip *************  

gofr *************


route add -net ***********/24 gw *************
route add -net ***********/24 gw *************

/genvict/bin/mec_channel --config_file=/genvict/etc/mec_channel.cfg
调试机器IP

*************


oub 测试设备
mqtt地址  ***********:1883

ping  ***********
ping ************





 mkdir -p /usr/lib/aarch64/
root@genvict_imx8qxp:/genvict/lib# cp ./* /usr/lib/aarch64/


问题 ,RSU 登录MEC 失败
mec_ip ************* 
mec_ip *************

