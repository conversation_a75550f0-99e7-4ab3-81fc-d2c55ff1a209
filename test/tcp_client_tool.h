/***********************************************
版本信息： 金溢科技版本权所有，保留一切权利
文件名称： tcp_client_tool.h
作者：张明
开始日期：2023-04-19 10:46:49
完成日期：
当前版本号: v 0.0.1
主要功能:
版本历史:
***********************************************/

#ifndef _TCP_CLIENT_TOOL_H_
#define _TCP_CLIENT_TOOL_H_
#include "ireceiver.h"
#include "event_engine.h"
#include <vector>
#include <string>
using namespace std;

class tcp_client_tool: public ireceiver
{
public:
    tcp_client_tool(event_engine &event_engine);
    ~tcp_client_tool();
    int init();
    int clean();

public: // interface irecevier
    void timeout_cb(long long ms) ;
    virtual void read_cb(engine_conn_handler & handler, const char* msg, int len) override;
    virtual void event_cb(int fd, short events ) override;
private:
    int load_hex_data();
    void proc_read_file();
private:
    event_engine &m_event_engine;
    engine_conn_handler  m_conn_handler;
    vector<string> m_bin_data;
    unsigned int  m_send_index = 0;
    bool m_b_connect_flag =  false;

};
#endif //_TCP_CLIENT_TOOL_H_
