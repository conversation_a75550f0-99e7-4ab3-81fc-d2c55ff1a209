#include "../engine_module/include/curl/curl.h"
#include <stdio.h>

#if 0
#define CURL_VERSION_IPV6         (1<<0)  /* IPv6-enabled */
#define CURL_VERSION_KERBEROS4    (1<<1)  /* Kerberos V4 auth is supported
                                             (deprecated) */
#define CURL_VERSION_SSL          (1<<2)  /* SSL options are present */
#define CURL_VERSION_LIBZ         (1<<3)  /* libz features are present */
#define CURL_VERSION_NTLM         (1<<4)  /* NTLM auth is supported */
#define CURL_VERSION_GSSNEGOTIATE (1<<5)  /* Negotiate auth is supported
                                             (deprecated) */
#define CURL_VERSION_DEBUG        (1<<6)  /* Built with debug capabilities */
#define CURL_VERSION_ASYNCHDNS    (1<<7)  /* Asynchronous DNS resolves */
#define CURL_VERSION_SPNEGO       (1<<8)  /* SPNEGO auth is supported */
#define CURL_VERSION_LARGEFILE    (1<<9)  /* Supports files larger than 2GB */
#define CURL_VERSION_IDN          (1<<10) /* Internationized Domain Names are
                                             supported */
#define CURL_VERSION_SSPI         (1<<11) /* Built against Windows SSPI */
#define CURL_VERSION_CONV         (1<<12) /* Character conversions supported */
#define CURL_VERSION_CURLDEBUG    (1<<13) /* Debug memory tracking supported */
#define CURL_VERSION_TLSAUTH_SRP  (1<<14) /* TLS-SRP auth is supported */
#define CURL_VERSION_NTLM_WB      (1<<15) /* NTLM delegation to winbind helper
                                             is supported */
#define CURL_VERSION_HTTP2        (1<<16) /* HTTP2 support built-in */
#define CURL_VERSION_GSSAPI       (1<<17) /* Built against a GSS-API library */
#define CURL_VERSION_KERBEROS5    (1<<18) /* Kerberos V5 auth is supported */
#define CURL_VERSION_UNIX_SOCKETS (1<<19) /* Unix domain sockets support */
#define CURL_VERSION_PSL          (1<<20) /* Mozilla's Public Suffix List, used
                                             for cookie domain verification */
#define CURL_VERSION_HTTPS_PROXY  (1<<21) /* HTTPS-proxy support built-in */
#define CURL_VERSION_MULTI_SSL    (1<<22) /* Multiple SSL backends available */
#define CURL_VERSION_BROTLI       (1<<23) /* Brotli features are present. */
#define CURL_VERSION_ALTSVC       (1<<24) /* Alt-Svc handling built-in */
#define CURL_VERSION_HTTP3        (1<<25) /* HTTP3 support built-in */
#define CURL_VERSION_ZSTD         (1<<26) /* zstd features are present */
#define CURL_VERSION_UNICODE      (1<<27) /* Unicode support on Windows */
#define CURL_VERSION_HSTS         (1<<28) /* HSTS is supported */
#define CURL_VERSION_GSASL        (1<<29) /* libgsasl is supported */

#endif

int main(int argc, char **argv)
{
    curl_global_init(CURL_GLOBAL_ALL);
    curl_version_info_data *data = curl_version_info(CURLVERSION_NOW);
    printf("version: %s\n", data->version);
    printf("host:%s\n", data->host);
    data->protocols
    if (data->features & CURL_VERSION_IPV6) {
        printf("IPv6 support enabled\n");
    }
    if (data->features & CURL_VERSION_KERBEROS4) {
        printf("Kerberos V4 auth is supported\n");
    }
    if (data->features & CURL_VERSION_SSL) {
        printf("SSL options are present\n");
    }
    if (data->features & CURL_VERSION_LIBZ) {
        printf("libz features are present\n");
    }
    if (data->features & CURL_VERSION_NTLM) {
        printf("NTLM auth is supported\n");
    }
    if (data->features & CURL_VERSION_GSSNEGOTIATE) {
        printf("Negotiate auth is supported\n");
    }
    if (data->features & CURL_VERSION_DEBUG) {
        printf("Built with debug capabilities\n");
    }
    if (data->features & CURL_VERSION_ASYNCHDNS) {
        printf("Asynchronous DNS resolves\n");
    }
    if (data->features & CURL_VERSION_SPNEGO) {
        printf("SPNEGO auth is supported\n");
    }
    if (data->features & CURL_VERSION_LARGEFILE) {
        printf("Supports files larger than 2GB\n");
    }
    if (data->features & CURL_VERSION_IDN) {
        printf("Internationized Domain Names are supported\n");
    }
    if (data->features & CURL_VERSION_SSPI) {
        printf("Built against Windows SSPI\n");
    }
    if (data->features & CURL_VERSION_CONV) {
        printf("Character conversions supported\n");
    }
    if (data->features & CURL_VERSION_CURLDEBUG) {
        printf("Debug memory tracking supported\n");
    }
    if (data->features & CURL_VERSION_TLSAUTH_SRP) {
        printf("TLS-SRP auth is supported\n");
    }
    if (data->features & CURL_VERSION_NTLM_WB) {
        printf("NTLM delegation to winbind helper is supported\n");
    }
    if (data->features & CURL_VERSION_HTTP2) {
        printf("HTTP2 support built-in\n");
    }
    if (data->features & CURL_VERSION_GSSAPI) {
        printf("Built against a GSS-API library\n");
    }
    if (data->features & CURL_VERSION_KERBEROS5) {
        printf("Kerberos V5 auth is supported\n");
    }
    if (data->features & CURL_VERSION_UNIX_SOCKETS) {
        printf("Unix domain sockets support\n");
    }
    if (data->features & CURL_VERSION_PSL) {
        printf("Mozilla's Public Suffix List, used for cookie domain verification\n");
    }
    if (data->features & CURL_VERSION_HTTPS_PROXY) {
        printf("HTTPS-proxy support built-in\n");
    }
    if (data->features & CURL_VERSION_MULTI_SSL) {
        printf("Multiple SSL backends available\n");
    }
    if (data->features & CURL_VERSION_BROTLI) {
        printf("Brotli features are present\n");
    }
    if (data->features & CURL_VERSION_ALTSVC) {
        printf("Alt-Svc handling built-in\n");
    }
    if (data->features & CURL_VERSION_HTTP3) {
        printf("HTTP3 support built-in\n");
    }
    if (data->features & CURL_VERSION_ZSTD) {
        printf("zstd features are present\n");
    }
    if (data->features & CURL_VERSION_UNICODE) {
        printf("Unicode support on Windows\n");
    }
    if (data->features & CURL_VERSION_HSTS) {
        printf("HSTS is supported\n");
    }
    if (data->features & CURL_VERSION_GSASL) {
        printf("libgsasl is supported\n");
    }

    curl_global_cleanup();
    return 0;
}
