#include "test_tianan_mec_protocal.h"
#include "common_utility.h"
#include "hex_str.h"
//#include "stdafx.h"

#define LINE_SPLIT  "==========================================================="

test_tianan_mec_protocal::test_tianan_mec_protocal()
{

}

test_tianan_mec_protocal::~test_tianan_mec_protocal()
{
	
}
	

void test_tianan_mec_protocal::setPositionTiann(perception::Position *pos,int64_t lat, int64_t lon , int32_t elev)
{
    pos->set_lat(lat); 
    pos->set_lon(lon); 
    pos->set_elev(elev); 
    
}
void test_tianan_mec_protocal::setSizeTianan(perception::Size *size ,int32_t len, int32_t width,int32_t height  )
{
    size->set_length(len); 
    size->set_width(width);
    size->set_height(height); 
}
void test_tianan_mec_protocal::setLaneInfoTianan(perception::LaneInfo * laneinfo, int32_t area_type,int32_t lane_count,float lane_heading )
{
    laneinfo->set_area_type(area_type); 
    laneinfo->set_lane_count(lane_count); 
    laneinfo->set_lane_heading(lane_heading); 
}
void test_tianan_mec_protocal::setAreaInfoTianan(perception::AreaInfo* areainfo,int32_t id, float width )
{
    areainfo->set_id(id);
    areainfo->set_width(12); 
    auto dot = areainfo->add_dot();
    setPositionTiann(dot,5,6,7);
}
void test_tianan_mec_protocal::setLaneCountTianan(perception::LaneCount * count)
{
    count->set_laneno(1);
    count->set_volume(2);
    count->set_pcu(3);
    count->set_avspeed(4);
    count->set_occupancy(0.05);
    count->set_headway(6);
    count->set_gap(7);
    count->set_avdelay(8);
    count->set_avstop(9);
    count->set_speed85(10);
    count->set_queuelength(20);
}


void test_tianan_mec_protocal::setRoadCountTianan(perception::RoadCount * count)
{
    count->set_deviceid("22");
    count->set_heading(33);
    count->set_roadname("my roadname");
    setPositionTiann(count->mutable_roadincoord(),44,55,66);
    count->set_volume(34);
    count->set_pcu(35);
    count->set_avspeed(36);
    count->set_occupancy(0.21);
    count->set_headway(0.22);
    count->set_gap(10.1);
    count->set_avdelay(33.0);
    count->set_avstop(34);
    count->set_speed85(12);
    count->set_queuelength(11);
    setLaneCountTianan(count->add_lanecount());
}
void test_tianan_mec_protocal::setIntersectionCountTianan(perception::IntersectionCount * count)
{
    count->set_volume(1);
    count->set_pcu(2);
    count->set_avspeed(2);
    count->set_occupancy(3);
    count->set_headway(4);
    count->set_gap(5);
    count->set_avdelay(6);
    count->set_avstop(7);
    count->set_speed85(8);
    count->set_queuelength(20);
    setRoadCountTianan(count->add_roadcount());
}
void test_tianan_mec_protocal::SetLaneCountInfoTianan(perception::LaneCountInfo *count)
{
    count->set_laneno(8);
    count->set_volume1(1);
    count->set_volume2(2);
    count->set_volume3(3);
    count->set_volume4(4);
    count->set_volume5(5);
}

void test_tianan_mec_protocal::SetRoadCountInfoTianan(perception::RoadCountInfo * count)
{
    count->set_deviceid("rci_12");
    count->set_heading(12);
    count->set_roadname("ddd4");
    setPositionTiann(count->mutable_roadincoord(),1,2,3);
    count->set_volume1(1);
    count->set_volume2(2);
    count->set_volume3(3);
    count->set_volume4(4);
    count->set_volume5(5);
    SetLaneCountInfoTianan(count->add_lanecount());
}

void test_tianan_mec_protocal::SetIntersectionInfoTianan(perception::IntersectionInfo *info)
{
    info->set_volume1(1);
    info->set_volume2(2);
    info->set_volume3(3);
    info->set_volume4(4);
    info->set_volume5(5);
    //SetRoadCountInfoTianan(info->add_roadcount());
}
void test_tianan_mec_protocal::printPerceptionMsg(PerceptionMsg &msg, const string & type)
{
    string msg_str = msg.SerializeAsString();
    hex_str ohex;
    string hex_msg_str ;  
    ohex.encode(msg_str,hex_msg_str);
    cout<<LINE_SPLIT<<endl<<type<<endl<<hex_msg_str<<endl;
}
void test_tianan_mec_protocal::test_tianan_protocal()
{
    {
        // target msg 
        PerceptionMsg msg;
        msg.set_time(get_ms());
        auto targets =  msg.mutable_target_msg();
        targets->set_device_sn("gv_rsu"); 
        targets->set_device_ip("*********"); 
        targets->set_fusion_state(1); //
        auto & target = *targets->add_array() ; 
        target.set_id(1);
        target.set_source(0); 
        setPositionTiann(target.mutable_pos(),1,2,3); 
        setSizeTianan(target.mutable_size(),5,6,7); 
        target.set_confidence(1);
        target.set_type(0); 
        target.set_speed(100); 
        target.set_heading(102); 
        target.set_lane_id(2); 
        setLaneInfoTianan(target.mutable_lane_info(),1,2,3); 
        target.set_license_plate("12345"); 
        target.set_car_type(0); 
        target.set_color(0xf00); 
        target.set_status(1); 
        target.set_distance(12); 
        target.set_rcs(11.00); 
        target.set_track_time(get_ms()); 
        // 机动车属性
        target.set_vehicle_pose(1); 
        target.set_vehicle_type(2);
        target.set_vehicle_marker(3); 
        target.set_vehicle_other(4);
        target.set_vehicle_roof(5);
        target.set_vehicle_carcolor(6);
        target.set_vehicle_make(7);
        target.set_vehicle_model(8); 
        target.set_vehicle_year(2022);
        target.set_vehicle_plate_color(3);
        target.set_vehicle_plate_type(1);
        // 行人
        target.set_pedestrian_shoecolor(1); 
        target.set_pedestrian_shoestyle(2);
        target.set_pedestrian_sleeve(3);
        target.set_pedestrian_hair(4); 
        target.set_pedestrian_nation(5);
        target.set_pedestrian_gender(1);
        target.set_pedestrian_lowerstyle(2);
        target.set_pedestrian_upperstyle(3); 
        target.set_pedestrian_age(22);
        target.set_pedestrian_lower(1);
        target.set_pedestrian_upper(2);
        target.set_pedestrian_backpack(3);
        // 非机动车
        target.set_nonmotor_attitude(1);
        target.set_nonmotor_gender(2);
        target.set_nonmotor_nation(3);
        target.set_nonmotor_transportation(4);
        target.set_nonmotor_uppercolor(5);
        target.set_nonmotor_upper(6);
        target.set_nonmotor_transportationcolor(12);

        printPerceptionMsg(msg,"target_msg");


    }
    {
        PerceptionMsg msg;
        msg.set_time(get_ms());
        auto events = msg.mutable_event_msg(); 
        auto &event  = *events->add_event(); 
        event.set_event_type(1);
        event.set_level(2); 
        event.set_device_sn("sn1263456");
        event.set_lane_id(1);
        event.set_fusion_state(1);
        event.set_source(0);
        setPositionTiann(event.add_pos(),1,2,3);
        event.set_license_plate("123456");
        event.set_track_time(12);
        event.set_ttl(22);
        auto refpath = event.add_refpath();
        setAreaInfoTianan(refpath,1,2);
        event.set_string_data("12345");

        printPerceptionMsg(msg,"event_msg");
    }
    {
        PerceptionMsg msg;
        msg.set_time(get_ms());
        auto & hearet_beat  =  *msg.mutable_heart_beat_msg();
        hearet_beat.set_seq_count(1);
        hearet_beat.set_time(get_ms());
        hearet_beat.set_device_sn("123456");
        hearet_beat.add_err_code(1);
        printPerceptionMsg(msg,"hearet_beat");
    }
    {
        PerceptionMsg msg;
        msg.set_time(get_ms());
        auto & dev_info = * msg.mutable_dev_info();
        dev_info.set_device_sn("123");
        setPositionTiann( dev_info.mutable_location(),8,9,10);
        dev_info.set_orientation(5);
        dev_info.set_type(1);
        auto perceptual_area = dev_info.add_perceptual_area();
        setAreaInfoTianan(perceptual_area,55,100);
        dev_info.set_description("test_dev_info");
        printPerceptionMsg(msg,"dev_info");
    }
    {
        PerceptionMsg msg;
        msg.set_time(get_ms());
        auto &road_count  =  *msg.mutable_road_count();
        road_count.set_rsuid("123456");
        road_count.set_seqnum("1");
        road_count.set_rsuesn("rusesu_gv");
        road_count.set_protocolversion("v1.0.0.1");
        road_count.set_cycle(3500);
        setIntersectionCountTianan(road_count.mutable_intersectioncount());
        printPerceptionMsg(msg,"road_count");     

    }

    {
        PerceptionMsg msg;
        msg.set_time(get_ms());
        auto &road_info  =  *msg.mutable_road_info();
        road_info.set_rsuid("12345678");
        road_info.set_seqnum("123456");
        road_info.set_rsuesn("rsu_esn");
        road_info.set_protocolversion("v0.0.01");
        road_info.set_cycle(30);
        SetIntersectionInfoTianan(road_info.mutable_intersectioninfo());
        printPerceptionMsg(msg,"road_info");     

    }


}