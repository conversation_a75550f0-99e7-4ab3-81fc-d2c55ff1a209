/***********************************************
版本信息： 金溢科技版本权所有，保留一切权利
文件名称： http_client_tool.h
作者：张明
开始日期：2023-07-28 09:36:03
完成日期：
当前版本号: v 0.0.1
主要功能: 
版本历史:
***********************************************/

#ifndef _HTTP_CLIENT_TOOL_H_
#define _HTTP_CLIENT_TOOL_H_
#include "ireceiver.h"
#include "event_engine.h"
#include <vector>
#include <string>
using namespace std;

class http_client_tool: public ihttpclient_receiver
{
public:
	http_client_tool(event_engine &event_engine);
	~http_client_tool();

    int init();
    int clean();

public: // interface irecevier
    void timeout_cb(long long ms) ;
    virtual void update_conn_handler(engine_conn_handler & new_client_handler) override;
    virtual int get_conn_fd (void *conn_handler_data) override;
    virtual void read_cb(engine_conn_handler & handler, const char* msg, int len) override;
    virtual void event_cb(int fd, short events ) override;
private:
    event_engine &m_event_engine;
	engine_conn_handler  m_conn_handler;
	
};
#endif //_HTTP_CLIENT_TOOL_H_
