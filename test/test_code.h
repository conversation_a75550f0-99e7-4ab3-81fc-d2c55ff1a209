/***********************************************
版本信息： 金溢科技版本权所有，保留一切权利
文件名称： test_code.h
作者：张明
完成日期：
当前版本号: v 0.0.1
主要功能:
版本历史:
***********************************************/

#ifndef _TEST_CODE_H_
#define _TEST_CODE_H_

#include <list>
#include <string>

#include "singleton.hpp"
#include "ireceiver.h"
#include "log.h"
#include "signal_handler.h"
#include "v2x_client.h"
#include "gxx_mec_client.h"
#include "baidu_mec_client.h"
#include "dvin_debug_client.h"
#include "imec_client.h"
#include "tcp_client_tool.h"
#include "event_engine.h"
#include "tcp_service_tool.h"
#include "http_client_tool.h"
#include "gv_mec_protocol.h"
#include "zmq.hpp"
#include "nlohmann/json.hpp"
using json = nlohmann::json;
using namespace std;

class GoEMECMessageFrame;



class test_code :public ireceiver
{
    enum
    {
        GB_IPV6 = 0,
        GB_WSMP,
        GB_FNTP,
        GB_ETSI,
        GB_DSMP,
    };

    typedef struct
    {
        bool is_assign_aid;//是否应用指定AID
        int aid;
        uint16_t ver;
        uint16_t opt_ind;
    } netlayer_info_t;

    static constexpr uint16_t aid_offset =  0X7F80;



public:
    test_code();
    virtual ~test_code();
    void run();
    void test_crc();
    void test_hex();
    void fill_map_pos_tool();
    bool proc_map_pos_line(nlohmann::json * j,char *pLineData,size_t size);
    
    void test_zmq_service();
    void test_gxx_mec_protocol();
    void free_frame_list(list<GoEMECMessageFrame*> &listFrame );
    void test_json();
    void test_raw_uper();
    void test_circle_fun();
    void test_huawei_protocol();
    void test_wgs84_to_cj02(); 
    void test_map_change_coor(); 
    void set_stop_flag(const string & stop_msg);
    void test_http_service();
    void test_map_cj02_towgs84(const string &map_org_path,const string & map_des_path);
    void test_string();
    int read_file(const string & path, string & data);

    void test_str_struct(); 
public: // tool
    void print_wmm_log_data();
    void tcp_client_tool_fun();
    void tcp_server_tool_fun();
    void http_client_tool_fun();
    void build_mec_protocal_packet(); 
    void test_asn_decode(); 
    void test_sha256();

    void test_print_asn(); 

    void test_tianan_obu_protocal();
    void test_tianan_protocal(); 
    

public: // interface irecevier
    virtual void timeout_cb();
    virtual void read_cb(engine_conn_handler & handler, const char* msg, int len);

private:
    
	// 求解直线方程
	void cal_line_fun(double a_x,double a_y, double b_x,double b_y, double &fun_a,double &fun_b);

	// 求经过ab线垂直的，经过c点垂直
	void cal_vertical_line_fun(double a_x,double a_y, double b_x,double b_y,double c_x,double c_y, double &fun_a,double &fun_b);

	// 求两条直线的交叉点
	void cal_line_cross_point(double line1_a,double line1_b,double line2_a,double line2_b,double &x,double &y);

	//根据三个点，求圆方程 (x-a)^2 +(y-b)^2 = r^2
	void cal_circle_fun(double a_x,double a_y, double b_x,double b_y,double c_x,double c_y,double & a,double & b , double & r ); 

    double  cal_dis(double a_x,double a_y, double b_x,double b_y); 
    int convert_from_netlayer_protocol(unsigned char * &pdst, uint32_t & len,const char *psrc, uint32_t  src_len, netlayer_info_t &pram);

private:
    signal_handler m_signal_handler;
    event_engine m_event_engine;



};
#endif //_TEST_CODE_H_
