/***********************************************
版本信息： 金溢科技版本权所有，保留一切权利
文件名称： test_tianan_mec_protocal.h
作者：张明
开始日期：2024-07-01 11:07:04
完成日期：
当前版本号: v 0.0.1
主要功能: 
版本历史:
***********************************************/

#ifndef _TEST_TIANAN_MEC_PROTOCAL_H_
#define _TEST_TIANAN_MEC_PROTOCAL_H_

#include <string>
#include "mec_protocol/perception.pb.h"
using namespace perception; 
using namespace std; 


class test_tianan_mec_protocal
{
public:
	test_tianan_mec_protocal();
	~test_tianan_mec_protocal();
	void setPositionTiann(perception::Position *pos,int64_t lat, int64_t lon , int32_t elev);
	void setSizeTianan(perception::Size *size ,int32_t len, int32_t width,int32_t height  );
	void setLaneInfoTianan(perception::LaneInfo * laneinfo, int32_t area_type,int32_t lane_count,float lane_heading );
	void setAreaInfoTianan(perception::AreaInfo* areainfo,int32_t id, float width );
	void setLaneCountTianan(perception::LaneCount * count);
	void setRoadCountTianan(perception::RoadCount * count);
	void setIntersectionCountTianan(perception::IntersectionCount * count);
	void SetLaneCountInfoTianan(perception::LaneCountInfo *count);
	void SetRoadCountInfoTianan(perception::RoadCountInfo * count);
	void SetIntersectionInfoTianan(perception::IntersectionInfo *info);
	void printPerceptionMsg(PerceptionMsg &msg, const string & type);
	void test_tianan_protocal();
};
#endif //_TEST_TIANAN_MEC_PROTOCAL_H_
