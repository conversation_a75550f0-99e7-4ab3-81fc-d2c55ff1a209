#include "test_code.h"
#include "stdafx.h"
#include <stdio.h>
#include "gxx_mec_protocol.h"
#include "config.h"
#include "mec_asn/GoEMECMessageFrame.h"
#include <string>
#include <fstream>
#include <unistd.h>
#include "v2x_client.h"
#include "common_utility.h"
#include <list>
#include "event_engine.h"
#include "service.h"
#include "tianan_obu_protocal.h"
#include <sys/types.h>
#include <sys/stat.h>
#include <fcntl.h>
#include "crc16.h"
#include "hex_str.h"
#include <memory>
#include "huawei_mec_protocol.h"
#include "dir_op.h"

#include "openssl/sha.h"
#include "openssl/hmac.h"
#include "hmac_code.h"
#include "openssl/rsa.h"
#include "test_tianan_mec_protocal.h"


#include "coordinate_convert.h"
#include "str_op.h"

using namespace std;

#define LINE_SPLIT  "==========================================================="

#define	IS_BIT_SET(value, n)  ((value >> (n-1)) & 0x01)


bool Encode_BitString(int val, const asn_TYPE_descriptor_t & dp, BIT_STRING_t & asn_bit)
{
    long bit_len = dp.encoding_constraints.per_constraints->size.lower_bound;
    long alloc_size = (bit_len + 7) / 8;
    if (asn_bit.buf == nullptr)
    {
        asn_bit.buf = reinterpret_cast<uint8_t*>(calloc(alloc_size, 1));
        if (asn_bit.buf == nullptr)
        {
            return false;
        }
    }

    asn_bit.size = alloc_size;
    asn_bit.bits_unused = alloc_size * 8 - bit_len;

    int itmp = 1;
    for (int i = 0; i < bit_len; i++)
    {
        if (val & itmp)
        {
            asn_bit.buf[i / 8] |= (0x80 >> (i % 8));
        }
        itmp *= 2;
    }
    return true;
}

/***********************************************************
 * 函数名称:
 * 功能描述: 构造函数
 * 输入参数:
 * 输出参数:
 * 返 回 值:
 ***********************************************************/
test_code::test_code()
{

}

/***********************************************************
 * 函数名称:
 * 功能描述: 虚构函数
 * 输入参数:
 * 输出参数:
 * 返 回 值:
 ***********************************************************/
test_code::~test_code()
{

}


/***********************************************************
 * 函数名称: run
 * 功能描述: 运行测试用例
 * 输入参数:
 * 输出参数:
 * 返 回 值:
 ***********************************************************/
void test_code::run()
{
    cout<<endl<<endl<<endl<<"test_code run begin ======================="<<endl;
    //test_map_cj02_towgs84();
    //test_rsa(); 
    //test_tianan_obu_protocal(); 
    //test_string();    
    print_wmm_log_data();
    //test_gxx_mec_protocol();
    //test_json();
    //test_crc();
    //test_hex();
    //test_uper();
    //cout<<"test_code run begin ======================="<<endl;
    //test_http_service();
    //tcp_client_tool_fun();
    //tcp_server_tool_fun();
    //http_client_tool_fun();
    //build_mec_protocal_packet();
    //test_circle_fun();
    //test_str_struct();

    //test_huawei_protocol();
    //test_asn_decode();
    //test_sha256();
    //test_print_asn();
    //test_zmq_service();
    //test_tianan_protocal(); 
    //test_wgs84_to_cj02();
    //fill_map_pos_tool();
}
void test_code::test_zmq_service()
{

}

bool test_code::proc_map_pos_line(nlohmann::json * j,char *pLineData,size_t size)
{
    string line(pLineData,size);;
    vector<string> id_list; 
    split_string(line,',',id_list); 
    if(id_list.size() != 4)
    {
        //cout<<pLineData<<endl; 
        return true; 
    }
    // 70,104.6139162,28.7274579,0.00  id , long , lat, e
    long lng,lat,ele; 
    lng = std::stod(id_list[1])*10000000 ; 
    lat = std::stod(id_list[2])*10000000 ; 
    ele = 0; 
    auto  ret = nlohmann::json{
        {"posOffset",{
            {"offsetLL",{
                {"choiceID",7},
                {"position_LatLon",{
                    {"lat",lat},
                    {"long",lng}
                }}
            }},
            {"offsetV",{
                {"choiceID",7},
                {"elevation", ele}
            }}
            }
        }
    }; 
    j->emplace_back(std::move(ret)); 

    return true; 
}
void test_code::test_map_cj02_towgs84(const string &map_org_path,const string & map_des_path)
{
    string map_02_path = "./map_data_02.json"; 
    string map_84_path = "./map_data_84.json"; 
    string map_file_data; 
    read_file(map_02_path,map_file_data);
    try
    {
        auto map_json = nlohmann::json::parse(map_file_data); 
        string map_value = map_json["value"].get<string>(); 
        auto map_value_json = nlohmann::json::parse(map_value); 
        string node_name; 
        string link_name; 
        int laneID; 

        utility::coordinate_convert::v2x_coordinate pos_02;
        utility::coordinate_convert::v2x_coordinate pos_84;
        
        for(auto & node :map_value_json["nodes"])
        {
            auto &refPos = node["refPos"];
            pos_02.latitude =  refPos["lat"]; 
            pos_02.longitude = refPos["long"]; 
            pos_02.latitude /=LATITUDE_RES; 
            pos_02.longitude /= LONGITUDE_RES; 
            utility::coordinate_convert::cj02_to_wgs84(pos_02,pos_84);
            long lat =  pos_84.latitude*LATITUDE_RES; 
            long lng = pos_84.longitude*LONGITUDE_RES; 
            refPos["lat"] =  lat; 
            refPos["long"] = lng; 

            node_name = node["name"].get<string>(); 
            for(auto &inLink: node["inLinks"])
            {
                link_name =  inLink["name"].get<string>();
                for(auto &lane: inLink["lanes"])
                {
                    for(auto &point :lane["points"])
                    {
                        auto &latlon = point["posOffset"]["offsetLL"]["position_LatLon"]; 
                        cout<<"02_lat "<<latlon["lat"]<<" long "<<latlon["long"]<<endl;
                        
                        pos_02.latitude =  latlon["lat"]; 
                        pos_02.longitude =  latlon["long"]; 
                        pos_02.latitude /=LATITUDE_RES; 
                        pos_02.longitude /= LONGITUDE_RES; 

                        utility::coordinate_convert::cj02_to_wgs84(pos_02,pos_84);

                        long lat =  pos_84.latitude*LATITUDE_RES; 
                        long lng = pos_84.longitude*LONGITUDE_RES; 
                        cout<<"84_lat "<<lat<<" long "<<lng<<endl; 
                        latlon["lat"] = lat; 
                        latlon["long"] =  lng; 
            
                    }
                }
            }
        }
        map_value = map_value_json.dump(); 
        map_json["value"] =  map_value; 
        map_file_data =  map_json.dump();
        dir_op::write_file_data(map_84_path,map_file_data);
    }
    catch(exception & e)
    {
        cout<<"catch exception"<<endl;
    }
}


void test_code::fill_map_pos_tool()
{
    string map_path = "./map_data.json"; 
   
    string pos_file_dir = "./tool/make_map/"; 
    string map_file_data; 
    read_file(map_path,map_file_data);
    try
    {
        auto map_json = nlohmann::json::parse(map_file_data); 
        string map_value = map_json["value"].get<string>(); 
        auto map_value_json = nlohmann::json::parse(map_value); 
        string node_name; 
        string link_name; 
        int laneID; 
        for(auto & node :map_value_json["nodes"])
        {
            node_name = node["name"].get<string>(); 
            for(auto &inLink: node["inLinks"])
            {
                link_name =  inLink["name"].get<string>();

                for(auto &lane: inLink["lanes"])
                {
                    laneID = lane["laneID"].get<int>(); 
                    stringstream ss; 
                    ss <<pos_file_dir<<node_name<<"_"<<link_name<<"_"<<laneID<<".txt"; 
                    string lane_pos_file =  ss.str(); 
                    cout<<"lane_pos_file:"<<lane_pos_file<<endl;
                    struct stat st;
                    bool file_exit  =  (0 == stat(lane_pos_file.c_str(), &st))?true:false; 

                    if(file_exit == true)
                    {

                        lane["points"] =  nlohmann::json::array();
                        auto fun  = std::bind(&test_code::proc_map_pos_line,this, &lane["points"],std::placeholders::_1,std::placeholders::_2); 
                        dir_op::read_file(lane_pos_file,1024,fun); 
                    }
                    else
                    {
                        cout<<"lane_pos_file not exist "<<lane_pos_file<<endl;
                    }
                    

                }
            }
        }
        map_value = map_value_json.dump(); 
        map_json["value"] =  map_value; 
        map_file_data =  map_json.dump();
        string map_save_path = "./tool/make_map/map_data_result.json";
        dir_op::write_file_data(map_save_path,map_file_data);

    }
    catch(exception & e)
    {

        cout<<"catch exception"<<endl;
        
    }


}

void test_code::test_tianan_protocal()
{
   test_tianan_mec_protocal protocal; 
   protocal.test_tianan_protocal();
   
}
bool wgs84_to_cj02(char *pLineData,size_t size)
{
    string line(pLineData,size);;
    list<string> id_list; 
    split_string(line,',',id_list); 
    if(id_list.size() != 2)
    {
        cout<<pLineData<<endl; 
        return true; 
    }

    utility::coordinate_convert::v2x_coordinate pos_84; 
    pos_84.latitude = std::stod(*id_list.begin()); 
    id_list.pop_front(); 
    pos_84.longitude = std::stod(*id_list.begin()); 
    utility::coordinate_convert::v2x_coordinate pos_02; 
    utility::coordinate_convert::wgs84_to_cj02(pos_84,pos_02); 

    //cout<<"84["<<pos_84.latitude<<","<<pos_84.longitude<<"]" => 02[]"
    printf("84[%0.7f,%0.7f] => 02[%0.7f,%0.7f]\n",pos_84.latitude,pos_84.longitude,pos_02.latitude,pos_02.longitude);
    return true;
}
void test_code::test_wgs84_to_cj02()
{
    string coor_file = "./84_pos.txt";
    dir_op::read_file(coor_file,1024,wgs84_to_cj02); 
}
void test_code::test_tianan_obu_protocal()
{
    tianan_obu_protocal protocal; 
    protocal.init(); 
    http_req req; 
    protocal.build_http_obu_regist_data("123456",req); 
    //WLI("data %s ",data.c_str()); 

}
void test_code::test_print_asn()
{
    cout<<LINE_SPLIT<<endl;
    cout<<"test_print_asn begin"<<endl;
    LaneTypeAttributes *attr  = reinterpret_cast<LaneTypeAttributes*>(calloc(1, sizeof(LaneTypeAttributes)));
    attr->present = LaneTypeAttributes_PR::LaneTypeAttributes_PR_vehicle; 
    int val =  1 << LaneAttributes_Vehicle::LaneAttributes_Vehicle_isRampLane ; 
    Encode_BitString(val,asn_DEF_LaneAttributes_Vehicle,attr->choice.vehicle); 
    asn_fprint(stdout, &asn_DEF_LaneTypeAttributes, attr);
    ASN_STRUCT_FREE(asn_DEF_LaneTypeAttributes,attr);
    cout<<"test_print_asn end"<<endl;

    
    //val =  1 << DriveBehavior::DriveBehavior_goStraightForward ; //  80 00 (2 bits unused)

    for(int i = DriveBehavior::DriveBehavior_goStraightForward; i<=DriveBehavior::DriveBehavior_parking ; i ++)
    {
        DriveBehavior_t *driveBehavior  = reinterpret_cast<DriveBehavior_t*>(calloc(1, sizeof(DriveBehavior_t)));
        val =  1 << i ; //  
        Encode_BitString(val,asn_DEF_DriveBehavior,*driveBehavior); 
        cout <<"DriveBehavior i = "<<i <<endl; 
        asn_fprint(stdout, &asn_DEF_DriveBehavior, driveBehavior);
        cout<<endl ;
        ASN_STRUCT_FREE(asn_DEF_DriveBehavior,driveBehavior);
    }
    


    cout<<"test_print_asn end"<<endl;



}


void test_code::test_sha256()
{
    cout<<LINE_SPLIT<<endl;
    cout<<"begin test_sha256 "<<endl;

    unsigned char code_256[SHA256_DIGEST_LENGTH]= {0}; 
    string data = "123456"; 
    SHA256((unsigned char *)data.c_str(),data.length(),code_256); 
    
    hex_str ohex;
    string hex_256; 
    ohex.encode(code_256,SHA256_DIGEST_LENGTH,hex_256);

    cout<<"data "<<data<<" sha256 code "<<hex_256<<endl;
    cout<<LINE_SPLIT<<endl;

    string key = "2024041501"; 
    string hmac_data = "12345678"; 
    string result ; 
    hmac_code::sha256(key,hmac_data,result);
    ohex.encode(result,hex_256);
    cout<<"data "<<data<<" HMAC - sha256 md_len "<<result.size()<< "hex " <<hex_256<<endl;
    cout<<LINE_SPLIT<<endl;
}

void test_json_par( nlohmann::json & a)
{
    a["test_key"] = 123;
}
void test_json_array( nlohmann::json & a)
{
    auto t_i = nlohmann::json{
        {"tk",1}
    }; 
    auto t_j = nlohmann::json{
        {"tk",2}
    }; 
    a.emplace_back(std::move(t_i)); 
    cout<<"test_json_array t_i json "<<t_i.dump()<<endl;

    t_i["tk"] = 3; 
    cout<<"test_json_array t_i json 3 "<<t_i.dump()<<endl;
    a.emplace_back(t_j); 
}

void test_code::test_huawei_protocol()
{

    int test_bsm = 1; 
    int test_map = 1;
    int test_rsi = 1; 
    int test_rsm = 1;
    int test_spat = 1;
    cout<<"test_huawei_protocol begin "<<endl;

    huawei_mec_protocol hw_protocol; 
    hw_req_ack_info_t ack_info;

    // bsm 
    if(test_bsm)
    {
        string gv_bsm_data;
        string hw_bsm_data;
        dir_op::read_file_data("/data1/work/datum/rsu-config/v2x_json_msg/gv_bsm_data.json",gv_bsm_data); 
        auto gv_bsm_json = make_shared<nlohmann::json>(json::parse(gv_bsm_data)); 
        hw_protocol.bsm_gv2hw(gv_bsm_json,hw_bsm_data); 
        cout<<"gv_bsm_data "<<gv_bsm_data.c_str()<<endl;
        cout<<endl<<endl;
        cout<<"hw_bsm_data "<<hw_bsm_data.c_str()<<endl;

    }



    // spat 
    if(test_spat)
    {
        ///data1/work/datum/rsu-config/v2x_json_msg
        string gv_spat_data;
        string hw_spat_data;
        dir_op::read_file_data("/data1/work/datum/rsu-config/v2x_json_msg/gv_spat_data.json",gv_spat_data); 
        auto gv_spat_json = make_shared<nlohmann::json>(json::parse(gv_spat_data)); 
        hw_protocol.spat_gv2hw(gv_spat_json,hw_spat_data); 
        cout<<"gv_spat_data "<<gv_spat_data.c_str()<<endl;
        cout<<endl<<endl;
        cout<<"hw_spat_data "<<hw_spat_data.c_str()<<endl;

        /// hw上报与下发的数据格式不一样
        
        string hw_spat_down_data;
        string gv_spat_down_data;
        dir_op::read_file_data("/data1/work/datum/rsu-config/v2x_json_msg/hw_spat_down_data.json",hw_spat_down_data); 
        hw_protocol.spat_hw2gv(hw_spat_down_data,gv_spat_down_data,ack_info); 
        cout<<"gv_spat_down_data "<<gv_spat_down_data.c_str()<<endl;
    }


    if(test_map)
    {

        string gv_map_data;
        string hw_map_data;
        dir_op::read_file_data("/data1/work/datum/rsu-config/v2x_json_msg/gv_map_data.json",gv_map_data); 
        auto gv_map_json = make_shared<nlohmann::json>(json::parse(gv_map_data)); 
        hw_protocol.map_gv2hw(gv_map_json,hw_map_data); 
        cout<<"gv_map_data "<<gv_map_data.c_str()<<endl;
        cout<<endl<<endl;
        cout<<"hw_map_data "<<hw_map_data.c_str()<<endl;

        /// hw上报与下发的数据格式不一样
        string hw_map_down_data;
        string gv_map_down_data;
        dir_op::read_file_data("/data1/work/datum/rsu-config/v2x_json_msg/hw_map_down_data.json",hw_map_down_data); 
        hw_protocol.map_hw2gv(hw_map_down_data,gv_map_down_data,ack_info); 
        cout<<"hw_map_down_data "<<hw_map_down_data.c_str()<<endl;
        cout<<endl<<endl;
        cout<<"gv_map_down_data "<<gv_map_down_data.c_str()<<endl;
    }


    if(test_rsm)
    {

        string gv_rsm_data;
        string hw_rsm_data;
        dir_op::read_file_data("/data1/work/datum/rsu-config/v2x_json_msg/gv_rsm_data.json",gv_rsm_data); 
        auto gv_rsm_json = make_shared<nlohmann::json>(json::parse(gv_rsm_data)); 
        hw_protocol.rsm_gv2hw(gv_rsm_json,hw_rsm_data); 
        cout<<"gv_rsm_data "<<gv_rsm_data.c_str()<<endl;
        cout<<endl<<endl;
        cout<<"hw_rsm_data "<<hw_rsm_data.c_str()<<endl;

        /// hw上报与下发的数据格式不一样
        cout<<endl<<endl<<"========================================="<<endl;
        string hw_rsm_down_data;
        string gv_rsm_down_data;
        dir_op::read_file_data("/data1/work/datum/rsu-config/v2x_json_msg/hw_rsm_down_data.json",hw_rsm_down_data); 
        hw_protocol.rsm_hw2gv(hw_rsm_down_data,gv_rsm_down_data,ack_info);
        cout<<"hw_rsm_down_data "<<hw_rsm_down_data.c_str()<<endl;
        cout<<endl<<endl;
        cout<<"gv_rsm_down_data "<<gv_rsm_down_data.c_str()<<endl;
    }


#if 0
    int hw_priority = 3;
    string gv_priority ;
    hw_protocol.rsi_priority_hw2gv(hw_priority,gv_priority);
    cout<<"rsi_priority_hw2gv hw_priority "<<hw_priority<<" gv_priority "<<gv_priority<<endl;

    hw_protocol.rsi_priority_gv2hw(gv_priority,hw_priority);
    cout<<"rsi_priority_gv2hw gv_priority "<<gv_priority<<" hw_priority "<<hw_priority<<endl;
#endif 


    // rsi 
    if(test_rsi)
    {

        string gv_rsi_data;
        string hw_rsi_data;
        dir_op::read_file_data("/data1/work/datum/rsu-config/v2x_json_msg/gv_rsi_data.json",gv_rsi_data); 
        auto gv_rsi_json = make_shared<nlohmann::json>(json::parse(gv_rsi_data)); 
        hw_protocol.rsi_gv2hw(gv_rsi_json,hw_rsi_data); 
        cout<<"gv_rsi_data "<<gv_rsi_data.c_str()<<endl;
        cout<<endl<<endl;
        cout<<"hw_rsi_data "<<hw_rsi_data.c_str()<<endl;

        /// hw上报与下发的数据格式不一样
        cout<<endl<<endl<<"========================================="<<endl;
        string hw_rsi_down_data;
        string gv_rsi_down_data;
        dir_op::read_file_data("/data1/work/datum/rsu-config/v2x_json_msg/hw_rsi_down_data.json",hw_rsi_down_data); 
        hw_protocol.rsi_hw2gv(hw_rsi_down_data,gv_rsi_down_data,ack_info);
        cout<<"hw_rsi_down_data "<<hw_rsi_down_data.c_str()<<endl;
        cout<<endl<<endl;
        cout<<"gv_rsi_down_data "<<gv_rsi_down_data.c_str()<<endl;
   
    }


    cout<<"test_huawei_protocol end "<<endl;
}


void test_code::test_str_struct()
{
    V2xRspHeartBeat beat;
    beat.dev_type = 1;
    memcpy(beat.id,"12345678",8);
    beat.gnss_status = 2;
    memcpy(beat.soft_ver,"versiong10.1",strlen("versiong10.1"));

    V2xRspHeartBeat beat2;
    beat2 = beat;
    cout<<"beat2 "<<beat2.soft_ver<<endl;
}


void test_code::build_mec_protocal_packet()
{
    string mec_protocal_file  ="./mec_protocal.txt";
    string sData; 
    if(RET_OK !=read_file(mec_protocal_file,sData))
    {
        cout<<"error to openfile "<< mec_protocal_file<<endl; 
        return ;
    }

    auto it_find = sData.find('|');
    if(it_find == string::npos)
    {
        cout<<"error format ./mec_protocal.txt "; 
        return ;
    }
    string s_command = sData.substr(0,it_find); 
    uint8_t u_command =  std::stoi(s_command);
    string data =  sData.substr(it_find+1); 
    gv_mec_protocol mec_protocol;
    string send_buf; 
    mec_protocol.bulid_frame(u_command,data,send_buf);
    hex_str hex;
    string hex_buf; 
    hex.encode(send_buf,hex_buf);
    cout<<hex_buf<<endl;
}


/***********************************************************
 * 函数名称: test_string
 * 功能描述: 运行字符串
 * 输入参数:
 * 输出参数:
 * 返 回 值:
 ***********************************************************/
void test_code::test_string()
{
    string sTemp;
    sTemp.reserve(256);
    cout<<"begin"<<sTemp.capacity()<<endl;
    for(int i =  0 ; i <100; i++)
    {
        sTemp.append("a");
        const char * p  = sTemp.c_str() ;
        //cout<<"addr "<<(int)(p)<<endl;
        if(i %10 == 0 )
        {
            cout<<sTemp.capacity()<<endl;
            printf("%p \n",p);
        }
    }
}


/***********************************************************
 * 函数名称: test_uper
 * 功能描述: 测试uper 编码数据
 * 输入参数:
 * 输出参数:
 * 返 回 值:
 ***********************************************************/
void test_code::test_raw_uper()
{

#if 0
    json_body = "{\"type\":\"rsc\",\"value\":\"8017000B140A1C9CD8DC9CD8DC9CE6E18D693A401AD2747FC0\"}"

#endif
    string value = "8017000B140A1C9CD8DC9CD8DC9CE6E18D693A401AD2747FC0";
    string sDecodeValue;
    sDecodeValue.resize(value.length()/2);
    hex_str  oHex(hex_str::c_type);
    oHex.decode((unsigned char*)value.data(),value.length(),(unsigned char*)sDecodeValue.data());
    MessageFrame_t * cv2xFrame =  NULL;


    asn_dec_rval_t rval = uper_decode(NULL,&asn_DEF_MessageFrame,
                                      (void **)&cv2xFrame,
                                      sDecodeValue.c_str(),sDecodeValue.size(),0,0);

    cout<<"rval.code "<<rval.code<<endl;
}

/***********************************************************
 * 函数名称: test_hex
 * 功能描述: 测试 hex
 * 输入参数:
 * 输出参数:
 * 返 回 值:
 ***********************************************************/
void test_code::test_hex()
{
    string encode_src = "ZZAABGcddsafeccc";
    string encode_dst;

    hex_str  oHex(hex_str::c_type);
    oHex.encode(encode_src,encode_dst);
    cout<<"encode_src:"<<encode_src<<endl<<"encode_dst:" <<encode_dst<<endl;


    unsigned char dfa[100] = {0};
    char * p2df = (char*) dfa;

    for(uint32_t i = 0; i<encode_src.size(); ++i)
    {
        sprintf(p2df,"%02X",encode_src[i]);
        p2df+=2;
    }
    cout<<"p2df:"<<dfa<<endl;
    //decode(unsigned char  *src, uint32_t  len,unsigned char *dst)

    unsigned char decode_temp[100]= {0};
    oHex.decode(dfa,encode_src.size()*2,(unsigned char*)decode_temp);
    cout<<"decode_temp:"<<decode_temp<<endl;


}

/***********************************************************
 * 函数名称: test_crc
 * 功能描述: 测试 crc
 * 输入参数:
 * 输出参数:
 * 返 回 值:
 ***********************************************************/
void test_code::test_crc()
{

#ifdef TEST_CODE
    string sTest ="123456";
    crc16 crc;
    uint16_t  arc  = crc.encode_arc(sTest.c_str(),sTest.size());
    uint16_t  v2x  = crc.encode_x25(sTest.c_str(),sTest.size());
    uint16_t  xmodem  = crc.encode_xmodem(sTest.c_str(),sTest.size());
    cout<<"crc 16 arc "<<arc<<" v2x "<<v2x<<" xmodem "<<xmodem<<endl;
#endif


}


/***********************************************************
 * 函数名称: free_frame_list
 * 功能描述: 协议 协议帧内容
 * 输入参数:
 * 输出参数:
 * 返 回 值:
 ***********************************************************/
void test_code::free_frame_list(list<GoEMECMessageFrame*> & listFrame )
{
    for(auto *pframe : listFrame )
    {
        ASN_STRUCT_FREE(asn_DEF_GoEMECMessageFrame,pframe);
    };
    listFrame.clear();
}

// OK

/***********************************************************
 * 函数名称: test_gxx_mec_protocol
 * 功能描述: 测试 mec 协议
 * 输入参数:
 * 输出参数:
 * 返 回 值:
 ***********************************************************/
void test_code::test_gxx_mec_protocol()
{


    int send_pack_size = 1024 ;
    config_ref.get(CK_GXX_MEC_SEND_PACK_SIZE,send_pack_size);
    string buffer;
    buffer.reserve(send_pack_size);

    gxx_mec_protocol m_gxx_mec_protocol;
    m_gxx_mec_protocol.init();



    // 创建发送buffer



    // 全解码
    list<GoEMECMessageFrame*>  listFrame;

#if 0

    packet_info m_send_buf;
    m_send_buf.len = send_pack_size;
    m_send_buf.pdata = (char*)buffer.c_str();
    packet_info out;

    m_gxx_mec_protocol.build_login(m_send_buf,out);

    m_gxx_mec_protocol.fetch_frame(out.pdata,out.len,listFrame);
    free_frame_list(listFrame);

    // 分包-1
    int par1 = 2;
    m_gxx_mec_protocol.fetch_frame(out.pdata,par1,listFrame);
    m_gxx_mec_protocol.fetch_frame(out.pdata+par1,out.len-par1,listFrame);
    free_frame_list(listFrame);


    // 分包-2
    par1 = 31;
    m_gxx_mec_protocol.fetch_frame(out.pdata,par1,listFrame);
    m_gxx_mec_protocol.fetch_frame(out.pdata+par1,out.len-par1,listFrame);
    free_frame_list(listFrame);


    // 两个包
    string twoFrame;
    twoFrame.reserve(out.len*2);
    twoFrame.append(out.pdata,out.len);
    twoFrame.append(out.pdata,out.len);
    m_gxx_mec_protocol.fetch_frame(twoFrame.c_str(),twoFrame.size(),listFrame);
    free_frame_list(listFrame);


    //破坏-头,再测试正常包
    char orgCh = out.pdata[10];
    out.pdata[10] = 'a';
    m_gxx_mec_protocol.fetch_frame(out.pdata,out.len,listFrame);

    out.pdata[10] = orgCh;
    m_gxx_mec_protocol.fetch_frame(out.pdata,out.len,listFrame);
    free_frame_list(listFrame);

    //破坏-包体,再测试正常包
    orgCh = out.pdata[100];
    out.pdata[100] = 'a';
    m_gxx_mec_protocol.fetch_frame(out.pdata,out.len,listFrame);

    out.pdata[100] = orgCh;
    m_gxx_mec_protocol.fetch_frame(out.pdata,out.len,listFrame);
    free_frame_list(listFrame);
#endif
    // 读取二进文件-进行解包测试
    string path ;
    //path="/data1/work/code/genvict/mec_channel/pcap/data.bin";
    //path="/data1/work/code/genvict/mec_channel/pcap/t";
    //path="/data1/work/code/genvict/mec_channel/pcap/rsu_bin";
    //path="/data1/work/code/genvict/mec_channel/pcap/rsu_my_bin";
    //path = "/data1/work/code/genvict/mec_channel/pcap/rsu_bin" ;
    path = "/data1/work/code/genvict/mec_channel/pcap/gxx_mec_bin" ;
    string sContent;
    read_file(path,sContent);

    m_gxx_mec_protocol.fetch_frame(sContent.c_str(),sContent.size(),listFrame);

    // v2x 客户端测试代码
    //event_engine engin;
    //v2x_client rsu_client(engin);
    //mec_client client(engin);


    free_frame_list(listFrame);


}


/***********************************************************
 * 函数名称: read_file
 * 功能描述: 读取文件
 * 输入参数:
 * 输出参数:
 * 返 回 值:
 ***********************************************************/
int test_code::read_file(const string & path, string & data)
{
    int fd =  open(path.c_str(),O_RDONLY);
    if(fd<=0)
    {
        return RET_FAIL;
    }

    char buf[1024];
    int nRead = 0;
    do
    {
        nRead = read(fd,buf,1024);
        if(nRead> 0)
        {
            data.append(buf,nRead);
        }
    }
    while(nRead>0);

    close(fd);
    return RET_OK;
}

/***********************************************************
 * 函数名称: test_json
 * 功能描述: 测试 json 函数
 * 输入参数:
 * 输出参数:
 * 返 回 值:
 ***********************************************************/
void test_code::test_json()
{
    string path="/data1/work/code/genvict/mec_channel/pcap/data.bin";
    string sContent;
    read_file(path,sContent);
    string key = "test_key";

    string sTemp;
    int content_size = sContent.size();
    sTemp.resize(content_size*2*2);
    char *pTemp = (char *)sTemp.c_str();
    for(int i = 0; i<content_size; i++)
    {
        sprintf(pTemp,"%02X",sContent[i]);
        pTemp +=2;
    };
    *pTemp='\0';
    json j;
    j[key] =  sTemp.c_str();
    j[key+"_str"] =  sTemp;
    string json_encode_data = j.dump();
    cout<<"json_encode_data "<<json_encode_data<<endl;
}


void test_code::test_asn_decode()
{

    cout<<LINE_SPLIT<<endl; 
    cout<<"test_asn_decode begin "<<endl;
    string asn_hex_data_path = "./asn_hex_data.log";
    ifstream  ifs;
    ifs.open(asn_hex_data_path.c_str());
    if(!ifs.good())
    {
        cout<<" path no exit "<<asn_hex_data_path<<endl;
        return ;
    }
    hex_str ohex;
    const int line_buf_size  = 1024*60; // 60K
    char line_buf[line_buf_size] ={0};
    int line_size = 0; 
    int line_idx = 0;
    while(!ifs.eof())
    {
        line_idx++; 
        ifs.getline(line_buf,line_buf_size);
        line_size = strlen(line_buf);
        if(line_size == 0 )
        {
            continue;
        } 
        
        cout<<"line_buf "<<line_buf<<endl;
        cout<<"line_size "<<line_size<<endl;
        string testHex =  line_buf; 
        string sAsn; 
        sAsn.resize(testHex.size()/2); 

        ohex.decode((unsigned char*)testHex.data(),testHex.length(),(unsigned char*)sAsn.c_str());
        cout<<"sAsn size  "<<sAsn.size()<<endl;
        MessageFrame_t * asn_v2x_frame = nullptr;
        
        asn_dec_rval_t rval = uper_decode(NULL,&asn_DEF_MessageFrame,
                                            (void **)&asn_v2x_frame,
                                            sAsn.c_str(),sAsn.length(),0,0);

        if(rval.code != RC_OK)
        {
            cout<<LINE_SPLIT<<endl; 
            cout<<"line_idx "<<line_idx<< "Error rval.code "<<rval.code<<" sAsn.length() "<<sAsn.length() <<endl;
            cout<<endl<<endl;
            continue;
        }
        //cout<<"==========================================================="<<endl; 
        cout<<LINE_SPLIT<<endl;
        cout<<"line_idx "<<line_idx<<" Success  "<<endl;
        asn_fprint(stdout, &asn_DEF_MessageFrame, asn_v2x_frame);
        ASN_STRUCT_FREE(asn_DEF_MessageFrame,asn_v2x_frame);
        cout<<endl<<endl;
    }
    cout<<LINE_SPLIT<<endl; 
    cout<<"test_asn_decode finish "<<endl;
    ifs.close();
}



void test_code::print_wmm_log_data()
{
    string log_file_path = "./wmm_log_data.log";

    ifstream  ifs;
    ifs.open(log_file_path);
    if(!ifs.good())
    {
        return ;
    }

    const int line_buf_size  = 1024*60; // 60K
    char line_buf[line_buf_size];
    static const char* light_map_name[]
    {
        "LightState_unavailable",
        "LightState_dark",
        "red",//LightState_flashing_red
        "red",//LightState_red
        "green",//LightState_flashing_green
        "green",//LightState_permissive_green
        "green",//LightState_protected_green
        "yellow",//LightState_yellow
        "yellow"//LightState_flashing_yellow
    };

    hex_str ohex;
    while(!ifs.eof())
    {
        ifs.getline(line_buf,line_buf_size);
        //line format  [2023-03-02 11:05:00.637]LB_LW10[D]recv wmm[map](len = 1112): 04 00 8
        int iFormatSplitCount = 0;
        string sTime;
        string lineHead;
        string sAsnHexData;

        bool bAppendAsnData = false;
        bool fetch_time = false;
        int iRightKH = 0;
        string sDir ;
        
        for(int i = 0 ; (i<line_buf_size&& line_buf[i]!='\0'); ++i)
        {
            if(bAppendAsnData == true && line_buf[i]!=' ')
            {
                sAsnHexData.push_back(line_buf[i]);
                continue;
            }

            if(line_buf[i] == ':')
            {
                iFormatSplitCount++;
            }
            if(line_buf[i] == ']')
            {
                iRightKH ++;
            }
           
            
            if(iRightKH == 2&& sDir.empty() )
            {
                if(line_buf[i] !=']')
                {
                   sDir.append(line_buf+i,4); 
                }
            }
            
            
            
            if(line_buf[i] == ']' && fetch_time == false)
            {
                sTime.append(line_buf+1,i-1);
                fetch_time =  true;
            }
            if(iFormatSplitCount == 3)
            {
                line_buf[i] = '\0';
                lineHead =  line_buf;
                bAppendAsnData =  true;
            }

        }
        if(sAsnHexData.empty() == true)
        {
            continue;
        }
        // 解码当前帧
        cout<<"begin ==================================================="<<endl;
        string sNetBinData;
        sNetBinData.resize(sAsnHexData.length()/2);
        ohex.decode((unsigned char*)sAsnHexData.c_str(),sAsnHexData.length(),(unsigned char*)sNetBinData.c_str());

        unsigned char * pAsnBinData = nullptr;
        uint32_t  asnBinDataLen  = 0;
        netlayer_info_t net_info;
        if(1!=convert_from_netlayer_protocol(pAsnBinData,asnBinDataLen,sNetBinData.c_str(),sNetBinData.length(),net_info))
        {

            continue;
        }

#if  1
        //cout<<"sNetBinData_len :"<<sNetBinData.length()  <<" asnBinDataLen:"<<asnBinDataLen<<endl;
        string asnBinDataLen_hex; 
        ohex.encode(pAsnBinData,asnBinDataLen,asnBinDataLen_hex); 
        
#endif 
        MessageFrame_t * asn_v2x_frame = nullptr;
        asn_dec_rval_t rval = uper_decode(NULL,&asn_DEF_MessageFrame,
                                          (void **)&asn_v2x_frame,
                                          pAsnBinData,asnBinDataLen,0,0);

        if(rval.code != RC_OK)
        {
            cout<<"asn decode error "<<sAsnHexData<<" rval.code "<<rval.code <<endl;
            continue;
        }
        cout<<lineHead<<endl;
        asn_fprint(stdout, &asn_DEF_MessageFrame, asn_v2x_frame);
        if(asn_v2x_frame->present == MessageFrame_PR_bsmFrame )
        {
            auto & bsmFrame  = asn_v2x_frame->choice.bsmFrame;
            // 保持格式一样
            char sID[9] ={0};
            memcpy(sID,bsmFrame.id.buf,8);
            long posConfidence = 0;
            if(bsmFrame.posConfidence !=nullptr)
            {
                posConfidence = bsmFrame.posConfidence->pos;
            }
            string elevation = "null"; 
            if(bsmFrame.pos.elevation !=nullptr)
            {
                elevation = std::to_string(*bsmFrame.pos.elevation);
            }
            string draw_time =  sTime; 
            for(auto &ch:draw_time )
            {
                if(ch==':')
                {
                    ch='-'; 
                }
            }
            
            cout<<"print_wmm_log_data_84"<<sTime<<","<<bsmFrame.pos.lat<<","<<bsmFrame.pos.Long<<","<<bsmFrame.speed<<","<<bsmFrame.heading<<endl;
            utility::coordinate_convert::v2x_coordinate pos_84, pos_02; 
            pos_84.latitude = bsmFrame.pos.lat/LATITUDE_RES; 
            pos_84.longitude = bsmFrame.pos.Long/LONGITUDE_RES; 
            utility::coordinate_convert::wgs84_to_cj02(pos_84,pos_02); 
            long pos_02_lat = pos_02.latitude*LATITUDE_RES; 
            long pos_02_lng = pos_02.longitude*LONGITUDE_RES; 
            cout<<"print_wmm_log_data_02"<<sTime<<","<<pos_02_lat<<","<<pos_02_lng<<","<<bsmFrame.speed<<","<<bsmFrame.heading<<endl;
            cout<<"bsm_tool_84_"<<draw_time<<" "<<sID<<" lat:"<<bsmFrame.pos.lat<<":log:"<<bsmFrame.pos.Long<<":elv:61:heading:"<<bsmFrame.heading<<":speed:"<<bsmFrame.speed<<":pos_confidence:13_a5cm"<<endl;
            cout<<"bsm_tool_02_"<<draw_time<<" "<<sID<<" lat:"<<pos_02_lat<<":log:"<<pos_02_lng<<":elv:61:heading:"<<bsmFrame.heading<<":speed:"<<bsmFrame.speed<<":pos_confidence:13_a5cm"<<endl;
        }

        if(asn_v2x_frame->present == MessageFrame_PR_mapFrame)
        {
            auto & mapFrame =  asn_v2x_frame->choice.mapFrame;
            for(int i = 0 ; i < mapFrame.nodes.list.count; i ++)
            {
                auto & node =   mapFrame.nodes.list.array[i];
                cout<<sTime<<" "<<sDir <<" map:"<<*(node->id.region)<<"-"<<node->id.id; 
                if(node->name!=nullptr)
                {
                    cout<<" name:"<<node->name->buf; 
                }
            }
        }

        if(asn_v2x_frame->present == MessageFrame_PR_rsmFrame)
        {
            cout<<"rsm" ;
        }

        if(asn_v2x_frame->present == MessageFrame_PR_spatFrame)
        {
            auto & spatFrame =  asn_v2x_frame->choice.spatFrame;
            for(int i = 0 ; i < spatFrame.intersections.list.count; ++i)
            {
                auto & intersection  =  spatFrame.intersections.list.array[i];
                auto & intersectionId =  intersection->intersectionId;
                cout<<sTime<<" "<<sDir <<" spat:"<<*(intersectionId.region)<<"-"<<intersectionId.id<<" ";
                for(int j = 0; j < intersection->phases.list.count; j++)
                {
                    auto & phase =  intersection->phases.list.array[j];
                    cout<<phase->id ;
                    for(int k = 0; k< phase->phaseStates.list.count ; k++)
                    {
                        auto &phaseState = phase->phaseStates.list.array[k];
                        auto & timing = phaseState->timing;
                        //phaseState->light;
                        if( timing->present == TimeChangeDetails_PR_counting)
                        {
                            auto & counting = timing->choice.counting ;
                            if(counting.startTime != 0)
                            {
                                continue;
                            }
                            cout<<light_map_name[phaseState->light]<<counting.likelyEndTime<<" ";
                        }
                        if(timing->present == TimeChangeDetails_PR_utcTiming)
                        {
                            //auto & utc =  timing->choice.utcTiming;
                            cout<<" not supt utc time ";
                            continue;
                        }
                    }

                }
            }


        }
        if(asn_v2x_frame->present == MessageFrame_PR_rsiFrame)
        {
            cout<<"rsi";
        }
        if(asn_v2x_frame->present == MessageFrame_PR_msgFrameExt)
        {
            cout<<"ext";
        }
        cout<<"asnBinDataLen_hex "<<asnBinDataLen_hex<<endl;
        cout<<endl<<"end ==================================================="<<endl<<endl;
        ASN_STRUCT_FREE(asn_DEF_MessageFrame,asn_v2x_frame);
    }
    ifs.close();
}

void test_code::tcp_client_tool_fun()
{
    tcp_client_tool tcp_tool(m_event_engine);
    int nRet = RET_OK;
    auto stop_cb =  bind(&test_code::set_stop_flag,this, std::placeholders::_1);
    m_signal_handler.init(stop_cb);
    int hz  = 10;
    config_ref.get(CK_SYS_HZ,hz);
    nRet =   m_event_engine.init(hz);

    if(RET_OK != nRet)
    {
        WLE("m_event_engine.init Error nRet %d ",nRet);
        return ;
    }
    if(RET_OK !=tcp_tool.init())
    {
        return ;
    }
    m_event_engine.run();
    tcp_tool.clean();
}

void test_code::http_client_tool_fun()
{
    http_client_tool http_tool(m_event_engine);
    int nRet = RET_OK;
    auto stop_cb =  bind(&test_code::set_stop_flag,this, std::placeholders::_1);
    m_signal_handler.init(stop_cb);
    int hz  = 10;
    config_ref.get(CK_SYS_HZ,hz);
    nRet =   m_event_engine.init(hz);
    if(RET_OK != nRet)
    {
        WLE("m_event_engine.init Error nRet %d ",nRet);
        return ;
    }
    if(RET_OK !=http_tool.init())
    {
        return ;
    }
    m_event_engine.run();
    http_tool.clean();   
}



void test_code::tcp_server_tool_fun()
{
    tcp_service_tool tcp_tool(m_event_engine);
    int nRet = RET_OK;
    auto stop_cb =  bind(&test_code::set_stop_flag,this, std::placeholders::_1);
    m_signal_handler.init(stop_cb);

    int hz  = 10;
    config_ref.get(CK_SYS_HZ,hz);
    nRet =   m_event_engine.init(hz);
    if(RET_OK != nRet)
    {
        WLE("m_event_engine.init Error nRet %d ",nRet);
        return ;
    }
    if(RET_OK !=tcp_tool.init())
    {
        return ;
    }
    m_event_engine.run();
    tcp_tool.clean();

}



int test_code::convert_from_netlayer_protocol(unsigned char * &pdst, uint32_t & len,const char *psrc, uint32_t  src_len, netlayer_info_t &pram)
{
    uint32_t sp = 0;
    unsigned char *pdata = (unsigned char *)psrc;

    if(pdata[sp++] != GB_DSMP)
    {
        return -1;
    }
    if(IS_BIT_SET(pdata[sp], 5))//支持扩展域
    {
        return -1;
    }
    pram.opt_ind = 0;
    pram.ver = (pdata[sp++] >> 5) & 0X07;
    if(!IS_BIT_SET(pdata[sp], 8)) //aid为一字节
    {
        pram.aid = pdata[sp++];
        if(pram.aid > 0x7F)
        {
            return -1;
        }
    }
    else if((pdata[sp] >> 6) == 0x02)
    {
        pram.aid = (pdata[sp++] << 8);
        pram.aid |= pdata[sp++];
        if(pram.aid  < 0x8000 || pram.aid  > 0xBFFF)
        {
            return -1;
        }
        pram.aid -= aid_offset;
    }
    else
    {
        return -1;
    }
    len = (pdata[sp++] << 8);
    len |= pdata[sp++];
    pdst = pdata+sp ;
    return 1;
}


/***********************************************************
 * 函数名称: set_stop_flag
 * 功能描述: 设置服务停止标记
 * 输入参数:
 * 输出参数:
 * 返 回 值:
 ***********************************************************/
void test_code::set_stop_flag(const string & stop_msg)
{
    WLI("recv stop flag msg %s ",stop_msg.c_str());
    m_event_engine.stop();
}


void test_code::timeout_cb()
{

}

void test_code::read_cb(engine_conn_handler & handler, const char* msg, int len)
{
    WLI("read_cb Http len %d msg %s",len,msg);
    const char *pRet =  "hello world";
    m_event_engine.send_http_service_respone_data(handler,pRet,strlen(pRet));
}

void test_code::test_http_service()
{

    int nRet = RET_OK;
    auto stop_cb =  bind(&test_code::set_stop_flag,this, std::placeholders::_1);
    m_signal_handler.init(stop_cb);

    int hz  = 10;
    config_ref.get(CK_SYS_HZ,hz);
    nRet =   m_event_engine.init(hz);
    if(RET_OK != nRet)
    {
        WLE("m_event_engine.init Error nRet %d ",nRet);
        return ;
    }

    // 注册 http
    engine_conn_handler  accept_handler;
    if(RET_OK != m_event_engine.regist_http_service(this,"127.0.0.1",8090,accept_handler))
    {

        WLE("m_event_engine.regist_http_service Error nRet %d ",nRet);
        return ;
    }
    m_event_engine.run();


}

void test_code::test_circle_fun()
{
    double circle_x; 
    double circle_y; 
    double circle_r; 
#if 0
    ring_a = {"name": "t1", "lng": 121.4111556, "lat": 31.2412466}
    ring_b = {"name": "t5", "lng": 121.4109573, "lat": 31.2411970}
    ring_c = {"name": "t9", "lng": 121.4107170, "lat": 31.2412453}
#endif 
    cal_circle_fun(
        121.4111556,31.2412466,
        121.4109573,31.2411970,
        121.4107170,31.2412453, 
        circle_x,circle_y,circle_r
        ); 

    //cout<<" circle_x "<<circle_x << " circle_y "<<circle_y<<" circle_r "<<circle_r<<endl;

//
    cout<<"A_dis "<<cal_dis(121.4111556,31.2412466,121.411,10258.8)<<endl;
    cout<<"B_dis "<<cal_dis(121.4109573,31.2411970,121.411,10258.8)<<endl;
    cout<<"C_dis "<<cal_dis(121.4107170,31.2412453,121.411,10258.8)<<endl;
    printf("circle_x %0.7f circle_y %0.7f circle_r%0.7f ", circle_x,circle_y,circle_r);


}

void test_code::cal_line_fun(double a_x,double a_y, double b_x,double b_y, double &fun_a,double &fun_b)
{
    fun_a =  (b_y - a_y)/(b_x -a_x); 
    fun_b =  a_y -  fun_a * a_x;
}

void test_code::cal_vertical_line_fun(double a_x,double a_y, double b_x,double b_y,double c_x,double c_y, double &fun_a,double &fun_b)
{
    fun_a =  -1/((b_y - a_y)/(b_x -a_x)); 
    fun_b =  c_y -  fun_a * c_x;
}

void test_code::cal_line_cross_point(double line1_a,double line1_b,double line2_a,double line2_b,double &x,double &y)
{
    x =(line1_b -  line2_b)/(line2_a-line1_a);
    y =  line1_a * x + line1_b;
}

void test_code::cal_circle_fun(double a_x,double a_y, double b_x,double b_y,double c_x,double c_y,double & circle_x,double & circle_y , double & r )
{

    // 求两个点的垂线
    double line1_a,line1_b,line2_a,line2_b; 
    cal_vertical_line_fun(a_x,a_y,b_x,b_y,(a_x+b_x)/2.0,(a_y+b_y)/2.0,line1_a, line1_b); 


    
    double line_y_check = 31.241244; 
    double line_x_check = (line_y_check - line1_b)/line1_a;
    printf("\nline1 %0.7f %0.7f \n",line_x_check,line_y_check);

    line_y_check = 31.2411970; //t5
    line_x_check = (line_y_check - line1_b)/line1_a;
    printf("\nline1 t5 %0.7f %0.7f \n",line_x_check,line_y_check);



    cal_vertical_line_fun(c_x,c_y,b_x,b_y,(c_x+b_x)/2.0,(c_y+b_y)/2.0,line2_a, line2_b); 
    line_y_check = 31.2412266; // x = 121.4108120
    line_x_check = (line_y_check - line2_b)/line2_a;
    printf("\nline2 %0.7f %0.7f \n",line_x_check,line_y_check);

    line_y_check = 31.2411970; 
    line_x_check = (line_y_check - line2_b)/line2_a;
    printf("\nline2 t5 %0.7f %0.7f \n",line_x_check,line_y_check);


    //求两条垂线的交叉点  
    cal_line_cross_point(line1_a,line1_b,line2_a,line2_b,circle_x,circle_y); 
    r = sqrt(pow((a_x-circle_x),2.0) + pow((a_y - circle_y),2.0)); 
}
double test_code::cal_dis(double a_x,double a_y, double b_x,double b_y)
{
    return sqrt(pow((a_x-b_x),2.0) + pow((a_y - b_y),2.0));
}
