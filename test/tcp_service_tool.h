/***********************************************
版本信息： 金溢科技版本权所有，保留一切权利
文件名称： tcp_service_tool.h
作者：张明
开始日期：2023-06-29 13:35:51
完成日期：
当前版本号: v 0.0.1
主要功能: 
版本历史:
***********************************************/

#ifndef _TCP_SERVICE_TOOL_H_
#define _TCP_SERVICE_TOOL_H_

#include "ireceiver.h"
#include "event_engine.h"
#include <vector>
#include <map>
#include <string>
using namespace std;


class tcp_service_tool: public itcpservice_receiver
{
public:
	tcp_service_tool(event_engine &event_engine);
	~tcp_service_tool();
 	int init();
    int clean();

public: // interface irecevier
	virtual void accept_cb(engine_conn_handler & new_client_handler);
    void timeout_cb(long long ms ) ;
    virtual void read_cb(engine_conn_handler & handler, const char* msg, int len) override;
    virtual void event_cb(int fd, short events ) override;

private:
    int load_hex_data();
    void proc_read_file();

private:
    event_engine &m_event_engine;
    engine_conn_handler  m_accept_handler;
    vector<string> m_bin_data;
    unsigned int  m_send_index = 0;
    map<int,engine_conn_handler> m_clients;

};
#endif //_TCP_SERVICE_TOOL_H_	
