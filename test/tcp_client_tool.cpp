#include <string>
#include <fstream>
#include "hex_str.h"
#include "config.h"
#include "tcp_client_tool.h"
#include "stdafx.h"
#include "service.h"
#include "config.h"
#include <sys/types.h>
#include <signal.h>

/***********************************************************
 * 函数名称:
 * 功能描述: 构造函数
 * 输入参数:
 * 输出参数:
 * 返 回 值:
 ***********************************************************/
tcp_client_tool::tcp_client_tool(event_engine &event_engine):m_event_engine(event_engine)
{

}

/***********************************************************
 * 函数名称:
 * 功能描述: 虚构函数
 * 输入参数:
 * 输出参数:
 * 返 回 值:
 ***********************************************************/
tcp_client_tool::~tcp_client_tool()
{

}

void tcp_client_tool::event_cb(int fd, short events )
{
    WLE("tcp_client_tool::event_cb fd %d events %d ",fd,events);

    if (events & BEV_EVENT_ERROR)
    {
        WLE("fd %d BEV_EVENT_ERROR %s",fd,strerror(EVUTIL_SOCKET_ERROR()));
        m_conn_handler.init();
        m_event_engine.stop();
        m_b_connect_flag = false;
        return ;
    }
    if (events & BEV_EVENT_CONNECTED )
    {
        WLI("fd %d BEV_EVENT_CONNECTED ",fd );
        m_b_connect_flag =  true;
    }

}

void tcp_client_tool::timeout_cb(long long ms)
{
    if(m_bin_data.size() == 0 )
    {
        WLE("m_bin_data.size() == 0 ");
        return;
    }
    if(m_b_connect_flag == false)
    {
        WLE("m_b_connect_flag == false");
        return ;
    }
    int test_finish = 1;
    config_ref.get(CK_TEST_TCP_CLIETN_TOOL_SEND_SIZE,test_finish);

    static int send_count = 0;

    if(send_count >= test_finish )
    {
        WLI(" finish  " );
        kill(0,SIGINT); // 发送中断信号，退出
        return ;
    }

    for(int i = 0 ; i<1;++i)
    {
        WLI(" begin test %d ",send_count );
        send_count ++;
        if(m_send_index >= m_bin_data.size())
        {
            m_send_index = 0;
        }
        m_event_engine.send_tcp_data(m_conn_handler,m_bin_data[m_send_index].c_str(),m_bin_data[m_send_index].length());
        m_send_index++;
    }
    //service_ref.set_stop_flag("tcp_client_tool set stop ");
    //WLI(" begin findish %d ")    ;
    
}

int tcp_client_tool::init()
{
    if(RET_OK!=load_hex_data())
    {
        return RET_FAIL;
    }

    m_event_engine.get_time_task().regist_task(std::bind(&tcp_client_tool::timeout_cb,FUN_BIND_1),1);

    string ip;
    int port;
    config_ref.get(CK_V2X_SERVICE_IP,ip);
    config_ref.get(CK_V2X_SERVICE_PORT,port);
    WLI(" ip %s port %d ",ip.c_str(),port);
    if(RET_OK != m_event_engine.regist_tcp_client(this,ip.c_str(),port,m_conn_handler))
    {

        WLE("m_event_engine.regist_tcp_client Error");
        return RET_FAIL;
    }
    WLI("m_event_engine.regist_tcp_client success");
    return RET_OK;
}
int tcp_client_tool::clean()
{
    return m_event_engine.unregist(this,m_conn_handler);
}


void tcp_client_tool::read_cb(engine_conn_handler & handler, const char* msg, int len)
{
    //WLI("len %d ",len);
}

int tcp_client_tool::load_hex_data()
{
    string tcp_hex_data = "./tcp_hex_data.txt";
    WLI("LOAD PATH %s ",tcp_hex_data.c_str());
    ifstream  ifs;
    ifs.open(tcp_hex_data);
    if(!ifs.good())
    {
        return RET_FAIL;
    }

    const int line_buf_size  = 1024*60; // 60K
    char line_buf[line_buf_size]= {0};
    hex_str ohex;
    while(!ifs.eof())
    {
        ifs.getline(line_buf,line_buf_size);
        int len = strlen(line_buf);
        if(len == 0 || len%2 !=0 )
        {
            cout<<"skin line_buf "<<line_buf<<" len "<<len<<endl;
            continue;
        }
        string bin_data;
        bin_data.resize(len/2);
        ohex.decode((unsigned char *)line_buf,len,(unsigned char *)bin_data.c_str());
        m_bin_data.emplace_back(move(bin_data));
    }
    ifs.close();
    return RET_OK;
}

