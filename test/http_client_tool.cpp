#include <string>
#include <fstream>
#include "hex_str.h"
#include "config.h"
#include "http_client_tool.h"
#include "stdafx.h"
#include "config.h"

#include <sys/types.h>
#include <signal.h>

/***********************************************************
 * 函数名称: 
 * 功能描述: 构造函数
 * 输入参数: 
 * 输出参数: 
 * 返 回 值: 
 ***********************************************************/
http_client_tool::http_client_tool(event_engine &event_engine):m_event_engine(event_engine)
{
}

/***********************************************************
 * 函数名称: 
 * 功能描述: 虚构函数
 * 输入参数: 
 * 输出参数: 
 * 返 回 值: 
 ***********************************************************/
http_client_tool::~http_client_tool()
{
	
}


void http_client_tool::event_cb(int fd, short events )
{
    WLE("tcp_client_tool::event_cb fd %d events %d ",fd,events);

}

void http_client_tool::timeout_cb(long long ms)
{
   
}

void http_client_tool::update_conn_handler(engine_conn_handler & new_client_handler)
{
    m_conn_handler = new_client_handler;
}



int http_client_tool::init()
{

    m_event_engine.get_time_task().regist_task(std::bind(&http_client_tool::timeout_cb,FUN_BIND_1),1);

    if(RET_OK != m_event_engine.regist_http_client(this,"http://www.baidu.com",m_conn_handler))
    {

        WLE("m_event_engine.regist_tcp_client Error");
        return RET_FAIL;
    }
    WLI("m_event_engine.regist_http_client success");
    http_req req_obj;
    req_obj.data ="abc";
    m_event_engine.http_post(m_conn_handler,req_obj);
    return RET_OK;
}
int http_client_tool::clean()
{
    return m_event_engine.unregist(this,m_conn_handler);
}

void http_client_tool::read_cb(engine_conn_handler & handler, const char* msg, int len)
{
    //WLI("%s",msg);
    cout<<msg;
}
int http_client_tool::get_conn_fd (void *conn_handler_data)
{
    return m_conn_handler.fd;
}