/***********************************************
版本信息： 金溢科技版本权所有，保留一切权利
文件名称： http_client_mgr.h
作者：张明
开始日期：2023-10-13 13:56:50
完成日期：
当前版本号: v 0.0.1
主要功能: curl http client 管理者
版本历史:
***********************************************/

#ifndef _HTTP_CLIENT_MGR_H_
#define _HTTP_CLIENT_MGR_H_

#include "engine_module_macro.h"
#include "event2/event.h"
#include "iengine.h"
#include "ireceiver.h"
#include <unordered_map>
#include <engine_event_notice.hpp>
#include "common_utility.h"
#include "curl/curl.h"

#if ENGINE_HTTP_CLIENT_ENABLE
namespace ENGINE_MODULE_NAMESPACE{

class http_client_mgr
{
public:
	struct crul_data_cb_par_data
	{
		http_client_mgr *pthis = {nullptr};
		ihttpclient_receiver *precv {nullptr};
		CURL *curl_client = {nullptr};
	};
    

	struct http_conn_node
    {
        // 此结构体不管理持有资源
        inline void init_http_client(ihttpclient_receiver *r,CURL * client,http_client_mgr::crul_data_cb_par_data *cb_par =  nullptr,curl_slist* headers = nullptr)
        {
            recv = r; 
            http_curl_client =  client;
            http_curl_cb_par =  cb_par;
            http_curl_headers = headers;
            http_curl_response_data.clear();
        }

        ihttpclient_receiver *recv{nullptr};

        // lib_curl_client
        CURL *http_curl_client{nullptr};
        event *http_curl_read_event{nullptr};	// 
        event *http_curl_write_event{nullptr};  //
        curl_slist *http_curl_headers{nullptr};
        struct curl_httppost *http_curl_formpost{nullptr};
        http_client_mgr::crul_data_cb_par_data *http_curl_cb_par{nullptr};
        string http_curl_response_data;
    };

public:
	http_client_mgr();
	~http_client_mgr();
	int init(event_base *event_base); 
	void destory(void); 
	void on_timeout(uint64_t now_tm);
	int regist_http_client(ihttpclient_receiver *precevier, const string &url, engine_conn_handler &conn_handler) ; 
	int unregist(ireceiver *precevier, engine_conn_handler &handler) ; 

	int http_post(engine_conn_handler &handler, http_req & data);
    int http_form_submit(engine_conn_handler &handler,http_req & data);
    int http_get(engine_conn_handler &handler, http_req & data); 


    // curl_multi 
    int do_curl_multi_socke_cb(CURL *easy, curl_socket_t s, int action, void *socketp);
    int do_curl_multi_timer_cb(CURLM *multi, long timeout_ms);
    size_t do_curl_write_data(http_client_mgr::crul_data_cb_par_data *par,void *buffer, size_t size, size_t nmemb);
    void do_curl_event_perform_cb(int fd, short event);
    void check_multi_info(void);
    void on_curl_timeout_event(evutil_socket_t fd, short events);


private:
	int init_curl_multi();
	int erase_conn_node(evutil_socket_t fd, http_conn_node &info);	
	evutil_socket_t get_fake_fd();
private:
	
	unordered_map<evutil_socket_t, http_conn_node> m_conn_node;
	event_base *m_pevent_base{nullptr};
	    // curl 库
    CURLM *m_crulm{nullptr}; 
    event * m_curl_timeout_event{nullptr};

};
}
#endif 

#endif //_HTTP_CLIENT_MGR_H_
