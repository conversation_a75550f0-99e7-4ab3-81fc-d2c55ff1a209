[{"arguments": ["/usr/local/bin/arm-linux-gnueabi-g++", "-DPLATFORM_ARM32", "-I/data/mec_channel/.", "-I/data/mec_channel/./mec_asn", "-I/data/mec_channel/./include", "-I/data/mec_channel/./engine_module/include", "-I/data/mec_channel/./engine_module/include/mosquitto", "-I/data/mec_channel/./engine_module/include/zmq", "-I/data/mec_channel/./engine_module/utility", "-I/data/mec_channel/./engine_module/signal", "-I/data/mec_channel/./engine_module/log", "-I/data/mec_channel/./engine_module/config", "-I/data/mec_channel/./engine_module/mem", "-I/data/mec_channel/./engine_module/engine", "-I/data/mec_channel/./engine_module/thread_resource", "-I/data/mec_channel/./mec_client", "-I/data/mec_channel/./mqtt_client", "-I/data/mec_channel/./dvin_client", "-I/data/mec_channel/./v2x_client", "-I/data/mec_channel/./platform", "-I/data/mec_channel/./platform/genvict", "-I/data/mec_channel/./platform/genvict_sdk", "-I/data/mec_channel/./platform/debug", "-I/data/mec_channel/./platform/empty", "-I/data/mec_channel/./platform/baidu", "-I/data/mec_channel/./platform/boyuan", "-I/data/mec_channel/./platform/gosuncn", "-I/data/mec_channel/./platform/huawei", "-I/data/mec_channel/./platform/multi", "-I/data/mec_channel/./platform/tianan_obu", "-I/data/mec_channel/./platform/tianan", "-I/data/mec_channel/./platform/tianan/mec_protocol", "-I/data/mec_channel/./test", "-I/data/mec_channel/./app", "-O3", "-DNDEBUG", "--std=c++11", "-Wall", "-O3", "-Wno-psabi", "-c", "-o", "CMakeFiles/mec_channel.dir/engine_module/utility/app_clock.cpp.o", "/data/mec_channel/engine_module/utility/app_clock.cpp"], "directory": "/data/mec_channel", "file": "/data/mec_channel/engine_module/utility/app_clock.cpp", "output": "/data/mec_channel/CMakeFiles/mec_channel.dir/engine_module/utility/app_clock.cpp.o"}, {"arguments": ["/usr/local/bin/arm-linux-gnueabi-g++", "-DPLATFORM_ARM32", "-I/data/mec_channel/.", "-I/data/mec_channel/./mec_asn", "-I/data/mec_channel/./include", "-I/data/mec_channel/./engine_module/include", "-I/data/mec_channel/./engine_module/include/mosquitto", "-I/data/mec_channel/./engine_module/include/zmq", "-I/data/mec_channel/./engine_module/utility", "-I/data/mec_channel/./engine_module/signal", "-I/data/mec_channel/./engine_module/log", "-I/data/mec_channel/./engine_module/config", "-I/data/mec_channel/./engine_module/mem", "-I/data/mec_channel/./engine_module/engine", "-I/data/mec_channel/./engine_module/thread_resource", "-I/data/mec_channel/./mec_client", "-I/data/mec_channel/./mqtt_client", "-I/data/mec_channel/./dvin_client", "-I/data/mec_channel/./v2x_client", "-I/data/mec_channel/./platform", "-I/data/mec_channel/./platform/genvict", "-I/data/mec_channel/./platform/genvict_sdk", "-I/data/mec_channel/./platform/debug", "-I/data/mec_channel/./platform/empty", "-I/data/mec_channel/./platform/baidu", "-I/data/mec_channel/./platform/boyuan", "-I/data/mec_channel/./platform/gosuncn", "-I/data/mec_channel/./platform/huawei", "-I/data/mec_channel/./platform/multi", "-I/data/mec_channel/./platform/tianan_obu", "-I/data/mec_channel/./platform/tianan", "-I/data/mec_channel/./platform/tianan/mec_protocol", "-I/data/mec_channel/./test", "-I/data/mec_channel/./app", "-O3", "-DNDEBUG", "--std=c++11", "-Wall", "-O3", "-Wno-psabi", "-c", "-o", "CMakeFiles/mec_channel.dir/engine_module/utility/common_utility.cpp.o", "/data/mec_channel/engine_module/utility/common_utility.cpp"], "directory": "/data/mec_channel", "file": "/data/mec_channel/engine_module/utility/common_utility.cpp", "output": "/data/mec_channel/CMakeFiles/mec_channel.dir/engine_module/utility/common_utility.cpp.o"}, {"arguments": ["/usr/local/bin/arm-linux-gnueabi-g++", "-DPLATFORM_ARM32", "-I/data/mec_channel/.", "-I/data/mec_channel/./mec_asn", "-I/data/mec_channel/./include", "-I/data/mec_channel/./engine_module/include", "-I/data/mec_channel/./engine_module/include/mosquitto", "-I/data/mec_channel/./engine_module/include/zmq", "-I/data/mec_channel/./engine_module/utility", "-I/data/mec_channel/./engine_module/signal", "-I/data/mec_channel/./engine_module/log", "-I/data/mec_channel/./engine_module/config", "-I/data/mec_channel/./engine_module/mem", "-I/data/mec_channel/./engine_module/engine", "-I/data/mec_channel/./engine_module/thread_resource", "-I/data/mec_channel/./mec_client", "-I/data/mec_channel/./mqtt_client", "-I/data/mec_channel/./dvin_client", "-I/data/mec_channel/./v2x_client", "-I/data/mec_channel/./platform", "-I/data/mec_channel/./platform/genvict", "-I/data/mec_channel/./platform/genvict_sdk", "-I/data/mec_channel/./platform/debug", "-I/data/mec_channel/./platform/empty", "-I/data/mec_channel/./platform/baidu", "-I/data/mec_channel/./platform/boyuan", "-I/data/mec_channel/./platform/gosuncn", "-I/data/mec_channel/./platform/huawei", "-I/data/mec_channel/./platform/multi", "-I/data/mec_channel/./platform/tianan_obu", "-I/data/mec_channel/./platform/tianan", "-I/data/mec_channel/./platform/tianan/mec_protocol", "-I/data/mec_channel/./test", "-I/data/mec_channel/./app", "-O3", "-DNDEBUG", "--std=c++11", "-Wall", "-O3", "-Wno-psabi", "-c", "-o", "CMakeFiles/mec_channel.dir/engine_module/utility/crc16.cpp.o", "/data/mec_channel/engine_module/utility/crc16.cpp"], "directory": "/data/mec_channel", "file": "/data/mec_channel/engine_module/utility/crc16.cpp", "output": "/data/mec_channel/CMakeFiles/mec_channel.dir/engine_module/utility/crc16.cpp.o"}, {"arguments": ["/usr/local/bin/arm-linux-gnueabi-g++", "-DPLATFORM_ARM32", "-I/data/mec_channel/.", "-I/data/mec_channel/./mec_asn", "-I/data/mec_channel/./include", "-I/data/mec_channel/./engine_module/include", "-I/data/mec_channel/./engine_module/include/mosquitto", "-I/data/mec_channel/./engine_module/include/zmq", "-I/data/mec_channel/./engine_module/utility", "-I/data/mec_channel/./engine_module/signal", "-I/data/mec_channel/./engine_module/log", "-I/data/mec_channel/./engine_module/config", "-I/data/mec_channel/./engine_module/mem", "-I/data/mec_channel/./engine_module/engine", "-I/data/mec_channel/./engine_module/thread_resource", "-I/data/mec_channel/./mec_client", "-I/data/mec_channel/./mqtt_client", "-I/data/mec_channel/./dvin_client", "-I/data/mec_channel/./v2x_client", "-I/data/mec_channel/./platform", "-I/data/mec_channel/./platform/genvict", "-I/data/mec_channel/./platform/genvict_sdk", "-I/data/mec_channel/./platform/debug", "-I/data/mec_channel/./platform/empty", "-I/data/mec_channel/./platform/baidu", "-I/data/mec_channel/./platform/boyuan", "-I/data/mec_channel/./platform/gosuncn", "-I/data/mec_channel/./platform/huawei", "-I/data/mec_channel/./platform/multi", "-I/data/mec_channel/./platform/tianan_obu", "-I/data/mec_channel/./platform/tianan", "-I/data/mec_channel/./platform/tianan/mec_protocol", "-I/data/mec_channel/./test", "-I/data/mec_channel/./app", "-O3", "-DNDEBUG", "--std=c++11", "-Wall", "-O3", "-Wno-psabi", "-c", "-o", "CMakeFiles/mec_channel.dir/engine_module/utility/coordinate_convert.cpp.o", "/data/mec_channel/engine_module/utility/coordinate_convert.cpp"], "directory": "/data/mec_channel", "file": "/data/mec_channel/engine_module/utility/coordinate_convert.cpp", "output": "/data/mec_channel/CMakeFiles/mec_channel.dir/engine_module/utility/coordinate_convert.cpp.o"}, {"arguments": ["/usr/local/bin/arm-linux-gnueabi-g++", "-DPLATFORM_ARM32", "-I/data/mec_channel/.", "-I/data/mec_channel/./mec_asn", "-I/data/mec_channel/./include", "-I/data/mec_channel/./engine_module/include", "-I/data/mec_channel/./engine_module/include/mosquitto", "-I/data/mec_channel/./engine_module/include/zmq", "-I/data/mec_channel/./engine_module/utility", "-I/data/mec_channel/./engine_module/signal", "-I/data/mec_channel/./engine_module/log", "-I/data/mec_channel/./engine_module/config", "-I/data/mec_channel/./engine_module/mem", "-I/data/mec_channel/./engine_module/engine", "-I/data/mec_channel/./engine_module/thread_resource", "-I/data/mec_channel/./mec_client", "-I/data/mec_channel/./mqtt_client", "-I/data/mec_channel/./dvin_client", "-I/data/mec_channel/./v2x_client", "-I/data/mec_channel/./platform", "-I/data/mec_channel/./platform/genvict", "-I/data/mec_channel/./platform/genvict_sdk", "-I/data/mec_channel/./platform/debug", "-I/data/mec_channel/./platform/empty", "-I/data/mec_channel/./platform/baidu", "-I/data/mec_channel/./platform/boyuan", "-I/data/mec_channel/./platform/gosuncn", "-I/data/mec_channel/./platform/huawei", "-I/data/mec_channel/./platform/multi", "-I/data/mec_channel/./platform/tianan_obu", "-I/data/mec_channel/./platform/tianan", "-I/data/mec_channel/./platform/tianan/mec_protocol", "-I/data/mec_channel/./test", "-I/data/mec_channel/./app", "-O3", "-DNDEBUG", "--std=c++11", "-Wall", "-O3", "-Wno-psabi", "-c", "-o", "CMakeFiles/mec_channel.dir/engine_module/utility/dev_performance.cpp.o", "/data/mec_channel/engine_module/utility/dev_performance.cpp"], "directory": "/data/mec_channel", "file": "/data/mec_channel/engine_module/utility/dev_performance.cpp", "output": "/data/mec_channel/CMakeFiles/mec_channel.dir/engine_module/utility/dev_performance.cpp.o"}, {"arguments": ["/usr/local/bin/arm-linux-gnueabi-g++", "-DPLATFORM_ARM32", "-I/data/mec_channel/.", "-I/data/mec_channel/./mec_asn", "-I/data/mec_channel/./include", "-I/data/mec_channel/./engine_module/include", "-I/data/mec_channel/./engine_module/include/mosquitto", "-I/data/mec_channel/./engine_module/include/zmq", "-I/data/mec_channel/./engine_module/utility", "-I/data/mec_channel/./engine_module/signal", "-I/data/mec_channel/./engine_module/log", "-I/data/mec_channel/./engine_module/config", "-I/data/mec_channel/./engine_module/mem", "-I/data/mec_channel/./engine_module/engine", "-I/data/mec_channel/./engine_module/thread_resource", "-I/data/mec_channel/./mec_client", "-I/data/mec_channel/./mqtt_client", "-I/data/mec_channel/./dvin_client", "-I/data/mec_channel/./v2x_client", "-I/data/mec_channel/./platform", "-I/data/mec_channel/./platform/genvict", "-I/data/mec_channel/./platform/genvict_sdk", "-I/data/mec_channel/./platform/debug", "-I/data/mec_channel/./platform/empty", "-I/data/mec_channel/./platform/baidu", "-I/data/mec_channel/./platform/boyuan", "-I/data/mec_channel/./platform/gosuncn", "-I/data/mec_channel/./platform/huawei", "-I/data/mec_channel/./platform/multi", "-I/data/mec_channel/./platform/tianan_obu", "-I/data/mec_channel/./platform/tianan", "-I/data/mec_channel/./platform/tianan/mec_protocol", "-I/data/mec_channel/./test", "-I/data/mec_channel/./app", "-O3", "-DNDEBUG", "--std=c++11", "-Wall", "-O3", "-Wno-psabi", "-c", "-o", "CMakeFiles/mec_channel.dir/engine_module/utility/dir_op.cpp.o", "/data/mec_channel/engine_module/utility/dir_op.cpp"], "directory": "/data/mec_channel", "file": "/data/mec_channel/engine_module/utility/dir_op.cpp", "output": "/data/mec_channel/CMakeFiles/mec_channel.dir/engine_module/utility/dir_op.cpp.o"}, {"arguments": ["/usr/local/bin/arm-linux-gnueabi-g++", "-DPLATFORM_ARM32", "-I/data/mec_channel/.", "-I/data/mec_channel/./mec_asn", "-I/data/mec_channel/./include", "-I/data/mec_channel/./engine_module/include", "-I/data/mec_channel/./engine_module/include/mosquitto", "-I/data/mec_channel/./engine_module/include/zmq", "-I/data/mec_channel/./engine_module/utility", "-I/data/mec_channel/./engine_module/signal", "-I/data/mec_channel/./engine_module/log", "-I/data/mec_channel/./engine_module/config", "-I/data/mec_channel/./engine_module/mem", "-I/data/mec_channel/./engine_module/engine", "-I/data/mec_channel/./engine_module/thread_resource", "-I/data/mec_channel/./mec_client", "-I/data/mec_channel/./mqtt_client", "-I/data/mec_channel/./dvin_client", "-I/data/mec_channel/./v2x_client", "-I/data/mec_channel/./platform", "-I/data/mec_channel/./platform/genvict", "-I/data/mec_channel/./platform/genvict_sdk", "-I/data/mec_channel/./platform/debug", "-I/data/mec_channel/./platform/empty", "-I/data/mec_channel/./platform/baidu", "-I/data/mec_channel/./platform/boyuan", "-I/data/mec_channel/./platform/gosuncn", "-I/data/mec_channel/./platform/huawei", "-I/data/mec_channel/./platform/multi", "-I/data/mec_channel/./platform/tianan_obu", "-I/data/mec_channel/./platform/tianan", "-I/data/mec_channel/./platform/tianan/mec_protocol", "-I/data/mec_channel/./test", "-I/data/mec_channel/./app", "-O3", "-DNDEBUG", "--std=c++11", "-Wall", "-O3", "-Wno-psabi", "-c", "-o", "CMakeFiles/mec_channel.dir/engine_module/utility/hex_str.cpp.o", "/data/mec_channel/engine_module/utility/hex_str.cpp"], "directory": "/data/mec_channel", "file": "/data/mec_channel/engine_module/utility/hex_str.cpp", "output": "/data/mec_channel/CMakeFiles/mec_channel.dir/engine_module/utility/hex_str.cpp.o"}, {"arguments": ["/usr/local/bin/arm-linux-gnueabi-g++", "-DPLATFORM_ARM32", "-I/data/mec_channel/.", "-I/data/mec_channel/./mec_asn", "-I/data/mec_channel/./include", "-I/data/mec_channel/./engine_module/include", "-I/data/mec_channel/./engine_module/include/mosquitto", "-I/data/mec_channel/./engine_module/include/zmq", "-I/data/mec_channel/./engine_module/utility", "-I/data/mec_channel/./engine_module/signal", "-I/data/mec_channel/./engine_module/log", "-I/data/mec_channel/./engine_module/config", "-I/data/mec_channel/./engine_module/mem", "-I/data/mec_channel/./engine_module/engine", "-I/data/mec_channel/./engine_module/thread_resource", "-I/data/mec_channel/./mec_client", "-I/data/mec_channel/./mqtt_client", "-I/data/mec_channel/./dvin_client", "-I/data/mec_channel/./v2x_client", "-I/data/mec_channel/./platform", "-I/data/mec_channel/./platform/genvict", "-I/data/mec_channel/./platform/genvict_sdk", "-I/data/mec_channel/./platform/debug", "-I/data/mec_channel/./platform/empty", "-I/data/mec_channel/./platform/baidu", "-I/data/mec_channel/./platform/boyuan", "-I/data/mec_channel/./platform/gosuncn", "-I/data/mec_channel/./platform/huawei", "-I/data/mec_channel/./platform/multi", "-I/data/mec_channel/./platform/tianan_obu", "-I/data/mec_channel/./platform/tianan", "-I/data/mec_channel/./platform/tianan/mec_protocol", "-I/data/mec_channel/./test", "-I/data/mec_channel/./app", "-O3", "-DNDEBUG", "--std=c++11", "-Wall", "-O3", "-Wno-psabi", "-c", "-o", "CMakeFiles/mec_channel.dir/engine_module/utility/hmac_code.cpp.o", "/data/mec_channel/engine_module/utility/hmac_code.cpp"], "directory": "/data/mec_channel", "file": "/data/mec_channel/engine_module/utility/hmac_code.cpp", "output": "/data/mec_channel/CMakeFiles/mec_channel.dir/engine_module/utility/hmac_code.cpp.o"}, {"arguments": ["/usr/local/bin/arm-linux-gnueabi-g++", "-DPLATFORM_ARM32", "-I/data/mec_channel/.", "-I/data/mec_channel/./mec_asn", "-I/data/mec_channel/./include", "-I/data/mec_channel/./engine_module/include", "-I/data/mec_channel/./engine_module/include/mosquitto", "-I/data/mec_channel/./engine_module/include/zmq", "-I/data/mec_channel/./engine_module/utility", "-I/data/mec_channel/./engine_module/signal", "-I/data/mec_channel/./engine_module/log", "-I/data/mec_channel/./engine_module/config", "-I/data/mec_channel/./engine_module/mem", "-I/data/mec_channel/./engine_module/engine", "-I/data/mec_channel/./engine_module/thread_resource", "-I/data/mec_channel/./mec_client", "-I/data/mec_channel/./mqtt_client", "-I/data/mec_channel/./dvin_client", "-I/data/mec_channel/./v2x_client", "-I/data/mec_channel/./platform", "-I/data/mec_channel/./platform/genvict", "-I/data/mec_channel/./platform/genvict_sdk", "-I/data/mec_channel/./platform/debug", "-I/data/mec_channel/./platform/empty", "-I/data/mec_channel/./platform/baidu", "-I/data/mec_channel/./platform/boyuan", "-I/data/mec_channel/./platform/gosuncn", "-I/data/mec_channel/./platform/huawei", "-I/data/mec_channel/./platform/multi", "-I/data/mec_channel/./platform/tianan_obu", "-I/data/mec_channel/./platform/tianan", "-I/data/mec_channel/./platform/tianan/mec_protocol", "-I/data/mec_channel/./test", "-I/data/mec_channel/./app", "-O3", "-DNDEBUG", "--std=c++11", "-Wall", "-O3", "-Wno-psabi", "-c", "-o", "CMakeFiles/mec_channel.dir/engine_module/utility/ota_upgade.cpp.o", "/data/mec_channel/engine_module/utility/ota_upgade.cpp"], "directory": "/data/mec_channel", "file": "/data/mec_channel/engine_module/utility/ota_upgade.cpp", "output": "/data/mec_channel/CMakeFiles/mec_channel.dir/engine_module/utility/ota_upgade.cpp.o"}, {"arguments": ["/usr/local/bin/arm-linux-gnueabi-g++", "-DPLATFORM_ARM32", "-I/data/mec_channel/.", "-I/data/mec_channel/./mec_asn", "-I/data/mec_channel/./include", "-I/data/mec_channel/./engine_module/include", "-I/data/mec_channel/./engine_module/include/mosquitto", "-I/data/mec_channel/./engine_module/include/zmq", "-I/data/mec_channel/./engine_module/utility", "-I/data/mec_channel/./engine_module/signal", "-I/data/mec_channel/./engine_module/log", "-I/data/mec_channel/./engine_module/config", "-I/data/mec_channel/./engine_module/mem", "-I/data/mec_channel/./engine_module/engine", "-I/data/mec_channel/./engine_module/thread_resource", "-I/data/mec_channel/./mec_client", "-I/data/mec_channel/./mqtt_client", "-I/data/mec_channel/./dvin_client", "-I/data/mec_channel/./v2x_client", "-I/data/mec_channel/./platform", "-I/data/mec_channel/./platform/genvict", "-I/data/mec_channel/./platform/genvict_sdk", "-I/data/mec_channel/./platform/debug", "-I/data/mec_channel/./platform/empty", "-I/data/mec_channel/./platform/baidu", "-I/data/mec_channel/./platform/boyuan", "-I/data/mec_channel/./platform/gosuncn", "-I/data/mec_channel/./platform/huawei", "-I/data/mec_channel/./platform/multi", "-I/data/mec_channel/./platform/tianan_obu", "-I/data/mec_channel/./platform/tianan", "-I/data/mec_channel/./platform/tianan/mec_protocol", "-I/data/mec_channel/./test", "-I/data/mec_channel/./app", "-O3", "-DNDEBUG", "--std=c++11", "-Wall", "-O3", "-Wno-psabi", "-c", "-o", "CMakeFiles/mec_channel.dir/engine_module/utility/rsa_crypt.cpp.o", "/data/mec_channel/engine_module/utility/rsa_crypt.cpp"], "directory": "/data/mec_channel", "file": "/data/mec_channel/engine_module/utility/rsa_crypt.cpp", "output": "/data/mec_channel/CMakeFiles/mec_channel.dir/engine_module/utility/rsa_crypt.cpp.o"}, {"arguments": ["/usr/local/bin/arm-linux-gnueabi-g++", "-DPLATFORM_ARM32", "-I/data/mec_channel/.", "-I/data/mec_channel/./mec_asn", "-I/data/mec_channel/./include", "-I/data/mec_channel/./engine_module/include", "-I/data/mec_channel/./engine_module/include/mosquitto", "-I/data/mec_channel/./engine_module/include/zmq", "-I/data/mec_channel/./engine_module/utility", "-I/data/mec_channel/./engine_module/signal", "-I/data/mec_channel/./engine_module/log", "-I/data/mec_channel/./engine_module/config", "-I/data/mec_channel/./engine_module/mem", "-I/data/mec_channel/./engine_module/engine", "-I/data/mec_channel/./engine_module/thread_resource", "-I/data/mec_channel/./mec_client", "-I/data/mec_channel/./mqtt_client", "-I/data/mec_channel/./dvin_client", "-I/data/mec_channel/./v2x_client", "-I/data/mec_channel/./platform", "-I/data/mec_channel/./platform/genvict", "-I/data/mec_channel/./platform/genvict_sdk", "-I/data/mec_channel/./platform/debug", "-I/data/mec_channel/./platform/empty", "-I/data/mec_channel/./platform/baidu", "-I/data/mec_channel/./platform/boyuan", "-I/data/mec_channel/./platform/gosuncn", "-I/data/mec_channel/./platform/huawei", "-I/data/mec_channel/./platform/multi", "-I/data/mec_channel/./platform/tianan_obu", "-I/data/mec_channel/./platform/tianan", "-I/data/mec_channel/./platform/tianan/mec_protocol", "-I/data/mec_channel/./test", "-I/data/mec_channel/./app", "-O3", "-DNDEBUG", "--std=c++11", "-Wall", "-O3", "-Wno-psabi", "-c", "-o", "CMakeFiles/mec_channel.dir/engine_module/utility/str_op.cpp.o", "/data/mec_channel/engine_module/utility/str_op.cpp"], "directory": "/data/mec_channel", "file": "/data/mec_channel/engine_module/utility/str_op.cpp", "output": "/data/mec_channel/CMakeFiles/mec_channel.dir/engine_module/utility/str_op.cpp.o"}, {"arguments": ["/usr/local/bin/arm-linux-gnueabi-g++", "-DPLATFORM_ARM32", "-I/data/mec_channel/.", "-I/data/mec_channel/./mec_asn", "-I/data/mec_channel/./include", "-I/data/mec_channel/./engine_module/include", "-I/data/mec_channel/./engine_module/include/mosquitto", "-I/data/mec_channel/./engine_module/include/zmq", "-I/data/mec_channel/./engine_module/utility", "-I/data/mec_channel/./engine_module/signal", "-I/data/mec_channel/./engine_module/log", "-I/data/mec_channel/./engine_module/config", "-I/data/mec_channel/./engine_module/mem", "-I/data/mec_channel/./engine_module/engine", "-I/data/mec_channel/./engine_module/thread_resource", "-I/data/mec_channel/./mec_client", "-I/data/mec_channel/./mqtt_client", "-I/data/mec_channel/./dvin_client", "-I/data/mec_channel/./v2x_client", "-I/data/mec_channel/./platform", "-I/data/mec_channel/./platform/genvict", "-I/data/mec_channel/./platform/genvict_sdk", "-I/data/mec_channel/./platform/debug", "-I/data/mec_channel/./platform/empty", "-I/data/mec_channel/./platform/baidu", "-I/data/mec_channel/./platform/boyuan", "-I/data/mec_channel/./platform/gosuncn", "-I/data/mec_channel/./platform/huawei", "-I/data/mec_channel/./platform/multi", "-I/data/mec_channel/./platform/tianan_obu", "-I/data/mec_channel/./platform/tianan", "-I/data/mec_channel/./platform/tianan/mec_protocol", "-I/data/mec_channel/./test", "-I/data/mec_channel/./app", "-O3", "-DNDEBUG", "--std=c++11", "-Wall", "-O3", "-Wno-psabi", "-c", "-o", "CMakeFiles/mec_channel.dir/engine_module/utility/time_task.cpp.o", "/data/mec_channel/engine_module/utility/time_task.cpp"], "directory": "/data/mec_channel", "file": "/data/mec_channel/engine_module/utility/time_task.cpp", "output": "/data/mec_channel/CMakeFiles/mec_channel.dir/engine_module/utility/time_task.cpp.o"}, {"arguments": ["/usr/local/bin/arm-linux-gnueabi-g++", "-DPLATFORM_ARM32", "-I/data/mec_channel/.", "-I/data/mec_channel/./mec_asn", "-I/data/mec_channel/./include", "-I/data/mec_channel/./engine_module/include", "-I/data/mec_channel/./engine_module/include/mosquitto", "-I/data/mec_channel/./engine_module/include/zmq", "-I/data/mec_channel/./engine_module/utility", "-I/data/mec_channel/./engine_module/signal", "-I/data/mec_channel/./engine_module/log", "-I/data/mec_channel/./engine_module/config", "-I/data/mec_channel/./engine_module/mem", "-I/data/mec_channel/./engine_module/engine", "-I/data/mec_channel/./engine_module/thread_resource", "-I/data/mec_channel/./mec_client", "-I/data/mec_channel/./mqtt_client", "-I/data/mec_channel/./dvin_client", "-I/data/mec_channel/./v2x_client", "-I/data/mec_channel/./platform", "-I/data/mec_channel/./platform/genvict", "-I/data/mec_channel/./platform/genvict_sdk", "-I/data/mec_channel/./platform/debug", "-I/data/mec_channel/./platform/empty", "-I/data/mec_channel/./platform/baidu", "-I/data/mec_channel/./platform/boyuan", "-I/data/mec_channel/./platform/gosuncn", "-I/data/mec_channel/./platform/huawei", "-I/data/mec_channel/./platform/multi", "-I/data/mec_channel/./platform/tianan_obu", "-I/data/mec_channel/./platform/tianan", "-I/data/mec_channel/./platform/tianan/mec_protocol", "-I/data/mec_channel/./test", "-I/data/mec_channel/./app", "-O3", "-DNDEBUG", "--std=c++11", "-Wall", "-O3", "-Wno-psabi", "-c", "-o", "CMakeFiles/mec_channel.dir/engine_module/log/log.cpp.o", "/data/mec_channel/engine_module/log/log.cpp"], "directory": "/data/mec_channel", "file": "/data/mec_channel/engine_module/log/log.cpp", "output": "/data/mec_channel/CMakeFiles/mec_channel.dir/engine_module/log/log.cpp.o"}, {"arguments": ["/usr/local/bin/arm-linux-gnueabi-g++", "-DPLATFORM_ARM32", "-I/data/mec_channel/.", "-I/data/mec_channel/./mec_asn", "-I/data/mec_channel/./include", "-I/data/mec_channel/./engine_module/include", "-I/data/mec_channel/./engine_module/include/mosquitto", "-I/data/mec_channel/./engine_module/include/zmq", "-I/data/mec_channel/./engine_module/utility", "-I/data/mec_channel/./engine_module/signal", "-I/data/mec_channel/./engine_module/log", "-I/data/mec_channel/./engine_module/config", "-I/data/mec_channel/./engine_module/mem", "-I/data/mec_channel/./engine_module/engine", "-I/data/mec_channel/./engine_module/thread_resource", "-I/data/mec_channel/./mec_client", "-I/data/mec_channel/./mqtt_client", "-I/data/mec_channel/./dvin_client", "-I/data/mec_channel/./v2x_client", "-I/data/mec_channel/./platform", "-I/data/mec_channel/./platform/genvict", "-I/data/mec_channel/./platform/genvict_sdk", "-I/data/mec_channel/./platform/debug", "-I/data/mec_channel/./platform/empty", "-I/data/mec_channel/./platform/baidu", "-I/data/mec_channel/./platform/boyuan", "-I/data/mec_channel/./platform/gosuncn", "-I/data/mec_channel/./platform/huawei", "-I/data/mec_channel/./platform/multi", "-I/data/mec_channel/./platform/tianan_obu", "-I/data/mec_channel/./platform/tianan", "-I/data/mec_channel/./platform/tianan/mec_protocol", "-I/data/mec_channel/./test", "-I/data/mec_channel/./app", "-O3", "-DNDEBUG", "--std=c++11", "-Wall", "-O3", "-Wno-psabi", "-c", "-o", "CMakeFiles/mec_channel.dir/engine_module/signal/signal_handler.cpp.o", "/data/mec_channel/engine_module/signal/signal_handler.cpp"], "directory": "/data/mec_channel", "file": "/data/mec_channel/engine_module/signal/signal_handler.cpp", "output": "/data/mec_channel/CMakeFiles/mec_channel.dir/engine_module/signal/signal_handler.cpp.o"}, {"arguments": ["/usr/local/bin/arm-linux-gnueabi-g++", "-DPLATFORM_ARM32", "-I/data/mec_channel/.", "-I/data/mec_channel/./mec_asn", "-I/data/mec_channel/./include", "-I/data/mec_channel/./engine_module/include", "-I/data/mec_channel/./engine_module/include/mosquitto", "-I/data/mec_channel/./engine_module/include/zmq", "-I/data/mec_channel/./engine_module/utility", "-I/data/mec_channel/./engine_module/signal", "-I/data/mec_channel/./engine_module/log", "-I/data/mec_channel/./engine_module/config", "-I/data/mec_channel/./engine_module/mem", "-I/data/mec_channel/./engine_module/engine", "-I/data/mec_channel/./engine_module/thread_resource", "-I/data/mec_channel/./mec_client", "-I/data/mec_channel/./mqtt_client", "-I/data/mec_channel/./dvin_client", "-I/data/mec_channel/./v2x_client", "-I/data/mec_channel/./platform", "-I/data/mec_channel/./platform/genvict", "-I/data/mec_channel/./platform/genvict_sdk", "-I/data/mec_channel/./platform/debug", "-I/data/mec_channel/./platform/empty", "-I/data/mec_channel/./platform/baidu", "-I/data/mec_channel/./platform/boyuan", "-I/data/mec_channel/./platform/gosuncn", "-I/data/mec_channel/./platform/huawei", "-I/data/mec_channel/./platform/multi", "-I/data/mec_channel/./platform/tianan_obu", "-I/data/mec_channel/./platform/tianan", "-I/data/mec_channel/./platform/tianan/mec_protocol", "-I/data/mec_channel/./test", "-I/data/mec_channel/./app", "-O3", "-DNDEBUG", "--std=c++11", "-Wall", "-O3", "-Wno-psabi", "-c", "-o", "CMakeFiles/mec_channel.dir/engine_module/config/config_base.cpp.o", "/data/mec_channel/engine_module/config/config_base.cpp"], "directory": "/data/mec_channel", "file": "/data/mec_channel/engine_module/config/config_base.cpp", "output": "/data/mec_channel/CMakeFiles/mec_channel.dir/engine_module/config/config_base.cpp.o"}, {"arguments": ["/usr/local/bin/arm-linux-gnueabi-g++", "-DPLATFORM_ARM32", "-I/data/mec_channel/.", "-I/data/mec_channel/./mec_asn", "-I/data/mec_channel/./include", "-I/data/mec_channel/./engine_module/include", "-I/data/mec_channel/./engine_module/include/mosquitto", "-I/data/mec_channel/./engine_module/include/zmq", "-I/data/mec_channel/./engine_module/utility", "-I/data/mec_channel/./engine_module/signal", "-I/data/mec_channel/./engine_module/log", "-I/data/mec_channel/./engine_module/config", "-I/data/mec_channel/./engine_module/mem", "-I/data/mec_channel/./engine_module/engine", "-I/data/mec_channel/./engine_module/thread_resource", "-I/data/mec_channel/./mec_client", "-I/data/mec_channel/./mqtt_client", "-I/data/mec_channel/./dvin_client", "-I/data/mec_channel/./v2x_client", "-I/data/mec_channel/./platform", "-I/data/mec_channel/./platform/genvict", "-I/data/mec_channel/./platform/genvict_sdk", "-I/data/mec_channel/./platform/debug", "-I/data/mec_channel/./platform/empty", "-I/data/mec_channel/./platform/baidu", "-I/data/mec_channel/./platform/boyuan", "-I/data/mec_channel/./platform/gosuncn", "-I/data/mec_channel/./platform/huawei", "-I/data/mec_channel/./platform/multi", "-I/data/mec_channel/./platform/tianan_obu", "-I/data/mec_channel/./platform/tianan", "-I/data/mec_channel/./platform/tianan/mec_protocol", "-I/data/mec_channel/./test", "-I/data/mec_channel/./app", "-O3", "-DNDEBUG", "--std=c++11", "-Wall", "-O3", "-Wno-psabi", "-c", "-o", "CMakeFiles/mec_channel.dir/engine_module/mem/frame_buffer.cpp.o", "/data/mec_channel/engine_module/mem/frame_buffer.cpp"], "directory": "/data/mec_channel", "file": "/data/mec_channel/engine_module/mem/frame_buffer.cpp", "output": "/data/mec_channel/CMakeFiles/mec_channel.dir/engine_module/mem/frame_buffer.cpp.o"}, {"arguments": ["/usr/local/bin/arm-linux-gnueabi-g++", "-DPLATFORM_ARM32", "-I/data/mec_channel/.", "-I/data/mec_channel/./mec_asn", "-I/data/mec_channel/./include", "-I/data/mec_channel/./engine_module/include", "-I/data/mec_channel/./engine_module/include/mosquitto", "-I/data/mec_channel/./engine_module/include/zmq", "-I/data/mec_channel/./engine_module/utility", "-I/data/mec_channel/./engine_module/signal", "-I/data/mec_channel/./engine_module/log", "-I/data/mec_channel/./engine_module/config", "-I/data/mec_channel/./engine_module/mem", "-I/data/mec_channel/./engine_module/engine", "-I/data/mec_channel/./engine_module/thread_resource", "-I/data/mec_channel/./mec_client", "-I/data/mec_channel/./mqtt_client", "-I/data/mec_channel/./dvin_client", "-I/data/mec_channel/./v2x_client", "-I/data/mec_channel/./platform", "-I/data/mec_channel/./platform/genvict", "-I/data/mec_channel/./platform/genvict_sdk", "-I/data/mec_channel/./platform/debug", "-I/data/mec_channel/./platform/empty", "-I/data/mec_channel/./platform/baidu", "-I/data/mec_channel/./platform/boyuan", "-I/data/mec_channel/./platform/gosuncn", "-I/data/mec_channel/./platform/huawei", "-I/data/mec_channel/./platform/multi", "-I/data/mec_channel/./platform/tianan_obu", "-I/data/mec_channel/./platform/tianan", "-I/data/mec_channel/./platform/tianan/mec_protocol", "-I/data/mec_channel/./test", "-I/data/mec_channel/./app", "-O3", "-DNDEBUG", "--std=c++11", "-Wall", "-O3", "-Wno-psabi", "-c", "-o", "CMakeFiles/mec_channel.dir/engine_module/engine/epoll_engine.cpp.o", "/data/mec_channel/engine_module/engine/epoll_engine.cpp"], "directory": "/data/mec_channel", "file": "/data/mec_channel/engine_module/engine/epoll_engine.cpp", "output": "/data/mec_channel/CMakeFiles/mec_channel.dir/engine_module/engine/epoll_engine.cpp.o"}, {"arguments": ["/usr/local/bin/arm-linux-gnueabi-g++", "-DPLATFORM_ARM32", "-I/data/mec_channel/.", "-I/data/mec_channel/./mec_asn", "-I/data/mec_channel/./include", "-I/data/mec_channel/./engine_module/include", "-I/data/mec_channel/./engine_module/include/mosquitto", "-I/data/mec_channel/./engine_module/include/zmq", "-I/data/mec_channel/./engine_module/utility", "-I/data/mec_channel/./engine_module/signal", "-I/data/mec_channel/./engine_module/log", "-I/data/mec_channel/./engine_module/config", "-I/data/mec_channel/./engine_module/mem", "-I/data/mec_channel/./engine_module/engine", "-I/data/mec_channel/./engine_module/thread_resource", "-I/data/mec_channel/./mec_client", "-I/data/mec_channel/./mqtt_client", "-I/data/mec_channel/./dvin_client", "-I/data/mec_channel/./v2x_client", "-I/data/mec_channel/./platform", "-I/data/mec_channel/./platform/genvict", "-I/data/mec_channel/./platform/genvict_sdk", "-I/data/mec_channel/./platform/debug", "-I/data/mec_channel/./platform/empty", "-I/data/mec_channel/./platform/baidu", "-I/data/mec_channel/./platform/boyuan", "-I/data/mec_channel/./platform/gosuncn", "-I/data/mec_channel/./platform/huawei", "-I/data/mec_channel/./platform/multi", "-I/data/mec_channel/./platform/tianan_obu", "-I/data/mec_channel/./platform/tianan", "-I/data/mec_channel/./platform/tianan/mec_protocol", "-I/data/mec_channel/./test", "-I/data/mec_channel/./app", "-O3", "-DNDEBUG", "--std=c++11", "-Wall", "-O3", "-Wno-psabi", "-c", "-o", "CMakeFiles/mec_channel.dir/engine_module/engine/http_client_mgr.cpp.o", "/data/mec_channel/engine_module/engine/http_client_mgr.cpp"], "directory": "/data/mec_channel", "file": "/data/mec_channel/engine_module/engine/http_client_mgr.cpp", "output": "/data/mec_channel/CMakeFiles/mec_channel.dir/engine_module/engine/http_client_mgr.cpp.o"}, {"arguments": ["/usr/local/bin/arm-linux-gnueabi-g++", "-DPLATFORM_ARM32", "-I/data/mec_channel/.", "-I/data/mec_channel/./mec_asn", "-I/data/mec_channel/./include", "-I/data/mec_channel/./engine_module/include", "-I/data/mec_channel/./engine_module/include/mosquitto", "-I/data/mec_channel/./engine_module/include/zmq", "-I/data/mec_channel/./engine_module/utility", "-I/data/mec_channel/./engine_module/signal", "-I/data/mec_channel/./engine_module/log", "-I/data/mec_channel/./engine_module/config", "-I/data/mec_channel/./engine_module/mem", "-I/data/mec_channel/./engine_module/engine", "-I/data/mec_channel/./engine_module/thread_resource", "-I/data/mec_channel/./mec_client", "-I/data/mec_channel/./mqtt_client", "-I/data/mec_channel/./dvin_client", "-I/data/mec_channel/./v2x_client", "-I/data/mec_channel/./platform", "-I/data/mec_channel/./platform/genvict", "-I/data/mec_channel/./platform/genvict_sdk", "-I/data/mec_channel/./platform/debug", "-I/data/mec_channel/./platform/empty", "-I/data/mec_channel/./platform/baidu", "-I/data/mec_channel/./platform/boyuan", "-I/data/mec_channel/./platform/gosuncn", "-I/data/mec_channel/./platform/huawei", "-I/data/mec_channel/./platform/multi", "-I/data/mec_channel/./platform/tianan_obu", "-I/data/mec_channel/./platform/tianan", "-I/data/mec_channel/./platform/tianan/mec_protocol", "-I/data/mec_channel/./test", "-I/data/mec_channel/./app", "-O3", "-DNDEBUG", "--std=c++11", "-Wall", "-O3", "-Wno-psabi", "-c", "-o", "CMakeFiles/mec_channel.dir/engine_module/engine/event_engine.cpp.o", "/data/mec_channel/engine_module/engine/event_engine.cpp"], "directory": "/data/mec_channel", "file": "/data/mec_channel/engine_module/engine/event_engine.cpp", "output": "/data/mec_channel/CMakeFiles/mec_channel.dir/engine_module/engine/event_engine.cpp.o"}, {"arguments": ["/usr/local/bin/arm-linux-gnueabi-g++", "-DPLATFORM_ARM32", "-I/data/mec_channel/.", "-I/data/mec_channel/./mec_asn", "-I/data/mec_channel/./include", "-I/data/mec_channel/./engine_module/include", "-I/data/mec_channel/./engine_module/include/mosquitto", "-I/data/mec_channel/./engine_module/include/zmq", "-I/data/mec_channel/./engine_module/utility", "-I/data/mec_channel/./engine_module/signal", "-I/data/mec_channel/./engine_module/log", "-I/data/mec_channel/./engine_module/config", "-I/data/mec_channel/./engine_module/mem", "-I/data/mec_channel/./engine_module/engine", "-I/data/mec_channel/./engine_module/thread_resource", "-I/data/mec_channel/./mec_client", "-I/data/mec_channel/./mqtt_client", "-I/data/mec_channel/./dvin_client", "-I/data/mec_channel/./v2x_client", "-I/data/mec_channel/./platform", "-I/data/mec_channel/./platform/genvict", "-I/data/mec_channel/./platform/genvict_sdk", "-I/data/mec_channel/./platform/debug", "-I/data/mec_channel/./platform/empty", "-I/data/mec_channel/./platform/baidu", "-I/data/mec_channel/./platform/boyuan", "-I/data/mec_channel/./platform/gosuncn", "-I/data/mec_channel/./platform/huawei", "-I/data/mec_channel/./platform/multi", "-I/data/mec_channel/./platform/tianan_obu", "-I/data/mec_channel/./platform/tianan", "-I/data/mec_channel/./platform/tianan/mec_protocol", "-I/data/mec_channel/./test", "-I/data/mec_channel/./app", "-O3", "-DNDEBUG", "--std=c++11", "-Wall", "-O3", "-Wno-psabi", "-c", "-o", "CMakeFiles/mec_channel.dir/engine_module/engine/http_service_mgr.cpp.o", "/data/mec_channel/engine_module/engine/http_service_mgr.cpp"], "directory": "/data/mec_channel", "file": "/data/mec_channel/engine_module/engine/http_service_mgr.cpp", "output": "/data/mec_channel/CMakeFiles/mec_channel.dir/engine_module/engine/http_service_mgr.cpp.o"}, {"arguments": ["/usr/local/bin/arm-linux-gnueabi-g++", "-DPLATFORM_ARM32", "-I/data/mec_channel/.", "-I/data/mec_channel/./mec_asn", "-I/data/mec_channel/./include", "-I/data/mec_channel/./engine_module/include", "-I/data/mec_channel/./engine_module/include/mosquitto", "-I/data/mec_channel/./engine_module/include/zmq", "-I/data/mec_channel/./engine_module/utility", "-I/data/mec_channel/./engine_module/signal", "-I/data/mec_channel/./engine_module/log", "-I/data/mec_channel/./engine_module/config", "-I/data/mec_channel/./engine_module/mem", "-I/data/mec_channel/./engine_module/engine", "-I/data/mec_channel/./engine_module/thread_resource", "-I/data/mec_channel/./mec_client", "-I/data/mec_channel/./mqtt_client", "-I/data/mec_channel/./dvin_client", "-I/data/mec_channel/./v2x_client", "-I/data/mec_channel/./platform", "-I/data/mec_channel/./platform/genvict", "-I/data/mec_channel/./platform/genvict_sdk", "-I/data/mec_channel/./platform/debug", "-I/data/mec_channel/./platform/empty", "-I/data/mec_channel/./platform/baidu", "-I/data/mec_channel/./platform/boyuan", "-I/data/mec_channel/./platform/gosuncn", "-I/data/mec_channel/./platform/huawei", "-I/data/mec_channel/./platform/multi", "-I/data/mec_channel/./platform/tianan_obu", "-I/data/mec_channel/./platform/tianan", "-I/data/mec_channel/./platform/tianan/mec_protocol", "-I/data/mec_channel/./test", "-I/data/mec_channel/./app", "-O3", "-DNDEBUG", "--std=c++11", "-Wall", "-O3", "-Wno-psabi", "-c", "-o", "CMakeFiles/mec_channel.dir/engine_module/engine/mqtt_client_mgr.cpp.o", "/data/mec_channel/engine_module/engine/mqtt_client_mgr.cpp"], "directory": "/data/mec_channel", "file": "/data/mec_channel/engine_module/engine/mqtt_client_mgr.cpp", "output": "/data/mec_channel/CMakeFiles/mec_channel.dir/engine_module/engine/mqtt_client_mgr.cpp.o"}, {"arguments": ["/usr/local/bin/arm-linux-gnueabi-g++", "-DPLATFORM_ARM32", "-I/data/mec_channel/.", "-I/data/mec_channel/./mec_asn", "-I/data/mec_channel/./include", "-I/data/mec_channel/./engine_module/include", "-I/data/mec_channel/./engine_module/include/mosquitto", "-I/data/mec_channel/./engine_module/include/zmq", "-I/data/mec_channel/./engine_module/utility", "-I/data/mec_channel/./engine_module/signal", "-I/data/mec_channel/./engine_module/log", "-I/data/mec_channel/./engine_module/config", "-I/data/mec_channel/./engine_module/mem", "-I/data/mec_channel/./engine_module/engine", "-I/data/mec_channel/./engine_module/thread_resource", "-I/data/mec_channel/./mec_client", "-I/data/mec_channel/./mqtt_client", "-I/data/mec_channel/./dvin_client", "-I/data/mec_channel/./v2x_client", "-I/data/mec_channel/./platform", "-I/data/mec_channel/./platform/genvict", "-I/data/mec_channel/./platform/genvict_sdk", "-I/data/mec_channel/./platform/debug", "-I/data/mec_channel/./platform/empty", "-I/data/mec_channel/./platform/baidu", "-I/data/mec_channel/./platform/boyuan", "-I/data/mec_channel/./platform/gosuncn", "-I/data/mec_channel/./platform/huawei", "-I/data/mec_channel/./platform/multi", "-I/data/mec_channel/./platform/tianan_obu", "-I/data/mec_channel/./platform/tianan", "-I/data/mec_channel/./platform/tianan/mec_protocol", "-I/data/mec_channel/./test", "-I/data/mec_channel/./app", "-O3", "-DNDEBUG", "--std=c++11", "-Wall", "-O3", "-Wno-psabi", "-c", "-o", "CMakeFiles/mec_channel.dir/engine_module/engine/tcp_mgr.cpp.o", "/data/mec_channel/engine_module/engine/tcp_mgr.cpp"], "directory": "/data/mec_channel", "file": "/data/mec_channel/engine_module/engine/tcp_mgr.cpp", "output": "/data/mec_channel/CMakeFiles/mec_channel.dir/engine_module/engine/tcp_mgr.cpp.o"}, {"arguments": ["/usr/local/bin/arm-linux-gnueabi-g++", "-DPLATFORM_ARM32", "-I/data/mec_channel/.", "-I/data/mec_channel/./mec_asn", "-I/data/mec_channel/./include", "-I/data/mec_channel/./engine_module/include", "-I/data/mec_channel/./engine_module/include/mosquitto", "-I/data/mec_channel/./engine_module/include/zmq", "-I/data/mec_channel/./engine_module/utility", "-I/data/mec_channel/./engine_module/signal", "-I/data/mec_channel/./engine_module/log", "-I/data/mec_channel/./engine_module/config", "-I/data/mec_channel/./engine_module/mem", "-I/data/mec_channel/./engine_module/engine", "-I/data/mec_channel/./engine_module/thread_resource", "-I/data/mec_channel/./mec_client", "-I/data/mec_channel/./mqtt_client", "-I/data/mec_channel/./dvin_client", "-I/data/mec_channel/./v2x_client", "-I/data/mec_channel/./platform", "-I/data/mec_channel/./platform/genvict", "-I/data/mec_channel/./platform/genvict_sdk", "-I/data/mec_channel/./platform/debug", "-I/data/mec_channel/./platform/empty", "-I/data/mec_channel/./platform/baidu", "-I/data/mec_channel/./platform/boyuan", "-I/data/mec_channel/./platform/gosuncn", "-I/data/mec_channel/./platform/huawei", "-I/data/mec_channel/./platform/multi", "-I/data/mec_channel/./platform/tianan_obu", "-I/data/mec_channel/./platform/tianan", "-I/data/mec_channel/./platform/tianan/mec_protocol", "-I/data/mec_channel/./test", "-I/data/mec_channel/./app", "-O3", "-DNDEBUG", "--std=c++11", "-Wall", "-O3", "-Wno-psabi", "-c", "-o", "CMakeFiles/mec_channel.dir/engine_module/engine/udp_mgr.cpp.o", "/data/mec_channel/engine_module/engine/udp_mgr.cpp"], "directory": "/data/mec_channel", "file": "/data/mec_channel/engine_module/engine/udp_mgr.cpp", "output": "/data/mec_channel/CMakeFiles/mec_channel.dir/engine_module/engine/udp_mgr.cpp.o"}, {"arguments": ["/usr/local/bin/arm-linux-gnueabi-g++", "-DPLATFORM_ARM32", "-I/data/mec_channel/.", "-I/data/mec_channel/./mec_asn", "-I/data/mec_channel/./include", "-I/data/mec_channel/./engine_module/include", "-I/data/mec_channel/./engine_module/include/mosquitto", "-I/data/mec_channel/./engine_module/include/zmq", "-I/data/mec_channel/./engine_module/utility", "-I/data/mec_channel/./engine_module/signal", "-I/data/mec_channel/./engine_module/log", "-I/data/mec_channel/./engine_module/config", "-I/data/mec_channel/./engine_module/mem", "-I/data/mec_channel/./engine_module/engine", "-I/data/mec_channel/./engine_module/thread_resource", "-I/data/mec_channel/./mec_client", "-I/data/mec_channel/./mqtt_client", "-I/data/mec_channel/./dvin_client", "-I/data/mec_channel/./v2x_client", "-I/data/mec_channel/./platform", "-I/data/mec_channel/./platform/genvict", "-I/data/mec_channel/./platform/genvict_sdk", "-I/data/mec_channel/./platform/debug", "-I/data/mec_channel/./platform/empty", "-I/data/mec_channel/./platform/baidu", "-I/data/mec_channel/./platform/boyuan", "-I/data/mec_channel/./platform/gosuncn", "-I/data/mec_channel/./platform/huawei", "-I/data/mec_channel/./platform/multi", "-I/data/mec_channel/./platform/tianan_obu", "-I/data/mec_channel/./platform/tianan", "-I/data/mec_channel/./platform/tianan/mec_protocol", "-I/data/mec_channel/./test", "-I/data/mec_channel/./app", "-O3", "-DNDEBUG", "--std=c++11", "-Wall", "-O3", "-Wno-psabi", "-c", "-o", "CMakeFiles/mec_channel.dir/engine_module/thread_resource/tr_center.cpp.o", "/data/mec_channel/engine_module/thread_resource/tr_center.cpp"], "directory": "/data/mec_channel", "file": "/data/mec_channel/engine_module/thread_resource/tr_center.cpp", "output": "/data/mec_channel/CMakeFiles/mec_channel.dir/engine_module/thread_resource/tr_center.cpp.o"}, {"arguments": ["/usr/local/bin/arm-linux-gnueabi-g++", "-DPLATFORM_ARM32", "-I/data/mec_channel/.", "-I/data/mec_channel/./mec_asn", "-I/data/mec_channel/./include", "-I/data/mec_channel/./engine_module/include", "-I/data/mec_channel/./engine_module/include/mosquitto", "-I/data/mec_channel/./engine_module/include/zmq", "-I/data/mec_channel/./engine_module/utility", "-I/data/mec_channel/./engine_module/signal", "-I/data/mec_channel/./engine_module/log", "-I/data/mec_channel/./engine_module/config", "-I/data/mec_channel/./engine_module/mem", "-I/data/mec_channel/./engine_module/engine", "-I/data/mec_channel/./engine_module/thread_resource", "-I/data/mec_channel/./mec_client", "-I/data/mec_channel/./mqtt_client", "-I/data/mec_channel/./dvin_client", "-I/data/mec_channel/./v2x_client", "-I/data/mec_channel/./platform", "-I/data/mec_channel/./platform/genvict", "-I/data/mec_channel/./platform/genvict_sdk", "-I/data/mec_channel/./platform/debug", "-I/data/mec_channel/./platform/empty", "-I/data/mec_channel/./platform/baidu", "-I/data/mec_channel/./platform/boyuan", "-I/data/mec_channel/./platform/gosuncn", "-I/data/mec_channel/./platform/huawei", "-I/data/mec_channel/./platform/multi", "-I/data/mec_channel/./platform/tianan_obu", "-I/data/mec_channel/./platform/tianan", "-I/data/mec_channel/./platform/tianan/mec_protocol", "-I/data/mec_channel/./test", "-I/data/mec_channel/./app", "-O3", "-DNDEBUG", "--std=c++11", "-Wall", "-O3", "-Wno-psabi", "-c", "-o", "CMakeFiles/mec_channel.dir/dvin_client/dvin_debug_client.cpp.o", "/data/mec_channel/dvin_client/dvin_debug_client.cpp"], "directory": "/data/mec_channel", "file": "/data/mec_channel/dvin_client/dvin_debug_client.cpp", "output": "/data/mec_channel/CMakeFiles/mec_channel.dir/dvin_client/dvin_debug_client.cpp.o"}, {"arguments": ["/usr/local/bin/arm-linux-gnueabi-g++", "-DPLATFORM_ARM32", "-I/data/mec_channel/.", "-I/data/mec_channel/./mec_asn", "-I/data/mec_channel/./include", "-I/data/mec_channel/./engine_module/include", "-I/data/mec_channel/./engine_module/include/mosquitto", "-I/data/mec_channel/./engine_module/include/zmq", "-I/data/mec_channel/./engine_module/utility", "-I/data/mec_channel/./engine_module/signal", "-I/data/mec_channel/./engine_module/log", "-I/data/mec_channel/./engine_module/config", "-I/data/mec_channel/./engine_module/mem", "-I/data/mec_channel/./engine_module/engine", "-I/data/mec_channel/./engine_module/thread_resource", "-I/data/mec_channel/./mec_client", "-I/data/mec_channel/./mqtt_client", "-I/data/mec_channel/./dvin_client", "-I/data/mec_channel/./v2x_client", "-I/data/mec_channel/./platform", "-I/data/mec_channel/./platform/genvict", "-I/data/mec_channel/./platform/genvict_sdk", "-I/data/mec_channel/./platform/debug", "-I/data/mec_channel/./platform/empty", "-I/data/mec_channel/./platform/baidu", "-I/data/mec_channel/./platform/boyuan", "-I/data/mec_channel/./platform/gosuncn", "-I/data/mec_channel/./platform/huawei", "-I/data/mec_channel/./platform/multi", "-I/data/mec_channel/./platform/tianan_obu", "-I/data/mec_channel/./platform/tianan", "-I/data/mec_channel/./platform/tianan/mec_protocol", "-I/data/mec_channel/./test", "-I/data/mec_channel/./app", "-O3", "-DNDEBUG", "--std=c++11", "-Wall", "-O3", "-Wno-psabi", "-c", "-o", "CMakeFiles/mec_channel.dir/v2x_client/v2x_client.cpp.o", "/data/mec_channel/v2x_client/v2x_client.cpp"], "directory": "/data/mec_channel", "file": "/data/mec_channel/v2x_client/v2x_client.cpp", "output": "/data/mec_channel/CMakeFiles/mec_channel.dir/v2x_client/v2x_client.cpp.o"}, {"arguments": ["/usr/local/bin/arm-linux-gnueabi-g++", "-DPLATFORM_ARM32", "-I/data/mec_channel/.", "-I/data/mec_channel/./mec_asn", "-I/data/mec_channel/./include", "-I/data/mec_channel/./engine_module/include", "-I/data/mec_channel/./engine_module/include/mosquitto", "-I/data/mec_channel/./engine_module/include/zmq", "-I/data/mec_channel/./engine_module/utility", "-I/data/mec_channel/./engine_module/signal", "-I/data/mec_channel/./engine_module/log", "-I/data/mec_channel/./engine_module/config", "-I/data/mec_channel/./engine_module/mem", "-I/data/mec_channel/./engine_module/engine", "-I/data/mec_channel/./engine_module/thread_resource", "-I/data/mec_channel/./mec_client", "-I/data/mec_channel/./mqtt_client", "-I/data/mec_channel/./dvin_client", "-I/data/mec_channel/./v2x_client", "-I/data/mec_channel/./platform", "-I/data/mec_channel/./platform/genvict", "-I/data/mec_channel/./platform/genvict_sdk", "-I/data/mec_channel/./platform/debug", "-I/data/mec_channel/./platform/empty", "-I/data/mec_channel/./platform/baidu", "-I/data/mec_channel/./platform/boyuan", "-I/data/mec_channel/./platform/gosuncn", "-I/data/mec_channel/./platform/huawei", "-I/data/mec_channel/./platform/multi", "-I/data/mec_channel/./platform/tianan_obu", "-I/data/mec_channel/./platform/tianan", "-I/data/mec_channel/./platform/tianan/mec_protocol", "-I/data/mec_channel/./test", "-I/data/mec_channel/./app", "-O3", "-DNDEBUG", "--std=c++11", "-Wall", "-O3", "-Wno-psabi", "-c", "-o", "CMakeFiles/mec_channel.dir/v2x_client/v2x_protocol.cpp.o", "/data/mec_channel/v2x_client/v2x_protocol.cpp"], "directory": "/data/mec_channel", "file": "/data/mec_channel/v2x_client/v2x_protocol.cpp", "output": "/data/mec_channel/CMakeFiles/mec_channel.dir/v2x_client/v2x_protocol.cpp.o"}, {"arguments": ["/usr/local/bin/arm-linux-gnueabi-g++", "-DPLATFORM_ARM32", "-I/data/mec_channel/.", "-I/data/mec_channel/./mec_asn", "-I/data/mec_channel/./include", "-I/data/mec_channel/./engine_module/include", "-I/data/mec_channel/./engine_module/include/mosquitto", "-I/data/mec_channel/./engine_module/include/zmq", "-I/data/mec_channel/./engine_module/utility", "-I/data/mec_channel/./engine_module/signal", "-I/data/mec_channel/./engine_module/log", "-I/data/mec_channel/./engine_module/config", "-I/data/mec_channel/./engine_module/mem", "-I/data/mec_channel/./engine_module/engine", "-I/data/mec_channel/./engine_module/thread_resource", "-I/data/mec_channel/./mec_client", "-I/data/mec_channel/./mqtt_client", "-I/data/mec_channel/./dvin_client", "-I/data/mec_channel/./v2x_client", "-I/data/mec_channel/./platform", "-I/data/mec_channel/./platform/genvict", "-I/data/mec_channel/./platform/genvict_sdk", "-I/data/mec_channel/./platform/debug", "-I/data/mec_channel/./platform/empty", "-I/data/mec_channel/./platform/baidu", "-I/data/mec_channel/./platform/boyuan", "-I/data/mec_channel/./platform/gosuncn", "-I/data/mec_channel/./platform/huawei", "-I/data/mec_channel/./platform/multi", "-I/data/mec_channel/./platform/tianan_obu", "-I/data/mec_channel/./platform/tianan", "-I/data/mec_channel/./platform/tianan/mec_protocol", "-I/data/mec_channel/./test", "-I/data/mec_channel/./app", "-O3", "-DNDEBUG", "--std=c++11", "-Wall", "-O3", "-Wno-psabi", "-c", "-o", "CMakeFiles/mec_channel.dir/test/http_client_tool.cpp.o", "/data/mec_channel/test/http_client_tool.cpp"], "directory": "/data/mec_channel", "file": "/data/mec_channel/test/http_client_tool.cpp", "output": "/data/mec_channel/CMakeFiles/mec_channel.dir/test/http_client_tool.cpp.o"}, {"arguments": ["/usr/local/bin/arm-linux-gnueabi-g++", "-DPLATFORM_ARM32", "-I/data/mec_channel/.", "-I/data/mec_channel/./mec_asn", "-I/data/mec_channel/./include", "-I/data/mec_channel/./engine_module/include", "-I/data/mec_channel/./engine_module/include/mosquitto", "-I/data/mec_channel/./engine_module/include/zmq", "-I/data/mec_channel/./engine_module/utility", "-I/data/mec_channel/./engine_module/signal", "-I/data/mec_channel/./engine_module/log", "-I/data/mec_channel/./engine_module/config", "-I/data/mec_channel/./engine_module/mem", "-I/data/mec_channel/./engine_module/engine", "-I/data/mec_channel/./engine_module/thread_resource", "-I/data/mec_channel/./mec_client", "-I/data/mec_channel/./mqtt_client", "-I/data/mec_channel/./dvin_client", "-I/data/mec_channel/./v2x_client", "-I/data/mec_channel/./platform", "-I/data/mec_channel/./platform/genvict", "-I/data/mec_channel/./platform/genvict_sdk", "-I/data/mec_channel/./platform/debug", "-I/data/mec_channel/./platform/empty", "-I/data/mec_channel/./platform/baidu", "-I/data/mec_channel/./platform/boyuan", "-I/data/mec_channel/./platform/gosuncn", "-I/data/mec_channel/./platform/huawei", "-I/data/mec_channel/./platform/multi", "-I/data/mec_channel/./platform/tianan_obu", "-I/data/mec_channel/./platform/tianan", "-I/data/mec_channel/./platform/tianan/mec_protocol", "-I/data/mec_channel/./test", "-I/data/mec_channel/./app", "-O3", "-DNDEBUG", "--std=c++11", "-Wall", "-O3", "-Wno-psabi", "-c", "-o", "CMakeFiles/mec_channel.dir/test/tcp_client_tool.cpp.o", "/data/mec_channel/test/tcp_client_tool.cpp"], "directory": "/data/mec_channel", "file": "/data/mec_channel/test/tcp_client_tool.cpp", "output": "/data/mec_channel/CMakeFiles/mec_channel.dir/test/tcp_client_tool.cpp.o"}, {"arguments": ["/usr/local/bin/arm-linux-gnueabi-g++", "-DPLATFORM_ARM32", "-I/data/mec_channel/.", "-I/data/mec_channel/./mec_asn", "-I/data/mec_channel/./include", "-I/data/mec_channel/./engine_module/include", "-I/data/mec_channel/./engine_module/include/mosquitto", "-I/data/mec_channel/./engine_module/include/zmq", "-I/data/mec_channel/./engine_module/utility", "-I/data/mec_channel/./engine_module/signal", "-I/data/mec_channel/./engine_module/log", "-I/data/mec_channel/./engine_module/config", "-I/data/mec_channel/./engine_module/mem", "-I/data/mec_channel/./engine_module/engine", "-I/data/mec_channel/./engine_module/thread_resource", "-I/data/mec_channel/./mec_client", "-I/data/mec_channel/./mqtt_client", "-I/data/mec_channel/./dvin_client", "-I/data/mec_channel/./v2x_client", "-I/data/mec_channel/./platform", "-I/data/mec_channel/./platform/genvict", "-I/data/mec_channel/./platform/genvict_sdk", "-I/data/mec_channel/./platform/debug", "-I/data/mec_channel/./platform/empty", "-I/data/mec_channel/./platform/baidu", "-I/data/mec_channel/./platform/boyuan", "-I/data/mec_channel/./platform/gosuncn", "-I/data/mec_channel/./platform/huawei", "-I/data/mec_channel/./platform/multi", "-I/data/mec_channel/./platform/tianan_obu", "-I/data/mec_channel/./platform/tianan", "-I/data/mec_channel/./platform/tianan/mec_protocol", "-I/data/mec_channel/./test", "-I/data/mec_channel/./app", "-O3", "-DNDEBUG", "--std=c++11", "-Wall", "-O3", "-Wno-psabi", "-c", "-o", "CMakeFiles/mec_channel.dir/test/tcp_service_tool.cpp.o", "/data/mec_channel/test/tcp_service_tool.cpp"], "directory": "/data/mec_channel", "file": "/data/mec_channel/test/tcp_service_tool.cpp", "output": "/data/mec_channel/CMakeFiles/mec_channel.dir/test/tcp_service_tool.cpp.o"}, {"arguments": ["/usr/local/bin/arm-linux-gnueabi-g++", "-DPLATFORM_ARM32", "-I/data/mec_channel/.", "-I/data/mec_channel/./mec_asn", "-I/data/mec_channel/./include", "-I/data/mec_channel/./engine_module/include", "-I/data/mec_channel/./engine_module/include/mosquitto", "-I/data/mec_channel/./engine_module/include/zmq", "-I/data/mec_channel/./engine_module/utility", "-I/data/mec_channel/./engine_module/signal", "-I/data/mec_channel/./engine_module/log", "-I/data/mec_channel/./engine_module/config", "-I/data/mec_channel/./engine_module/mem", "-I/data/mec_channel/./engine_module/engine", "-I/data/mec_channel/./engine_module/thread_resource", "-I/data/mec_channel/./mec_client", "-I/data/mec_channel/./mqtt_client", "-I/data/mec_channel/./dvin_client", "-I/data/mec_channel/./v2x_client", "-I/data/mec_channel/./platform", "-I/data/mec_channel/./platform/genvict", "-I/data/mec_channel/./platform/genvict_sdk", "-I/data/mec_channel/./platform/debug", "-I/data/mec_channel/./platform/empty", "-I/data/mec_channel/./platform/baidu", "-I/data/mec_channel/./platform/boyuan", "-I/data/mec_channel/./platform/gosuncn", "-I/data/mec_channel/./platform/huawei", "-I/data/mec_channel/./platform/multi", "-I/data/mec_channel/./platform/tianan_obu", "-I/data/mec_channel/./platform/tianan", "-I/data/mec_channel/./platform/tianan/mec_protocol", "-I/data/mec_channel/./test", "-I/data/mec_channel/./app", "-O3", "-DNDEBUG", "--std=c++11", "-Wall", "-O3", "-Wno-psabi", "-c", "-o", "CMakeFiles/mec_channel.dir/test/test_code.cpp.o", "/data/mec_channel/test/test_code.cpp"], "directory": "/data/mec_channel", "file": "/data/mec_channel/test/test_code.cpp", "output": "/data/mec_channel/CMakeFiles/mec_channel.dir/test/test_code.cpp.o"}, {"arguments": ["/usr/local/bin/arm-linux-gnueabi-g++", "-DPLATFORM_ARM32", "-I/data/mec_channel/.", "-I/data/mec_channel/./mec_asn", "-I/data/mec_channel/./include", "-I/data/mec_channel/./engine_module/include", "-I/data/mec_channel/./engine_module/include/mosquitto", "-I/data/mec_channel/./engine_module/include/zmq", "-I/data/mec_channel/./engine_module/utility", "-I/data/mec_channel/./engine_module/signal", "-I/data/mec_channel/./engine_module/log", "-I/data/mec_channel/./engine_module/config", "-I/data/mec_channel/./engine_module/mem", "-I/data/mec_channel/./engine_module/engine", "-I/data/mec_channel/./engine_module/thread_resource", "-I/data/mec_channel/./mec_client", "-I/data/mec_channel/./mqtt_client", "-I/data/mec_channel/./dvin_client", "-I/data/mec_channel/./v2x_client", "-I/data/mec_channel/./platform", "-I/data/mec_channel/./platform/genvict", "-I/data/mec_channel/./platform/genvict_sdk", "-I/data/mec_channel/./platform/debug", "-I/data/mec_channel/./platform/empty", "-I/data/mec_channel/./platform/baidu", "-I/data/mec_channel/./platform/boyuan", "-I/data/mec_channel/./platform/gosuncn", "-I/data/mec_channel/./platform/huawei", "-I/data/mec_channel/./platform/multi", "-I/data/mec_channel/./platform/tianan_obu", "-I/data/mec_channel/./platform/tianan", "-I/data/mec_channel/./platform/tianan/mec_protocol", "-I/data/mec_channel/./test", "-I/data/mec_channel/./app", "-O3", "-DNDEBUG", "--std=c++11", "-Wall", "-O3", "-Wno-psabi", "-c", "-o", "CMakeFiles/mec_channel.dir/test/test_tianan_mec_protocal.cpp.o", "/data/mec_channel/test/test_tianan_mec_protocal.cpp"], "directory": "/data/mec_channel", "file": "/data/mec_channel/test/test_tianan_mec_protocal.cpp", "output": "/data/mec_channel/CMakeFiles/mec_channel.dir/test/test_tianan_mec_protocal.cpp.o"}, {"arguments": ["/usr/local/bin/arm-linux-gnueabi-g++", "-DPLATFORM_ARM32", "-I/data/mec_channel/.", "-I/data/mec_channel/./mec_asn", "-I/data/mec_channel/./include", "-I/data/mec_channel/./engine_module/include", "-I/data/mec_channel/./engine_module/include/mosquitto", "-I/data/mec_channel/./engine_module/include/zmq", "-I/data/mec_channel/./engine_module/utility", "-I/data/mec_channel/./engine_module/signal", "-I/data/mec_channel/./engine_module/log", "-I/data/mec_channel/./engine_module/config", "-I/data/mec_channel/./engine_module/mem", "-I/data/mec_channel/./engine_module/engine", "-I/data/mec_channel/./engine_module/thread_resource", "-I/data/mec_channel/./mec_client", "-I/data/mec_channel/./mqtt_client", "-I/data/mec_channel/./dvin_client", "-I/data/mec_channel/./v2x_client", "-I/data/mec_channel/./platform", "-I/data/mec_channel/./platform/genvict", "-I/data/mec_channel/./platform/genvict_sdk", "-I/data/mec_channel/./platform/debug", "-I/data/mec_channel/./platform/empty", "-I/data/mec_channel/./platform/baidu", "-I/data/mec_channel/./platform/boyuan", "-I/data/mec_channel/./platform/gosuncn", "-I/data/mec_channel/./platform/huawei", "-I/data/mec_channel/./platform/multi", "-I/data/mec_channel/./platform/tianan_obu", "-I/data/mec_channel/./platform/tianan", "-I/data/mec_channel/./platform/tianan/mec_protocol", "-I/data/mec_channel/./test", "-I/data/mec_channel/./app", "-O3", "-DNDEBUG", "--std=c++11", "-Wall", "-O3", "-Wno-psabi", "-c", "-o", "CMakeFiles/mec_channel.dir/app/config.cpp.o", "/data/mec_channel/app/config.cpp"], "directory": "/data/mec_channel", "file": "/data/mec_channel/app/config.cpp", "output": "/data/mec_channel/CMakeFiles/mec_channel.dir/app/config.cpp.o"}, {"arguments": ["/usr/local/bin/arm-linux-gnueabi-g++", "-DPLATFORM_ARM32", "-I/data/mec_channel/.", "-I/data/mec_channel/./mec_asn", "-I/data/mec_channel/./include", "-I/data/mec_channel/./engine_module/include", "-I/data/mec_channel/./engine_module/include/mosquitto", "-I/data/mec_channel/./engine_module/include/zmq", "-I/data/mec_channel/./engine_module/utility", "-I/data/mec_channel/./engine_module/signal", "-I/data/mec_channel/./engine_module/log", "-I/data/mec_channel/./engine_module/config", "-I/data/mec_channel/./engine_module/mem", "-I/data/mec_channel/./engine_module/engine", "-I/data/mec_channel/./engine_module/thread_resource", "-I/data/mec_channel/./mec_client", "-I/data/mec_channel/./mqtt_client", "-I/data/mec_channel/./dvin_client", "-I/data/mec_channel/./v2x_client", "-I/data/mec_channel/./platform", "-I/data/mec_channel/./platform/genvict", "-I/data/mec_channel/./platform/genvict_sdk", "-I/data/mec_channel/./platform/debug", "-I/data/mec_channel/./platform/empty", "-I/data/mec_channel/./platform/baidu", "-I/data/mec_channel/./platform/boyuan", "-I/data/mec_channel/./platform/gosuncn", "-I/data/mec_channel/./platform/huawei", "-I/data/mec_channel/./platform/multi", "-I/data/mec_channel/./platform/tianan_obu", "-I/data/mec_channel/./platform/tianan", "-I/data/mec_channel/./platform/tianan/mec_protocol", "-I/data/mec_channel/./test", "-I/data/mec_channel/./app", "-O3", "-DNDEBUG", "--std=c++11", "-Wall", "-O3", "-Wno-psabi", "-c", "-o", "CMakeFiles/mec_channel.dir/app/main.cpp.o", "/data/mec_channel/app/main.cpp"], "directory": "/data/mec_channel", "file": "/data/mec_channel/app/main.cpp", "output": "/data/mec_channel/CMakeFiles/mec_channel.dir/app/main.cpp.o"}, {"arguments": ["/usr/local/bin/arm-linux-gnueabi-g++", "-DPLATFORM_ARM32", "-I/data/mec_channel/.", "-I/data/mec_channel/./mec_asn", "-I/data/mec_channel/./include", "-I/data/mec_channel/./engine_module/include", "-I/data/mec_channel/./engine_module/include/mosquitto", "-I/data/mec_channel/./engine_module/include/zmq", "-I/data/mec_channel/./engine_module/utility", "-I/data/mec_channel/./engine_module/signal", "-I/data/mec_channel/./engine_module/log", "-I/data/mec_channel/./engine_module/config", "-I/data/mec_channel/./engine_module/mem", "-I/data/mec_channel/./engine_module/engine", "-I/data/mec_channel/./engine_module/thread_resource", "-I/data/mec_channel/./mec_client", "-I/data/mec_channel/./mqtt_client", "-I/data/mec_channel/./dvin_client", "-I/data/mec_channel/./v2x_client", "-I/data/mec_channel/./platform", "-I/data/mec_channel/./platform/genvict", "-I/data/mec_channel/./platform/genvict_sdk", "-I/data/mec_channel/./platform/debug", "-I/data/mec_channel/./platform/empty", "-I/data/mec_channel/./platform/baidu", "-I/data/mec_channel/./platform/boyuan", "-I/data/mec_channel/./platform/gosuncn", "-I/data/mec_channel/./platform/huawei", "-I/data/mec_channel/./platform/multi", "-I/data/mec_channel/./platform/tianan_obu", "-I/data/mec_channel/./platform/tianan", "-I/data/mec_channel/./platform/tianan/mec_protocol", "-I/data/mec_channel/./test", "-I/data/mec_channel/./app", "-O3", "-DNDEBUG", "--std=c++11", "-Wall", "-O3", "-Wno-psabi", "-c", "-o", "CMakeFiles/mec_channel.dir/app/service.cpp.o", "/data/mec_channel/app/service.cpp"], "directory": "/data/mec_channel", "file": "/data/mec_channel/app/service.cpp", "output": "/data/mec_channel/CMakeFiles/mec_channel.dir/app/service.cpp.o"}, {"arguments": ["/usr/local/bin/arm-linux-gnueabi-g++", "-DPLATFORM_ARM32", "-I/data/mec_channel/.", "-I/data/mec_channel/./mec_asn", "-I/data/mec_channel/./include", "-I/data/mec_channel/./engine_module/include", "-I/data/mec_channel/./engine_module/include/mosquitto", "-I/data/mec_channel/./engine_module/include/zmq", "-I/data/mec_channel/./engine_module/utility", "-I/data/mec_channel/./engine_module/signal", "-I/data/mec_channel/./engine_module/log", "-I/data/mec_channel/./engine_module/config", "-I/data/mec_channel/./engine_module/mem", "-I/data/mec_channel/./engine_module/engine", "-I/data/mec_channel/./engine_module/thread_resource", "-I/data/mec_channel/./mec_client", "-I/data/mec_channel/./mqtt_client", "-I/data/mec_channel/./dvin_client", "-I/data/mec_channel/./v2x_client", "-I/data/mec_channel/./platform", "-I/data/mec_channel/./platform/genvict", "-I/data/mec_channel/./platform/genvict_sdk", "-I/data/mec_channel/./platform/debug", "-I/data/mec_channel/./platform/empty", "-I/data/mec_channel/./platform/baidu", "-I/data/mec_channel/./platform/boyuan", "-I/data/mec_channel/./platform/gosuncn", "-I/data/mec_channel/./platform/huawei", "-I/data/mec_channel/./platform/multi", "-I/data/mec_channel/./platform/tianan_obu", "-I/data/mec_channel/./platform/tianan", "-I/data/mec_channel/./platform/tianan/mec_protocol", "-I/data/mec_channel/./test", "-I/data/mec_channel/./app", "-O3", "-DNDEBUG", "--std=c++11", "-Wall", "-O3", "-Wno-psabi", "-c", "-o", "CMakeFiles/mec_channel.dir/platform/icloud_platform_client.cpp.o", "/data/mec_channel/platform/icloud_platform_client.cpp"], "directory": "/data/mec_channel", "file": "/data/mec_channel/platform/icloud_platform_client.cpp", "output": "/data/mec_channel/CMakeFiles/mec_channel.dir/platform/icloud_platform_client.cpp.o"}, {"arguments": ["/usr/local/bin/arm-linux-gnueabi-g++", "-DPLATFORM_ARM32", "-I/data/mec_channel/.", "-I/data/mec_channel/./mec_asn", "-I/data/mec_channel/./include", "-I/data/mec_channel/./engine_module/include", "-I/data/mec_channel/./engine_module/include/mosquitto", "-I/data/mec_channel/./engine_module/include/zmq", "-I/data/mec_channel/./engine_module/utility", "-I/data/mec_channel/./engine_module/signal", "-I/data/mec_channel/./engine_module/log", "-I/data/mec_channel/./engine_module/config", "-I/data/mec_channel/./engine_module/mem", "-I/data/mec_channel/./engine_module/engine", "-I/data/mec_channel/./engine_module/thread_resource", "-I/data/mec_channel/./mec_client", "-I/data/mec_channel/./mqtt_client", "-I/data/mec_channel/./dvin_client", "-I/data/mec_channel/./v2x_client", "-I/data/mec_channel/./platform", "-I/data/mec_channel/./platform/genvict", "-I/data/mec_channel/./platform/genvict_sdk", "-I/data/mec_channel/./platform/debug", "-I/data/mec_channel/./platform/empty", "-I/data/mec_channel/./platform/baidu", "-I/data/mec_channel/./platform/boyuan", "-I/data/mec_channel/./platform/gosuncn", "-I/data/mec_channel/./platform/huawei", "-I/data/mec_channel/./platform/multi", "-I/data/mec_channel/./platform/tianan_obu", "-I/data/mec_channel/./platform/tianan", "-I/data/mec_channel/./platform/tianan/mec_protocol", "-I/data/mec_channel/./test", "-I/data/mec_channel/./app", "-O3", "-DNDEBUG", "--std=c++11", "-Wall", "-O3", "-Wno-psabi", "-c", "-o", "CMakeFiles/mec_channel.dir/platform/imec_client.cpp.o", "/data/mec_channel/platform/imec_client.cpp"], "directory": "/data/mec_channel", "file": "/data/mec_channel/platform/imec_client.cpp", "output": "/data/mec_channel/CMakeFiles/mec_channel.dir/platform/imec_client.cpp.o"}, {"arguments": ["/usr/local/bin/arm-linux-gnueabi-g++", "-DPLATFORM_ARM32", "-I/data/mec_channel/.", "-I/data/mec_channel/./mec_asn", "-I/data/mec_channel/./include", "-I/data/mec_channel/./engine_module/include", "-I/data/mec_channel/./engine_module/include/mosquitto", "-I/data/mec_channel/./engine_module/include/zmq", "-I/data/mec_channel/./engine_module/utility", "-I/data/mec_channel/./engine_module/signal", "-I/data/mec_channel/./engine_module/log", "-I/data/mec_channel/./engine_module/config", "-I/data/mec_channel/./engine_module/mem", "-I/data/mec_channel/./engine_module/engine", "-I/data/mec_channel/./engine_module/thread_resource", "-I/data/mec_channel/./mec_client", "-I/data/mec_channel/./mqtt_client", "-I/data/mec_channel/./dvin_client", "-I/data/mec_channel/./v2x_client", "-I/data/mec_channel/./platform", "-I/data/mec_channel/./platform/genvict", "-I/data/mec_channel/./platform/genvict_sdk", "-I/data/mec_channel/./platform/debug", "-I/data/mec_channel/./platform/empty", "-I/data/mec_channel/./platform/baidu", "-I/data/mec_channel/./platform/boyuan", "-I/data/mec_channel/./platform/gosuncn", "-I/data/mec_channel/./platform/huawei", "-I/data/mec_channel/./platform/multi", "-I/data/mec_channel/./platform/tianan_obu", "-I/data/mec_channel/./platform/tianan", "-I/data/mec_channel/./platform/tianan/mec_protocol", "-I/data/mec_channel/./test", "-I/data/mec_channel/./app", "-O3", "-DNDEBUG", "--std=c++11", "-Wall", "-O3", "-Wno-psabi", "-c", "-o", "CMakeFiles/mec_channel.dir/platform/genvict/gv_http_client.cpp.o", "/data/mec_channel/platform/genvict/gv_http_client.cpp"], "directory": "/data/mec_channel", "file": "/data/mec_channel/platform/genvict/gv_http_client.cpp", "output": "/data/mec_channel/CMakeFiles/mec_channel.dir/platform/genvict/gv_http_client.cpp.o"}, {"arguments": ["/usr/local/bin/arm-linux-gnueabi-g++", "-DPLATFORM_ARM32", "-I/data/mec_channel/.", "-I/data/mec_channel/./mec_asn", "-I/data/mec_channel/./include", "-I/data/mec_channel/./engine_module/include", "-I/data/mec_channel/./engine_module/include/mosquitto", "-I/data/mec_channel/./engine_module/include/zmq", "-I/data/mec_channel/./engine_module/utility", "-I/data/mec_channel/./engine_module/signal", "-I/data/mec_channel/./engine_module/log", "-I/data/mec_channel/./engine_module/config", "-I/data/mec_channel/./engine_module/mem", "-I/data/mec_channel/./engine_module/engine", "-I/data/mec_channel/./engine_module/thread_resource", "-I/data/mec_channel/./mec_client", "-I/data/mec_channel/./mqtt_client", "-I/data/mec_channel/./dvin_client", "-I/data/mec_channel/./v2x_client", "-I/data/mec_channel/./platform", "-I/data/mec_channel/./platform/genvict", "-I/data/mec_channel/./platform/genvict_sdk", "-I/data/mec_channel/./platform/debug", "-I/data/mec_channel/./platform/empty", "-I/data/mec_channel/./platform/baidu", "-I/data/mec_channel/./platform/boyuan", "-I/data/mec_channel/./platform/gosuncn", "-I/data/mec_channel/./platform/huawei", "-I/data/mec_channel/./platform/multi", "-I/data/mec_channel/./platform/tianan_obu", "-I/data/mec_channel/./platform/tianan", "-I/data/mec_channel/./platform/tianan/mec_protocol", "-I/data/mec_channel/./test", "-I/data/mec_channel/./app", "-O3", "-DNDEBUG", "--std=c++11", "-Wall", "-O3", "-Wno-psabi", "-c", "-o", "CMakeFiles/mec_channel.dir/platform/genvict/gv_mec_client.cpp.o", "/data/mec_channel/platform/genvict/gv_mec_client.cpp"], "directory": "/data/mec_channel", "file": "/data/mec_channel/platform/genvict/gv_mec_client.cpp", "output": "/data/mec_channel/CMakeFiles/mec_channel.dir/platform/genvict/gv_mec_client.cpp.o"}, {"arguments": ["/usr/local/bin/arm-linux-gnueabi-g++", "-DPLATFORM_ARM32", "-I/data/mec_channel/.", "-I/data/mec_channel/./mec_asn", "-I/data/mec_channel/./include", "-I/data/mec_channel/./engine_module/include", "-I/data/mec_channel/./engine_module/include/mosquitto", "-I/data/mec_channel/./engine_module/include/zmq", "-I/data/mec_channel/./engine_module/utility", "-I/data/mec_channel/./engine_module/signal", "-I/data/mec_channel/./engine_module/log", "-I/data/mec_channel/./engine_module/config", "-I/data/mec_channel/./engine_module/mem", "-I/data/mec_channel/./engine_module/engine", "-I/data/mec_channel/./engine_module/thread_resource", "-I/data/mec_channel/./mec_client", "-I/data/mec_channel/./mqtt_client", "-I/data/mec_channel/./dvin_client", "-I/data/mec_channel/./v2x_client", "-I/data/mec_channel/./platform", "-I/data/mec_channel/./platform/genvict", "-I/data/mec_channel/./platform/genvict_sdk", "-I/data/mec_channel/./platform/debug", "-I/data/mec_channel/./platform/empty", "-I/data/mec_channel/./platform/baidu", "-I/data/mec_channel/./platform/boyuan", "-I/data/mec_channel/./platform/gosuncn", "-I/data/mec_channel/./platform/huawei", "-I/data/mec_channel/./platform/multi", "-I/data/mec_channel/./platform/tianan_obu", "-I/data/mec_channel/./platform/tianan", "-I/data/mec_channel/./platform/tianan/mec_protocol", "-I/data/mec_channel/./test", "-I/data/mec_channel/./app", "-O3", "-DNDEBUG", "--std=c++11", "-Wall", "-O3", "-Wno-psabi", "-c", "-o", "CMakeFiles/mec_channel.dir/platform/genvict/gv_mec_protocol.cpp.o", "/data/mec_channel/platform/genvict/gv_mec_protocol.cpp"], "directory": "/data/mec_channel", "file": "/data/mec_channel/platform/genvict/gv_mec_protocol.cpp", "output": "/data/mec_channel/CMakeFiles/mec_channel.dir/platform/genvict/gv_mec_protocol.cpp.o"}, {"arguments": ["/usr/local/bin/arm-linux-gnueabi-g++", "-DPLATFORM_ARM32", "-I/data/mec_channel/.", "-I/data/mec_channel/./mec_asn", "-I/data/mec_channel/./include", "-I/data/mec_channel/./engine_module/include", "-I/data/mec_channel/./engine_module/include/mosquitto", "-I/data/mec_channel/./engine_module/include/zmq", "-I/data/mec_channel/./engine_module/utility", "-I/data/mec_channel/./engine_module/signal", "-I/data/mec_channel/./engine_module/log", "-I/data/mec_channel/./engine_module/config", "-I/data/mec_channel/./engine_module/mem", "-I/data/mec_channel/./engine_module/engine", "-I/data/mec_channel/./engine_module/thread_resource", "-I/data/mec_channel/./mec_client", "-I/data/mec_channel/./mqtt_client", "-I/data/mec_channel/./dvin_client", "-I/data/mec_channel/./v2x_client", "-I/data/mec_channel/./platform", "-I/data/mec_channel/./platform/genvict", "-I/data/mec_channel/./platform/genvict_sdk", "-I/data/mec_channel/./platform/debug", "-I/data/mec_channel/./platform/empty", "-I/data/mec_channel/./platform/baidu", "-I/data/mec_channel/./platform/boyuan", "-I/data/mec_channel/./platform/gosuncn", "-I/data/mec_channel/./platform/huawei", "-I/data/mec_channel/./platform/multi", "-I/data/mec_channel/./platform/tianan_obu", "-I/data/mec_channel/./platform/tianan", "-I/data/mec_channel/./platform/tianan/mec_protocol", "-I/data/mec_channel/./test", "-I/data/mec_channel/./app", "-O3", "-DNDEBUG", "--std=c++11", "-Wall", "-O3", "-Wno-psabi", "-c", "-o", "CMakeFiles/mec_channel.dir/platform/genvict/gv_mqtt_client.cpp.o", "/data/mec_channel/platform/genvict/gv_mqtt_client.cpp"], "directory": "/data/mec_channel", "file": "/data/mec_channel/platform/genvict/gv_mqtt_client.cpp", "output": "/data/mec_channel/CMakeFiles/mec_channel.dir/platform/genvict/gv_mqtt_client.cpp.o"}, {"arguments": ["/usr/local/bin/arm-linux-gnueabi-g++", "-DPLATFORM_ARM32", "-I/data/mec_channel/.", "-I/data/mec_channel/./mec_asn", "-I/data/mec_channel/./include", "-I/data/mec_channel/./engine_module/include", "-I/data/mec_channel/./engine_module/include/mosquitto", "-I/data/mec_channel/./engine_module/include/zmq", "-I/data/mec_channel/./engine_module/utility", "-I/data/mec_channel/./engine_module/signal", "-I/data/mec_channel/./engine_module/log", "-I/data/mec_channel/./engine_module/config", "-I/data/mec_channel/./engine_module/mem", "-I/data/mec_channel/./engine_module/engine", "-I/data/mec_channel/./engine_module/thread_resource", "-I/data/mec_channel/./mec_client", "-I/data/mec_channel/./mqtt_client", "-I/data/mec_channel/./dvin_client", "-I/data/mec_channel/./v2x_client", "-I/data/mec_channel/./platform", "-I/data/mec_channel/./platform/genvict", "-I/data/mec_channel/./platform/genvict_sdk", "-I/data/mec_channel/./platform/debug", "-I/data/mec_channel/./platform/empty", "-I/data/mec_channel/./platform/baidu", "-I/data/mec_channel/./platform/boyuan", "-I/data/mec_channel/./platform/gosuncn", "-I/data/mec_channel/./platform/huawei", "-I/data/mec_channel/./platform/multi", "-I/data/mec_channel/./platform/tianan_obu", "-I/data/mec_channel/./platform/tianan", "-I/data/mec_channel/./platform/tianan/mec_protocol", "-I/data/mec_channel/./test", "-I/data/mec_channel/./app", "-O3", "-DNDEBUG", "--std=c++11", "-Wall", "-O3", "-Wno-psabi", "-c", "-o", "CMakeFiles/mec_channel.dir/platform/genvict/gv_mqtt_protocol.cpp.o", "/data/mec_channel/platform/genvict/gv_mqtt_protocol.cpp"], "directory": "/data/mec_channel", "file": "/data/mec_channel/platform/genvict/gv_mqtt_protocol.cpp", "output": "/data/mec_channel/CMakeFiles/mec_channel.dir/platform/genvict/gv_mqtt_protocol.cpp.o"}, {"arguments": ["/usr/local/bin/arm-linux-gnueabi-g++", "-DPLATFORM_ARM32", "-I/data/mec_channel/.", "-I/data/mec_channel/./mec_asn", "-I/data/mec_channel/./include", "-I/data/mec_channel/./engine_module/include", "-I/data/mec_channel/./engine_module/include/mosquitto", "-I/data/mec_channel/./engine_module/include/zmq", "-I/data/mec_channel/./engine_module/utility", "-I/data/mec_channel/./engine_module/signal", "-I/data/mec_channel/./engine_module/log", "-I/data/mec_channel/./engine_module/config", "-I/data/mec_channel/./engine_module/mem", "-I/data/mec_channel/./engine_module/engine", "-I/data/mec_channel/./engine_module/thread_resource", "-I/data/mec_channel/./mec_client", "-I/data/mec_channel/./mqtt_client", "-I/data/mec_channel/./dvin_client", "-I/data/mec_channel/./v2x_client", "-I/data/mec_channel/./platform", "-I/data/mec_channel/./platform/genvict", "-I/data/mec_channel/./platform/genvict_sdk", "-I/data/mec_channel/./platform/debug", "-I/data/mec_channel/./platform/empty", "-I/data/mec_channel/./platform/baidu", "-I/data/mec_channel/./platform/boyuan", "-I/data/mec_channel/./platform/gosuncn", "-I/data/mec_channel/./platform/huawei", "-I/data/mec_channel/./platform/multi", "-I/data/mec_channel/./platform/tianan_obu", "-I/data/mec_channel/./platform/tianan", "-I/data/mec_channel/./platform/tianan/mec_protocol", "-I/data/mec_channel/./test", "-I/data/mec_channel/./app", "-O3", "-DNDEBUG", "--std=c++11", "-Wall", "-O3", "-Wno-psabi", "-c", "-o", "CMakeFiles/mec_channel.dir/platform/genvict/gv_platform_client.cpp.o", "/data/mec_channel/platform/genvict/gv_platform_client.cpp"], "directory": "/data/mec_channel", "file": "/data/mec_channel/platform/genvict/gv_platform_client.cpp", "output": "/data/mec_channel/CMakeFiles/mec_channel.dir/platform/genvict/gv_platform_client.cpp.o"}, {"arguments": ["/usr/local/bin/arm-linux-gnueabi-g++", "-DPLATFORM_ARM32", "-I/data/mec_channel/.", "-I/data/mec_channel/./mec_asn", "-I/data/mec_channel/./include", "-I/data/mec_channel/./engine_module/include", "-I/data/mec_channel/./engine_module/include/mosquitto", "-I/data/mec_channel/./engine_module/include/zmq", "-I/data/mec_channel/./engine_module/utility", "-I/data/mec_channel/./engine_module/signal", "-I/data/mec_channel/./engine_module/log", "-I/data/mec_channel/./engine_module/config", "-I/data/mec_channel/./engine_module/mem", "-I/data/mec_channel/./engine_module/engine", "-I/data/mec_channel/./engine_module/thread_resource", "-I/data/mec_channel/./mec_client", "-I/data/mec_channel/./mqtt_client", "-I/data/mec_channel/./dvin_client", "-I/data/mec_channel/./v2x_client", "-I/data/mec_channel/./platform", "-I/data/mec_channel/./platform/genvict", "-I/data/mec_channel/./platform/genvict_sdk", "-I/data/mec_channel/./platform/debug", "-I/data/mec_channel/./platform/empty", "-I/data/mec_channel/./platform/baidu", "-I/data/mec_channel/./platform/boyuan", "-I/data/mec_channel/./platform/gosuncn", "-I/data/mec_channel/./platform/huawei", "-I/data/mec_channel/./platform/multi", "-I/data/mec_channel/./platform/tianan_obu", "-I/data/mec_channel/./platform/tianan", "-I/data/mec_channel/./platform/tianan/mec_protocol", "-I/data/mec_channel/./test", "-I/data/mec_channel/./app", "-O3", "-DNDEBUG", "--std=c++11", "-Wall", "-O3", "-Wno-psabi", "-c", "-o", "CMakeFiles/mec_channel.dir/platform/genvict_sdk/gv_sdk_protocol.cpp.o", "/data/mec_channel/platform/genvict_sdk/gv_sdk_protocol.cpp"], "directory": "/data/mec_channel", "file": "/data/mec_channel/platform/genvict_sdk/gv_sdk_protocol.cpp", "output": "/data/mec_channel/CMakeFiles/mec_channel.dir/platform/genvict_sdk/gv_sdk_protocol.cpp.o"}, {"arguments": ["/usr/local/bin/arm-linux-gnueabi-g++", "-DPLATFORM_ARM32", "-I/data/mec_channel/.", "-I/data/mec_channel/./mec_asn", "-I/data/mec_channel/./include", "-I/data/mec_channel/./engine_module/include", "-I/data/mec_channel/./engine_module/include/mosquitto", "-I/data/mec_channel/./engine_module/include/zmq", "-I/data/mec_channel/./engine_module/utility", "-I/data/mec_channel/./engine_module/signal", "-I/data/mec_channel/./engine_module/log", "-I/data/mec_channel/./engine_module/config", "-I/data/mec_channel/./engine_module/mem", "-I/data/mec_channel/./engine_module/engine", "-I/data/mec_channel/./engine_module/thread_resource", "-I/data/mec_channel/./mec_client", "-I/data/mec_channel/./mqtt_client", "-I/data/mec_channel/./dvin_client", "-I/data/mec_channel/./v2x_client", "-I/data/mec_channel/./platform", "-I/data/mec_channel/./platform/genvict", "-I/data/mec_channel/./platform/genvict_sdk", "-I/data/mec_channel/./platform/debug", "-I/data/mec_channel/./platform/empty", "-I/data/mec_channel/./platform/baidu", "-I/data/mec_channel/./platform/boyuan", "-I/data/mec_channel/./platform/gosuncn", "-I/data/mec_channel/./platform/huawei", "-I/data/mec_channel/./platform/multi", "-I/data/mec_channel/./platform/tianan_obu", "-I/data/mec_channel/./platform/tianan", "-I/data/mec_channel/./platform/tianan/mec_protocol", "-I/data/mec_channel/./test", "-I/data/mec_channel/./app", "-O3", "-DNDEBUG", "--std=c++11", "-Wall", "-O3", "-Wno-psabi", "-c", "-o", "CMakeFiles/mec_channel.dir/platform/genvict_sdk/gv_sdk_service.cpp.o", "/data/mec_channel/platform/genvict_sdk/gv_sdk_service.cpp"], "directory": "/data/mec_channel", "file": "/data/mec_channel/platform/genvict_sdk/gv_sdk_service.cpp", "output": "/data/mec_channel/CMakeFiles/mec_channel.dir/platform/genvict_sdk/gv_sdk_service.cpp.o"}, {"arguments": ["/usr/local/bin/arm-linux-gnueabi-g++", "-DPLATFORM_ARM32", "-I/data/mec_channel/.", "-I/data/mec_channel/./mec_asn", "-I/data/mec_channel/./include", "-I/data/mec_channel/./engine_module/include", "-I/data/mec_channel/./engine_module/include/mosquitto", "-I/data/mec_channel/./engine_module/include/zmq", "-I/data/mec_channel/./engine_module/utility", "-I/data/mec_channel/./engine_module/signal", "-I/data/mec_channel/./engine_module/log", "-I/data/mec_channel/./engine_module/config", "-I/data/mec_channel/./engine_module/mem", "-I/data/mec_channel/./engine_module/engine", "-I/data/mec_channel/./engine_module/thread_resource", "-I/data/mec_channel/./mec_client", "-I/data/mec_channel/./mqtt_client", "-I/data/mec_channel/./dvin_client", "-I/data/mec_channel/./v2x_client", "-I/data/mec_channel/./platform", "-I/data/mec_channel/./platform/genvict", "-I/data/mec_channel/./platform/genvict_sdk", "-I/data/mec_channel/./platform/debug", "-I/data/mec_channel/./platform/empty", "-I/data/mec_channel/./platform/baidu", "-I/data/mec_channel/./platform/boyuan", "-I/data/mec_channel/./platform/gosuncn", "-I/data/mec_channel/./platform/huawei", "-I/data/mec_channel/./platform/multi", "-I/data/mec_channel/./platform/tianan_obu", "-I/data/mec_channel/./platform/tianan", "-I/data/mec_channel/./platform/tianan/mec_protocol", "-I/data/mec_channel/./test", "-I/data/mec_channel/./app", "-O3", "-DNDEBUG", "--std=c++11", "-Wall", "-O3", "-Wno-psabi", "-c", "-o", "CMakeFiles/mec_channel.dir/platform/baidu/baidu_mec_client.cpp.o", "/data/mec_channel/platform/baidu/baidu_mec_client.cpp"], "directory": "/data/mec_channel", "file": "/data/mec_channel/platform/baidu/baidu_mec_client.cpp", "output": "/data/mec_channel/CMakeFiles/mec_channel.dir/platform/baidu/baidu_mec_client.cpp.o"}, {"arguments": ["/usr/local/bin/arm-linux-gnueabi-g++", "-DPLATFORM_ARM32", "-I/data/mec_channel/.", "-I/data/mec_channel/./mec_asn", "-I/data/mec_channel/./include", "-I/data/mec_channel/./engine_module/include", "-I/data/mec_channel/./engine_module/include/mosquitto", "-I/data/mec_channel/./engine_module/include/zmq", "-I/data/mec_channel/./engine_module/utility", "-I/data/mec_channel/./engine_module/signal", "-I/data/mec_channel/./engine_module/log", "-I/data/mec_channel/./engine_module/config", "-I/data/mec_channel/./engine_module/mem", "-I/data/mec_channel/./engine_module/engine", "-I/data/mec_channel/./engine_module/thread_resource", "-I/data/mec_channel/./mec_client", "-I/data/mec_channel/./mqtt_client", "-I/data/mec_channel/./dvin_client", "-I/data/mec_channel/./v2x_client", "-I/data/mec_channel/./platform", "-I/data/mec_channel/./platform/genvict", "-I/data/mec_channel/./platform/genvict_sdk", "-I/data/mec_channel/./platform/debug", "-I/data/mec_channel/./platform/empty", "-I/data/mec_channel/./platform/baidu", "-I/data/mec_channel/./platform/boyuan", "-I/data/mec_channel/./platform/gosuncn", "-I/data/mec_channel/./platform/huawei", "-I/data/mec_channel/./platform/multi", "-I/data/mec_channel/./platform/tianan_obu", "-I/data/mec_channel/./platform/tianan", "-I/data/mec_channel/./platform/tianan/mec_protocol", "-I/data/mec_channel/./test", "-I/data/mec_channel/./app", "-O3", "-DNDEBUG", "--std=c++11", "-Wall", "-O3", "-Wno-psabi", "-c", "-o", "CMakeFiles/mec_channel.dir/platform/baidu/baidu_mec_protocol.cpp.o", "/data/mec_channel/platform/baidu/baidu_mec_protocol.cpp"], "directory": "/data/mec_channel", "file": "/data/mec_channel/platform/baidu/baidu_mec_protocol.cpp", "output": "/data/mec_channel/CMakeFiles/mec_channel.dir/platform/baidu/baidu_mec_protocol.cpp.o"}, {"arguments": ["/usr/local/bin/arm-linux-gnueabi-g++", "-DPLATFORM_ARM32", "-I/data/mec_channel/.", "-I/data/mec_channel/./mec_asn", "-I/data/mec_channel/./include", "-I/data/mec_channel/./engine_module/include", "-I/data/mec_channel/./engine_module/include/mosquitto", "-I/data/mec_channel/./engine_module/include/zmq", "-I/data/mec_channel/./engine_module/utility", "-I/data/mec_channel/./engine_module/signal", "-I/data/mec_channel/./engine_module/log", "-I/data/mec_channel/./engine_module/config", "-I/data/mec_channel/./engine_module/mem", "-I/data/mec_channel/./engine_module/engine", "-I/data/mec_channel/./engine_module/thread_resource", "-I/data/mec_channel/./mec_client", "-I/data/mec_channel/./mqtt_client", "-I/data/mec_channel/./dvin_client", "-I/data/mec_channel/./v2x_client", "-I/data/mec_channel/./platform", "-I/data/mec_channel/./platform/genvict", "-I/data/mec_channel/./platform/genvict_sdk", "-I/data/mec_channel/./platform/debug", "-I/data/mec_channel/./platform/empty", "-I/data/mec_channel/./platform/baidu", "-I/data/mec_channel/./platform/boyuan", "-I/data/mec_channel/./platform/gosuncn", "-I/data/mec_channel/./platform/huawei", "-I/data/mec_channel/./platform/multi", "-I/data/mec_channel/./platform/tianan_obu", "-I/data/mec_channel/./platform/tianan", "-I/data/mec_channel/./platform/tianan/mec_protocol", "-I/data/mec_channel/./test", "-I/data/mec_channel/./app", "-O3", "-DNDEBUG", "--std=c++11", "-Wall", "-O3", "-Wno-psabi", "-c", "-o", "CMakeFiles/mec_channel.dir/platform/boyuan/boyuan_mqtt_protocol.cpp.o", "/data/mec_channel/platform/boyuan/boyuan_mqtt_protocol.cpp"], "directory": "/data/mec_channel", "file": "/data/mec_channel/platform/boyuan/boyuan_mqtt_protocol.cpp", "output": "/data/mec_channel/CMakeFiles/mec_channel.dir/platform/boyuan/boyuan_mqtt_protocol.cpp.o"}, {"arguments": ["/usr/local/bin/arm-linux-gnueabi-g++", "-DPLATFORM_ARM32", "-I/data/mec_channel/.", "-I/data/mec_channel/./mec_asn", "-I/data/mec_channel/./include", "-I/data/mec_channel/./engine_module/include", "-I/data/mec_channel/./engine_module/include/mosquitto", "-I/data/mec_channel/./engine_module/include/zmq", "-I/data/mec_channel/./engine_module/utility", "-I/data/mec_channel/./engine_module/signal", "-I/data/mec_channel/./engine_module/log", "-I/data/mec_channel/./engine_module/config", "-I/data/mec_channel/./engine_module/mem", "-I/data/mec_channel/./engine_module/engine", "-I/data/mec_channel/./engine_module/thread_resource", "-I/data/mec_channel/./mec_client", "-I/data/mec_channel/./mqtt_client", "-I/data/mec_channel/./dvin_client", "-I/data/mec_channel/./v2x_client", "-I/data/mec_channel/./platform", "-I/data/mec_channel/./platform/genvict", "-I/data/mec_channel/./platform/genvict_sdk", "-I/data/mec_channel/./platform/debug", "-I/data/mec_channel/./platform/empty", "-I/data/mec_channel/./platform/baidu", "-I/data/mec_channel/./platform/boyuan", "-I/data/mec_channel/./platform/gosuncn", "-I/data/mec_channel/./platform/huawei", "-I/data/mec_channel/./platform/multi", "-I/data/mec_channel/./platform/tianan_obu", "-I/data/mec_channel/./platform/tianan", "-I/data/mec_channel/./platform/tianan/mec_protocol", "-I/data/mec_channel/./test", "-I/data/mec_channel/./app", "-O3", "-DNDEBUG", "--std=c++11", "-Wall", "-O3", "-Wno-psabi", "-c", "-o", "CMakeFiles/mec_channel.dir/platform/boyuan/boyuan_platform_client.cpp.o", "/data/mec_channel/platform/boyuan/boyuan_platform_client.cpp"], "directory": "/data/mec_channel", "file": "/data/mec_channel/platform/boyuan/boyuan_platform_client.cpp", "output": "/data/mec_channel/CMakeFiles/mec_channel.dir/platform/boyuan/boyuan_platform_client.cpp.o"}, {"arguments": ["/usr/local/bin/arm-linux-gnueabi-g++", "-DPLATFORM_ARM32", "-I/data/mec_channel/.", "-I/data/mec_channel/./mec_asn", "-I/data/mec_channel/./include", "-I/data/mec_channel/./engine_module/include", "-I/data/mec_channel/./engine_module/include/mosquitto", "-I/data/mec_channel/./engine_module/include/zmq", "-I/data/mec_channel/./engine_module/utility", "-I/data/mec_channel/./engine_module/signal", "-I/data/mec_channel/./engine_module/log", "-I/data/mec_channel/./engine_module/config", "-I/data/mec_channel/./engine_module/mem", "-I/data/mec_channel/./engine_module/engine", "-I/data/mec_channel/./engine_module/thread_resource", "-I/data/mec_channel/./mec_client", "-I/data/mec_channel/./mqtt_client", "-I/data/mec_channel/./dvin_client", "-I/data/mec_channel/./v2x_client", "-I/data/mec_channel/./platform", "-I/data/mec_channel/./platform/genvict", "-I/data/mec_channel/./platform/genvict_sdk", "-I/data/mec_channel/./platform/debug", "-I/data/mec_channel/./platform/empty", "-I/data/mec_channel/./platform/baidu", "-I/data/mec_channel/./platform/boyuan", "-I/data/mec_channel/./platform/gosuncn", "-I/data/mec_channel/./platform/huawei", "-I/data/mec_channel/./platform/multi", "-I/data/mec_channel/./platform/tianan_obu", "-I/data/mec_channel/./platform/tianan", "-I/data/mec_channel/./platform/tianan/mec_protocol", "-I/data/mec_channel/./test", "-I/data/mec_channel/./app", "-O3", "-DNDEBUG", "--std=c++11", "-Wall", "-O3", "-Wno-psabi", "-c", "-o", "CMakeFiles/mec_channel.dir/platform/gosuncn/gxx_mec_client.cpp.o", "/data/mec_channel/platform/gosuncn/gxx_mec_client.cpp"], "directory": "/data/mec_channel", "file": "/data/mec_channel/platform/gosuncn/gxx_mec_client.cpp", "output": "/data/mec_channel/CMakeFiles/mec_channel.dir/platform/gosuncn/gxx_mec_client.cpp.o"}, {"arguments": ["/usr/local/bin/arm-linux-gnueabi-g++", "-DPLATFORM_ARM32", "-I/data/mec_channel/.", "-I/data/mec_channel/./mec_asn", "-I/data/mec_channel/./include", "-I/data/mec_channel/./engine_module/include", "-I/data/mec_channel/./engine_module/include/mosquitto", "-I/data/mec_channel/./engine_module/include/zmq", "-I/data/mec_channel/./engine_module/utility", "-I/data/mec_channel/./engine_module/signal", "-I/data/mec_channel/./engine_module/log", "-I/data/mec_channel/./engine_module/config", "-I/data/mec_channel/./engine_module/mem", "-I/data/mec_channel/./engine_module/engine", "-I/data/mec_channel/./engine_module/thread_resource", "-I/data/mec_channel/./mec_client", "-I/data/mec_channel/./mqtt_client", "-I/data/mec_channel/./dvin_client", "-I/data/mec_channel/./v2x_client", "-I/data/mec_channel/./platform", "-I/data/mec_channel/./platform/genvict", "-I/data/mec_channel/./platform/genvict_sdk", "-I/data/mec_channel/./platform/debug", "-I/data/mec_channel/./platform/empty", "-I/data/mec_channel/./platform/baidu", "-I/data/mec_channel/./platform/boyuan", "-I/data/mec_channel/./platform/gosuncn", "-I/data/mec_channel/./platform/huawei", "-I/data/mec_channel/./platform/multi", "-I/data/mec_channel/./platform/tianan_obu", "-I/data/mec_channel/./platform/tianan", "-I/data/mec_channel/./platform/tianan/mec_protocol", "-I/data/mec_channel/./test", "-I/data/mec_channel/./app", "-O3", "-DNDEBUG", "--std=c++11", "-Wall", "-O3", "-Wno-psabi", "-c", "-o", "CMakeFiles/mec_channel.dir/platform/gosuncn/gxx_mec_protocol.cpp.o", "/data/mec_channel/platform/gosuncn/gxx_mec_protocol.cpp"], "directory": "/data/mec_channel", "file": "/data/mec_channel/platform/gosuncn/gxx_mec_protocol.cpp", "output": "/data/mec_channel/CMakeFiles/mec_channel.dir/platform/gosuncn/gxx_mec_protocol.cpp.o"}, {"arguments": ["/usr/local/bin/arm-linux-gnueabi-g++", "-DPLATFORM_ARM32", "-I/data/mec_channel/.", "-I/data/mec_channel/./mec_asn", "-I/data/mec_channel/./include", "-I/data/mec_channel/./engine_module/include", "-I/data/mec_channel/./engine_module/include/mosquitto", "-I/data/mec_channel/./engine_module/include/zmq", "-I/data/mec_channel/./engine_module/utility", "-I/data/mec_channel/./engine_module/signal", "-I/data/mec_channel/./engine_module/log", "-I/data/mec_channel/./engine_module/config", "-I/data/mec_channel/./engine_module/mem", "-I/data/mec_channel/./engine_module/engine", "-I/data/mec_channel/./engine_module/thread_resource", "-I/data/mec_channel/./mec_client", "-I/data/mec_channel/./mqtt_client", "-I/data/mec_channel/./dvin_client", "-I/data/mec_channel/./v2x_client", "-I/data/mec_channel/./platform", "-I/data/mec_channel/./platform/genvict", "-I/data/mec_channel/./platform/genvict_sdk", "-I/data/mec_channel/./platform/debug", "-I/data/mec_channel/./platform/empty", "-I/data/mec_channel/./platform/baidu", "-I/data/mec_channel/./platform/boyuan", "-I/data/mec_channel/./platform/gosuncn", "-I/data/mec_channel/./platform/huawei", "-I/data/mec_channel/./platform/multi", "-I/data/mec_channel/./platform/tianan_obu", "-I/data/mec_channel/./platform/tianan", "-I/data/mec_channel/./platform/tianan/mec_protocol", "-I/data/mec_channel/./test", "-I/data/mec_channel/./app", "-O3", "-DNDEBUG", "--std=c++11", "-Wall", "-O3", "-Wno-psabi", "-c", "-o", "CMakeFiles/mec_channel.dir/platform/tianan/tianan_http_client.cpp.o", "/data/mec_channel/platform/tianan/tianan_http_client.cpp"], "directory": "/data/mec_channel", "file": "/data/mec_channel/platform/tianan/tianan_http_client.cpp", "output": "/data/mec_channel/CMakeFiles/mec_channel.dir/platform/tianan/tianan_http_client.cpp.o"}, {"arguments": ["/usr/local/bin/arm-linux-gnueabi-g++", "-DPLATFORM_ARM32", "-I/data/mec_channel/.", "-I/data/mec_channel/./mec_asn", "-I/data/mec_channel/./include", "-I/data/mec_channel/./engine_module/include", "-I/data/mec_channel/./engine_module/include/mosquitto", "-I/data/mec_channel/./engine_module/include/zmq", "-I/data/mec_channel/./engine_module/utility", "-I/data/mec_channel/./engine_module/signal", "-I/data/mec_channel/./engine_module/log", "-I/data/mec_channel/./engine_module/config", "-I/data/mec_channel/./engine_module/mem", "-I/data/mec_channel/./engine_module/engine", "-I/data/mec_channel/./engine_module/thread_resource", "-I/data/mec_channel/./mec_client", "-I/data/mec_channel/./mqtt_client", "-I/data/mec_channel/./dvin_client", "-I/data/mec_channel/./v2x_client", "-I/data/mec_channel/./platform", "-I/data/mec_channel/./platform/genvict", "-I/data/mec_channel/./platform/genvict_sdk", "-I/data/mec_channel/./platform/debug", "-I/data/mec_channel/./platform/empty", "-I/data/mec_channel/./platform/baidu", "-I/data/mec_channel/./platform/boyuan", "-I/data/mec_channel/./platform/gosuncn", "-I/data/mec_channel/./platform/huawei", "-I/data/mec_channel/./platform/multi", "-I/data/mec_channel/./platform/tianan_obu", "-I/data/mec_channel/./platform/tianan", "-I/data/mec_channel/./platform/tianan/mec_protocol", "-I/data/mec_channel/./test", "-I/data/mec_channel/./app", "-O3", "-DNDEBUG", "--std=c++11", "-Wall", "-O3", "-Wno-psabi", "-c", "-o", "CMakeFiles/mec_channel.dir/platform/tianan/tianan_mec_client.cpp.o", "/data/mec_channel/platform/tianan/tianan_mec_client.cpp"], "directory": "/data/mec_channel", "file": "/data/mec_channel/platform/tianan/tianan_mec_client.cpp", "output": "/data/mec_channel/CMakeFiles/mec_channel.dir/platform/tianan/tianan_mec_client.cpp.o"}, {"arguments": ["/usr/local/bin/arm-linux-gnueabi-g++", "-DPLATFORM_ARM32", "-I/data/mec_channel/.", "-I/data/mec_channel/./mec_asn", "-I/data/mec_channel/./include", "-I/data/mec_channel/./engine_module/include", "-I/data/mec_channel/./engine_module/include/mosquitto", "-I/data/mec_channel/./engine_module/include/zmq", "-I/data/mec_channel/./engine_module/utility", "-I/data/mec_channel/./engine_module/signal", "-I/data/mec_channel/./engine_module/log", "-I/data/mec_channel/./engine_module/config", "-I/data/mec_channel/./engine_module/mem", "-I/data/mec_channel/./engine_module/engine", "-I/data/mec_channel/./engine_module/thread_resource", "-I/data/mec_channel/./mec_client", "-I/data/mec_channel/./mqtt_client", "-I/data/mec_channel/./dvin_client", "-I/data/mec_channel/./v2x_client", "-I/data/mec_channel/./platform", "-I/data/mec_channel/./platform/genvict", "-I/data/mec_channel/./platform/genvict_sdk", "-I/data/mec_channel/./platform/debug", "-I/data/mec_channel/./platform/empty", "-I/data/mec_channel/./platform/baidu", "-I/data/mec_channel/./platform/boyuan", "-I/data/mec_channel/./platform/gosuncn", "-I/data/mec_channel/./platform/huawei", "-I/data/mec_channel/./platform/multi", "-I/data/mec_channel/./platform/tianan_obu", "-I/data/mec_channel/./platform/tianan", "-I/data/mec_channel/./platform/tianan/mec_protocol", "-I/data/mec_channel/./test", "-I/data/mec_channel/./app", "-O3", "-DNDEBUG", "--std=c++11", "-Wall", "-O3", "-Wno-psabi", "-c", "-o", "CMakeFiles/mec_channel.dir/platform/tianan/tianan_mec_protocol.cpp.o", "/data/mec_channel/platform/tianan/tianan_mec_protocol.cpp"], "directory": "/data/mec_channel", "file": "/data/mec_channel/platform/tianan/tianan_mec_protocol.cpp", "output": "/data/mec_channel/CMakeFiles/mec_channel.dir/platform/tianan/tianan_mec_protocol.cpp.o"}, {"arguments": ["/usr/local/bin/arm-linux-gnueabi-g++", "-DPLATFORM_ARM32", "-I/data/mec_channel/.", "-I/data/mec_channel/./mec_asn", "-I/data/mec_channel/./include", "-I/data/mec_channel/./engine_module/include", "-I/data/mec_channel/./engine_module/include/mosquitto", "-I/data/mec_channel/./engine_module/include/zmq", "-I/data/mec_channel/./engine_module/utility", "-I/data/mec_channel/./engine_module/signal", "-I/data/mec_channel/./engine_module/log", "-I/data/mec_channel/./engine_module/config", "-I/data/mec_channel/./engine_module/mem", "-I/data/mec_channel/./engine_module/engine", "-I/data/mec_channel/./engine_module/thread_resource", "-I/data/mec_channel/./mec_client", "-I/data/mec_channel/./mqtt_client", "-I/data/mec_channel/./dvin_client", "-I/data/mec_channel/./v2x_client", "-I/data/mec_channel/./platform", "-I/data/mec_channel/./platform/genvict", "-I/data/mec_channel/./platform/genvict_sdk", "-I/data/mec_channel/./platform/debug", "-I/data/mec_channel/./platform/empty", "-I/data/mec_channel/./platform/baidu", "-I/data/mec_channel/./platform/boyuan", "-I/data/mec_channel/./platform/gosuncn", "-I/data/mec_channel/./platform/huawei", "-I/data/mec_channel/./platform/multi", "-I/data/mec_channel/./platform/tianan_obu", "-I/data/mec_channel/./platform/tianan", "-I/data/mec_channel/./platform/tianan/mec_protocol", "-I/data/mec_channel/./test", "-I/data/mec_channel/./app", "-O3", "-DNDEBUG", "--std=c++11", "-Wall", "-O3", "-Wno-psabi", "-c", "-o", "CMakeFiles/mec_channel.dir/platform/tianan/tianan_mqtt_client.cpp.o", "/data/mec_channel/platform/tianan/tianan_mqtt_client.cpp"], "directory": "/data/mec_channel", "file": "/data/mec_channel/platform/tianan/tianan_mqtt_client.cpp", "output": "/data/mec_channel/CMakeFiles/mec_channel.dir/platform/tianan/tianan_mqtt_client.cpp.o"}, {"arguments": ["/usr/local/bin/arm-linux-gnueabi-g++", "-DPLATFORM_ARM32", "-I/data/mec_channel/.", "-I/data/mec_channel/./mec_asn", "-I/data/mec_channel/./include", "-I/data/mec_channel/./engine_module/include", "-I/data/mec_channel/./engine_module/include/mosquitto", "-I/data/mec_channel/./engine_module/include/zmq", "-I/data/mec_channel/./engine_module/utility", "-I/data/mec_channel/./engine_module/signal", "-I/data/mec_channel/./engine_module/log", "-I/data/mec_channel/./engine_module/config", "-I/data/mec_channel/./engine_module/mem", "-I/data/mec_channel/./engine_module/engine", "-I/data/mec_channel/./engine_module/thread_resource", "-I/data/mec_channel/./mec_client", "-I/data/mec_channel/./mqtt_client", "-I/data/mec_channel/./dvin_client", "-I/data/mec_channel/./v2x_client", "-I/data/mec_channel/./platform", "-I/data/mec_channel/./platform/genvict", "-I/data/mec_channel/./platform/genvict_sdk", "-I/data/mec_channel/./platform/debug", "-I/data/mec_channel/./platform/empty", "-I/data/mec_channel/./platform/baidu", "-I/data/mec_channel/./platform/boyuan", "-I/data/mec_channel/./platform/gosuncn", "-I/data/mec_channel/./platform/huawei", "-I/data/mec_channel/./platform/multi", "-I/data/mec_channel/./platform/tianan_obu", "-I/data/mec_channel/./platform/tianan", "-I/data/mec_channel/./platform/tianan/mec_protocol", "-I/data/mec_channel/./test", "-I/data/mec_channel/./app", "-O3", "-DNDEBUG", "--std=c++11", "-Wall", "-O3", "-Wno-psabi", "-c", "-o", "CMakeFiles/mec_channel.dir/platform/tianan/tianan_mqtt_protocol.cpp.o", "/data/mec_channel/platform/tianan/tianan_mqtt_protocol.cpp"], "directory": "/data/mec_channel", "file": "/data/mec_channel/platform/tianan/tianan_mqtt_protocol.cpp", "output": "/data/mec_channel/CMakeFiles/mec_channel.dir/platform/tianan/tianan_mqtt_protocol.cpp.o"}, {"arguments": ["/usr/local/bin/arm-linux-gnueabi-g++", "-DPLATFORM_ARM32", "-I/data/mec_channel/.", "-I/data/mec_channel/./mec_asn", "-I/data/mec_channel/./include", "-I/data/mec_channel/./engine_module/include", "-I/data/mec_channel/./engine_module/include/mosquitto", "-I/data/mec_channel/./engine_module/include/zmq", "-I/data/mec_channel/./engine_module/utility", "-I/data/mec_channel/./engine_module/signal", "-I/data/mec_channel/./engine_module/log", "-I/data/mec_channel/./engine_module/config", "-I/data/mec_channel/./engine_module/mem", "-I/data/mec_channel/./engine_module/engine", "-I/data/mec_channel/./engine_module/thread_resource", "-I/data/mec_channel/./mec_client", "-I/data/mec_channel/./mqtt_client", "-I/data/mec_channel/./dvin_client", "-I/data/mec_channel/./v2x_client", "-I/data/mec_channel/./platform", "-I/data/mec_channel/./platform/genvict", "-I/data/mec_channel/./platform/genvict_sdk", "-I/data/mec_channel/./platform/debug", "-I/data/mec_channel/./platform/empty", "-I/data/mec_channel/./platform/baidu", "-I/data/mec_channel/./platform/boyuan", "-I/data/mec_channel/./platform/gosuncn", "-I/data/mec_channel/./platform/huawei", "-I/data/mec_channel/./platform/multi", "-I/data/mec_channel/./platform/tianan_obu", "-I/data/mec_channel/./platform/tianan", "-I/data/mec_channel/./platform/tianan/mec_protocol", "-I/data/mec_channel/./test", "-I/data/mec_channel/./app", "-O3", "-DNDEBUG", "--std=c++11", "-Wall", "-O3", "-Wno-psabi", "-c", "-o", "CMakeFiles/mec_channel.dir/platform/tianan/tianan_platform_client.cpp.o", "/data/mec_channel/platform/tianan/tianan_platform_client.cpp"], "directory": "/data/mec_channel", "file": "/data/mec_channel/platform/tianan/tianan_platform_client.cpp", "output": "/data/mec_channel/CMakeFiles/mec_channel.dir/platform/tianan/tianan_platform_client.cpp.o"}, {"arguments": ["/usr/local/bin/arm-linux-gnueabi-g++", "-DPLATFORM_ARM32", "-I/data/mec_channel/.", "-I/data/mec_channel/./mec_asn", "-I/data/mec_channel/./include", "-I/data/mec_channel/./engine_module/include", "-I/data/mec_channel/./engine_module/include/mosquitto", "-I/data/mec_channel/./engine_module/include/zmq", "-I/data/mec_channel/./engine_module/utility", "-I/data/mec_channel/./engine_module/signal", "-I/data/mec_channel/./engine_module/log", "-I/data/mec_channel/./engine_module/config", "-I/data/mec_channel/./engine_module/mem", "-I/data/mec_channel/./engine_module/engine", "-I/data/mec_channel/./engine_module/thread_resource", "-I/data/mec_channel/./mec_client", "-I/data/mec_channel/./mqtt_client", "-I/data/mec_channel/./dvin_client", "-I/data/mec_channel/./v2x_client", "-I/data/mec_channel/./platform", "-I/data/mec_channel/./platform/genvict", "-I/data/mec_channel/./platform/genvict_sdk", "-I/data/mec_channel/./platform/debug", "-I/data/mec_channel/./platform/empty", "-I/data/mec_channel/./platform/baidu", "-I/data/mec_channel/./platform/boyuan", "-I/data/mec_channel/./platform/gosuncn", "-I/data/mec_channel/./platform/huawei", "-I/data/mec_channel/./platform/multi", "-I/data/mec_channel/./platform/tianan_obu", "-I/data/mec_channel/./platform/tianan", "-I/data/mec_channel/./platform/tianan/mec_protocol", "-I/data/mec_channel/./test", "-I/data/mec_channel/./app", "-O3", "-DNDEBUG", "--std=c++11", "-Wall", "-O3", "-Wno-psabi", "-c", "-o", "CMakeFiles/mec_channel.dir/platform/tianan/tianan_spat_client.cpp.o", "/data/mec_channel/platform/tianan/tianan_spat_client.cpp"], "directory": "/data/mec_channel", "file": "/data/mec_channel/platform/tianan/tianan_spat_client.cpp", "output": "/data/mec_channel/CMakeFiles/mec_channel.dir/platform/tianan/tianan_spat_client.cpp.o"}, {"arguments": ["/usr/local/bin/arm-linux-gnueabi-g++", "-DPLATFORM_ARM32", "-I/data/mec_channel/.", "-I/data/mec_channel/./mec_asn", "-I/data/mec_channel/./include", "-I/data/mec_channel/./engine_module/include", "-I/data/mec_channel/./engine_module/include/mosquitto", "-I/data/mec_channel/./engine_module/include/zmq", "-I/data/mec_channel/./engine_module/utility", "-I/data/mec_channel/./engine_module/signal", "-I/data/mec_channel/./engine_module/log", "-I/data/mec_channel/./engine_module/config", "-I/data/mec_channel/./engine_module/mem", "-I/data/mec_channel/./engine_module/engine", "-I/data/mec_channel/./engine_module/thread_resource", "-I/data/mec_channel/./mec_client", "-I/data/mec_channel/./mqtt_client", "-I/data/mec_channel/./dvin_client", "-I/data/mec_channel/./v2x_client", "-I/data/mec_channel/./platform", "-I/data/mec_channel/./platform/genvict", "-I/data/mec_channel/./platform/genvict_sdk", "-I/data/mec_channel/./platform/debug", "-I/data/mec_channel/./platform/empty", "-I/data/mec_channel/./platform/baidu", "-I/data/mec_channel/./platform/boyuan", "-I/data/mec_channel/./platform/gosuncn", "-I/data/mec_channel/./platform/huawei", "-I/data/mec_channel/./platform/multi", "-I/data/mec_channel/./platform/tianan_obu", "-I/data/mec_channel/./platform/tianan", "-I/data/mec_channel/./platform/tianan/mec_protocol", "-I/data/mec_channel/./test", "-I/data/mec_channel/./app", "-O3", "-DNDEBUG", "--std=c++11", "-Wall", "-O3", "-Wno-psabi", "-c", "-o", "CMakeFiles/mec_channel.dir/platform/tianan/tianan_spat_protocol.cpp.o", "/data/mec_channel/platform/tianan/tianan_spat_protocol.cpp"], "directory": "/data/mec_channel", "file": "/data/mec_channel/platform/tianan/tianan_spat_protocol.cpp", "output": "/data/mec_channel/CMakeFiles/mec_channel.dir/platform/tianan/tianan_spat_protocol.cpp.o"}, {"arguments": ["/usr/local/bin/arm-linux-gnueabi-g++", "-DPLATFORM_ARM32", "-I/data/mec_channel/.", "-I/data/mec_channel/./mec_asn", "-I/data/mec_channel/./include", "-I/data/mec_channel/./engine_module/include", "-I/data/mec_channel/./engine_module/include/mosquitto", "-I/data/mec_channel/./engine_module/include/zmq", "-I/data/mec_channel/./engine_module/utility", "-I/data/mec_channel/./engine_module/signal", "-I/data/mec_channel/./engine_module/log", "-I/data/mec_channel/./engine_module/config", "-I/data/mec_channel/./engine_module/mem", "-I/data/mec_channel/./engine_module/engine", "-I/data/mec_channel/./engine_module/thread_resource", "-I/data/mec_channel/./mec_client", "-I/data/mec_channel/./mqtt_client", "-I/data/mec_channel/./dvin_client", "-I/data/mec_channel/./v2x_client", "-I/data/mec_channel/./platform", "-I/data/mec_channel/./platform/genvict", "-I/data/mec_channel/./platform/genvict_sdk", "-I/data/mec_channel/./platform/debug", "-I/data/mec_channel/./platform/empty", "-I/data/mec_channel/./platform/baidu", "-I/data/mec_channel/./platform/boyuan", "-I/data/mec_channel/./platform/gosuncn", "-I/data/mec_channel/./platform/huawei", "-I/data/mec_channel/./platform/multi", "-I/data/mec_channel/./platform/tianan_obu", "-I/data/mec_channel/./platform/tianan", "-I/data/mec_channel/./platform/tianan/mec_protocol", "-I/data/mec_channel/./test", "-I/data/mec_channel/./app", "-O3", "-DNDEBUG", "--std=c++11", "-Wall", "-O3", "-Wno-psabi", "-c", "-o", "CMakeFiles/mec_channel.dir/platform/tianan/mec_protocol/perception.pb.cc.o", "/data/mec_channel/platform/tianan/mec_protocol/perception.pb.cc"], "directory": "/data/mec_channel", "file": "/data/mec_channel/platform/tianan/mec_protocol/perception.pb.cc", "output": "/data/mec_channel/CMakeFiles/mec_channel.dir/platform/tianan/mec_protocol/perception.pb.cc.o"}, {"arguments": ["/usr/local/bin/arm-linux-gnueabi-g++", "-DPLATFORM_ARM32", "-I/data/mec_channel/.", "-I/data/mec_channel/./mec_asn", "-I/data/mec_channel/./include", "-I/data/mec_channel/./engine_module/include", "-I/data/mec_channel/./engine_module/include/mosquitto", "-I/data/mec_channel/./engine_module/include/zmq", "-I/data/mec_channel/./engine_module/utility", "-I/data/mec_channel/./engine_module/signal", "-I/data/mec_channel/./engine_module/log", "-I/data/mec_channel/./engine_module/config", "-I/data/mec_channel/./engine_module/mem", "-I/data/mec_channel/./engine_module/engine", "-I/data/mec_channel/./engine_module/thread_resource", "-I/data/mec_channel/./mec_client", "-I/data/mec_channel/./mqtt_client", "-I/data/mec_channel/./dvin_client", "-I/data/mec_channel/./v2x_client", "-I/data/mec_channel/./platform", "-I/data/mec_channel/./platform/genvict", "-I/data/mec_channel/./platform/genvict_sdk", "-I/data/mec_channel/./platform/debug", "-I/data/mec_channel/./platform/empty", "-I/data/mec_channel/./platform/baidu", "-I/data/mec_channel/./platform/boyuan", "-I/data/mec_channel/./platform/gosuncn", "-I/data/mec_channel/./platform/huawei", "-I/data/mec_channel/./platform/multi", "-I/data/mec_channel/./platform/tianan_obu", "-I/data/mec_channel/./platform/tianan", "-I/data/mec_channel/./platform/tianan/mec_protocol", "-I/data/mec_channel/./test", "-I/data/mec_channel/./app", "-O3", "-DNDEBUG", "--std=c++11", "-Wall", "-O3", "-Wno-psabi", "-c", "-o", "CMakeFiles/mec_channel.dir/platform/debug/debug_mec_client.cpp.o", "/data/mec_channel/platform/debug/debug_mec_client.cpp"], "directory": "/data/mec_channel", "file": "/data/mec_channel/platform/debug/debug_mec_client.cpp", "output": "/data/mec_channel/CMakeFiles/mec_channel.dir/platform/debug/debug_mec_client.cpp.o"}, {"arguments": ["/usr/local/bin/arm-linux-gnueabi-g++", "-DPLATFORM_ARM32", "-I/data/mec_channel/.", "-I/data/mec_channel/./mec_asn", "-I/data/mec_channel/./include", "-I/data/mec_channel/./engine_module/include", "-I/data/mec_channel/./engine_module/include/mosquitto", "-I/data/mec_channel/./engine_module/include/zmq", "-I/data/mec_channel/./engine_module/utility", "-I/data/mec_channel/./engine_module/signal", "-I/data/mec_channel/./engine_module/log", "-I/data/mec_channel/./engine_module/config", "-I/data/mec_channel/./engine_module/mem", "-I/data/mec_channel/./engine_module/engine", "-I/data/mec_channel/./engine_module/thread_resource", "-I/data/mec_channel/./mec_client", "-I/data/mec_channel/./mqtt_client", "-I/data/mec_channel/./dvin_client", "-I/data/mec_channel/./v2x_client", "-I/data/mec_channel/./platform", "-I/data/mec_channel/./platform/genvict", "-I/data/mec_channel/./platform/genvict_sdk", "-I/data/mec_channel/./platform/debug", "-I/data/mec_channel/./platform/empty", "-I/data/mec_channel/./platform/baidu", "-I/data/mec_channel/./platform/boyuan", "-I/data/mec_channel/./platform/gosuncn", "-I/data/mec_channel/./platform/huawei", "-I/data/mec_channel/./platform/multi", "-I/data/mec_channel/./platform/tianan_obu", "-I/data/mec_channel/./platform/tianan", "-I/data/mec_channel/./platform/tianan/mec_protocol", "-I/data/mec_channel/./test", "-I/data/mec_channel/./app", "-O3", "-DNDEBUG", "--std=c++11", "-Wall", "-O3", "-Wno-psabi", "-c", "-o", "CMakeFiles/mec_channel.dir/platform/debug/debug_platform_client.cpp.o", "/data/mec_channel/platform/debug/debug_platform_client.cpp"], "directory": "/data/mec_channel", "file": "/data/mec_channel/platform/debug/debug_platform_client.cpp", "output": "/data/mec_channel/CMakeFiles/mec_channel.dir/platform/debug/debug_platform_client.cpp.o"}, {"arguments": ["/usr/local/bin/arm-linux-gnueabi-g++", "-DPLATFORM_ARM32", "-I/data/mec_channel/.", "-I/data/mec_channel/./mec_asn", "-I/data/mec_channel/./include", "-I/data/mec_channel/./engine_module/include", "-I/data/mec_channel/./engine_module/include/mosquitto", "-I/data/mec_channel/./engine_module/include/zmq", "-I/data/mec_channel/./engine_module/utility", "-I/data/mec_channel/./engine_module/signal", "-I/data/mec_channel/./engine_module/log", "-I/data/mec_channel/./engine_module/config", "-I/data/mec_channel/./engine_module/mem", "-I/data/mec_channel/./engine_module/engine", "-I/data/mec_channel/./engine_module/thread_resource", "-I/data/mec_channel/./mec_client", "-I/data/mec_channel/./mqtt_client", "-I/data/mec_channel/./dvin_client", "-I/data/mec_channel/./v2x_client", "-I/data/mec_channel/./platform", "-I/data/mec_channel/./platform/genvict", "-I/data/mec_channel/./platform/genvict_sdk", "-I/data/mec_channel/./platform/debug", "-I/data/mec_channel/./platform/empty", "-I/data/mec_channel/./platform/baidu", "-I/data/mec_channel/./platform/boyuan", "-I/data/mec_channel/./platform/gosuncn", "-I/data/mec_channel/./platform/huawei", "-I/data/mec_channel/./platform/multi", "-I/data/mec_channel/./platform/tianan_obu", "-I/data/mec_channel/./platform/tianan", "-I/data/mec_channel/./platform/tianan/mec_protocol", "-I/data/mec_channel/./test", "-I/data/mec_channel/./app", "-O3", "-DNDEBUG", "--std=c++11", "-Wall", "-O3", "-Wno-psabi", "-c", "-o", "CMakeFiles/mec_channel.dir/platform/empty/empty_mec_client.cpp.o", "/data/mec_channel/platform/empty/empty_mec_client.cpp"], "directory": "/data/mec_channel", "file": "/data/mec_channel/platform/empty/empty_mec_client.cpp", "output": "/data/mec_channel/CMakeFiles/mec_channel.dir/platform/empty/empty_mec_client.cpp.o"}, {"arguments": ["/usr/local/bin/arm-linux-gnueabi-g++", "-DPLATFORM_ARM32", "-I/data/mec_channel/.", "-I/data/mec_channel/./mec_asn", "-I/data/mec_channel/./include", "-I/data/mec_channel/./engine_module/include", "-I/data/mec_channel/./engine_module/include/mosquitto", "-I/data/mec_channel/./engine_module/include/zmq", "-I/data/mec_channel/./engine_module/utility", "-I/data/mec_channel/./engine_module/signal", "-I/data/mec_channel/./engine_module/log", "-I/data/mec_channel/./engine_module/config", "-I/data/mec_channel/./engine_module/mem", "-I/data/mec_channel/./engine_module/engine", "-I/data/mec_channel/./engine_module/thread_resource", "-I/data/mec_channel/./mec_client", "-I/data/mec_channel/./mqtt_client", "-I/data/mec_channel/./dvin_client", "-I/data/mec_channel/./v2x_client", "-I/data/mec_channel/./platform", "-I/data/mec_channel/./platform/genvict", "-I/data/mec_channel/./platform/genvict_sdk", "-I/data/mec_channel/./platform/debug", "-I/data/mec_channel/./platform/empty", "-I/data/mec_channel/./platform/baidu", "-I/data/mec_channel/./platform/boyuan", "-I/data/mec_channel/./platform/gosuncn", "-I/data/mec_channel/./platform/huawei", "-I/data/mec_channel/./platform/multi", "-I/data/mec_channel/./platform/tianan_obu", "-I/data/mec_channel/./platform/tianan", "-I/data/mec_channel/./platform/tianan/mec_protocol", "-I/data/mec_channel/./test", "-I/data/mec_channel/./app", "-O3", "-DNDEBUG", "--std=c++11", "-Wall", "-O3", "-Wno-psabi", "-c", "-o", "CMakeFiles/mec_channel.dir/platform/empty/empty_platform_client.cpp.o", "/data/mec_channel/platform/empty/empty_platform_client.cpp"], "directory": "/data/mec_channel", "file": "/data/mec_channel/platform/empty/empty_platform_client.cpp", "output": "/data/mec_channel/CMakeFiles/mec_channel.dir/platform/empty/empty_platform_client.cpp.o"}, {"arguments": ["/usr/local/bin/arm-linux-gnueabi-g++", "-DPLATFORM_ARM32", "-I/data/mec_channel/.", "-I/data/mec_channel/./mec_asn", "-I/data/mec_channel/./include", "-I/data/mec_channel/./engine_module/include", "-I/data/mec_channel/./engine_module/include/mosquitto", "-I/data/mec_channel/./engine_module/include/zmq", "-I/data/mec_channel/./engine_module/utility", "-I/data/mec_channel/./engine_module/signal", "-I/data/mec_channel/./engine_module/log", "-I/data/mec_channel/./engine_module/config", "-I/data/mec_channel/./engine_module/mem", "-I/data/mec_channel/./engine_module/engine", "-I/data/mec_channel/./engine_module/thread_resource", "-I/data/mec_channel/./mec_client", "-I/data/mec_channel/./mqtt_client", "-I/data/mec_channel/./dvin_client", "-I/data/mec_channel/./v2x_client", "-I/data/mec_channel/./platform", "-I/data/mec_channel/./platform/genvict", "-I/data/mec_channel/./platform/genvict_sdk", "-I/data/mec_channel/./platform/debug", "-I/data/mec_channel/./platform/empty", "-I/data/mec_channel/./platform/baidu", "-I/data/mec_channel/./platform/boyuan", "-I/data/mec_channel/./platform/gosuncn", "-I/data/mec_channel/./platform/huawei", "-I/data/mec_channel/./platform/multi", "-I/data/mec_channel/./platform/tianan_obu", "-I/data/mec_channel/./platform/tianan", "-I/data/mec_channel/./platform/tianan/mec_protocol", "-I/data/mec_channel/./test", "-I/data/mec_channel/./app", "-O3", "-DNDEBUG", "--std=c++11", "-Wall", "-O3", "-Wno-psabi", "-c", "-o", "CMakeFiles/mec_channel.dir/platform/huawei/huawei_mec_client.cpp.o", "/data/mec_channel/platform/huawei/huawei_mec_client.cpp"], "directory": "/data/mec_channel", "file": "/data/mec_channel/platform/huawei/huawei_mec_client.cpp", "output": "/data/mec_channel/CMakeFiles/mec_channel.dir/platform/huawei/huawei_mec_client.cpp.o"}, {"arguments": ["/usr/local/bin/arm-linux-gnueabi-g++", "-DPLATFORM_ARM32", "-I/data/mec_channel/.", "-I/data/mec_channel/./mec_asn", "-I/data/mec_channel/./include", "-I/data/mec_channel/./engine_module/include", "-I/data/mec_channel/./engine_module/include/mosquitto", "-I/data/mec_channel/./engine_module/include/zmq", "-I/data/mec_channel/./engine_module/utility", "-I/data/mec_channel/./engine_module/signal", "-I/data/mec_channel/./engine_module/log", "-I/data/mec_channel/./engine_module/config", "-I/data/mec_channel/./engine_module/mem", "-I/data/mec_channel/./engine_module/engine", "-I/data/mec_channel/./engine_module/thread_resource", "-I/data/mec_channel/./mec_client", "-I/data/mec_channel/./mqtt_client", "-I/data/mec_channel/./dvin_client", "-I/data/mec_channel/./v2x_client", "-I/data/mec_channel/./platform", "-I/data/mec_channel/./platform/genvict", "-I/data/mec_channel/./platform/genvict_sdk", "-I/data/mec_channel/./platform/debug", "-I/data/mec_channel/./platform/empty", "-I/data/mec_channel/./platform/baidu", "-I/data/mec_channel/./platform/boyuan", "-I/data/mec_channel/./platform/gosuncn", "-I/data/mec_channel/./platform/huawei", "-I/data/mec_channel/./platform/multi", "-I/data/mec_channel/./platform/tianan_obu", "-I/data/mec_channel/./platform/tianan", "-I/data/mec_channel/./platform/tianan/mec_protocol", "-I/data/mec_channel/./test", "-I/data/mec_channel/./app", "-O3", "-DNDEBUG", "--std=c++11", "-Wall", "-O3", "-Wno-psabi", "-c", "-o", "CMakeFiles/mec_channel.dir/platform/huawei/huawei_mec_protocol.cpp.o", "/data/mec_channel/platform/huawei/huawei_mec_protocol.cpp"], "directory": "/data/mec_channel", "file": "/data/mec_channel/platform/huawei/huawei_mec_protocol.cpp", "output": "/data/mec_channel/CMakeFiles/mec_channel.dir/platform/huawei/huawei_mec_protocol.cpp.o"}, {"arguments": ["/usr/local/bin/arm-linux-gnueabi-g++", "-DPLATFORM_ARM32", "-I/data/mec_channel/.", "-I/data/mec_channel/./mec_asn", "-I/data/mec_channel/./include", "-I/data/mec_channel/./engine_module/include", "-I/data/mec_channel/./engine_module/include/mosquitto", "-I/data/mec_channel/./engine_module/include/zmq", "-I/data/mec_channel/./engine_module/utility", "-I/data/mec_channel/./engine_module/signal", "-I/data/mec_channel/./engine_module/log", "-I/data/mec_channel/./engine_module/config", "-I/data/mec_channel/./engine_module/mem", "-I/data/mec_channel/./engine_module/engine", "-I/data/mec_channel/./engine_module/thread_resource", "-I/data/mec_channel/./mec_client", "-I/data/mec_channel/./mqtt_client", "-I/data/mec_channel/./dvin_client", "-I/data/mec_channel/./v2x_client", "-I/data/mec_channel/./platform", "-I/data/mec_channel/./platform/genvict", "-I/data/mec_channel/./platform/genvict_sdk", "-I/data/mec_channel/./platform/debug", "-I/data/mec_channel/./platform/empty", "-I/data/mec_channel/./platform/baidu", "-I/data/mec_channel/./platform/boyuan", "-I/data/mec_channel/./platform/gosuncn", "-I/data/mec_channel/./platform/huawei", "-I/data/mec_channel/./platform/multi", "-I/data/mec_channel/./platform/tianan_obu", "-I/data/mec_channel/./platform/tianan", "-I/data/mec_channel/./platform/tianan/mec_protocol", "-I/data/mec_channel/./test", "-I/data/mec_channel/./app", "-O3", "-DNDEBUG", "--std=c++11", "-Wall", "-O3", "-Wno-psabi", "-c", "-o", "CMakeFiles/mec_channel.dir/platform/multi/multi_mec_client.cpp.o", "/data/mec_channel/platform/multi/multi_mec_client.cpp"], "directory": "/data/mec_channel", "file": "/data/mec_channel/platform/multi/multi_mec_client.cpp", "output": "/data/mec_channel/CMakeFiles/mec_channel.dir/platform/multi/multi_mec_client.cpp.o"}, {"arguments": ["/usr/local/bin/arm-linux-gnueabi-g++", "-DPLATFORM_ARM32", "-I/data/mec_channel/.", "-I/data/mec_channel/./mec_asn", "-I/data/mec_channel/./include", "-I/data/mec_channel/./engine_module/include", "-I/data/mec_channel/./engine_module/include/mosquitto", "-I/data/mec_channel/./engine_module/include/zmq", "-I/data/mec_channel/./engine_module/utility", "-I/data/mec_channel/./engine_module/signal", "-I/data/mec_channel/./engine_module/log", "-I/data/mec_channel/./engine_module/config", "-I/data/mec_channel/./engine_module/mem", "-I/data/mec_channel/./engine_module/engine", "-I/data/mec_channel/./engine_module/thread_resource", "-I/data/mec_channel/./mec_client", "-I/data/mec_channel/./mqtt_client", "-I/data/mec_channel/./dvin_client", "-I/data/mec_channel/./v2x_client", "-I/data/mec_channel/./platform", "-I/data/mec_channel/./platform/genvict", "-I/data/mec_channel/./platform/genvict_sdk", "-I/data/mec_channel/./platform/debug", "-I/data/mec_channel/./platform/empty", "-I/data/mec_channel/./platform/baidu", "-I/data/mec_channel/./platform/boyuan", "-I/data/mec_channel/./platform/gosuncn", "-I/data/mec_channel/./platform/huawei", "-I/data/mec_channel/./platform/multi", "-I/data/mec_channel/./platform/tianan_obu", "-I/data/mec_channel/./platform/tianan", "-I/data/mec_channel/./platform/tianan/mec_protocol", "-I/data/mec_channel/./test", "-I/data/mec_channel/./app", "-O3", "-DNDEBUG", "--std=c++11", "-Wall", "-O3", "-Wno-psabi", "-c", "-o", "CMakeFiles/mec_channel.dir/platform/multi/multi_platform_client.cpp.o", "/data/mec_channel/platform/multi/multi_platform_client.cpp"], "directory": "/data/mec_channel", "file": "/data/mec_channel/platform/multi/multi_platform_client.cpp", "output": "/data/mec_channel/CMakeFiles/mec_channel.dir/platform/multi/multi_platform_client.cpp.o"}, {"arguments": ["/usr/local/bin/arm-linux-gnueabi-g++", "-DPLATFORM_ARM32", "-I/data/mec_channel/.", "-I/data/mec_channel/./mec_asn", "-I/data/mec_channel/./include", "-I/data/mec_channel/./engine_module/include", "-I/data/mec_channel/./engine_module/include/mosquitto", "-I/data/mec_channel/./engine_module/include/zmq", "-I/data/mec_channel/./engine_module/utility", "-I/data/mec_channel/./engine_module/signal", "-I/data/mec_channel/./engine_module/log", "-I/data/mec_channel/./engine_module/config", "-I/data/mec_channel/./engine_module/mem", "-I/data/mec_channel/./engine_module/engine", "-I/data/mec_channel/./engine_module/thread_resource", "-I/data/mec_channel/./mec_client", "-I/data/mec_channel/./mqtt_client", "-I/data/mec_channel/./dvin_client", "-I/data/mec_channel/./v2x_client", "-I/data/mec_channel/./platform", "-I/data/mec_channel/./platform/genvict", "-I/data/mec_channel/./platform/genvict_sdk", "-I/data/mec_channel/./platform/debug", "-I/data/mec_channel/./platform/empty", "-I/data/mec_channel/./platform/baidu", "-I/data/mec_channel/./platform/boyuan", "-I/data/mec_channel/./platform/gosuncn", "-I/data/mec_channel/./platform/huawei", "-I/data/mec_channel/./platform/multi", "-I/data/mec_channel/./platform/tianan_obu", "-I/data/mec_channel/./platform/tianan", "-I/data/mec_channel/./platform/tianan/mec_protocol", "-I/data/mec_channel/./test", "-I/data/mec_channel/./app", "-O3", "-DNDEBUG", "--std=c++11", "-Wall", "-O3", "-Wno-psabi", "-c", "-o", "CMakeFiles/mec_channel.dir/platform/tianan_obu/tianan_obu_http_client.cpp.o", "/data/mec_channel/platform/tianan_obu/tianan_obu_http_client.cpp"], "directory": "/data/mec_channel", "file": "/data/mec_channel/platform/tianan_obu/tianan_obu_http_client.cpp", "output": "/data/mec_channel/CMakeFiles/mec_channel.dir/platform/tianan_obu/tianan_obu_http_client.cpp.o"}, {"arguments": ["/usr/local/bin/arm-linux-gnueabi-g++", "-DPLATFORM_ARM32", "-I/data/mec_channel/.", "-I/data/mec_channel/./mec_asn", "-I/data/mec_channel/./include", "-I/data/mec_channel/./engine_module/include", "-I/data/mec_channel/./engine_module/include/mosquitto", "-I/data/mec_channel/./engine_module/include/zmq", "-I/data/mec_channel/./engine_module/utility", "-I/data/mec_channel/./engine_module/signal", "-I/data/mec_channel/./engine_module/log", "-I/data/mec_channel/./engine_module/config", "-I/data/mec_channel/./engine_module/mem", "-I/data/mec_channel/./engine_module/engine", "-I/data/mec_channel/./engine_module/thread_resource", "-I/data/mec_channel/./mec_client", "-I/data/mec_channel/./mqtt_client", "-I/data/mec_channel/./dvin_client", "-I/data/mec_channel/./v2x_client", "-I/data/mec_channel/./platform", "-I/data/mec_channel/./platform/genvict", "-I/data/mec_channel/./platform/genvict_sdk", "-I/data/mec_channel/./platform/debug", "-I/data/mec_channel/./platform/empty", "-I/data/mec_channel/./platform/baidu", "-I/data/mec_channel/./platform/boyuan", "-I/data/mec_channel/./platform/gosuncn", "-I/data/mec_channel/./platform/huawei", "-I/data/mec_channel/./platform/multi", "-I/data/mec_channel/./platform/tianan_obu", "-I/data/mec_channel/./platform/tianan", "-I/data/mec_channel/./platform/tianan/mec_protocol", "-I/data/mec_channel/./test", "-I/data/mec_channel/./app", "-O3", "-DNDEBUG", "--std=c++11", "-Wall", "-O3", "-Wno-psabi", "-c", "-o", "CMakeFiles/mec_channel.dir/platform/tianan_obu/tianan_obu_platform_client.cpp.o", "/data/mec_channel/platform/tianan_obu/tianan_obu_platform_client.cpp"], "directory": "/data/mec_channel", "file": "/data/mec_channel/platform/tianan_obu/tianan_obu_platform_client.cpp", "output": "/data/mec_channel/CMakeFiles/mec_channel.dir/platform/tianan_obu/tianan_obu_platform_client.cpp.o"}, {"arguments": ["/usr/local/bin/arm-linux-gnueabi-g++", "-DPLATFORM_ARM32", "-I/data/mec_channel/.", "-I/data/mec_channel/./mec_asn", "-I/data/mec_channel/./include", "-I/data/mec_channel/./engine_module/include", "-I/data/mec_channel/./engine_module/include/mosquitto", "-I/data/mec_channel/./engine_module/include/zmq", "-I/data/mec_channel/./engine_module/utility", "-I/data/mec_channel/./engine_module/signal", "-I/data/mec_channel/./engine_module/log", "-I/data/mec_channel/./engine_module/config", "-I/data/mec_channel/./engine_module/mem", "-I/data/mec_channel/./engine_module/engine", "-I/data/mec_channel/./engine_module/thread_resource", "-I/data/mec_channel/./mec_client", "-I/data/mec_channel/./mqtt_client", "-I/data/mec_channel/./dvin_client", "-I/data/mec_channel/./v2x_client", "-I/data/mec_channel/./platform", "-I/data/mec_channel/./platform/genvict", "-I/data/mec_channel/./platform/genvict_sdk", "-I/data/mec_channel/./platform/debug", "-I/data/mec_channel/./platform/empty", "-I/data/mec_channel/./platform/baidu", "-I/data/mec_channel/./platform/boyuan", "-I/data/mec_channel/./platform/gosuncn", "-I/data/mec_channel/./platform/huawei", "-I/data/mec_channel/./platform/multi", "-I/data/mec_channel/./platform/tianan_obu", "-I/data/mec_channel/./platform/tianan", "-I/data/mec_channel/./platform/tianan/mec_protocol", "-I/data/mec_channel/./test", "-I/data/mec_channel/./app", "-O3", "-DNDEBUG", "--std=c++11", "-Wall", "-O3", "-Wno-psabi", "-c", "-o", "CMakeFiles/mec_channel.dir/platform/tianan_obu/tianan_obu_protocal.cpp.o", "/data/mec_channel/platform/tianan_obu/tianan_obu_protocal.cpp"], "directory": "/data/mec_channel", "file": "/data/mec_channel/platform/tianan_obu/tianan_obu_protocal.cpp", "output": "/data/mec_channel/CMakeFiles/mec_channel.dir/platform/tianan_obu/tianan_obu_protocal.cpp.o"}, {"arguments": ["/usr/local/bin/arm-linux-gnueabi-g++", "-DPLATFORM_ARM32", "-I/data/mec_channel/.", "-I/data/mec_channel/./mec_asn", "-I/data/mec_channel/./include", "-I/data/mec_channel/./engine_module/include", "-I/data/mec_channel/./engine_module/include/mosquitto", "-I/data/mec_channel/./engine_module/include/zmq", "-I/data/mec_channel/./engine_module/utility", "-I/data/mec_channel/./engine_module/signal", "-I/data/mec_channel/./engine_module/log", "-I/data/mec_channel/./engine_module/config", "-I/data/mec_channel/./engine_module/mem", "-I/data/mec_channel/./engine_module/engine", "-I/data/mec_channel/./engine_module/thread_resource", "-I/data/mec_channel/./mec_client", "-I/data/mec_channel/./mqtt_client", "-I/data/mec_channel/./dvin_client", "-I/data/mec_channel/./v2x_client", "-I/data/mec_channel/./platform", "-I/data/mec_channel/./platform/genvict", "-I/data/mec_channel/./platform/genvict_sdk", "-I/data/mec_channel/./platform/debug", "-I/data/mec_channel/./platform/empty", "-I/data/mec_channel/./platform/baidu", "-I/data/mec_channel/./platform/boyuan", "-I/data/mec_channel/./platform/gosuncn", "-I/data/mec_channel/./platform/huawei", "-I/data/mec_channel/./platform/multi", "-I/data/mec_channel/./platform/tianan_obu", "-I/data/mec_channel/./platform/tianan", "-I/data/mec_channel/./platform/tianan/mec_protocol", "-I/data/mec_channel/./test", "-I/data/mec_channel/./app", "-O3", "-DNDEBUG", "--std=c++11", "-Wall", "-O3", "-Wno-psabi", "-c", "-o", "CMakeFiles/mec_channel.dir/platform/tianan_obu/tiannan_obu_mqtt_client.cpp.o", "/data/mec_channel/platform/tianan_obu/tiannan_obu_mqtt_client.cpp"], "directory": "/data/mec_channel", "file": "/data/mec_channel/platform/tianan_obu/tiannan_obu_mqtt_client.cpp", "output": "/data/mec_channel/CMakeFiles/mec_channel.dir/platform/tianan_obu/tiannan_obu_mqtt_client.cpp.o"}]