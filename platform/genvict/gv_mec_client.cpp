#include "gv_mec_client.h"
#include "stdafx.h"
#include "common_utility.h"
#include "service.h"



#define CHECK_CLIENT_INFO_FD(client_fd) \
    auto it_find = m_all_client.find(client_fd); \
    if(it_find == m_all_client.end()) \
    { \
        WLE("can not find client fd %d ",client_fd); \
        return RET_FAIL; \
    }\

#define CHECK_CLIENT_INFO_FD_NOT_RETCODE(client_fd) \
    auto it_find = m_all_client.find(client_fd); \
    if(it_find == m_all_client.end()) \
    { \
        WLE("can not find client fd %d ",client_fd); \
        return ; \
    }\


gv_mec_client::gv_mec_client()
{

    // v2x设备-注册命令处理函数
    auto & v2x_command = v2x_client_ref.get_command_pattern();

    //注册-上报OBU预警信息（06H）
    v2x_client_ref.get_rsp_report_command().regist_command("bsm",this,std::bind(&gv_mec_client::proc_v2x_bsm, FUN_BIND_5) );

    //注册-上报设备告警信息（07H）
    v2x_command.regist_command(rsp_type::RSP_DEVALARM,this,std::bind(&gv_mec_client::proc_v2x_devalarm_07H, FUN_BIND_2));

    //注册-上报扩展信息（0DH）
    v2x_command.regist_command(rsp_type::RSP_REPORT_EXTEND,this,std::bind(&gv_mec_client::proc_v2x_report_extend_0DH, FUN_BIND_2));



    // mec设备-注册指令处理函数 
    m_mec_command_pattern.regist_command(mec_command_enum::MEC_HEARTBEAT,this,std::bind(&gv_mec_client::proc_heartbeat,FUN_BIND_3));
    m_mec_command_pattern.regist_command(mec_command_enum::MEC_BSM,this,std::bind(&gv_mec_client::proc_bsm, FUN_BIND_3));
    m_mec_command_pattern.regist_command(mec_command_enum::MEC_RTE,this,std::bind(&gv_mec_client::proc_rte, FUN_BIND_3));
    m_mec_command_pattern.regist_command(mec_command_enum::MEC_RTS,this,std::bind(&gv_mec_client::proc_rts, FUN_BIND_3));
    m_mec_command_pattern.regist_command(mec_command_enum::MEC_RSM,this,std::bind(&gv_mec_client::proc_rsm, FUN_BIND_3));
    m_mec_command_pattern.regist_command(mec_command_enum::MEC_SPAT,this,std::bind(&gv_mec_client::proc_spat, FUN_BIND_3));
    m_mec_command_pattern.regist_command(mec_command_enum::MEC_MAP,this,std::bind(&gv_mec_client::proc_map, FUN_BIND_3));
    m_mec_command_pattern.regist_command(mec_command_enum::MEC_WARN,this,std::bind(&gv_mec_client::proc_warn, FUN_BIND_3));
    m_mec_command_pattern.regist_command(mec_command_enum::MEC_RTCM,this,std::bind(&gv_mec_client::proc_rtcm, FUN_BIND_3));
    m_mec_command_pattern.regist_command(mec_command_enum::MEC_QDZL,this,std::bind(&gv_mec_client::proc_qdzl, FUN_BIND_3));
    m_mec_command_pattern.regist_command(mec_command_enum::MEC_ACK,this,std::bind(&gv_mec_client::proc_ack, FUN_BIND_3));


    m_time_task.regist_task(std::bind(&gv_mec_client::ontime_sendheartbeat,FUN_BIND_1), 1000);// 1秒一次心跳
    m_time_task.regist_task(std::bind(&gv_mec_client::ontime_checkclient,FUN_BIND_1), 10000); // 10 秒检查一次客户端连接

}

gv_mec_client::~gv_mec_client()
{
	
}
	

int gv_mec_client::init()
{
    //注册 命令回调
    string ip = config_ref.get_def(CK_GV_MEC_IP,"127.0.0.1"); 
    int port = config_ref.get_def(CK_GV_MEC_PORT,30024);

    m_listen_handler.init();
    if(RET_OK != engine_ref.regist_tcp_service(this,ip,port,m_listen_handler))
    {
        WLE("engine_ref.regist_tcp_service error  ip %s port %d ",ip.c_str(),port); 
        return RET_FAIL;
    }

    WLI("regist_tcp_service success ip %s port %d ",ip.c_str(),port); 
    return RET_OK;
}

void gv_mec_client::stop()
{

}

void gv_mec_client::destory()
{
    engine_ref.unregist(this,m_listen_handler); 
    
}


void gv_mec_client::ontime_check(long long cur_ms)
{
    m_time_task.on_time(cur_ms);
}

int gv_mec_client::get_mec_status()
{
    int status = 0;
    if(m_all_client.empty() == false)
    {
        status = 1;
    }
    return status;
}

void gv_mec_client::accept_cb(engine_conn_handler & new_client_handler)
{
    WLI("new_client_handler.fd %d ",new_client_handler.fd);
    client_info_t info; 
    info.handler =  new_client_handler;
    info.last_heartbeat_sec = app_clock_ref.now_sec();

    m_all_client[new_client_handler.fd] =  info;
}

void gv_mec_client::read_cb(engine_conn_handler & handler, const char* msg, int len)
{
    CHECK_CLIENT_INFO_FD_NOT_RETCODE(handler.fd);
    client_info_t & client_info  = it_find->second;

    list<mec_gv_frame> frame_list;
    client_info.protocol.fetch_frame(msg,len,frame_list);
    if(frame_list.empty())
    {
        return ;
    }

    for(auto & frame: frame_list)
    {
        WLD("proc commend %d",frame.head.low_command); 
        m_mec_command_pattern.do_command((mec_command_enum)frame.head.low_command,handler.fd,frame);
    }
}

void gv_mec_client::event_cb(int fd, short events )
{
    WLD("fd %d events %d",fd,events );
    if (events & BEV_EVENT_ERROR)
    {
        WLE("fd %d BEV_EVENT_ERROR %s",fd,strerror(EVUTIL_SOCKET_ERROR()));
        auto it_find = m_all_client.find(fd); 
        if(it_find != m_all_client.end())
        {
            m_all_client.erase(it_find);
        }
    }
}

int gv_mec_client::proc_v2x_bsm(const string & type,uint8_t &direct_i,string & s_json,string & str_value_json, shared_ptr<json> & value_json_obj)
{
    //WLD("s_json %s ",s_json.c_str());
    //WLD("str_value_json %s ",str_value_json.c_str());
    return send_msg(mec_command_enum::MEC_BSM,s_json);
}


int gv_mec_client::proc_v2x_devalarm_07H(const rsp_type & type,v2x_client_respond & frame)
{
    auto j = nlohmann::json{
            {"type", "warning"},
            {"value", ""}
        };
    auto j_v = nlohmann::json{
       {"reboot",1}
    };
    j["value"] = j_v.dump();
    string str_j = j.dump();
    return send_msg(mec_command_enum::MEC_WARN,str_j);
}

int gv_mec_client::proc_v2x_report_extend_0DH(const rsp_type & type,v2x_client_respond & frame)
{
    string str_json;
    str_json.assign((char*)frame.data.reportExtend.json,frame.data.reportExtend.data_len);
    return send_msg(mec_command_enum::MEC_QDZL,str_json);
}


int gv_mec_client::proc_heartbeat(const mec_command_enum & command,int client_fd , mec_gv_frame & frame)
{
    CHECK_CLIENT_INFO_FD(client_fd);
    client_info_t & client_info  = it_find->second;
    client_info.last_heartbeat_sec =  app_clock_ref.now_sec();
    return RET_OK;
}

int gv_mec_client::proc_bsm(const mec_command_enum & command,int client_fd , mec_gv_frame & frame)
{
    WLI("fd %d , no rsp ", client_fd);
    return RET_OK;
}

int gv_mec_client::proc_rte(const mec_command_enum & command,int client_fd , mec_gv_frame & frame)
{
    CHECK_CLIENT_INFO_FD(client_fd);
    client_info_t & client_info  = it_find->second;
    send_ack(client_info,frame);
    v2x_client_ref.send_cv2x_json_81H((char*)frame.data.c_str(),frame.data.size());
    return RET_OK;
}

int gv_mec_client::proc_rts(const mec_command_enum & command,int client_fd , mec_gv_frame & frame)
{
    CHECK_CLIENT_INFO_FD(client_fd);
    client_info_t & client_info  = it_find->second;
    send_ack(client_info,frame);
    v2x_client_ref.send_cv2x_json_81H((char*)frame.data.c_str(),frame.data.size());
    return RET_OK;
}

int gv_mec_client::proc_rsm(const mec_command_enum & command,int client_fd , mec_gv_frame & frame)
{
    CHECK_CLIENT_INFO_FD(client_fd);
    v2x_client_ref.send_cv2x_json_81H((char*)frame.data.c_str(),frame.data.size());
    return RET_OK;
}


int gv_mec_client::proc_spat(const mec_command_enum & command,int client_fd , mec_gv_frame & frame)
{
    CHECK_CLIENT_INFO_FD(client_fd);
    v2x_client_ref.send_cv2x_json_81H((char*)frame.data.c_str(),frame.data.size());
    return RET_OK;
}


int gv_mec_client::proc_map(const mec_command_enum & command,int client_fd , mec_gv_frame & frame)
{
    CHECK_CLIENT_INFO_FD(client_fd);
    client_info_t & client_info  = it_find->second;
    send_ack(client_info,frame);
    v2x_client_ref.send_cv2x_json_81H((char*)frame.data.c_str(),frame.data.size());
    return RET_OK;
}

int gv_mec_client::proc_warn(const mec_command_enum & command,int client_fd , mec_gv_frame & frame)
{
    v2x_client_ref.send_last_command_ack();
    return RET_OK;
}

int gv_mec_client::proc_rtcm(const mec_command_enum & command,int client_fd , mec_gv_frame & frame)
{
    CHECK_CLIENT_INFO_FD(client_fd);
    v2x_client_ref.send_cv2x_json_81H((char*)frame.data.c_str(),frame.data.size());
    return RET_OK;
}

int gv_mec_client::proc_qdzl(const mec_command_enum & command,int client_fd , mec_gv_frame & frame)
{
    CHECK_CLIENT_INFO_FD(client_fd);
    v2x_client_ref.send_cv2x_extend_8DH(v2x_opt_enum::c8d_msg_format_json,(char*)frame.data.c_str(),frame.data.size());
    return RET_OK;
}

int gv_mec_client::proc_ack(const mec_command_enum & command,int client_fd , mec_gv_frame & frame)
{
    v2x_client_ref.send_last_command_ack();
    return RET_OK;
}

int gv_mec_client::send_msg(uint8_t command,string &json_data)
{
    int frame_len = m_protocol.bulid_frame(command,json_data,m_send_buffer);
    for(auto & client_info_it:m_all_client)
    {
        auto & client_info = client_info_it.second;
        engine_ref.send_tcp_data(client_info.handler,m_send_buffer.c_str(),frame_len);
    }
    return RET_OK;
}

int gv_mec_client::send_ack(client_info_t & client_info,mec_gv_frame & frame )
{
    int frame_len = client_info.protocol.build_ack_frame(frame.head.seq_num, client_info.send_buf );
    engine_ref.send_tcp_data(client_info.handler,client_info.send_buf.c_str(),frame_len);
    return RET_OK;
}

void gv_mec_client::ontime_sendheartbeat(long long cur_ms)
{
    if(m_all_client.empty())
    {
        return ;
    }
    string send_buf;
    int frame_len = m_protocol.build_heartbeat_frame(send_buf);
    for(auto &client_info : m_all_client)
    {
        engine_ref.send_tcp_data(client_info.second.handler,send_buf.c_str(),frame_len);
    }
}

void gv_mec_client::ontime_checkclient(long long cur_ms)
{
    auto cur_time_sec =  app_clock_ref.now_sec();

    auto all_client_map_tmp =  m_all_client; 
    for(auto & client_info_it : all_client_map_tmp)
    {
        auto & client_info = client_info_it.second;
        if(cur_time_sec - client_info.last_heartbeat_sec >= 300)
        {// 超过 5 分钟无心跳，则认为对端异常中断，需要断开连接

            WLI("erase fd %d ",client_info.handler.fd);
            engine_ref.unregist(this,client_info.handler);
            m_all_client.erase(client_info_it.first);
        }
    }
}
