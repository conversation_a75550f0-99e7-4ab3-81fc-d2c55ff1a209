/***********************************************
版本信息： 金溢科技版本权所有，保留一切权利
文件名称： gv_mqtt_client.h
作者：张明
开始日期：2023-08-01 09:20:42
完成日期：
当前版本号: v 0.0.1
主要功能: 
版本历史:
***********************************************/

#ifndef _GV_MQTT_H_
#define _GV_MQTT_H_

#include "icloud_platform_client.h"
#include "gv_mqtt_client.h"
#include "gv_http_client.h"
#include "gv_mqtt_protocol.h"

class gv_platform_client:public icloud_platform_client
{
public:
	gv_platform_client();
	virtual ~gv_platform_client();
private:
	virtual int init() override;
	virtual int destory() override;
	virtual void get_cloud_platform_id(string &id) override;
	virtual int get_cloud_platform_status() override;

	virtual void ontime_check(long long cur_ms)override;
private:
	gv_mqtt_protocol m_protocol;
	gv_http_client m_cloud_platform;
	gv_mqtt_client m_mqtt_client;



};
#endif //_GV_MQTT_H_
