#include "gv_mec_protocol.h"
#include "stdafx.h"
#include <arpa/inet.h>
#include "common_utility.h"
#include "engine_module_macro.h"
#include "engine_module_log.h"
const int MEC_GV_HEAD_SIZE = sizeof(mec_gv_head);
const int MEC_GV_TAIL_SIZE = sizeof(mec_gv_tail);

gv_mec_protocol::gv_mec_protocol()
{

}

gv_mec_protocol::~gv_mec_protocol()
{
	
}
	
int gv_mec_protocol::bulid_frame(const uint8_t command , const string & json_str, string & send_buf)
{
    int json_len = json_str.length();
    int frame_len =  make_sure_send_buf_size(json_len,send_buf);
    char *pdata =  (char*)send_buf.data();
    mec_gv_head & head  = *(mec_gv_head*)pdata; 
    head.magic_stx[0] = 'S';
    head.magic_stx[1] = 'T';
    head.hight_command = 0; 
    head.low_command =  command;
    head.seq_num = htonl(m_seq_num++);
    head.data_len = htonl(json_len);
    char *send_data = pdata + MEC_GV_HEAD_SIZE; 
    memcpy(send_data,json_str.c_str(),json_len);

    // calc crl 
    char *crc_data =  (pdata+sizeof(head.magic_stx));
    int crc_len =  MEC_GV_HEAD_SIZE - sizeof(head.magic_stx) + json_len;
    mec_gv_tail & tail =  *(mec_gv_tail*)(pdata+MEC_GV_HEAD_SIZE + json_len); 
    tail.crc = htons(m_crc16.encode_x25(crc_data,crc_len) );
    tail.magix_etx[0] = 'E';
    tail.magix_etx[1] = 'T';

    return frame_len;
}

int gv_mec_protocol::fetch_frame(const char* pdata, uint32_t len,list<mec_gv_frame> &frame_list)
{
    if(m_left_buffer.size()!=0)
    {
        m_left_buffer.append(pdata,len);
        m_fetch_buffer.assign(move(m_left_buffer));
        m_left_buffer =  "";
        len = m_fetch_buffer.size();
        pdata = (const char*)m_fetch_buffer.c_str();
    }
    do
    {
        // 处理多帧
        if(MEC_GV_HEAD_SIZE > len)
        {
            // 回包不足一帧
            m_left_buffer.append(pdata,len);
            break;
        }
        // copy 头，可能重复转码
        
        auto head = *(mec_gv_head*)(pdata);
        ntoh_head(head);
        if(false == check_head(head))
        {
            m_left_buffer = "";
            break;
        }
        // 一帧数据 =  头大小 + data_len + 尾
        uint32_t frame_len =  MEC_GV_HEAD_SIZE +  head.data_len + MEC_GV_TAIL_SIZE;
        if(frame_len > len)
        { //不足一帧，则缓存
            m_left_buffer.append(pdata,len);
            break;
        }

        // 检查 crc 
        const char *crc_data =  (pdata+sizeof(head.magic_stx));
        int crc_len =  MEC_GV_HEAD_SIZE - sizeof(head.magic_stx) + head.data_len;
        uint16_t crc_code = m_crc16.encode_x25(crc_data,crc_len); 

        auto tail = *(mec_gv_tail*)(pdata+MEC_GV_HEAD_SIZE+head.data_len);
        ntoh_tail(tail);
        if(false ==check_tail(crc_code,tail))
        {
            m_left_buffer = "";
            break;
        }

        mec_gv_frame rsp;
        rsp.head = head;
        rsp.data.assign(pdata+MEC_GV_HEAD_SIZE,head.data_len);
        frame_list.emplace_back(std::move(rsp));

        len = len - frame_len;
        pdata =  pdata + frame_len;
    }
    while(len > 0 );

    if(frame_list.empty())
    {
        return RET_FAIL;
    }
    return RET_OK;
}

void gv_mec_protocol::ntoh_head(mec_gv_head & head )
{
    head.seq_num =  ntohl(head.seq_num);
    head.data_len =  ntohl(head.data_len);   
}
void gv_mec_protocol::ntoh_tail(mec_gv_tail & tail )
{
    tail.crc =  ntohs(tail.crc);
}

bool gv_mec_protocol::check_head(mec_gv_head & head)
{
    if(head.magic_stx[0] != 'S' || head.magic_stx[1] != 'T')
    {
        WLE(" error head.magic_stx not ST %c%c ",head.magic_stx[0],head.magic_stx[1]);
        return false;
    }
    return true;
}
bool gv_mec_protocol::check_tail(uint16_t crc ,mec_gv_tail &tail)
{
    if(crc != tail.crc)
    {
        WLE("crc error cal crc %d tail.crc %d ",crc, tail.crc);
        return false;
    }
    if(tail.magix_etx[0] != 'E' || tail.magix_etx[1] != 'T')
    {
        WLE("tail.magix_etx not ET invlid %c%c ",tail.magix_etx[0],tail.magix_etx[1]);

        return false;
    }
    return true;
}

int gv_mec_protocol::make_sure_send_buf_size(int len,string &send_buf)
{
    size_t min_len = MEC_GV_HEAD_SIZE + MEC_GV_TAIL_SIZE + len;
    if(send_buf.size() < min_len)
    {
        send_buf.resize(min_len);
    }
    return min_len;
}

int gv_mec_protocol::build_heartbeat_frame(string &data)
{
    string heart_beat_json;
    get_heartbeat_json(heart_beat_json);
    int frame_len = bulid_frame(mec_command_enum::MEC_HEARTBEAT,heart_beat_json,data);
    return frame_len;
}


int gv_mec_protocol::build_ack_frame(int sn,string &data)
{
    string ack_json = get_ack_json(sn);
    int frame_len = bulid_frame(mec_command_enum::MEC_ACK,ack_json,data);
    return frame_len;
}

int gv_mec_protocol::get_heartbeat_json(string & data)
{
    auto j = nlohmann::json{
            {"type", "heartbeat"},
            {"value", ""}
        };
    auto j_v = nlohmann::json{
       {"time",get_us()}
    };
    j["value"] = j_v.dump();
    data = j.dump();
    return RET_OK;
}

string gv_mec_protocol::get_ack_json(int sn)
{
    string str;

    auto j = nlohmann::json{
            {"type", "ack"},
            {"value", ""}
        };
    auto j_v = nlohmann::json::object(); 
    j_v["seqnum"] = sn; 
    j_v["errCode"] = 0; 
    j_v["errDesc"] = ""; 
    j["value"] = j_v.dump();
    str = j.dump();
    return str;
}

