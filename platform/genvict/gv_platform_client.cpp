#include "gv_platform_client.h"
#include "stdafx.h"
#include <functional>
using namespace std;


gv_platform_client::gv_platform_client():m_cloud_platform(m_protocol),m_mqtt_client(m_protocol)
{
    auto fun =  std::bind(&gv_mqtt_client::cloudid_cb, &m_mqtt_client, std::placeholders::_1); 
    m_cloud_platform.regist_cloudid_cb(fun);
}


gv_platform_client::~gv_platform_client()
{
    destory();
}
	
int gv_platform_client::init()
{
    if(RET_OK != m_cloud_platform.init())
    {
        WLE("m_cloud_platform.init error ");
        return RET_FAIL;
    }

    if(RET_OK != m_mqtt_client.init())
    {
        WLE("m_mqtt_client.init error ");
        return RET_FAIL;
    }
    return RET_OK;
}
int gv_platform_client::destory()
{
    m_mqtt_client.destory();
    m_cloud_platform.destory();
    return RET_OK;
}
void gv_platform_client::ontime_check(long long cur_ms)
{
    m_cloud_platform.ontime_check(cur_ms);
    m_mqtt_client.ontime_check(cur_ms);
}

void gv_platform_client::get_cloud_platform_id(string &id)
{
    m_cloud_platform.get_cloudid(id);
}
// 云平台连接状态，0：未连接  非0:连接
int gv_platform_client::get_cloud_platform_status()
{
    string id ;
    m_cloud_platform.get_cloudid(id);
    if(id.size()> 0 )
    {
        return 1;
    }
    return 0;
}

