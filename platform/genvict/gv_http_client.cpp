#include "gv_http_client.h"
#include "service.h"
#include "stdafx.h"

gv_http_client::gv_http_client(gv_mqtt_protocol &protocol):m_protocol(protocol),m_status_control(status_enum::logout,"gv_http_client",this)
{
    m_status_control.regist_status(status_enum::logout, &gv_http_client::proc_status_logout, "logout", 0);
    m_status_control.regist_status(status_enum::login, &gv_http_client::proc_status_login, "login", 0);
    m_status_control.regist_status(status_enum::login_send, &gv_http_client::proc_status_login_send, "login_send", 10000);
    m_status_control.regist_status(status_enum::keep_alive, &gv_http_client::proc_status_keep_alive, "keep_alive", 3600000); // 一小时执行一次此时，
    
}

gv_http_client::~gv_http_client()
{
    destory();
}
int gv_http_client::init()
{
    //向云平台申请ID
    string regist_url;
    m_protocol.get_regist_url(regist_url);
    
    if(RET_OK != engine_ref.regist_http_client(this,regist_url,m_client_handler))
    {
        WLE("engine_ref.regist_http_client error url %s ",regist_url.c_str());
        return RET_FAIL;
    }

    http_req req; 
    m_protocol.build_regist_http_req(req);
    if(RET_OK != engine_ref.http_post(m_client_handler,req))
    {
        WLE("engine_ref.http_post error url %s ", regist_url.c_str());
        return RET_FAIL; 
    }
    m_status_control.enter_status(status_enum::login_send);

    return RET_OK;
}
int gv_http_client::destory()
{
    engine_ref.unregist(this,m_client_handler);
    return RET_OK;
}
void gv_http_client::ontime_check(long long cur_ms)
{
    m_status_control.call_status_fun(cur_ms);
}
	
void gv_http_client::update_conn_handler(engine_conn_handler & new_client_handler)
{
    m_client_handler =  new_client_handler;
}


int gv_http_client::get_conn_fd(void *conn_handler_data) 
{
    return m_client_handler.fd;
}

void gv_http_client::get_cloudid(string &cloud_id)
{
    cloud_id =  m_cloudid;
}


void gv_http_client::read_cb(engine_conn_handler & handler, const char* msg, int len)
{
    string cloudid;
    if(RET_OK == m_protocol.fetch_http_rsp_cloudid(msg,len,cloudid))
    {
        m_cloudid =  cloudid;
        if(m_notice_fun)
        {
            m_notice_fun(cloudid);
        }
        m_status_control.enter_status(status_enum::login);
    }
}

void gv_http_client::event_cb(int fd, short events )
{
    WLD("fd %d evenets %d ",fd, events) ;
    if(events == BEV_EVENT_ERROR )
    {// http 需要处理
        m_client_handler.init();
    }
    
}

//=========================================================
// 状态处理函数

int gv_http_client::proc_status_logout(status_info & status)
{
    destory();
    init();
    return RET_OK;
}

int gv_http_client::proc_status_login_send(status_info & status)
{
    m_status_control.enter_status(status_enum::logout);
    return RET_OK;
}


// 成功获取云平台ID后断开连接，不再需要http连接
int gv_http_client::proc_status_login(status_info & status)
{
    //destory();
    engine_ref.unregist(this,m_client_handler);
    m_status_control.enter_status(status_enum::keep_alive);
    return RET_OK;
}

// keep_alive 不做什么
int gv_http_client::proc_status_keep_alive(status_info & status)
{
    m_status_control.enter_status(status_enum::keep_alive);
    return RET_OK;
}
