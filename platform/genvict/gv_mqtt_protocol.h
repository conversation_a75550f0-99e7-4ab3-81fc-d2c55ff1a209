/***********************************************
版本信息： 金溢科技版本权所有，保留一切权利
文件名称： gv_mqtt_protocol.h
作者：张明
开始日期：2023-08-01 08:43:10
完成日期：
当前版本号: v 0.0.1
主要功能: 
版本历史:
***********************************************/

#ifndef _GV_MQTT_PROTOCOL_H_
#define _GV_MQTT_PROTOCOL_H_
#include "iengine.h"

struct frame_power_down
{
	int seqNum; 
	int time; // 0：立即生效；>0：UTC时间
	int power_type; // 1 关机  0重启
};
struct frame_ota_down
{
	string downloadUrl; 
	string downloadMd5;
};


class gv_mqtt_protocol
{
public:
	gv_mqtt_protocol();
	~gv_mqtt_protocol();
	int get_regist_url(string & url);


	int fetch_v2x_json();
	// clould platform 
	int build_regist_http_req(http_req & req);
	int fetch_http_rsp_cloudid( const char* frame, int frame_len,string & cloudid);

	// mqtt 
	int fetch_mqtt_power_down(const char *frame,int frame_len,frame_power_down &power_down );
	int build_mqtt_power_down_ack(frame_power_down &power_down,string &req);

	int build_mqtt_heartbeat(string & req);

	int fetch_mqtt_config_query_down(const char *frame,int frame_len,uint64_t & session_id);

	int build_mqtt_config_up(const char *v2x_json,uint64_t  session_id,string &req);

	int fetch_type_value_json(const char *strjson, string &type, string &value);

	int fetch_ota_down(const char *strjson, frame_ota_down & ota_info);

	int build_mqtt_perf_up(const char *monitor_json,string &sperf_up_json); 

private:
	int build_http_head(http_req & req);

private:
	long m_nreqid {1};
	int m_v2x_dev_type;
	int m_edgeid;
	string m_esn; 
	string m_gantryno;
	string m_cloudid;
	

};
#endif //_GV_MQTT_PROTOCOL_H_
