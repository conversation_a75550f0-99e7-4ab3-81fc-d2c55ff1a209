/***********************************************
版本信息： 金溢科技版本权所有，保留一切权利
文件名称： gv_mqtt_client.h
作者：张明
开始日期：2023-06-25 14:24:55
完成日期：
当前版本号: v 0.0.1
主要功能: 金溢mqtt 客户端
版本历史:
***********************************************/

#ifndef _GV_MQTT_CLIENT_H_
#define _GV_MQTT_CLIENT_H_

#include "icloud_platform_client.h"
#include "iengine.h"
#include "ireceiver.h"
#include "gv_mqtt_protocol.h"
#include "v2x_client.h"
#include "status_control.hpp"// 引入 状态管理，进行重新，关注，上报各种状态处理
#include "command_pattern.hpp"

class gv_mqtt_client : public imqttclient_receiver
{
	typedef status_control<gv_mqtt_client,status_enum> status_control_type;
    typedef status_control_type::status_info  status_info;
public:
	gv_mqtt_client(gv_mqtt_protocol &protocol);
	~gv_mqtt_client();

	void cloudid_cb(string & cloudid);


public:
	virtual int init() ;
	virtual int destory() ;
	virtual void ontime_check(long long cur_ms) ;
public: // interface ireceiver
    virtual void read_cb(engine_conn_handler & handler, const char* msg, int len) override;
    virtual void event_cb(int fd, short events ) override; 	
public: // imqttclient_receiver
	virtual void message_cb(const struct mosquitto_message *message,const mosquitto_property *properties) override; 

public: //状态管理函数
	int proc_status_logout(status_info & status);
    int proc_status_disconnect(status_info & status);
    int proc_status_connected(status_info & status);
	int proc_status_heartbeat_send(status_info & status);
private://mqtt 相关命令处理
	int init_mqtt_topic_proc_fun();
	
	int proc_topic_ota_down(const string &topic, char * payload, int payload_len); 
	int proc_topic_config_down(const string &topic, char * payload, int payload_len); 
	int proc_topic_rtcm_down(const string &topic, char * payload, int payload_len); 
	int proc_topic_rte_down(const string &topic, char * payload, int payload_len); 
	int proc_topic_rts_down(const string &topic, char * payload, int payload_len); 
	int proc_topic_power_down(const string &topic, char * payload, int payload_len); 
	int proc_topic_config_query_down(const string &topic, char * payload, int payload_len); 

private:
	int proc_v2x_report_05H(const string & type,uint8_t &direct_i,string & s_json,string & str_value_json,shared_ptr<json> & value_json_obj);
	int proc_rsp_obu_alarm_06H(const rsp_type & type,v2x_client_respond & frame);
	int proc_rsu_debug_report_0AH(const rsp_type & type,v2x_client_respond & frame);
	int proc_rsu_config_report_0CH(const rsp_type & type,v2x_client_respond & frame);
	int proc_v2x_report_monitor_0EH(const rsp_type & type,v2x_client_respond & frame); 
private:
	string get_mqtt_topic_prefix();

	bool get_report_topic(const string &type,uint8_t &direct_i,string & topic);
	


private:
	command_pattern <string, char*, int > m_topic_commander ; 

	gv_mqtt_protocol &m_protocol;
	status_control_type m_status_control;
	mqtt_regist_info m_regist_info;
	engine_conn_handler m_mqtt_handler;
	
	//string m_regist_url;
	int m_v2x_dev_type ; 
	string m_cloudid; ///<云平台 ID 
	uint64_t m_config_query_down_session{0};

};
#endif //_GV_MQTT_CLIENT_H_
