/***********************************************
版本信息： 金溢科技版本权所有，保留一切权利
文件名称： gv_mec_client.h
作者：张明
开始日期：2023-09-19 10:15:49
完成日期：
当前版本号: v 0.0.1
主要功能: 金溢MEC接口
版本历史:
***********************************************/

#ifndef _GV_MEC_CLIENT_H_
#define _GV_MEC_CLIENT_H_
#include "iengine.h"
#include "imec_client.h"
#include "gv_mec_protocol.h"
#include "v2x_protocol.h"
#include "log.h"
#include "time_task.h"
#include <map>
#include "command_pattern.hpp"
#include "nlohmann/json.hpp"
#include "common_utility.h"
using json = nlohmann::json;

using namespace std;


class gv_mec_client final :public imec_client, public itcpservice_receiver
{
    struct client_info_t
    {//每个客户端一个协议解释器，
        engine_conn_handler handler;
        gv_mec_protocol protocol;
        int64_t last_heartbeat_sec {0};
        string send_buf;
    };
    typedef command_pattern<mec_command_enum,int , mec_gv_frame>  mec_gv_command_type;
public:
	gv_mec_client();
	virtual ~gv_mec_client();

    int send_msg(uint8_t command,string &json_data);

public:	//imec_client interface
    virtual int init() override;
    virtual void stop() override;
    virtual void destory() override;
    virtual void ontime_check(long long cur_ms) override;
    virtual int get_mec_status() override;
public:// ireceiver
    virtual void accept_cb(engine_conn_handler & new_client_handler) override;
    virtual void read_cb(engine_conn_handler & handler, const char* msg, int len) override;
    virtual void event_cb(int fd, short events ) override; 
private:// 处理 v2x 设备上报的 消息
    int proc_v2x_devalarm_07H(const rsp_type & type,v2x_client_respond & frame);
    int proc_v2x_report_extend_0DH(const rsp_type & type,v2x_client_respond & frame);
    int proc_v2x_bsm(const string & type,uint8_t &direct_i,string & s_json,string & str_value_json, shared_ptr<json> & value_json_obj);

private: // 处理 mec 下发的命令
    int proc_heartbeat(const mec_command_enum & command,int client_fd , mec_gv_frame & frame);
    int proc_bsm(const mec_command_enum & command,int client_fd , mec_gv_frame & frame);
    int proc_rte(const mec_command_enum & command,int client_fd , mec_gv_frame & frame);
    int proc_rts(const mec_command_enum & command,int client_fd , mec_gv_frame & frame);
    int proc_rsm(const mec_command_enum & command,int client_fd , mec_gv_frame & frame);
    int proc_spat(const mec_command_enum & command,int client_fd , mec_gv_frame & frame);
    int proc_map(const mec_command_enum & command,int client_fd , mec_gv_frame & frame);
    int proc_warn(const mec_command_enum & command,int client_fd , mec_gv_frame & frame);
    int proc_rtcm(const mec_command_enum & command,int client_fd , mec_gv_frame & frame);
    int proc_qdzl(const mec_command_enum & command,int client_fd , mec_gv_frame & frame);
    int proc_ack(const mec_command_enum & command,int client_fd , mec_gv_frame & frame);
private:
    int send_ack(client_info_t & client_info,mec_gv_frame & frame ); 
private:
    void ontime_sendheartbeat(long long cur_ms);
    void ontime_checkclient(long long cur_ms);

private:
	engine_conn_handler  m_listen_handler;
    map<int,client_info_t> m_all_client;
    mec_gv_command_type m_mec_command_pattern;
    time_task m_time_task; 
    gv_mec_protocol m_protocol; // 广播用
    string m_send_buffer; // 广播用

};
#endif //_GV_MEC_CLIENT_H_
