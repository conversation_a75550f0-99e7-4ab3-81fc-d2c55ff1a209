/***********************************************
版本信息： 金溢科技版本权所有，保留一切权利
文件名称： gv_mec_protocol.h
作者：张明
开始日期：2023-08-15 16:00:30
完成日期：
当前版本号: v 0.0.1
主要功能: 金溢-mec 通信协议
版本历史:
***********************************************/

#ifndef _GV_MEC_PROTOCOL_H_
#define _GV_MEC_PROTOCOL_H_
#include "stdafx.h"
#include <stdint.h>
#include <string>
#include <list>
#include "crc16.h"
#include "nlohmann/json.hpp"
using json = nlohmann::json;
using namespace std;

enum mec_command_enum
{
    MEC_HEARTBEAT = 0,  
    MEC_BSM = 1,        
    MEC_RTE = 2,        
    MEC_RTS = 3,         
    MEC_RSM = 4,         
    MEC_SPAT = 5, 
    MEC_MAP = 6,
    MEC_WARN = 7,
    MEC_RTCM = 8,
    MEC_ECI = 9,
    MEC_EVI = 10,
    MEC_QDZL = 30,  ///<青岛中量项目透传
    MEC_ACK = 50,
}; 


#pragma pack (1)
struct mec_gv_head
{
	uint8_t magic_stx[2];    //2 byte	魔术字-协议包开始字段 ST
    uint8_t hight_command;
	uint8_t low_command;
    uint32_t seq_num;  
	uint32_t data_len; //报文净荷，详见“数据内容示例”
};

struct mec_gv_tail
{
	uint16_t crc; 
	uint8_t magix_etx[2];
};
#pragma pack()
struct mec_gv_frame
{
	mec_gv_head head;
	string data;
}; 

class gv_mec_protocol
{
public:
	gv_mec_protocol();
	~gv_mec_protocol();
	int fetch_frame(const char* pdata, uint32_t len,list<mec_gv_frame> &frame_list);
	int bulid_frame(const uint8_t command , const string & json_str, string &send_buf );

	int build_heartbeat_frame(string &data);
	int build_ack_frame(int sn,string &data); 

private:
	int make_sure_send_buf_size(int len,string &send_buf);
	void ntoh_head(mec_gv_head & head );
	void ntoh_tail(mec_gv_tail & tail );
	bool check_head(mec_gv_head & head);
	bool check_tail(uint16_t crc ,mec_gv_tail &tail);
	
	int get_heartbeat_json(string & data); 
	string get_ack_json(int sn);
	
private:
	uint32_t m_seq_num{0}; 
	crc16 m_crc16;
	string m_fetch_buffer; // 必须使用双缓冲（m_fetch_buffer+m_left_buffer），因为 fetch_body 会返回 m_fetch_buffer 内数据
    string m_left_buffer;  //
};
#endif //_GV_MEC_PROTOCOL_H_
