#include "gv_mqtt_client.h"
#include "stdafx.h"
#include "event2/event.h"
#include "event2/bufferevent.h"
#include "service.h"
#include "common_utility.h"

gv_mqtt_client::gv_mqtt_client(gv_mqtt_protocol &protocol):m_protocol(protocol),m_status_control(status_enum::logout,"gv_mqtt_client_status",this)
{
    config_ref.get(CK_V2X_TYPE,m_v2x_dev_type);

    m_regist_info.tls = config_ref.get_def(GET_GV_MQTT_KEY(MQTT_TLS),false);
    config_ref.get(GET_GV_MQTT_KEY(MQTT_CLIENT_ID),m_regist_info.client_id); 
    config_ref.get(GET_GV_MQTT_KEY(MQTT_HOST),m_regist_info.hostname);
    config_ref.get(GET_GV_MQTT_KEY(MQTT_PORT),m_regist_info.port);
    config_ref.get(GET_GV_MQTT_KEY(MQTT_CA_PATH),m_regist_info.cafile);
    config_ref.get(GET_GV_MQTT_KEY(MQTT_USER),m_regist_info.username);   
    config_ref.get(GET_GV_MQTT_KEY(MQTT_PASSWD),m_regist_info.passwd);

    int reconnect_interval_ms = 3000;
    int heart_send_timeout_ms = 60000;

    m_status_control.regist_status(status_enum::logout, &gv_mqtt_client::proc_status_logout, "logout", reconnect_interval_ms);
    m_status_control.regist_status(status_enum::disconnect, &gv_mqtt_client::proc_status_disconnect, "disconnect", reconnect_interval_ms);
    m_status_control.regist_status(status_enum::connected, &gv_mqtt_client::proc_status_connected, "connected", 0);
    m_status_control.regist_status(status_enum::heartbeat_send, &gv_mqtt_client::proc_status_heartbeat_send, "heart_send", heart_send_timeout_ms);


    // 向 v2x_client 注册 回调
    auto & v2x_command = v2x_client_ref.get_command_pattern();

    //注册-上报OBU预警信息（06H）
    v2x_command.regist_command(rsp_type::RSP_OBU_ALARM,this,std::bind(&gv_mqtt_client::proc_rsp_obu_alarm_06H, FUN_BIND_2));

    //注册-上报调试信息（0AH）
    v2x_command.regist_command(rsp_type::RSP_DEBUG_REPORT,this,std::bind(&gv_mqtt_client::proc_rsu_debug_report_0AH, FUN_BIND_2));

    //注册-上报扩展信息（0CH）
    v2x_command.regist_command(rsp_type::RSP_CONFIG_REPORT,this,std::bind(&gv_mqtt_client::proc_rsu_config_report_0CH, FUN_BIND_2));

    //注册-上报消息（05H）
    auto & rsu_report_command  = v2x_client_ref.get_rsp_report_command(); 
    rsu_report_command.regist_all_command(this,std::bind(&gv_mqtt_client::proc_v2x_report_05H, FUN_BIND_5) );

    //注册-上报监控数据(0EH)
    v2x_command.regist_command(rsp_type::RSP_REPORT_MONITOR,this,std::bind(&gv_mqtt_client::proc_v2x_report_monitor_0EH, FUN_BIND_2));

}

gv_mqtt_client::~gv_mqtt_client()
{
    auto & v2x_command = v2x_client_ref.get_command_pattern();
	v2x_command.unregist_command(rsp_type::RSP_CONFIG_REPORT,this);
}

int gv_mqtt_client::init_mqtt_topic_proc_fun()
{
    #define REGIST_TOPIC_COMMAND(topic,func) m_topic_commander.regist_command(get_mqtt_topic_prefix()+topic,this,std::bind(&func, FUN_BIND_3) )

    REGIST_TOPIC_COMMAND("/ota/down", gv_mqtt_client::proc_topic_ota_down);
    REGIST_TOPIC_COMMAND("/config/down", gv_mqtt_client::proc_topic_config_down);
    REGIST_TOPIC_COMMAND("/rtcm/down", gv_mqtt_client::proc_topic_rtcm_down);
    REGIST_TOPIC_COMMAND("/rte/down", gv_mqtt_client::proc_topic_rte_down);
    REGIST_TOPIC_COMMAND("/rts/down", gv_mqtt_client::proc_topic_rts_down);
    REGIST_TOPIC_COMMAND("/power/down", gv_mqtt_client::proc_topic_power_down);
    REGIST_TOPIC_COMMAND("/config-query/down", gv_mqtt_client::proc_topic_config_query_down);

    return RET_OK;
}

//=========================================================
// icloud_platform_client
int gv_mqtt_client::init()
{
    if(true==m_cloudid.empty())
    {// 没有取到 云平台ID 初始不做其它
        return RET_OK;
    }
    
    if(RET_OK != engine_ref.regist_mqtt_client(this,m_regist_info,m_mqtt_handler))
    {
        WLE("engine_ref.regist_mqtt_client error ");
        return RET_FAIL;
    }
    return  RET_OK;
}

int gv_mqtt_client::destory()
{
    engine_ref.unregist(this,m_mqtt_handler);
    return  RET_OK;
}

void gv_mqtt_client::cloudid_cb(string & cloudid)
{
    m_cloudid = cloudid;
    WLI(" cloudid %s ",m_cloudid.c_str());
}


void gv_mqtt_client::ontime_check(long long cur_ms)
{
    m_status_control.call_status_fun(cur_ms);
}

//=========================================================
// ireceiver
void gv_mqtt_client::read_cb(engine_conn_handler & handler, const char* msg, int len)
{
    WLD("msg %s ",msg);
}

// 事件处理
void gv_mqtt_client::event_cb(int fd, short events )
{
    WLI("fd %d events %d ",fd, events);

    if(events & BEV_EVENT_CONNECTED)
    {// 连接成功
        //向MQTT注册 成功后则进入 connected 
        m_status_control.enter_status(status_enum::connected);

    }
    if(events & BEV_EVENT_ERROR)
    {// 连接断开
        m_mqtt_handler.init();
        m_status_control.enter_status(status_enum::disconnect);
    }
}

// mqtt topic call back 
void gv_mqtt_client::message_cb(const struct mosquitto_message *message,const mosquitto_property *properties)
{
    WLD(" topic %s  payload %s ", message->topic, (char*)message->payload);
    char * data =  (char *)(message->payload);
    int payloadlen = message->payloadlen;
    m_topic_commander.do_command(message->topic,data,payloadlen);

}


//=========================================================
// 状态处理函数

//状态处理-等待云平台ID
int gv_mqtt_client::proc_status_logout(status_info & status)
{
    if(false == m_cloudid.empty())
    {// 如果取的云平台ID，刚进入连接mqtt 状态
        init_mqtt_topic_proc_fun(); // 
        m_status_control.enter_status(status_enum::disconnect); 
    }
    return RET_OK;
}

//状态处理-连接断开
int gv_mqtt_client::proc_status_disconnect(status_info & status)
{
    destory();
    init();
    return RET_OK;
}

//状态处理-连接成功
int gv_mqtt_client::proc_status_connected(status_info & status)
{
    // 进行消息订阅
    string mqtt_topic = get_mqtt_topic_prefix() + "/#";
    
    if(RET_OK == engine_ref.mqtt_subscribe(m_mqtt_handler,mqtt_topic))
    {
        m_status_control.enter_status(status_enum::heartbeat_send);
    }
    else
    {
        m_status_control.enter_status(status_enum::disconnect);
    }
    return RET_OK;
}

//状态处理-心跳发送
int gv_mqtt_client::proc_status_heartbeat_send(status_info & status)
{
    // 发送心跳
    string req;
    if(RET_OK != m_protocol.build_mqtt_heartbeat(req))
    {
        WLE("m_protocol.build_mqtt_heartbeat error ");
        m_status_control.enter_status(status_enum::disconnect);
        return RET_OK;
    }
    string mqtt_topic = get_mqtt_topic_prefix() + "/heartbeat/up";
    WLD("topic %s data %s ",mqtt_topic.c_str(),req.c_str() );
    if(RET_OK!=engine_ref.mqtt_public(m_mqtt_handler,mqtt_topic,req.c_str(),req.size()))
    {
        WLE("engine_ref.mqtt_public error topic %s ", mqtt_topic.c_str());
        m_status_control.enter_status(status_enum::disconnect);
        return RET_OK;
    }
    return RET_OK;
}

string gv_mqtt_client::get_mqtt_topic_prefix()
{   
    return ((m_v2x_dev_type == config::v2x_type::VT_RSU)  ? "/rsu/" : "/obu/")  + m_cloudid;
}

//=========================================================
// mqtt-接收到的主题处理

int gv_mqtt_client::proc_topic_ota_down(const string &topic, char * payload, int payload_len)
{
    //
    frame_ota_down ota_down;
    if(RET_OK!=m_protocol.fetch_ota_down(payload,ota_down))
    {
        return RET_FAIL;
    }

    updage_info info; 
    info.downloadMd5 =  ota_down.downloadMd5; 
    info.downloadUrl =  ota_down.downloadUrl; 
    info.local_path = "/tmp/update.gv"; 
    ota_upgade_ref.upgade(info); 
    return RET_OK;
}

int gv_mqtt_client::proc_topic_config_down(const string &topic, char * payload, int payload_len)
{
    try
    {
        auto j = json::parse(payload);
        auto config_down = j["config"];
        if(config_down.end() != config_down.find("v2x_mass"))
        {// 是否有相关配置
            //map<string,string> config_update_map;
            auto v2x_mass_config =  config_down["v2x_mass"] ;
            #if  0
            auto it_find = v2x_mass_config.find("debug");
            if(it_find!= v2x_mass_config.end())
            {
                config_update_map["debug"] = to_string(it_find->get<bool>());
            }
            #endif 

            auto it_find =  v2x_mass_config.find("edgeid");
            if(it_find != v2x_mass_config.end() )
            {
                config_ref.set(CK_EDGEID,it_find->get<int64_t>());
            }

            it_find =  v2x_mass_config.find("esn");
            if(it_find != v2x_mass_config.end() )
            {
                config_ref.set(CK_ESN,it_find->get<string>());
            }

            it_find =  v2x_mass_config.find("gantryno");
            if(it_find != v2x_mass_config.end() )
            {
                config_ref.set(CK_GANTRYNO,it_find->get<string>());
            }

            it_find =  v2x_mass_config.find("gv_httpurl");
            if(it_find != v2x_mass_config.end() )
            {
                config_ref.set(CK_GV_REGIST_HTTPURL,it_find->get<string>());
            }

            it_find =  v2x_mass_config.find("logfile");
            if(it_find != v2x_mass_config.end() )
            {
                config_ref.set(CK_LOG_DIR,it_find->get<string>());
            }

            it_find =  v2x_mass_config.find("loglevel");
            if(it_find != v2x_mass_config.end() )
            {
                config_ref.set(CK_LOG_LEVEL,it_find->get<int32_t>());   
            }

            it_find =  v2x_mass_config.find("logsize");
            if(it_find != v2x_mass_config.end() )
            {
               // config_update_map["logsize"] = to_string(it_find->get<int32_t>());
            }

            it_find =  v2x_mass_config.find("mqtt_ca");
            if(it_find != v2x_mass_config.end() )
            {
                config_ref.set(GET_GV_MQTT_KEY(MQTT_CA_PATH),it_find->get<string>());
            }

            it_find =  v2x_mass_config.find("mqtt_host");
            if(it_find != v2x_mass_config.end() )
            {
                config_ref.set(GET_GV_MQTT_KEY(MQTT_HOST),it_find->get<string>());
            }

            it_find =  v2x_mass_config.find("mqtt_port");
            if(it_find != v2x_mass_config.end() )
            {
                config_ref.set(GET_GV_MQTT_KEY(MQTT_PORT),it_find->get<int32_t>());
            }

            it_find =  v2x_mass_config.find("mqtt_tls");
            if(it_find != v2x_mass_config.end() )
            {
                config_ref.set(GET_GV_MQTT_KEY(MQTT_TLS),(it_find->get<bool>()== true?1:0));     
            }

            #if 0
            it_find =  v2x_mass_config.find("rsutype");
            if(it_find != v2x_mass_config.end() )
            {
                //config_update_map["rsutype"] = to_string(it_find->get<int32_t>());    
            }
            #endif 

            it_find =  v2x_mass_config.find("v2x_host");
            if(it_find != v2x_mass_config.end() )
            {
                config_ref.set(CK_V2X_SERVICE_IP,it_find->get<string>());
            }
            it_find =  v2x_mass_config.find("v2x_port");
            if(it_find != v2x_mass_config.end() )
            {
                 config_ref.set(CK_V2X_SERVICE_PORT,it_find->get<int32_t>());
            }

            #if 0
            it_find =  v2x_mass_config.find("v2x_type");
            if(it_find != v2x_mass_config.end() )
            {
                config_ref.set(CK_V2X_TYPE,it_find->get<int32_t>());
            }
            #endif 
            //config::write_default_cfg(FLAGS_flagfile,config_update_map);
            config_ref.save_config();
        }

        // remote  v2x_mass config 
        config_down.erase("v2x_mass");
        string v2x_config  = config_down.dump();
        v2x_client_ref.send_config_8CH(1,(char*)v2x_config.c_str(),v2x_config.length());
    }
    catch(exception & e)
    {
        
        WLE("config_down parse json error tempstr %s ", payload);
    } 

    return RET_OK;
}

int gv_mqtt_client::proc_topic_rtcm_down(const string &topic, char * payload, int payload_len)
{
    v2x_client_ref.send_cv2x_json_81H(payload,payload_len);
    return RET_OK;
}

int gv_mqtt_client::proc_topic_rte_down(const string &topic, char * payload, int payload_len)
{
    v2x_client_ref.send_cv2x_json_81H(payload,payload_len);
    return RET_OK;
}

int gv_mqtt_client::proc_topic_rts_down(const string &topic, char * payload, int payload_len)
{
    v2x_client_ref.send_cv2x_json_81H(payload,payload_len);
    return RET_OK;
}

int gv_mqtt_client::proc_topic_power_down(const string &topic, char * payload, int payload_len)
{
    frame_power_down power_down;
    if(RET_OK != m_protocol.fetch_mqtt_power_down(payload,payload_len,power_down))
    {
        return RET_FAIL;
    }
    // 执行回包确认
    string ack_topic =  get_mqtt_topic_prefix() + "/power/down/ack";
    string ack_rep;
    m_protocol.build_mqtt_power_down_ack(power_down,ack_rep);
    engine_ref.mqtt_public(m_mqtt_handler,ack_topic,ack_rep.c_str(),ack_rep.size() );
    if(power_down.time != 0)
    {
        WLI("power_down.time %d power_type %d ,donot execute now", power_down.time,power_down.power_type); 
        return RET_OK; 
    }
    // 执行 reboot 
    service_ref.delay_reboot(5000);
    WLI(" execute reboot after 5 seconds ");
    return RET_OK;
}

int gv_mqtt_client::proc_topic_config_query_down(const string &topic, char * payload, int payload_len)
{
    
    WLD(" topic %s \n%s ",topic.c_str(), payload);
    m_protocol.fetch_mqtt_config_query_down(payload,payload_len,m_config_query_down_session);
    
    // 向rsu 发送 config_query_down 
    v2x_client_ref.send_config_8CH(2,nullptr,0);
    return RET_OK;
}

int gv_mqtt_client::proc_rsu_config_report_0CH(const rsp_type & type,v2x_client_respond & frame)
{
    auto & rsp =  frame.data.configRsp;
    WLD("type %d opt %d json %s ",rsp.type,rsp.opt,rsp.json);

    if(rsp.opt == 1)
    {// 配置响应，不进行处理
        return RET_OK;
    }
    string req;
    if(RET_FAIL==m_protocol.build_mqtt_config_up((const char *)rsp.json,m_config_query_down_session,req))
    {
        return RET_FAIL;
    }

    string mqtt_topic = get_mqtt_topic_prefix() + "/config/up";
    WLI("topic %s data %s ",mqtt_topic.c_str(),req.c_str() );
    if(RET_OK!=engine_ref.mqtt_public(m_mqtt_handler,mqtt_topic,req.c_str(),req.size()))
    {
        WLE("engine_ref.mqtt_public error topic %s ", mqtt_topic.c_str());
        return RET_FAIL;
    }
    return RET_OK;
}

int gv_mqtt_client::proc_rsu_debug_report_0AH(const rsp_type & type,v2x_client_respond & frame)
{
    auto & rsp =  frame.data.debugRsp;
    string json_type , req; 
    m_protocol.fetch_type_value_json((const char *)rsp.json,json_type,req);
    string mqtt_topic = get_mqtt_topic_prefix() + "/ack/up";
    WLI("topic %s data %s ",mqtt_topic.c_str(),req.c_str() );
    if(RET_OK!=engine_ref.mqtt_public(m_mqtt_handler,mqtt_topic,req.c_str(),req.size()))
    {
        WLE("engine_ref.mqtt_public error topic %s ", mqtt_topic.c_str());
        return RET_FAIL;
    }
    return RET_OK;
}
/***********************************************************
 * 函数名称: proc_v2x_report_05H
 * 功能描述: v2x-上报消息（05H） 处理
 * 输入参数: 
 * 输出参数: 
 * 返 回 值: 
 ***********************************************************/
int gv_mqtt_client::proc_v2x_report_05H(const string & type,uint8_t &direct_i,string & s_json,string & str_value_json,shared_ptr<json> & value_json_obj)
{
    if(m_cloudid.empty())
    {// 云平台注册失败，不进行上报
        WLD(" cloudid is empty not report ");
        return RET_OK;
    }
    if(m_mqtt_handler.fd <=0)
    {
        return RET_OK;
    }

    string strtopic;
    if(m_v2x_dev_type == config::v2x_type::VT_OBU && type != "bsm")
    {// obu 只上报bsm 
        return RET_OK;
    }
    if(false==get_report_topic(type,direct_i,strtopic))
    {
        return RET_FAIL;
    }
    return engine_ref.mqtt_public(m_mqtt_handler,strtopic,str_value_json.c_str(),str_value_json.length());
}

/***********************************************************
 * 函数名称: proc_rsp_obu_alarm_06H
 * 功能描述: 上报OBU预警信息（06H）
 * 输入参数: 
 * 输出参数: 
 * 返 回 值: 
 ***********************************************************/
int gv_mqtt_client::proc_rsp_obu_alarm_06H(const rsp_type & type,v2x_client_respond & frame)
{
    if(m_cloudid.empty())
    {// 云平台注册失败，不进行上报
        WLI(" cloudid is empty not report ");
        return RET_OK;
    }
    if(m_mqtt_handler.fd <=0)
    {
        return RET_OK;
    }
    if(m_v2x_dev_type != config::v2x_type::VT_OBU)
    {// 非OBU不上报
        return RET_OK;
    }

    string s_json_type; 
    string s_json_value;
    if(RET_OK != m_protocol.fetch_type_value_json((const char*)frame.data.obuAlarm.json,s_json_type,s_json_value))
    {
        return RET_FAIL;
    }
    string strtopic;
    uint8_t direct_i =  v2x_report_direct_enum::vrde_send;
    if(false==get_report_topic(s_json_type,direct_i,strtopic))
    {
        return RET_FAIL;
    }
    WLD("strtopic %s type %s value %s ",strtopic.c_str(),s_json_type.c_str(), s_json_value.c_str());
    return engine_ref.mqtt_public(m_mqtt_handler,strtopic,(char *)s_json_value.c_str(),s_json_value.length());
}

/***********************************************************
 * 函数名称: proc_v2x_report_monitor_0EH
 * 功能描述: v2x设备-上报监控数据信息（0EH）
 * 输入参数: 
 * 输出参数: 
 * 返 回 值: 
 ***********************************************************/
int gv_mqtt_client::proc_v2x_report_monitor_0EH(const rsp_type & type,v2x_client_respond & frame)
{
    try
    {
        string perf_up_json;
        m_protocol.build_mqtt_perf_up((const char*)frame.data.reportMonitor.json,perf_up_json);
        string topic = get_mqtt_topic_prefix() + "/perf/up";
        WLD("topic %s data %s ",topic.c_str(),perf_up_json.c_str() );
        return engine_ref.mqtt_public(m_mqtt_handler,topic,perf_up_json.c_str(),perf_up_json.length()); 
    }

    catch(const std::exception& e)
    {
        WLE(" catch exception %s ",e.what()); 
        return RET_FAIL;
    }
    return RET_OK;
}


bool gv_mqtt_client::get_report_topic(const string &strtype,uint8_t &direct_i,string & strtopic)
{
    strtopic =  get_mqtt_topic_prefix();

    if (strtype == "bsm")
    {
        if (m_v2x_dev_type == config::v2x_type::VT_RSU) // rsu
        {
            strtopic += "/bsm/up";
        }
        else
        {
            if (direct_i == v2x_report_direct_enum::vrde_send )
                strtopic += "/bsm/send/up";
            else
                strtopic += "/bsm/receive/up";
        }
    }
    else if (strtype == "rsm")
    {
        strtopic += "/rsm/up";
    }
    else if (strtype == "rts")
    {
        strtopic += "/rsi/rts/up";
    }
    else if (strtype == "rte")
    {
        strtopic += "/rsi/rte/up";
    }
    else if (strtype == "map")
    {
        strtopic += "/map/up";
    }
    else if (strtype == "rsi")
    {
        strtopic += "/rsi/up";
    }
    else if (strtype == "warning")
    {
        strtopic += "/warning/up";
    }
    else if (strtype == "alarm")
    {
        strtopic += "/alarm/up";
    }
    else if (strtype == "evi")
    {
        strtopic += "/etc20Vehicle/up";
    }
    else if (strtype == "debug")
    {
        strtopic +="/ack/up";
    }
    // 关机的应答
    else if (strtype == "down")
    {
        strtopic += "/down/ack";
    }
    else
    {
        WLE("can not set topic ,type invalid %s ",strtype.c_str());
        return false;
    }
    return true;
}
