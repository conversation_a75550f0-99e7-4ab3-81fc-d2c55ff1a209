/***********************************************
版本信息： 金溢科技版本权所有，保留一切权利
文件名称： gv_http_client.h
作者：张明
开始日期：2023-08-01 09:22:06
完成日期：
当前版本号: v 0.0.1
主要功能: 
版本历史:
***********************************************/

#ifndef _GV_HTTP_CLIENT_H_
#define _GV_HTTP_CLIENT_H_
#include "iengine.h"
#include "ireceiver.h"
#include <string>
#include "gv_mqtt_protocol.h"
#include <functional>
#include "status_control.hpp"// 引入 状态管理，进行重新，关注，上报各种状态处理
using namespace std;

class gv_http_client: public ihttpclient_receiver
{
	typedef  std::function<void(string & )> CLOUDID_CB_FUN; 
	typedef status_control<gv_http_client,status_enum> status_control_type;
    typedef status_control_type::status_info  status_info;

public:
	gv_http_client(gv_mqtt_protocol &protocol);
	virtual ~gv_http_client();
public:
	inline void regist_cloudid_cb(CLOUDID_CB_FUN fun)
	{
		m_notice_fun = fun; 
	}

	int init();
	int destory();
	void get_cloudid(string &cloud_id);
	void ontime_check(long long cur_ms);

public: // interface ireceiver

    virtual void read_cb(engine_conn_handler & handler, const char* msg, int len) override;
    virtual void event_cb(int fd, short events ) override;
public: // interface ihttpclient_receiver

	virtual void update_conn_handler(engine_conn_handler & new_client_handler) override;
	virtual int get_conn_fd(void *conn_handler_data) override;

public: //状态管理函数
	int proc_status_logout(status_info & status);
	int proc_status_login_send(status_info & status);	
	int proc_status_login(status_info & status);
	int proc_status_keep_alive(status_info & status);
private:
	gv_mqtt_protocol &m_protocol;
	status_control_type m_status_control;

	engine_conn_handler m_client_handler;
	int m_v2x_dev_type ; 
	CLOUDID_CB_FUN m_notice_fun;
	string m_cloudid;

};
#endif //_GV_HTTP_CLIENT_H_
