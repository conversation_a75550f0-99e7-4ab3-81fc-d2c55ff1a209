#include "gv_mqtt_protocol.h"
#include "config.h"
#include "stdafx.h"
#include "common_utility.h"
#include "log.h"
#include "service.h"
#include "nlohmann/json.hpp"
using json = nlohmann::json;

gv_mqtt_protocol::gv_mqtt_protocol()
{
    config_ref.get(CK_V2X_TYPE,m_v2x_dev_type);
    config_ref.get(CK_EDGEID,m_edgeid);
    config_ref.get(CK_ESN,m_esn);
    config_ref.get(CK_GANTRYNO,m_gantryno);


}

gv_mqtt_protocol::~gv_mqtt_protocol()
{
	
}

int gv_mqtt_protocol::get_regist_url(string & url)
{
    config_ref.get(CK_GV_REGIST_HTTPURL,url);// 云平台注册URL
    url += (m_v2x_dev_type == config::v2x_type::VT_OBU ? "ObuRegister" : "RsuRegister"); 

    return RET_OK;
}

int gv_mqtt_protocol::build_regist_http_req(http_req & req)
{
    build_http_head(req);
    if(m_v2x_dev_type == config::v2x_type::VT_RSU)
    {
        char ip[64] = {0}; 
        get_local_ip("eth0",ip);

        auto j = nlohmann::json{
            {"ReqID", m_nreqid++}, 
            {"IpAddr", ip}, 
            {"RsuESN", m_esn},
            {"RsuType",1},
            {"GantryNo",m_gantryno},
            {"EdgeId",m_edgeid}
        };
        req.data = j.dump();
    }
    else if(m_v2x_dev_type == config::v2x_type::VT_OBU)
    {
        auto j = nlohmann::json{
            {"ReqID", m_nreqid++}, 
            {"ObuESN", m_esn}
        };
        req.data = j.dump();
    }
    return RET_OK;
}


int gv_mqtt_protocol::fetch_http_rsp_cloudid( const char* frame, int frame_len,string & cloudid)
{
    WLI(" %s ",frame);
    uint64_t id = 0;
    string key = (m_v2x_dev_type == config::v2x_type::VT_RSU ? "RsuID" : "ObuId");
    
    try
    {
        auto j = json::parse(frame);
        if(j.end() == j.find(key))
        {
            WLE("json error  %s  has not key  %s ", frame,key.c_str());
            return RET_FAIL;
        }
        auto j_id =  j[key.c_str()];
        id = j_id.get<uint64_t>();
        cloudid = std::to_string(id);
        m_cloudid = cloudid;
        WLI(" success cloudid %s ",m_cloudid.c_str());
    }
    catch(exception & e)
    {
        //WLE("catch error %s ",e.what());
        cloudid = "";
        WLE("parse json error %s errdesc %s ", frame,e.what());
        return RET_FAIL;
    }
    return RET_OK;
}
// 提取mqtt power_down
int gv_mqtt_protocol::fetch_mqtt_power_down(const char *frame,int frame_len,frame_power_down &power_down )
{
    try
    {
        auto j = json::parse(frame);
        if(j.end() == j.find("seqNum")|| j.end() ==  j.find("time"))
        {
            WLE("json error  %s  has not key seqNum or time ", frame);
            return RET_FAIL;
        }
        
        power_down.seqNum = j["seqNum"].get<int>(); 
        power_down.time = j["time"].get<int>();
        power_down.power_type = j["power"].get<int>();
    }
    catch(exception & e)
    {
        //WLE("catch error %s ",e.what());
        
        WLE("parse json error %s ", frame);
        return RET_FAIL;
    }
    return RET_OK;
}

int gv_mqtt_protocol::build_mqtt_power_down_ack(frame_power_down &power_down,string &req)
{
    auto j = json{
            {"seqNum", power_down.seqNum}, 
            {"code", "200"}, 
            {"message", ""}, 
        };
    req  = j.dump();
    return RET_OK;
}


int gv_mqtt_protocol::build_mqtt_heartbeat(string & req)
{
    try
    {
        string key_prefix = (m_v2x_dev_type == config::v2x_type::VT_OBU ? "obu" : "rsu"); 
        auto j = json{
            {key_prefix+"Id", m_cloudid}, 
            {key_prefix+"Esn", m_esn}, 
            {"timestamp", get_us()/1000},
            {key_prefix+"Status",1},
            {"softwareVersion",get_package_ver()}
        };
        struct timespec ts;
        auto ret_code =  clock_gettime(CLOCK_MONOTONIC, &ts);
        if(ret_code != 0 )
        {
            WLE("send_heart error clock_gettime  errno %d desc %s ",errno,strerror(errno) );
        }
        else
        {
            auto system_json = json::object(); 
            system_json["uptime"] = ts.tv_sec;
            j["system"] = system_json;
        }
        auto my_pid = getpid();
        auto uptime_mass = service_ref.get_curpro_runtime();  
        auto my_process_info = json{
                {"pid",  my_pid}, 
                {"uptime",uptime_mass}, 
                {"name","v2x_mass"}, 
                {"running",true}
        };
        j["processes"].push_back(my_process_info);
        auto v2x_heartbeat_info =   v2x_client_ref.get_heartbeat_info();
        if(v2x_heartbeat_info.dev_type != 0)
        {
            auto v2x_process_info = json{
                {"pid", v2x_heartbeat_info.pid}, 
                {"uptime",v2x_heartbeat_info.runtime}, 
                {"name",v2x_heartbeat_info.dev_type == 1?"v2x_app_obu_bin":"v2x_app_rsu_bin"},  //1 obu ; 2 rsu 
                {"running",true}   
            };
             j["processes"].push_back(v2x_process_info);
        }
        req  = j.dump();
    }
    catch(exception & e)
    {
        WLE(" catch error %s ",e.what());
        return RET_FAIL;
    }
    return RET_OK;
}
int gv_mqtt_protocol::fetch_mqtt_config_query_down(const char *frame,int frame_len,uint64_t & session_id)
{
    try
    {
        auto j = json::parse(frame);
        if(j.end() == j.find("sessionId"))
        {
            WLE("can not find sessionId %s ",frame);
            return RET_FAIL;
        }
        session_id = std::stoull(j["sessionId"].get<string>());
    }
    catch(exception & e)
    {
        WLE(" catch error %s ",e.what());
        return RET_FAIL;
    }
    return RET_OK;
}

int gv_mqtt_protocol::build_mqtt_config_up(const char *strjson,uint64_t  session_id,string &req)
{
    try
    {   
        string gv_httpurl = config_ref.get_def(CK_GV_REGIST_HTTPURL,"");

        string log_file = config_ref.get_def(CK_LOG_DIR,"");
        int log_level = config_ref.get_def(CK_LOG_LEVEL,0);
        int log_size = 52428800;
        string mqtt_ca = config_ref.get_def(GET_GV_MQTT_KEY(MQTT_CA_PATH),"");
        string mqtt_host = config_ref.get_def(GET_GV_MQTT_KEY(MQTT_HOST),"");
        int mqtt_port = config_ref.get_def(GET_GV_MQTT_KEY(MQTT_PORT),0);
        bool mqtt_tls = config_ref.get_def(GET_GV_MQTT_KEY(MQTT_TLS),false);
        string rsu_ip = config_ref.get_def(CK_V2X_SERVICE_IP,"");
        int rsu_port = config_ref.get_def(CK_V2X_SERVICE_PORT,0);



        auto j  = json::parse(strjson);
        json v2x_mass_config;
        v2x_mass_config["debug"] = false;
        v2x_mass_config["edgeid"] = m_edgeid;
        v2x_mass_config["esn"] = m_esn;
        v2x_mass_config["gantryno"] = m_gantryno;
        v2x_mass_config["gv_httpurl"] = gv_httpurl;
        v2x_mass_config["logfile"] = log_file;
        v2x_mass_config["loglevel"] = log_level;
        v2x_mass_config["logsize"] = log_size;

        v2x_mass_config["mqtt_ca"] = mqtt_ca;
        v2x_mass_config["mqtt_host"] = mqtt_host;
        v2x_mass_config["mqtt_port"] = mqtt_port;
        v2x_mass_config["mqtt_tls"] = mqtt_tls; 
        v2x_mass_config["rsutype"] = 1;
        v2x_mass_config["v2x_host"] = rsu_ip;
        v2x_mass_config["v2x_port"] = rsu_port;
        v2x_mass_config["v2x_type"] = m_v2x_dev_type;
        j["v2x_mass"] = v2x_mass_config;

        auto timestamp = get_us();

        json config_up ;

        config_up["messageId"] = timestamp;
        config_up["sessionId"] = to_string(session_id);
        config_up["deviceId"] = std::stoull(m_cloudid);
        config_up["timestamp"] = timestamp;
        config_up["config"] =  j;
        req =  config_up.dump();
        WLI("strjson %s ",req.c_str());
    }

    catch(const std::exception& e)
    {
        WLE("build_config_up parse json error %s ",strjson);
        return RET_FAIL;
    }
    return RET_OK;
}
int gv_mqtt_protocol::fetch_type_value_json(const char *strjson, string &type, string &value)
{  
    try
    {
       auto j  = json::parse(strjson); 
       type =  j["type"].get<string>(); 
       value =  j["value"].get<string>(); 
    }   
    catch(const std::exception& e)
    {
        WLE("build_config_up parse json error %s ",strjson);
        return RET_FAIL;
    }
    return RET_OK;    
}

int gv_mqtt_protocol::fetch_ota_down(const char *strjson, frame_ota_down & ota_info)
{
    try
    {
        auto j = json::parse(strjson);
        ota_info.downloadUrl = j["downloadUrl"].get<string>();
        ota_info.downloadMd5 =  j["downloadMd5"].get<string>();
    }
    catch(const std::exception& e)
    {
        WLE(" catch %s  json %s ",e.what(),strjson);
    }
    return RET_OK;   
}


int gv_mqtt_protocol::build_http_head(http_req & req)
{
    req.heads.push_back("Accept: application/json") ; 
    req.heads.push_back("Content-Type: application/json") ; 
    req.heads.push_back("charsets: utf-8") ; 


    return RET_OK;
}


int gv_mqtt_protocol::build_mqtt_perf_up(const char *monitor_json,string &perf_up_json)
{
    try
    {
        if(false == dev_performance_ref.is_stat_ok())
        {
            WLE("dev_performance_ref.is_stat_ok  == false"); 
            return RET_FAIL;
        }

        cpu_info_t cpu_info; 
        dev_performance_ref.get_cpu_info(cpu_info); 

        mem_info_t  mem_info;
        dev_performance_ref.get_mem_info(mem_info);
        disk_info_t  disk_info;
        dev_performance_ref.get_disk_info(disk_info);

        auto j =  json::parse(monitor_json);
        j["dev_type"] =  m_v2x_dev_type == config::v2x_type::VT_OBU ? "obu" : "rsu"; 
        j["cloud_id"] =  m_cloudid; 

        j["run_time"] =  time(nullptr); 
        auto performance_info =  nlohmann::json{
            {"load_average_1",cpu_info.load1},
            {"load_average_5",cpu_info.load5},
            {"load_average_15",cpu_info.load15},
            {"mem",mem_info.total*1.0/1024.0}, ///< MB
            {"mem_rate",mem_info.use*100.0/mem_info.total},
            {"disk",disk_info.total*1.0/1024}, ///< MB
            {"disk_rate",disk_info.use*100.0/disk_info.total}
        }; 
        j["performance_info"] = performance_info;
        perf_up_json =  j.dump(); 
    }
    catch(const std::exception& e)
    {
        WLE(" catch exception %s monitor_json %s ",e.what(),monitor_json); 
        return RET_FAIL; 
    }
    return RET_OK;
}
