#include "debug_platform_client.h"
#include "config.h"
#include "stdafx.h"


debug_platform_client::debug_platform_client()
{

}

debug_platform_client::~debug_platform_client()
{
	
}
	
int debug_platform_client::init()
{
    return RET_OK;
} 
int debug_platform_client::destory()
{
    return RET_OK;
}

void debug_platform_client::get_cloud_platform_id(string &id)
{
    config_ref.get(CK_DEBUG_DEV_ID,id);

}

//云平台-连接状态，0：未连接  非0:连接
int debug_platform_client::get_cloud_platform_status()
{
    int status; 
    config_ref.get(CK_DEBUG_CLOUD_PLATFORM_LINK,status); 
    return status; 
}

void debug_platform_client::ontime_check(long long cur_ms)
{

}