/***********************************************
版本信息： 金溢科技版本权所有，保留一切权利
文件名称： debug_platform_client.h
作者：张明
开始日期：2023-12-22 11:25:26
完成日期：
当前版本号: v 0.0.1
主要功能: 
版本历史:
***********************************************/

#ifndef _DEBUG_PLATFORM_CLIENT_H_
#define _DEBUG_PLATFORM_CLIENT_H_

#include "icloud_platform_client.h"

class debug_platform_client:public icloud_platform_client
{
public:
	debug_platform_client();
	virtual ~debug_platform_client();
	
public: // interface  icloud_platform_client
	virtual int init() ;
	virtual int destory() ;

	//云平台-分配ID
	virtual void get_cloud_platform_id(string &id);

	//云平台-连接状态，0：未连接  非0:连接
	virtual int get_cloud_platform_status();
	virtual void ontime_check(long long cur_ms);

};
#endif //_DEBUG_PLATFORM_CLIENT_H_
