#include "debug_mec_client.h"
#include "service.h"
#include "stdafx.h"
#include "hex_str.h"
#include <algorithm>

debug_mec_client::debug_mec_client()
{
    config_ref.get(CK_DEBUG_MEC_JSON_SAVE_PATH,m_json_save);

    m_print_asn_enable = config_ref.get_def(CK_DEBUG_MEC_PRINT_ASN,false);

    m_save_log_enable = config_ref.get_def(CK_DEBUG_MEC_SAVE_FILE,false);

    m_light_color_map[1] = "DARK";
    m_light_color_map[2] = "RED";    ///< TRAFFIC_LIGHT_RED_FLASH
    m_light_color_map[3] = "RED";    ///< TRAFFIC_LIGHT_RED
    m_light_color_map[4] = "GREEN";  ///< TRAFFIC_LIGHT_WAIT
    m_light_color_map[5] = "GREEN";  ///< TRAFFIC_LIGHT_GREEN
    m_light_color_map[6] = "GREEN";  ///< TRAFFIC_LIGHT_ARROW
    m_light_color_map[7] = "YELLOW"; ///< TRAFFIC_LIGHT_YELLOW
    m_light_color_map[8] = "YELLOW"; ///< TRAFFIC_LIGHT_YELLOW_FLASH
}

debug_mec_client::~debug_mec_client()
{

}


int debug_mec_client::init()
{
    //注册 命令回调
    v2x_client_ref.get_command_pattern().regist_command(rsp_type::RSP_ASN_BIN,this,std::bind(&debug_mec_client::print_rsu_asn_bin, FUN_BIND_2));
    v2x_client_ref.get_rsp_report_command().regist_all_command(this,std::bind(&debug_mec_client::print_rsu_report, FUN_BIND_5));
    return RET_OK;
}

void debug_mec_client::stop()
{

}

void debug_mec_client::destory()
{
    v2x_client_ref.get_command_pattern().unregist_command(rsp_type::RSP_ASN_BIN,this);
    v2x_client_ref.get_rsp_report_command().unregist_all_command(this);
}


void debug_mec_client::ontime_check(long long cur_ms)
{

}

int debug_mec_client::get_mec_status()
{
    int status = 0;
    config_ref.get(CK_DEBUG_MEC_STATUS,status);
    return status;
}

int debug_mec_client::print_rsu_asn_bin(const rsp_type & type,v2x_client_respond & frame)
{

    //WLD("");
    if(m_print_asn_enable == false)
    {
        return RET_OK;
    }
    try
    {
        auto j = json::parse(frame.data.asnUper.json);

        // hex_decode
        //auto & hex_value =  j["value"];

        string hex_value(move(j["value"].get<string>()));


        string sAsnValue;
        sAsnValue.resize(hex_value.length()/2);

        hex_str ohex;
        ohex.decode((unsigned char *)hex_value.c_str(),hex_value.length(),(unsigned char *)sAsnValue.c_str());

        MessageFrame_t * asn_v2x_frame  =  nullptr;
        asn_dec_rval_t rval = uper_decode(NULL,&asn_DEF_MessageFrame,
                                          (void **)&asn_v2x_frame,
                                          sAsnValue.c_str(),
                                          sAsnValue.length(),0,0
                                         );
        if(rval.code != RC_OK )
        {
            //WLI("frame.data.asnUper.json  %s ",frame.data.asnUper.json);

            WLE("uper_decode error rval %d hex_data %s ",rval.code,hex_value.c_str());
            return RET_FAIL;
        }
        asn_fprint(stdout, &asn_DEF_MessageFrame, asn_v2x_frame);
        ASN_STRUCT_FREE(asn_DEF_MessageFrame,asn_v2x_frame);
        asn_v2x_frame = nullptr;
    }
    catch(exception & e)
    {
        WLE("catch error %s ",e.what());
        return RET_FAIL;
    }

    return RET_OK;
}

int debug_mec_client::print_rsu_report(const string & type,uint8_t &direct_i,string & s_json,string & str_value , shared_ptr<json> & value_json_obj)
{
    string direct = (direct_i==0?"recv":"send");
    string file_mode = "a";
    string file_name =m_json_save+direct+"_";
    bool bSave =  false;
    stringstream ss;
    if(type == "bsm" || type =="rsm" || type == "rsi" || type =="spat" )
    {
        // save map
        file_name = file_name + type + ".txt";
        bSave =  true;
        if(type == "bsm")
        {
            file_mode = "w";
            auto &j_bsm = *(value_json_obj.get());
            string elv = "null";
            if(j_bsm["pos"].end() != j_bsm["pos"].find("elevation"))
            {
                elv =  std::to_string( j_bsm["pos"]["elevation"].get<int>());
            }
            int posConfidence = 0;
            if(j_bsm.end() != j_bsm.find("posConfidence"))
            {
                posConfidence = j_bsm["posConfidence"]["pos"].get<int>();
            }

            ss<<"["<<j_bsm["id"].get<string>()<<"]"
                <<" lat:"<<j_bsm["pos"]["lat"].get<int>()
                <<" log:"<<j_bsm["pos"]["long"].get<int>()
                <<" elv:"<<elv
                <<" heading:"<<j_bsm["heading"].get<int>()
                <<" speed:"<<j_bsm["speed"].get<int>() 
                <<" pos_confidence:"<<posConfidence<<"_"<<asn_SPC_PositionConfidence_specs_1.value2enum[posConfidence].enum_name;


        }
        if(type == "spat")
        {
            auto &j_spat = *(value_json_obj.get());
            if(j_spat.end()!= j_spat.find("intersections"))
            {
                for(auto & intersection:j_spat["intersections"])
                {
                    int region = -1;
                    int id = intersection["intersectionId"]["id"].get<int>();
                    if(intersection["intersectionId"].find("region") != intersection["intersectionId"].end())
                    {
                        region =  intersection["intersectionId"]["region"].get<int>();
                    }
                    ss<<"["<<region<<"-"<<id<<"]";
                    for(auto & phase :intersection["phases"] )
                    {
                        //
                        ss<<phase["id"];
                        for(auto &phaseState :phase["phaseStates"])
                        {
                            int light = phaseState["light"].get<int>();
                            auto &timing  =  phaseState["timing"];
                            int likelyEndTime = -1;
                            if(timing["choiceID"].get<int>() == 1 )
                            {
                                // counting --
                                auto & counting = timing["counting"];
                                int startTime =  counting["startTime"].get<int>();
                                if(startTime != 0 )
                                {
                                    continue;
                                }
                                likelyEndTime = counting["likelyEndTime"].get<int>();
                            }
                            if(timing["choiceID"].get<int>() == 2 )
                            {
                                // utc
                                ss<<" not supt utc time ";
                                continue;
                            }

                            auto it_light_find =  m_light_color_map.find(light);
                            if(it_light_find != m_light_color_map.end())
                            {
                                ss<<it_light_find->second<<likelyEndTime<<" ";
                            }
                            else
                            {
                                continue;
                            }
                        }
                    }
                }
            }
        }
    }
    if(type == "map")
    {
        bSave =  true;
        file_mode = "w";
        auto &j_map = *(value_json_obj.get());
        ss<<file_name<<"map_";
        for (auto &node:j_map["nodes"])
        {
            string node_name  = "not_name";
            if(node.find("name") != node.end())
            {
                node_name= node["name"].get<string>();
            }

            char *pch_name  =(char * )node_name.c_str();
            std::replace(pch_name,pch_name+node_name.length(),' ','_');

            int id = node["id"]["id"].get<int>();
            int region =  -1;
            if(node["id"].find("region") != node["id"].end())
            {
                region = node["id"]["region"].get<int>();
            }
            ss<<node_name<<"_"<<region<<"_"<<id<<"";
        }
        ss <<".json";
        file_name = ss.str();
    }
    WLD("direct %s type %s %s ",direct.c_str(),type.c_str(),ss.str().c_str());
    if(bSave == true && m_save_log_enable == true)
    {
        log_ref.save_log_file(file_name.c_str(),s_json.c_str(),s_json.length(),file_mode);
    }
    return RET_OK ;
}



int debug_mec_client::thread_work_rsu_report(v2x_client_respond & frame,string & json)
{

    static atomic<unsigned int > count;
    unsigned int proc_count = count.fetch_add(1);
    if(proc_count %1000 == 0 )
    {
        WLI(" proc_count %d ",proc_count);
    }
    try
    {
        //frame.head.data_len;
        
       
    }
    catch(exception & e)
    {
        string file_name =m_json_save+"json_error_deocde.log";
        WLE("catch error %s ",e.what());
        log_ref.save_log_file(file_name.c_str(),json.c_str(),json.length(),"a");
        return RET_FAIL;
    }



    return RET_OK;
}