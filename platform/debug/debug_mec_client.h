/***********************************************
版本信息： 金溢科技版本权所有，保留一切权利
文件名称： debug_mec_client.h
作者：张明
开始日期：2022-12-27 15:15:51
完成日期：
当前版本号: v 0.0.1
主要功能:
版本历史:
***********************************************/

#ifndef _DEBUG_MEC_CLIENT_H_
#define _DEBUG_MEC_CLIENT_H_

#include "iengine.h"
#include "imec_client.h"
#include "log.h"
#include "v2x_protocol.h"
#include <unordered_map>
#include "nlohmann/json.hpp"
using json = nlohmann::json;
using namespace std;

/**
* @brief 调度mec客户端，用于输出收到的信息
*/
class debug_mec_client final :public imec_client
{
    
    typedef int(debug_mec_client::*THREAD_WORK_FUN)(v2x_client_respond &);

    
public:
    debug_mec_client();
    ~debug_mec_client();

public:	//imec_client interface
    virtual int init() override;
    virtual void stop() override;
    virtual void destory() override;
    virtual void ontime_check(long long cur_ms) override;
    virtual int get_mec_status() override;

private:
    int print_rsu_asn_bin(const rsp_type & type,v2x_client_respond & frame);
    int print_rsu_report(const string & type,uint8_t &direct_i,string & s_json,string & str_value, shared_ptr<json> & value_json_obj);
    int thread_work_rsu_report(v2x_client_respond & rsp,string & json);
private:

    string m_json_save;
    bool m_print_asn_enable {false};
    bool m_save_log_enable {false};
    unordered_map<int,string> m_light_color_map;
 

};
#endif //_DEBUG_MEC_CLIENT_H_