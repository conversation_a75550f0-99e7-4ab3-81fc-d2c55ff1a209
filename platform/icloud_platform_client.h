/***********************************************
版本信息： 金溢科技版本权所有，保留一切权利
文件名称： icloud_platform_client.h
作者：张明
开始日期：2023-06-13 10:31:36
完成日期：
当前版本号: v 0.0.1
主要功能: mqtt_client 接口封装
版本历史:
***********************************************/

#ifndef _IMQTT_CLIENT_H_
#define _IMQTT_CLIENT_H_
#include <string>
using namespace std;

class icloud_platform_client
{
public:
	static icloud_platform_client * create_client();
	static void destory_client(icloud_platform_client * & client);
public:
	icloud_platform_client();
	virtual ~icloud_platform_client();
	virtual int init() = 0;
	virtual int destory() = 0;

	//云平台-分配ID
	virtual void get_cloud_platform_id(string &id){};

	//云平台-连接状态，0：未连接  非0:连接
	virtual int get_cloud_platform_status(){ return 0; };
	virtual void ontime_check(long long cur_ms){};
protected:

};



#endif //_IMQTT_CLIENT_H_
