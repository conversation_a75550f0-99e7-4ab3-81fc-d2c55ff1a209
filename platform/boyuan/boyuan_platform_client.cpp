#include "boyuan_platform_client.h"
#include "stdafx.h"
#include "log.h"
#include "service.h"

boyuan_platform_client::boyuan_platform_client():m_status_control(status_enum::disconnect,"boyuan_platform_client",this)
{
    int val = 0;
    config_ref.get(GET_BOYUAN_MQTT_KEY(MQTT_TLS),val);
    if(val == 1)
    {
        m_regist_info.tls =  true;
    }
    else 
    {
        m_regist_info.tls =  false;
    }
    config_ref.get(GET_BOYUAN_MQTT_KEY(MQTT_CLIENT_ID),m_regist_info.client_id); 
    config_ref.get(GET_BOYUAN_MQTT_KEY(MQTT_HOST),m_regist_info.hostname);
    config_ref.get(GET_BOYUAN_MQTT_KEY(MQTT_PORT),m_regist_info.port);
    config_ref.get(GET_BOYUAN_MQTT_KEY(MQTT_CA_PATH),m_regist_info.cafile);
    config_ref.get(GET_BOYUAN_MQTT_KEY(MQTT_USER),m_regist_info.username);
    config_ref.get(GET_BOYUAN_MQTT_KEY(MQTT_PASSWD),m_regist_info.passwd);

    int reconnect_interval_ms = 3000;
    int heart_send_timeout_ms = 30000;

    m_status_control.regist_status(status_enum::disconnect, &boyuan_platform_client::proc_status_disconnect, "disconnect", reconnect_interval_ms);
    m_status_control.regist_status(status_enum::connected, &boyuan_platform_client::proc_status_connected, "connected", 0);
    m_status_control.regist_status(status_enum::heartbeat_send, &boyuan_platform_client::proc_status_heartbeat_send, "heart_send", heart_send_timeout_ms);

}

boyuan_platform_client::~boyuan_platform_client()
{
	
}
	
int boyuan_platform_client::init()
{
    if(RET_OK != engine_ref.regist_mqtt_client(this,m_regist_info,m_mqtt_handler))
    {
        WLE("engine_ref.regist_mqtt_client error ");
        return RET_FAIL;
    }
    return RET_OK;
}
int boyuan_platform_client::destory() 
{
    engine_ref.unregist(this,m_mqtt_handler);
    return RET_OK;
}
void boyuan_platform_client::get_cloud_platform_id(string &id) 
{

}
int boyuan_platform_client::get_cloud_platform_status() 
{
    return 1; 
}

void boyuan_platform_client::ontime_check(long long cur_ms)
{
    m_status_control.call_status_fun(cur_ms);
}
void boyuan_platform_client::read_cb(engine_conn_handler & handler, const char* msg, int len)
{
    WLD("msg %s ",msg);
}
void boyuan_platform_client::event_cb(int fd, short events )
{
    WLI("fd %d events %d ",fd, events);
    if(events & BEV_EVENT_CONNECTED)
    {// 连接成功
        //向MQTT注册 成功后则进入 connected 
        m_status_control.enter_status(status_enum::connected);
    }
    if(events & BEV_EVENT_ERROR)
    {// 连接断开
        m_mqtt_handler.init();
        m_status_control.enter_status(status_enum::disconnect);
    }
}

void boyuan_platform_client::message_cb(const struct mosquitto_message *message,const mosquitto_property *properties)
{
    WLI(" topic %s  payload %s ", message->topic, (char*)message->payload);
}

int boyuan_platform_client::proc_status_disconnect(status_info & status)
{
    destory();
    init();
    return RET_OK;
}
int boyuan_platform_client::proc_status_connected(status_info & status)
{
    m_status_control.enter_status(status_enum::heartbeat_send);
    return RET_OK;
}

// 每隔30 秒上报一次心跳
int boyuan_platform_client::proc_status_heartbeat_send(status_info & status)
{
    string topic ;
    string heart_beat_json; 
    m_protocol.build_mqtt_heartbeat(topic,heart_beat_json);
    if(RET_OK != engine_ref.mqtt_public(m_mqtt_handler,topic,heart_beat_json.c_str(),heart_beat_json.length()))
    {
        m_status_control.enter_status(status_enum::disconnect);
    }

    return RET_OK;
}