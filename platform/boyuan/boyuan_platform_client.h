/***********************************************
版本信息： 金溢科技版本权所有，保留一切权利
文件名称： boyuan_platform_client.h
作者：张明
开始日期：2023-09-26 11:13:32
完成日期：
当前版本号: v 0.0.1
主要功能: 接入 深智城&博远 5G智能网联
版本历史:
***********************************************/

#ifndef _BOYUAN_MQTT_H_
#define _BOYUAN_MQTT_H_
#include "icloud_platform_client.h"
#include "iengine.h"
#include "status_control.hpp"// 引入 状态管理，进行重新，关注，上报各种状态处理
#include "ireceiver.h"
#include "boyuan_mqtt_protocol.h"

class boyuan_platform_client:public icloud_platform_client, public imqttclient_receiver
{
	typedef status_control<boyuan_platform_client,status_enum> status_control_type;
    typedef status_control_type::status_info  status_info;

public:
	boyuan_platform_client();
	virtual ~boyuan_platform_client();

private: // icloud_platform_client
	virtual int init() override;
	virtual int destory() override;
	virtual void get_cloud_platform_id(string &id) override;
	virtual int get_cloud_platform_status() override;
	virtual void ontime_check(long long cur_ms)override;

public: //状态管理函数
	int proc_status_logout(status_info & status);
    int proc_status_disconnect(status_info & status);
    int proc_status_connected(status_info & status);
	int proc_status_heartbeat_send(status_info & status);

public: // interface ireceiver
    virtual void read_cb(engine_conn_handler & handler, const char* msg, int len) override;
    virtual void event_cb(int fd, short events ) override; 	
public: // imqttclient_receiver
	virtual void message_cb(const struct mosquitto_message *message,const mosquitto_property *properties) override; 

private:
	boyuan_mqtt_protocol m_protocol;
	status_control_type m_status_control;
	engine_conn_handler m_mqtt_handler;
	mqtt_regist_info m_regist_info;
};
#endif //_BOYUAN_MQTT_H_
