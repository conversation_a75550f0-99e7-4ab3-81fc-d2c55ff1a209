#include "boyuan_mqtt_protocol.h"
#include "stdafx.h"
#include "service.h"
#include "common_utility.h"


boyuan_mqtt_protocol::boyuan_mqtt_protocol()
{

}

boyuan_mqtt_protocol::~boyuan_mqtt_protocol()
{
	
}

int boyuan_mqtt_protocol::build_mqtt_heartbeat(string &topic,string & heart_beat_json)
{
    string esn  = config_ref.get_def(CK_ESN,"");
    string productKey = "QyWs4bVw3k6";

    topic  = "/sys/"+productKey+"/"+esn+"/thing/event/property/post";
    uint32_t seqNum = ++m_send_seq; 
    auto rsu_heart_beat_info = v2x_client_ref.get_heartbeat_info();

    double lat = rsu_heart_beat_info.latitude;
    double lon = rsu_heart_beat_info.longitude;

    lat /= LATITUDE_RES;
    lon /= LONGITUDE_RES;
    uint64_t now_ms = get_ms();

    auto frame =  build_head(esn,productKey,seqNum,now_ms);
    auto location =  json::object();
    location["lat"] = lat; 
    location["lon"] = lon; 

    auto baseinfo = json::object(); 
    baseinfo["seqNum"] = std::to_string(seqNum);
    baseinfo["rsuId"] = esn; /// 无要求，目前把 esn号当作 rsuId
    baseinfo["rsuEsn"] = esn;
    baseinfo["timestamp"] = now_ms/1000.0;
    baseinfo["protocolVersion"] = "V1.0";
    baseinfo["rsuStatus"] = std::to_string(rsu_heart_beat_info.dev_status);
    baseinfo["location"] = location ;
    baseinfo["transProtocal"] = 2 ;
    baseinfo["softwareVersion"] = get_package_ver() ;
    auto params = json::object(); 
    params["baseinfo"] = baseinfo;
    frame["params"] = params;
    heart_beat_json  = frame.dump();

    return RET_OK;
}
json boyuan_mqtt_protocol::build_head(string &esn, string &productKey,uint32_t seqNum, uint64_t now_ms)
{
    auto j = json{
        {"deviceName", esn}, 
        {"id", seqNum}, 
        {"method", "thing.event.property.post"},
        {"productKey", productKey},
        {"version","1.0"},
        {"timestamp",now_ms} ///<以毫秒为单位
    };

    return j;
}