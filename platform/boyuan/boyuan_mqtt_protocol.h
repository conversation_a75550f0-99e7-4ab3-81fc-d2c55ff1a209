/***********************************************
版本信息： 金溢科技版本权所有，保留一切权利
文件名称： boyuan_mqtt_protocol.h
作者：张明
开始日期：2023-09-26 11:53:38
完成日期：
当前版本号: v 0.0.1
主要功能: 接入 深智城&博远 5G智能网联
版本历史:
***********************************************/

#ifndef _BOYUAN_MQTT_PROTOCOL_H_
#define _BOYUAN_MQTT_PROTOCOL_H_
#include "nlohmann/json.hpp"
#include <string>
using namespace std;
using json = nlohmann::json;

class boyuan_mqtt_protocol
{
public:
	boyuan_mqtt_protocol();
	~boyuan_mqtt_protocol();
	int build_mqtt_heartbeat(string &topic,string & heart_beat_json);
private:
	json build_head(string &esn, string &productKey,uint32_t seqNum, uint64_t now_ms);
private:
	int m_send_seq{0};
	

};
#endif //_BOYUAN_MQTT_PROTOCOL_H_
