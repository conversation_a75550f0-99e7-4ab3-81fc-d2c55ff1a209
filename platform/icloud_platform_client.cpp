#include "icloud_platform_client.h"
#include "error_code.h"
#include "config.h"
#include "str_op.h"
#include "nlohmann/json.hpp"

#include "gv_platform_client.h"
#include "empty_platform_client.h"
#include "boyuan_platform_client.h"
#include "tianan_platform_client.h"
#include "debug_platform_client.h"
#include "multi_platform_client.h"
#include "tianan_obu_platform_client.h"

using json = nlohmann::json;

icloud_platform_client * create_single_mqtt_client(int platfrom_type )
{
    icloud_platform_client * pclient{nullptr};
    switch (platfrom_type)
    {
        case config::PT_GENVICT:
        {
            pclient = new(std::nothrow) gv_platform_client();
            break;
        }
        case config::PT_BOYUAN:
        {
            pclient = new(std::nothrow) boyuan_platform_client();
            break;
        }
        case config::PT_DEBUG:
        {
            pclient = new(std::nothrow) debug_platform_client(); 
            break;
        }
        case config::PT_TIANAN_OBU:
        {
            pclient =  new(std::nothrow) tianan_obu_platform_client();
            break;
        }
        case config::PT_TIANAN_RSU:
        {
            pclient =  new(std::nothrow) tianan_platform_client();
            break;
        }
        default:
        {
            break;
        }
    }
    return pclient;
}


icloud_platform_client * icloud_platform_client::create_client()
{
    string def_type = to_string(config::PT_EMPTY); 
    string mul_type = config_ref.get_def(CK_SYS_PLATFORM,def_type.c_str());
    list<string> client_list; 
    string split_str = MULTIC_VAL_SPLIT; 
    ENGINE_MODULE_NAMESPACE::split_string(mul_type,split_str[0],client_list); 

    multi_platform_client *p_mul_client = new(std::nothrow) multi_platform_client();
    for(auto & str_type : client_list)
    {
        int nType = std::stoi(str_type); 
         WLI("begin to create mqtt_client type[%d] name[%s] ",nType,config_ref.get_platform_type_desc(nType).c_str()); 
        auto p_client = create_single_mqtt_client(nType); 
        if(p_client == nullptr)
        {
            WLI("create mqtt_client type [%d] name[%s] fail",nType,config_ref.get_platform_type_desc(nType).c_str()); 
            continue;
        }
        WLI("create mqtt_client type [%d] name[%s] success",nType,config_ref.get_platform_type_desc(nType).c_str()); 
        p_mul_client->add_client(p_client); 
    }
    return p_mul_client;
}



void icloud_platform_client::destory_client(icloud_platform_client * & client)
{
    delete client;
    client =  nullptr;
}


icloud_platform_client::icloud_platform_client()
{

}

icloud_platform_client::~icloud_platform_client()
{
	
}

