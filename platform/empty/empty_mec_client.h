/***********************************************
版本信息： 金溢科技版本权所有，保留一切权利
文件名称： empty_mec_client.h
作者：张明
开始日期：2023-06-13 13:59:45
完成日期：
当前版本号: v 0.0.1
主要功能: 
版本历史:
***********************************************/

#ifndef _EMPTY_MEC_CLIENT_H_
#define _EMPTY_MEC_CLIENT_H_
#include "imec_client.h"

class empty_mec_client final :public imec_client
{
public:
	empty_mec_client();
	~empty_mec_client();
public : // interface imec_client 
	virtual int init() override;
    virtual void stop() override;
    virtual void destory() override;
    virtual void ontime_check(long long cur_ms)override;
    //返回值: 1: 表示 mec 在线 ， 0 表示mec 不再线
    virtual int get_mec_status() override ;  


};
#endif //_EMPTY_MEC_CLIENT_H_
