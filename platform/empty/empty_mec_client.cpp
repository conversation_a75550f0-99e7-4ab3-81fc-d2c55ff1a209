#include "empty_mec_client.h"
#include "stdafx.h"
#include "config.h"

empty_mec_client::empty_mec_client()
{

}


empty_mec_client::~empty_mec_client()
{
	
}
	
int empty_mec_client::init()
{
    return RET_OK;
}
void empty_mec_client::stop()
{
    
}
void empty_mec_client::destory()
{

}
void empty_mec_client::ontime_check(long long cur_ms)
{

}

int empty_mec_client::get_mec_status()
{
    return 1;
}
