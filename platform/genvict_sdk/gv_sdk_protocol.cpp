#include "gv_sdk_protocol.h"
#include "stdafx.h"
#include <arpa/inet.h>
#include "common_utility.h"
#include "engine_module_macro.h"
#include "engine_module_log.h"
const int GV_SDK_HEAD_SIZE = sizeof(gv_sdk_head);
const int GV_SDK_TAIL_SIZE = sizeof(gv_sdk_tail);

gv_sdk_protocol::gv_sdk_protocol()
{
}

gv_sdk_protocol::~gv_sdk_protocol()
{
}

int gv_sdk_protocol::bulid_frame(const uint8_t command, const string &json_str, string &send_buf)
{
    int json_len = json_str.length();
    int frame_len = make_sure_send_buf_size(json_len, send_buf);
    char *pdata = (char *)send_buf.data();
    gv_sdk_head &head = *(gv_sdk_head *)pdata;
    head.magic_stx[0] = 'S';
    head.magic_stx[1] = 'T';
    head.hight_command = 0;
    head.low_command = command;
    head.seq_num = htonl(m_seq_num++);
    head.data_len = htonl(json_len);
    char *send_data = pdata + GV_SDK_HEAD_SIZE;
    memcpy(send_data, json_str.c_str(), json_len);

    // calc crl
    char *crc_data = (pdata + sizeof(head.magic_stx));
    int crc_len = GV_SDK_HEAD_SIZE - sizeof(head.magic_stx) + json_len;
    gv_sdk_tail &tail = *(gv_sdk_tail *)(pdata + GV_SDK_HEAD_SIZE + json_len);
    tail.crc = htons(m_crc16.encode_x25(crc_data, crc_len));
    tail.magix_etx[0] = 'E';
    tail.magix_etx[1] = 'T';

    return frame_len;
}
int gv_sdk_protocol::fetch_frame(const char *pdata, uint32_t len, list<gv_sdk_frame> &frame_list)
{
    if (m_left_buffer.size() != 0)
    {
        m_left_buffer.append(pdata, len);
        m_fetch_buffer.assign(move(m_left_buffer));
        m_left_buffer = "";
        len = m_fetch_buffer.size();
        pdata = (const char *)m_fetch_buffer.c_str();
    }
    do
    {
        // 处理多帧
        if (GV_SDK_HEAD_SIZE > len)
        {
            // 回包不足一帧
            m_left_buffer.append(pdata, len);
            break;
        }
        // copy 头，可能重复转码

        auto head = *(gv_sdk_head *)(pdata);
        ntoh_head(head);
        if (false == check_head(head))
        {
            m_left_buffer = "";
            break;
        }
        // 一帧数据 =  头大小 + data_len + 尾
        uint32_t frame_len = GV_SDK_HEAD_SIZE + head.data_len + GV_SDK_TAIL_SIZE;
        if (frame_len > len)
        { // 不足一帧，则缓存
            m_left_buffer.append(pdata, len);
            break;
        }

        // 检查 crc
        const char *crc_data = (pdata + sizeof(head.magic_stx));
        int crc_len = GV_SDK_HEAD_SIZE - sizeof(head.magic_stx) + head.data_len;
        uint16_t crc_code = m_crc16.encode_x25(crc_data, crc_len);

        auto tail = *(gv_sdk_tail *)(pdata + GV_SDK_HEAD_SIZE + head.data_len);
        ntoh_tail(tail);
        if (false == check_tail(crc_code, tail))
        {
            m_left_buffer = "";
            break;
        }

        gv_sdk_frame rsp;
        rsp.head = head;
        rsp.data.assign(pdata + GV_SDK_HEAD_SIZE, head.data_len);
        frame_list.emplace_back(std::move(rsp));

        len = len - frame_len;
        pdata = pdata + frame_len;
    } while (len > 0);

    if (frame_list.empty())
    {
        return RET_FAIL;
    }
    return RET_OK;
}
void gv_sdk_protocol::ntoh_head(gv_sdk_head &head)
{
    head.seq_num = ntohl(head.seq_num);
    head.data_len = ntohl(head.data_len);
}
void gv_sdk_protocol::ntoh_tail(gv_sdk_tail &tail)
{
    tail.crc = ntohs(tail.crc);
}

bool gv_sdk_protocol::check_head(gv_sdk_head &head)
{
    if (head.magic_stx[0] != 'S' || head.magic_stx[1] != 'T')
    {
        WLE(" error head.magic_stx not ST %c%c ", head.magic_stx[0], head.magic_stx[1]);
        return false;
    }
    return true;
}
bool gv_sdk_protocol::check_tail(uint16_t crc, gv_sdk_tail &tail)
{
    if (crc != tail.crc)
    {
        WLE("crc error cal crc %d tail.crc %d ", crc, tail.crc);
        return false;
    }
    if (tail.magix_etx[0] != 'E' || tail.magix_etx[1] != 'T')
    {
        WLE("tail.magix_etx not ET invlid %c%c ", tail.magix_etx[0], tail.magix_etx[1]);

        return false;
    }
    return true;
}

int gv_sdk_protocol::make_sure_send_buf_size(int len, string &send_buf)
{
    size_t min_len = GV_SDK_HEAD_SIZE + GV_SDK_TAIL_SIZE + len;
    if (send_buf.size() < min_len)
    {
        send_buf.resize(min_len);
    }
    return min_len;
}

/***********************************************************
 * 函数名称: get_heartbeat_json
 * 功能描述: 构建 心跳包
 * 输入参数:
 * 输出参数:
 * 返 回 值:
 ***********************************************************/
int gv_sdk_protocol::get_heartbeat_json(string &data)
{
    auto j_v = nlohmann::json{
        {"time", get_us()}};
    data = j_v.dump();
    return RET_OK;
}

/***********************************************************
 * 函数名称: get_ack_json
 * 功能描述: 构建 ack json
 * 输入参数:
 * 输出参数:
 * 返 回 值:
 ***********************************************************/
int gv_sdk_protocol::get_ack_json(int sn, string &data)
{

    auto j = nlohmann::json{
        {"type", "ack"},
        {"value", ""}};
    auto j_v = nlohmann::json::object();
    j_v["seqnum"] = sn;
    j_v["errCode"] = 0;
    j_v["errDesc"] = "";
    j["value"] = j_v.dump();
    data = j.dump();
    return RET_OK;
}
int gv_sdk_protocol::get_report_status_json(gv_sdk_status_t &status, string &data)
{
    auto j = nlohmann::json{
        {"dev_status", status.dev_status},
        {"v2x_status", status.v2x_status},
        {"gnss_status", status.gnss_status},
        {"soft_ver", status.soft_ver},
        {"longitude", status.longitude},
        {"latitude", status.latitude},
        {"desc",status.desc}
    };

    data = j.dump();
    return RET_OK;
}

int gv_sdk_protocol::get_config_req_json(nlohmann::json & config, const string &rsp_data, string &data, bool &need_updata)
{

#if 0
{"system":{"log_level":4,"id":"40000028","ant_sw":1,"coordinate_type":1,
    "security":{"enable_security":0,"security_module":1,"security_cert_dir":"/home/<USER>/certs"},
    "ntrip":{"enable":0,"username":"qxcmobu005","password":"cdgvwh1"},
    "server_comm":{"msg_comm_info":{"print_recv_enable":1,"print_send_enable":0},
    "report_debug_info":{"enable":1,"print_enable":1,"msg_filters":"bsm"}}},
"obu":{
    
    "host_veh":{"veh_class":60,"width":202,"length":501,"height":401}, // 新增加

    "map":{"pos_match_any_enable":0,"heading_diff_threshold":30,"radius_correction":25},
    "sense_rtcm":{"enable":0,"wmm_enable":0,"net_enable":0},
    "sense_glosa":{"enable":1,"dis_threshold":10000,"speed_threshold":10}
    }
    }
#endif

    try
    {

        auto j = json::parse(rsp_data);
        auto j_bak = j;
        auto &system_json = j["system"];

        if(config.end()!= config.find("coordinate_type"))
        {
            system_json["coordinate_type"] =  config["coordinate_type"]; 
        }
        if(config.end()!= config.find("ant_sw"))
        {
            system_json["ant_sw"] =  config["ant_sw"]; 
        }
        if(config.end()!= config.find("security"))
        {
            system_json["security"] =  config["security"]; 
        }
        if(config.end()!= config.find("ntrip"))
        {
            system_json["ntrip"] =  config["ntrip"]; 
        }
        if(j.end() != j.find("obu") && config.end() != config.find("obu") )
        {
            auto & obu = j["obu"]; 
            auto & config_obu =  config["obu"] ;
            if(config_obu.end() != config_obu.find("host_veh") )
            {
                obu["host_veh"] =  config_obu["host_veh"];
            }
        }

        need_updata = (j == j_bak ? false : true);
        data = j.dump();
    }

    catch (exception &e)
    {
        WLE("parse json error %s errdesc %s ", rsp_data.c_str(), e.what());
        return RET_FAIL;
    }
    return RET_OK;
}
