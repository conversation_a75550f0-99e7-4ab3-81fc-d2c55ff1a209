/***********************************************
版本信息： 金溢科技版本权所有，保留一切权利
文件名称： gv_sdk_protocol.h
作者：张明
开始日期：2023-10-24 09:43:24
完成日期：
当前版本号: v 0.0.1
主要功能: 
版本历史:
***********************************************/

#ifndef _GV_SDK_PROTOCOL_H_
#define _GV_SDK_PROTOCOL_H_
#include "stdafx.h"
#include <stdint.h>
#include <string>
#include <list>
#include "crc16.h"
#include "nlohmann/json.hpp"
using json = nlohmann::json;
using namespace std;



enum sdk_command_enum
{
    SDK_HEARTBEAT = 0,  
    SDK_BSM = 1, 
	SDK_MAP = 2,
	SDK_RSM = 3,
	SDK_SPAT = 4, 
    SDK_RSI = 5,
	SDK_TEST = 6,
	SDK_RTCM = 7,      ///< value_PR_RTCMcorrections
	SDK_PAM = 8,       ///< value_PR_PAMData
	SDK_CLPMM = 9,     ///< value_PR_PlatooningManagementMessage
	SDK_PSM = 10,      ///< value_PR_PersonalSafetyMessage
	SDK_RSC = 11,      ///< value_PR_RoadsideCoordination
	SDK_SSM = 12 ,     ///< value_PR_SensorSharingMsg
	SDK_VIR =13 ,      ///< value_PR_VehIntentionAndRequest
	SDK_VPM = 14,      ///< value_PR_VehiclePaymentMessage
	SDK_HOST_BSM = 15, ///< host_bsm
	SDK_REPORT_STATUS = 16,   ////< 状态上报
	SDK_CONFIG_REQ = 17,  ///<配置查询 或者 更新，带json 刚更新，
	SDK_CONFIG_RSP = 18,  ///<配置回包命令
	SDK_TPS_VIM = 19 ,  ///<更新gps 及车辆相关数据
	SDK_ASN_BIN = 20 , ///< 发送 asn 编码数据 
    SDK_ACK = 50,
}; 


#pragma pack (1)
struct gv_sdk_head
{
	uint8_t magic_stx[2];    //2 byte	魔术字-协议包开始字段 ST
    uint8_t hight_command;
	uint8_t low_command;
    uint32_t seq_num;  
	uint32_t data_len; //报文净荷，详见“数据内容示例”
};

struct gv_sdk_tail
{
	uint16_t crc; 
	uint8_t magix_etx[2];
};
#pragma pack()
struct gv_sdk_frame
{
	gv_sdk_head head;
	string data;
}; 


struct gv_sdk_status_t
{
    uint8_t dev_status{0};   ///<  0正常；1异常
    uint8_t v2x_status{0};   ///<  0正常；1异常
    uint8_t gnss_status{0};  ///<  0正常；1异常
    string soft_ver;
    uint32_t longitude{0}; ///<设备坐标 
    uint32_t latitude{0};  ///<设备坐标
	string desc;  ///<状态描述 
	
};

class gv_sdk_protocol
{
public:
	gv_sdk_protocol();
	~gv_sdk_protocol();
	int fetch_frame(const char* pdata, uint32_t len,list<gv_sdk_frame> &frame_list);
	int bulid_frame(const uint8_t command , const string & json_str, string &send_buf );

	int get_heartbeat_json(string & data); 
	int get_ack_json(int sn,string & data);
	int get_report_status_json(gv_sdk_status_t & status, string & data); 
	int get_config_req_json(nlohmann::json & config, const string &rsp_data,string &data,bool & need_updata); 

private:
	int make_sure_send_buf_size(int len,string &send_buf);
	void ntoh_head(gv_sdk_head & head );
	void ntoh_tail(gv_sdk_tail & tail );
	bool check_head(gv_sdk_head & head);
	bool check_tail(uint16_t crc ,gv_sdk_tail &tail);


private:
	uint32_t m_seq_num{0}; 
	crc16 m_crc16;
	string m_fetch_buffer; // 必须使用双缓冲（m_fetch_buffer+m_left_buffer），因为 fetch_body 会返回 m_fetch_buffer 内数据
    string m_left_buffer;  //	

};
#endif //_GV_SDK_PROTOCOL_H_

