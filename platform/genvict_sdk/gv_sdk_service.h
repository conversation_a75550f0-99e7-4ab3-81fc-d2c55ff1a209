/***********************************************
版本信息： 金溢科技版本权所有，保留一切权利
文件名称： gv_sdk_service.h
作者：张明
开始日期：2023-10-24 09:41:58
完成日期：
当前版本号: v 0.0.1
主要功能: 
版本历史:
***********************************************/

#ifndef _GV_SDK_SERVICE_H_
#define _GV_SDK_SERVICE_H_
#include "iengine.h"
#include "gv_sdk_protocol.h"
#include "v2x_protocol.h"
#include "log.h"
#include "time_task.h"
#include <map>
#include "command_pattern.hpp"
#include "common_utility.h"
using namespace std;

class gv_sdk_service final :public itcpservice_receiver
{
	struct client_info_t
    {//每个客户端一个协议解释器，
        engine_conn_handler handler;
        gv_sdk_protocol protocol;
        int64_t last_heartbeat_sec {0};
        string send_buf;
    };
    typedef command_pattern<sdk_command_enum,int , gv_sdk_frame>  gv_sdk_command_type;

public:
	gv_sdk_service();
	~gv_sdk_service();
	
    int broadcast_msg(uint8_t command,const string &json_data);
    int send_msg(client_info_t & client_info,uint8_t command,string &json_data);
    

public:	//imec_client interface
    virtual int init() ;
    virtual void destory() ;
    virtual void ontime_check(long long cur_ms) ;

public:// ireceiver
    virtual void accept_cb(engine_conn_handler & new_client_handler) override;
    virtual void read_cb(engine_conn_handler & handler, const char* msg, int len) override;
    virtual void event_cb(int fd, short events ) override; 
private:// 处理 v2x 设备上报的 消息
    int proc_v2x_msg(sdk_command_enum command,const string & type,uint8_t &direct_i,string & s_json,string & str_value_json,shared_ptr< json> & value_json_obj);
    int proc_v2x_heartbeat(const rsp_type & type,v2x_client_respond & frame); 
    int proc_v2x_config_report_0CH(const rsp_type & type,v2x_client_respond & frame); 

private: // 处理 sdk 下发的命令
    int proc_sdk_heartbeat(const sdk_command_enum & command,int client_fd , gv_sdk_frame & frame);
    int proc_sdk_config_req(const sdk_command_enum & command,int client_fd , gv_sdk_frame & frame);
    int proc_sdk_ack(const sdk_command_enum & command,int client_fd , gv_sdk_frame & frame);

    int proc_sdk_publish_msg(const string &command_name,bool need_ack,const sdk_command_enum & command,int client_fd , gv_sdk_frame & frame);
    int proc_sdk_publish_asn_bin_msg(const string &command_name,bool need_ack,const sdk_command_enum & command,int client_fd , gv_sdk_frame & frame); 
private:
    int send_ack(client_info_t & client_info,gv_sdk_frame & frame ); 
private:
    void ontime_sendheartbeat(long long cur_ms);
    void ontime_checkclient(long long cur_ms);

    // 接收v2x_client 断开连接状态通知
    int v2x_client_disconnect_notice(const status_enum & cur_status,status_enum & enter_status); 

private:
	engine_conn_handler  m_listen_handler;
    map<int,client_info_t> m_all_client;
    gv_sdk_command_type m_sdk_command_pattern;
    time_task m_time_task; 
    gv_sdk_protocol m_protocol; // 广播用
    string m_send_buffer; // 广播用
    gv_sdk_status_t m_last_sdk_status;
};
#endif //_GV_SDK_SERVICE_H_
