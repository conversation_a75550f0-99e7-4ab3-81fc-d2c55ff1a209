#include "gv_sdk_service.h"
#include "stdafx.h"
#include "common_utility.h"
#include "service.h"



#define CHECK_CLIENT_INFO_FD(client_fd) \
    auto it_find = m_all_client.find(client_fd); \
    if(it_find == m_all_client.end()) \
    { \
        WLE("can not find client fd %d ",client_fd); \
        return RET_FAIL; \
    }\

#define CHECK_CLIENT_INFO_FD_NOT_RETCODE(client_fd) \
    auto it_find = m_all_client.find(client_fd); \
    if(it_find == m_all_client.end()) \
    { \
        WLE("can not find client fd %d ",client_fd); \
        return ; \
    }\


gv_sdk_service::gv_sdk_service()
{

    // v2x设备-注册命令处理函数
    auto & v2x_command = v2x_client_ref.get_command_pattern();

    v2x_command.regist_command(rsp_type::RSP_HEARTBEAT,this,std::bind(&gv_sdk_service::proc_v2x_heartbeat, FUN_BIND_2));
    v2x_command.regist_command(rsp_type::RSP_CONFIG_REPORT,this,std::bind(&gv_sdk_service::proc_v2x_config_report_0CH, FUN_BIND_2));   
    //注册-上报OBU预警信息（06H）
    #define V2X_MSG_PAR(comamnd_type) this,comamnd_type, std::placeholders::_1, std::placeholders::_2,std::placeholders::_3,std::placeholders::_4,std::placeholders::_5

        
    v2x_client_ref.get_rsp_report_command().regist_command("bsm",this,std::bind(&gv_sdk_service::proc_v2x_msg , V2X_MSG_PAR(SDK_BSM) ) );
    v2x_client_ref.get_rsp_report_command().regist_command("map",this,std::bind(&gv_sdk_service::proc_v2x_msg , V2X_MSG_PAR(SDK_MAP) ) );
    v2x_client_ref.get_rsp_report_command().regist_command("rsm",this,std::bind(&gv_sdk_service::proc_v2x_msg , V2X_MSG_PAR(SDK_RSM)  ) );
    v2x_client_ref.get_rsp_report_command().regist_command("spat",this,std::bind(&gv_sdk_service::proc_v2x_msg, V2X_MSG_PAR(SDK_SPAT) ) );
    v2x_client_ref.get_rsp_report_command().regist_command("rsi",this,std::bind(&gv_sdk_service::proc_v2x_msg , V2X_MSG_PAR(SDK_RSI) ) );
    v2x_client_ref.get_rsp_report_command().regist_command("test",this,std::bind(&gv_sdk_service::proc_v2x_msg , V2X_MSG_PAR(SDK_TEST) ) );
    v2x_client_ref.get_rsp_report_command().regist_command("rtcm",this,std::bind(&gv_sdk_service::proc_v2x_msg, V2X_MSG_PAR(SDK_RTCM) ) );
    v2x_client_ref.get_rsp_report_command().regist_command("pam",this,std::bind(&gv_sdk_service::proc_v2x_msg , V2X_MSG_PAR(SDK_PAM) ) );
    v2x_client_ref.get_rsp_report_command().regist_command("clpmm",this,std::bind(&gv_sdk_service::proc_v2x_msg,V2X_MSG_PAR(SDK_CLPMM) ) );
    v2x_client_ref.get_rsp_report_command().regist_command("psm",this,std::bind(&gv_sdk_service::proc_v2x_msg , V2X_MSG_PAR(SDK_PSM)  ) );
    v2x_client_ref.get_rsp_report_command().regist_command("rsc",this,std::bind(&gv_sdk_service::proc_v2x_msg , V2X_MSG_PAR(SDK_RSC) ) );
    v2x_client_ref.get_rsp_report_command().regist_command("ssm",this,std::bind(&gv_sdk_service::proc_v2x_msg , V2X_MSG_PAR(SDK_SSM) ) );
    v2x_client_ref.get_rsp_report_command().regist_command("vir",this,std::bind(&gv_sdk_service::proc_v2x_msg , V2X_MSG_PAR(SDK_VIR) ) );
    v2x_client_ref.get_rsp_report_command().regist_command("vpm",this,std::bind(&gv_sdk_service::proc_v2x_msg , V2X_MSG_PAR(SDK_VPM) ) );
    

    // 关注 v2x_client 进入断开状态
    v2x_client_ref.get_statsu_control().regist_enter_status_notice_fun(status_enum::disconnect,this,std::bind(&gv_sdk_service::v2x_client_disconnect_notice,FUN_BIND_2)); 




    //处理SDK发送来的数据 
    m_sdk_command_pattern.regist_command(sdk_command_enum::SDK_HEARTBEAT,this,std::bind(&gv_sdk_service::proc_sdk_heartbeat,FUN_BIND_3));
    m_sdk_command_pattern.regist_command(sdk_command_enum::SDK_CONFIG_REQ,this,std::bind(&gv_sdk_service::proc_sdk_config_req,FUN_BIND_3));

    
    m_sdk_command_pattern.regist_command(sdk_command_enum::SDK_ACK,this,std::bind(&gv_sdk_service::proc_sdk_ack, FUN_BIND_3));


    #define REGIST_SDK_COMMAND(sdk_command,name,need_ack_flag) m_sdk_command_pattern.regist_command(sdk_command,this,std::bind(&gv_sdk_service::proc_sdk_publish_msg,this,name,need_ack_flag,std::placeholders::_1, std::placeholders::_2,std::placeholders::_3))

    REGIST_SDK_COMMAND(SDK_BSM,"bsm",false); 
    REGIST_SDK_COMMAND(SDK_MAP,"map",true); 
    REGIST_SDK_COMMAND(SDK_RSM,"rsm",false); 
    REGIST_SDK_COMMAND(SDK_SPAT,"spat",false); 
    REGIST_SDK_COMMAND(SDK_RSI,"rsi",true); 
    REGIST_SDK_COMMAND(SDK_TEST,"test",false); 
    REGIST_SDK_COMMAND(SDK_RTCM,"rtcm",false); 
    REGIST_SDK_COMMAND(SDK_PAM,"pam",false); 
    REGIST_SDK_COMMAND(SDK_CLPMM,"clpmm",false); 
    REGIST_SDK_COMMAND(SDK_PSM,"psm",false); 
    REGIST_SDK_COMMAND(SDK_RSC,"rsc",false); 
    REGIST_SDK_COMMAND(SDK_SSM,"ssm",false); 
    REGIST_SDK_COMMAND(SDK_VIR,"vir",false); 
    REGIST_SDK_COMMAND(SDK_VPM,"vpm",false); 
    //REGIST_SDK_COMMAND(SDK_HOST_BSM,"host_bsm",false); 
    REGIST_SDK_COMMAND(SDK_HOST_BSM,"bsm",false);  // 发布时， host bsm 编码成 bsm 
    REGIST_SDK_COMMAND(SDK_TPS_VIM,"tps_vim",false);  //tps 更新 与 vim 更新

    m_sdk_command_pattern.regist_command(SDK_ASN_BIN,this,std::bind(&gv_sdk_service::proc_sdk_publish_asn_bin_msg,this,"asn_bin",false,std::placeholders::_1, std::placeholders::_2,std::placeholders::_3)); 



    m_time_task.regist_task(std::bind(&gv_sdk_service::ontime_sendheartbeat,FUN_BIND_1), 1000);// 1秒一次心跳
    m_time_task.regist_task(std::bind(&gv_sdk_service::ontime_checkclient,FUN_BIND_1), 10000); // 10 秒检查一次客户端连接

}


gv_sdk_service::~gv_sdk_service()
{
    v2x_client_ref.get_statsu_control().unregist_enter_status_notice_fun(status_enum::disconnect,this); 
}
	

/**
 * @brief 初始化
 * 
 * @return int 
 */
int gv_sdk_service::init()
{
    //注册 命令回调
    string ip =  config_ref.get_def(CK_GV_SDK_IP,"127.0.0.1");
    int port = config_ref.get_def(CK_GV_SDK_PORT,30025);

    m_listen_handler.init();
    if(RET_OK != engine_ref.regist_tcp_service(this,ip,port,m_listen_handler))
    {
        WLE("engine_ref.regist_tcp_service error  ip %s port %d ",ip.c_str(),port); 
        return RET_FAIL;
    }

    WLI("regist_tcp_service success ip %s port %d ",ip.c_str(),port); 
    return RET_OK;
}


/**
 * @brief 返回初始化
 * 
 */
void gv_sdk_service::destory()
{
    engine_ref.unregist(this,m_listen_handler); 
    
}


/**
 * @brief 定时任务
 * 
 * @param cur_ms 
 */
void gv_sdk_service::ontime_check(long long cur_ms)
{
    m_time_task.on_time(cur_ms);
}


/**
 * @brief ireceiver 接口-accept_cb
 * 
 * @param new_client_handler 
 */
void gv_sdk_service::accept_cb(engine_conn_handler & new_client_handler)
{
    WLI("new_client_handler.fd %d ",new_client_handler.fd);
    client_info_t info; 
    info.handler =  new_client_handler;
    info.last_heartbeat_sec = app_clock_ref.now_sec();

    m_all_client[new_client_handler.fd] =  info;
}

/**
 * @brief ireceiver 接口-read_cb
 * 
 * @param handler 
 * @param msg 
 * @param len 
 */
void gv_sdk_service::read_cb(engine_conn_handler & handler, const char* msg, int len)
{
    WLD("fd %d recv len %d ", handler.fd,len); 
    CHECK_CLIENT_INFO_FD_NOT_RETCODE(handler.fd);
    client_info_t & client_info  = it_find->second;

    list<gv_sdk_frame> frame_list;
    client_info.protocol.fetch_frame(msg,len,frame_list);
    if(frame_list.empty())
    {
        return ;
    }

    for(auto & frame: frame_list)
    {
        WLD("proc commend %d",frame.head.low_command); 
        m_sdk_command_pattern.do_command((sdk_command_enum)frame.head.low_command,handler.fd,frame);
    }
}


/**
 * @brief ireceiver 接口-event_cb  
 * 
 * @param fd 
 * @param events 
 */
void gv_sdk_service::event_cb(int fd, short events )
{
    WLD("fd %d events %d",fd,events );
    if (events & BEV_EVENT_ERROR)
    {
        WLE("fd %d BEV_EVENT_ERROR %s",fd,strerror(EVUTIL_SOCKET_ERROR()));
        auto it_find = m_all_client.find(fd); 
        if(it_find != m_all_client.end())
        {
            m_all_client.erase(it_find);
        }
    }
}



/**
 * @brief 处理v2x设备 向外发送的数据
 * 
 * @param command 
 * @param type 
 * @param direct_i 
 * @param s_json 
 * @param str_value_json 
 * @param value_json_obj 
 * @return int 
 */
int gv_sdk_service::proc_v2x_msg(sdk_command_enum command,const string & type,uint8_t &direct_i,string & s_json,string & str_value_json, shared_ptr< json> & value_json_obj)
{
    if(command == sdk_command_enum::SDK_BSM && direct_i == v2x_report_direct_enum::vrde_send )
    {
        command =  sdk_command_enum::SDK_HOST_BSM;
    }

    return broadcast_msg(command,str_value_json);
}



/**
 * @brief 
 * 
 * @param command 
 * @param client_fd 
 * @param frame 
 * @return int 
 */
int gv_sdk_service::proc_sdk_heartbeat(const sdk_command_enum & command,int client_fd , gv_sdk_frame & frame)
{
    CHECK_CLIENT_INFO_FD(client_fd);
    client_info_t & client_info  = it_find->second;
    client_info.last_heartbeat_sec =  app_clock_ref.now_sec();
    return RET_OK;
}


/**
 * @brief 
 * 
 * @param command 
 * @param client_fd 
 * @param frame 
 * @return int 
 */
int gv_sdk_service::proc_sdk_config_req(const sdk_command_enum & command,int client_fd , gv_sdk_frame & frame)
{
    if(frame.head.data_len == 0)
    {// 配置划询
        return v2x_client_ref.send_config_8CH(2,nullptr,0);
    }
    else
    {
        WLD("%s",frame.data.c_str()); 
        return v2x_client_ref.send_config_8CH(1,(char*)frame.data.c_str(),frame.head.data_len);
    }
}


/**
 * @brief 
 * 
 * @param command_name 
 * @param need_ack 
 * @param command 
 * @param client_fd 
 * @param frame 
 * @return int 
 */
int gv_sdk_service::proc_sdk_publish_msg(const string &command_name,bool need_ack,const sdk_command_enum & command,int client_fd , gv_sdk_frame & frame)
{
    WLI(" command_name %s need_ack %d ",command_name.c_str(),need_ack ); 
    try
    {
        auto j = nlohmann::json{
                {"type", command_name},
                {"value", frame.data}
            };
        string send_data =  j.dump();     
        if(need_ack == true )
        {
            CHECK_CLIENT_INFO_FD(client_fd);
            client_info_t & client_info  = it_find->second;
            send_ack(client_info,frame);

        }
        return v2x_client_ref.send_cv2x_json_81H((char*)send_data.c_str(),send_data.size());
    } 
    catch(exception & e)
    {
        WLE("catch error %s json %s ",e.what(),frame.data.c_str());
        return RET_FAIL;
    }
}


int gv_sdk_service::proc_sdk_publish_asn_bin_msg(const string &command_name,bool need_ack,const sdk_command_enum & command,int client_fd , gv_sdk_frame & frame)
{
    WLI(" command_name %s need_ack %d  data %s",command_name.c_str(),need_ack,frame.data.c_str() ); 

    // 解码
    MessageFrame_t * asn_v2x_frame = nullptr;
    string asn_bin_data;
    asn_bin_data.resize(frame.data.length()/2); 
    hex_str ohex; 
    ohex.decode((unsigned char*)frame.data.c_str(),frame.data.length(),(unsigned char*)asn_bin_data.data());
    asn_dec_rval_t rval = uper_decode(NULL,&asn_DEF_MessageFrame,
                                        (void **)&asn_v2x_frame,
                                        asn_bin_data.c_str(),asn_bin_data.size(),0,0
                                        );
    if(rval.code != RC_OK )
    {
        WLE("uper_decode error rval %d hex_data %s ",rval.code,frame.data.c_str());
        return RET_FAIL;
    }
    if(need_ack == true )
    {
        CHECK_CLIENT_INFO_FD(client_fd);
        client_info_t & client_info  = it_find->second;
        send_ack(client_info,frame);
    }
    v2x_client_ref.send_cv2x_asn_88H(*asn_v2x_frame);
    ASN_STRUCT_FREE(asn_DEF_MessageFrame,asn_v2x_frame);
    return RET_OK;
}

/**
 * @brief 
 * 
 * @param command 
 * @param client_fd 
 * @param frame 
 * @return int 
 */
int gv_sdk_service::proc_sdk_ack(const sdk_command_enum & command,int client_fd , gv_sdk_frame & frame)
{
    v2x_client_ref.send_last_command_ack();
    return RET_OK;
}


/**
 * @brief 向所有客户端广播此消息
 * 
 * @param command 
 * @param json_data 
 * @return int 
 */
int gv_sdk_service::broadcast_msg(uint8_t command,const string &json_data)
{
    //WLD("command[%d] data:%s",command,json_data.c_str()); 
    int frame_len = m_protocol.bulid_frame(command,json_data,m_send_buffer);
    for(auto & client_info_it:m_all_client)
    {
        auto & client_info = client_info_it.second;
        engine_ref.send_tcp_data(client_info.handler,m_send_buffer.c_str(),frame_len);
    }
    return RET_OK;
}
/**
 * @brief 
 * 
 * @param client_info 
 * @param command 
 * @param json_data 
 * @return int 
 */
int gv_sdk_service::send_msg(client_info_t & client_info,uint8_t command,string &json_data)
{
    int frame_len =client_info.protocol.bulid_frame(command,json_data, client_info.send_buf);
    return engine_ref.send_tcp_data(client_info.handler,client_info.send_buf.c_str(),frame_len);
}

/**
 * @brief 发送 ack 包
 * 
 * @param client_info 
 * @param frame 
 * @return int 
 */
int gv_sdk_service::send_ack(client_info_t & client_info,gv_sdk_frame & frame )
{
    string ack_json; 
    client_info.protocol.get_ack_json(frame.head.seq_num,ack_json);
    return send_msg(client_info,sdk_command_enum::SDK_ACK, ack_json ); 
}



/**
 * @brief 定时任务-发送心跳包
 * 
 * @param cur_ms 
 */
void gv_sdk_service::ontime_sendheartbeat(long long cur_ms)
{
    if(m_all_client.empty())
    {
        return ;
    }
    string heartbeat_json; 
    m_protocol.get_heartbeat_json(heartbeat_json); 
    broadcast_msg(sdk_command_enum::SDK_HEARTBEAT,heartbeat_json); 
}

/**
 * @brief 定时任务-检查客户端心跳状态
 * 
 * @param cur_ms 
 */
void gv_sdk_service::ontime_checkclient(long long cur_ms)
{
    auto cur_time_sec =  app_clock_ref.now_sec();
#if SDK_SERVICE_HEART_CHECK_STOP
    return ; 
#endif 
    auto all_client_map_tmp =  m_all_client; 
    for(auto & client_info_it : all_client_map_tmp)
    {
        auto & client_info = client_info_it.second;
        if(cur_time_sec - client_info.last_heartbeat_sec >= 300)
        {// 超过 5 分钟无心跳，则认为对端异常中断，需要断开连接

            WLI("erase fd %d ",client_info.handler.fd);
            engine_ref.unregist(this,client_info.handler);
            m_all_client.erase(client_info_it.first);
        }
    }
}

/**
 * @brief 处理 v2x 心跳包
 * 
 * @param type 
 * @param frame 
 * @return int 
 */
int gv_sdk_service::proc_v2x_heartbeat(const rsp_type & type,v2x_client_respond & frame)
{
    auto & s =  frame.data.heartBeatRsp;

    string status_json;
    m_last_sdk_status.dev_status =  s.dev_status; 
    m_last_sdk_status.gnss_status = s.gnss_status;
    m_last_sdk_status.v2x_status =  s.v2x_status; 
    m_last_sdk_status.soft_ver =  s.soft_ver;
    m_last_sdk_status.latitude =  s.latitude;
    m_last_sdk_status.longitude = s.longitude; 
    m_protocol.get_report_status_json(m_last_sdk_status,status_json);

    return broadcast_msg(sdk_command_enum::SDK_REPORT_STATUS,status_json); 
}

/**
 * @brief 处理v2x配置上报命令
 * 
 * @param type 
 * @param frame 
 * @return int 
 */
int gv_sdk_service::proc_v2x_config_report_0CH(const rsp_type & type,v2x_client_respond & frame)
{
    
    auto & configRsp = frame.data.configRsp;
    WLD("%s ",configRsp.json); 
    return broadcast_msg(sdk_command_enum::SDK_CONFIG_RSP,(const char*)configRsp.json); 
}

/**
 * @brief 检测到 v2x_client 连接异常，则广播异常状态
 * 
 * @param cur_status 
 * @param enter_status 
 */
int gv_sdk_service::v2x_client_disconnect_notice(const status_enum & cur_status,status_enum & enter_status)
{
    auto disconnect_sdk_status = m_last_sdk_status ;
    disconnect_sdk_status.dev_status = 1;  // 设备异常
    disconnect_sdk_status.desc = "v2x_client_dis_connect"; 
    string status_json;
    m_protocol.get_report_status_json(disconnect_sdk_status,status_json);
    broadcast_msg(sdk_command_enum::SDK_REPORT_STATUS,status_json); 
    return RET_OK;
}
