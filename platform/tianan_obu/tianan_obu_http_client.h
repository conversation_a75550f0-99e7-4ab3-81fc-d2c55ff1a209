/***********************************************
版本信息： 金溢科技版本权所有，保留一切权利
文件名称： tianan_obu_http_client.h
作者：张明
开始日期：2024-06-20 13:37:44
完成日期：
当前版本号: v 0.0.1
主要功能: 天安平台-先注册 
版本历史:
***********************************************/

#ifndef _TIANAN_OBU_PLATFORM_H_
#define _TIANAN_OBU_PLATFORM_H_
#include "iengine.h"
#include "ireceiver.h"

#include <functional>
#include <string>
#include "status_control.hpp"// 引入 状态管理，进行重新，关注，上报各种状态处理
#include "tianan_obu_protocal.h"


using namespace std; 
class tianan_obu_http_client : public ihttpclient_receiver
{
	typedef status_control<tianan_obu_http_client,status_enum> status_control_type;
    typedef status_control_type::status_info  status_info;

	typedef  std::function<void(tianan_obu_regist_rsp & )> PLATFORM_CB_FUN; 
public:
	tianan_obu_http_client(tianan_obu_protocal &protocal);
	~tianan_obu_http_client();
	inline void regist_platform_cb(PLATFORM_CB_FUN fun)
	{
		m_notice_fun = fun; 
	}
	bool init(); 
	void destory();
	void ontime_check(long long cur_ms); 
public: // interface ireceiver
    virtual void read_cb(engine_conn_handler & handler, const char* msg, int len) override;
    virtual void event_cb(int fd, short events ) override;
public: // interface ihttpclient_receiver
	virtual void update_conn_handler(engine_conn_handler & new_client_handler) override;
	virtual int get_conn_fd(void *conn_handler_data) override;

public: //状态管理函数
	int proc_status_logout(status_info & status);
	int proc_status_login_send(status_info & status);	
	int proc_status_login(status_info & status);
	int proc_status_keep_alive(status_info & status);
private:
	string get_test_json(); 
	string create_meid();

private:
	tianan_obu_protocal &m_protocal;

	status_control_type m_status_control;
	PLATFORM_CB_FUN m_notice_fun; 
	tianan_obu_regist_rsp m_regist_rsp ; 
	engine_conn_handler m_client_handler ; 


};
#endif //_TIANAN_OBU_PLATFORM_H_
