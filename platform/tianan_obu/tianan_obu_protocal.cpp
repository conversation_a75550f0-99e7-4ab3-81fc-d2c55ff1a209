#include "tianan_obu_protocal.h"
#include "common_utility.h"
#include <openssl/md5.h>
#include "service.h"
#include "hex_str.h"
#include "google/protobuf/stubs/strutil.h"

tianan_obu_protocal::tianan_obu_protocal()
{
}

tianan_obu_protocal::~tianan_obu_protocal()
{
}

int tianan_obu_protocal::init()
{
    int type  = config_ref.get_def(CK_TIANAN_OBU_MQTT_CONFIG_TYPE,2); // 为了兼容宜宾项目，宜宾项目不需要向平台获取,不需要加载 公钥 。
    if(type == 2 )
    {
        return RET_OK; 
    }
    

    auto pub_key_path = config_ref.get_def(CK_TIANAN_OBU_RSA_PUB_KEY_PAH, "");
    WLD("pub_key_path %s ",pub_key_path.c_str()); 
    
    if (false == m_rsa.load_public_key(pub_key_path))
    {
        return RET_FAIL;
    }
    return RET_OK;
}

int tianan_obu_protocal::build_mqtt_heartbeat(const string &meid, string &data)
{
    auto rsu_heart_beat_info = v2x_client_ref.get_heartbeat_info();
    auto j = nlohmann::json{
        {"id", get_msg_id()},
        {"meid", meid},
        {"tm", get_ms()},
        {"state", (rsu_heart_beat_info.dev_status == 0 ? "0000" : "99999")}
    };
    data = j.dump();
    return RET_OK;
}

int tianan_obu_protocal::build_http_obu_regist_data(const string &meid, http_req &req)
{
    string carId = meid;
    string iccid = meid;
    auto j = nlohmann::json{
        {"deviceType", "102"}, // 101:后视镜 ; 102:OBU ; 103:APP  ; 104:小程序
        {"meid", meid},
        {"carId", carId},
        {"iccid", iccid},
        {"protocol", "genvict_mqtt_10"} // #需要确认 协议ID

#if 0 // 以下为非必填
        ,
        {"ventor",},
        {"carType",},
        {"carNo",},
        {"vin",},
        {"tel",},
        {"name",},
        {"hdSer",},
        {"softSer",},
        {"memo",},
        {"extend",}
#endif
    };
    string data = j.dump();
    WLD(" data:%s ",data.c_str());

    // rsa 加密
    string rsa_encrypt_data;
    if (false == m_rsa.public_key_encrypt((unsigned char *)data.c_str(), data.length(), rsa_encrypt_data))
    {
        return RET_FAIL;
    }
    // 进行 base64 编码
    int base_length = google::protobuf::CalculateBase64EscapedLen(rsa_encrypt_data.length());
    string base_64_data; 
    base_64_data.resize(base_length); 
    int base_64_ret_size = google::protobuf::Base64Escape((unsigned char *)rsa_encrypt_data.data(),rsa_encrypt_data.length(),(char*)base_64_data.data(),base_length); 
    if(base_64_ret_size != base_length)
    {
        base_64_data.resize(base_64_ret_size); 
    }

    // 转成 curl 数据
    //string escape_data = curl_easy_escape(NULL, encrypt_data.c_str(), encrypt_data.length());
    string &escape_data =  base_64_data;

    // 生成 md5
    stringstream ss;
    string nonce =  "gvobu"; 
    auto timestame_ms = ENGINE_MODULE_NAMESPACE::get_ms(); 
    ss << "data=" << escape_data << "&nonce="<<nonce<<"&timestamp=" << timestame_ms;
    string md5_data = ss.str();
    unsigned char md5_code[MD5_DIGEST_LENGTH] = {0};
    MD5((const unsigned char *)md5_data.c_str(), md5_data.length(), md5_code);
    hex_str ohex;
    string m5d_code_hex;
    ohex.encode(md5_code, MD5_DIGEST_LENGTH, m5d_code_hex);


    req.form_datas.push_back({"data",escape_data});
    req.form_datas.push_back({"timestamp",std::to_string(timestame_ms)});
    req.form_datas.push_back({"nonce",nonce});
    req.form_datas.push_back({"sign",m5d_code_hex});

    return RET_OK;
}

int tianan_obu_protocal::fetch_http_obu_regist_rsp(const char *msg, int len, tianan_obu_regist_rsp &rsp)
{
#if 0
{
	"code": 0,
	"data": {
		"password": "v1djglcwr2m3n21g",
		"port": "1883",
		"host": "************",
		"memo": ""
	},
	"msg": "ok",
	"path": null,
	"extra": null,
	"timestamp": 1720139724442,
	"errorMsg": "",
	"isSuccess": true
}
#endif


    try
    {
        WLD("recv: %s",msg);
        auto j = json::parse(msg);

        auto code = j["code"].get<int>(); 
        if(code!=0&& code!=200)
        {
            WLE("Error return %s ", msg); 
            return RET_FAIL;
        }
        auto &data = j["data"];
        rsp.mqtt_info.username = data["username"].get<string>(); 
        rsp.mqtt_info.passwd = data["password"].get<string>(); 
        rsp.mqtt_info.hostname = data["host"].get<string>(); 
        rsp.mqtt_info.port = data["port"].get<int>(); 
        rsp.mqtt_info.client_id =  rsp.meid;
    }

    catch (exception &e)
    {
        WLE("parse json error %s errdesc %s ", msg, e.what());
        return RET_FAIL;
    }
    return RET_OK;
}

uint32_t tianan_obu_protocal::get_msg_id()
{
    static uint32_t msg_id = 1;
    return msg_id++;
}
int tianan_obu_protocal::build_mqtt_deviceinfo_up(const string & bsm_asn,const string &  meid,string &data)
{
    auto j = nlohmann::json{
        {"type", "OBU"},
        {"meid", meid},
        {"bsmDatas", bsm_asn},
        {"ts", std::to_string(get_ms())}
    };
    data = j.dump();
    return RET_OK;
}