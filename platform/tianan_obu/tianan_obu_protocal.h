/***********************************************
版本信息： 金溢科技版本权所有，保留一切权利
文件名称： tianan_obu_protocal.h
作者：张明
开始日期：2024-06-20 14:30:44
完成日期：
当前版本号: v 0.0.1
主要功能: 
版本历史:
***********************************************/

#ifndef _TIANAN_OBU_PROTOCAL_H_
#define _TIANAN_OBU_PROTOCAL_H_
#include "nlohmann/json.hpp"
#include "iengine.h"
#include "rsa_crypt.h"
using json = nlohmann::json;
using namespace std;

//

struct tianan_obu_regist_rsp
{
	string meid ; 
	mqtt_regist_info mqtt_info; 
}; 


class tianan_obu_protocal
{
public:
	tianan_obu_protocal();
	~tianan_obu_protocal();
	int init(); 

	int build_mqtt_heartbeat(const string & meid,string & data); 
	int build_mqtt_deviceinfo_up(const string & bsm_asn,const string &  meid,string &data); 

	int build_http_obu_regist_data(const string &meid, http_req &req); 
	int fetch_http_obu_regist_rsp(const char* msg, int len,tianan_obu_regist_rsp &rsp); 
	static uint32_t get_msg_id(); 
private:
	rsa_crypt m_rsa; 	

};
#endif //_TIANAN_OBU_PROTOCAL_H_
