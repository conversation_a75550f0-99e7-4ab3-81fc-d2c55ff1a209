/***********************************************
版本信息： 金溢科技版本权所有，保留一切权利
文件名称： tiannan_obu_mqtt_client.h
作者：张明
开始日期：2024-06-20 13:38:19
完成日期：
当前版本号: v 0.0.1
主要功能: 
版本历史:
***********************************************/

#ifndef _TIANNAN_OBU_MQTT_CLIENT_H_
#define _TIANNAN_OBU_MQTT_CLIENT_H_
#include "ireceiver.h"
#include "status_control.hpp"
#include "command_pattern.hpp"
#include "tianan_obu_protocal.h"
#include "v2x_protocol.h"
#include "time_task.h"


class tiannan_obu_mqtt_client:public imqttclient_receiver
{
	typedef status_control<tiannan_obu_mqtt_client,status_enum> status_control_type;
    typedef status_control_type::status_info  status_info;
public:
	tiannan_obu_mqtt_client(tianan_obu_protocal &protocal);
	~tiannan_obu_mqtt_client();
	bool init(); 
	void destory(); 
	void platform_cb(tianan_obu_regist_rsp & regist_rsp); 	
	void ontime_check(long long cur_ms); 

public: //状态管理函数
	int proc_status_logout(status_info & status);
    int proc_status_disconnect(status_info & status);
    int proc_status_connected(status_info & status);
	int proc_status_heartbeat_send(status_info & status);

public: // 接口 imqttclient_receiver
	virtual void read_cb(engine_conn_handler & handler, const char* msg, int len)  override; 
    virtual void event_cb(int fd, short events ) override; 
	virtual void message_cb(const struct mosquitto_message *message,const mosquitto_property *properties) override; 
private://mqtt 相关命令处理
	int init_mqtt_topic_proc_fun();
	int subcribe_mqtt_topic(); 
	int proc_topic_v2x_down(const string &topic, char * payload, int payload_len); 
	string get_mqtt_topic(const string & sub_topic);

private:
	int proc_rsu_asn_bin(const rsp_type & type,v2x_client_respond & frame); 
	void ontime_pub_bsm_msg(long long cur_ms); 

private:
	tianan_obu_protocal &m_protocal; 
	time_task m_time_task;
	
	status_control_type m_status_control;
	command_pattern <string, char*, int > m_topic_commander ; 

	tianan_obu_regist_rsp m_regist_rsp;
	engine_conn_handler m_mqtt_handler;
	string m_bsm_asn;
	atomic<uint32_t> m_bsm_count{0}; 
	uint32_t m_last_bsm_id{0}; 	
	 
};
#endif //_TIANNAN_OBU_MQTT_CLIENT_H_
