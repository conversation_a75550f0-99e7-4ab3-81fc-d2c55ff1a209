#include "tiannan_obu_mqtt_client.h"

#include "service.h"


tiannan_obu_mqtt_client::tiannan_obu_mqtt_client(tianan_obu_protocal &protocal):m_protocal(protocal),m_status_control(status_enum::logout,"tiannan_obu_mqtt_client",this)
{
    int reconnect_interval_ms = 3000;
    int heart_send_timeout_ms = 30000;
    m_status_control.regist_status(status_enum::logout, &tiannan_obu_mqtt_client::proc_status_logout, "logout", reconnect_interval_ms);
    m_status_control.regist_status(status_enum::disconnect, &tiannan_obu_mqtt_client::proc_status_disconnect, "disconnect", reconnect_interval_ms);
    m_status_control.regist_status(status_enum::connected, &tiannan_obu_mqtt_client::proc_status_connected, "connected", 0);
    m_status_control.regist_status(status_enum::heartbeat_send, &tiannan_obu_mqtt_client::proc_status_heartbeat_send, "heart_send", heart_send_timeout_ms);

    //注册 v2x消息
    v2x_client_ref.get_command_pattern().regist_command(rsp_type::RSP_ASN_BIN,this,std::bind(&tiannan_obu_mqtt_client::proc_rsu_asn_bin, FUN_BIND_2));

    // 每秒发一次 bsm
    m_time_task.regist_task(std::bind(&tiannan_obu_mqtt_client::ontime_pub_bsm_msg,FUN_BIND_1),1000); 

}


tiannan_obu_mqtt_client::~tiannan_obu_mqtt_client()
{
    m_time_task.unregist_task(0); 
}
	
bool tiannan_obu_mqtt_client::init()
{

    if(true==m_regist_rsp.mqtt_info.hostname.empty())
    {// 没有取到 云平台ID 初始不做其它
        return RET_OK;
    }
    if(RET_OK != engine_ref.regist_mqtt_client(this,m_regist_rsp.mqtt_info,m_mqtt_handler))
    {
        WLE("engine_ref.regist_mqtt_client error ");
        return RET_FAIL;
    }
    return true; 
}



void tiannan_obu_mqtt_client::destory()
{
    engine_ref.unregist(this,m_mqtt_handler);
}

void tiannan_obu_mqtt_client::platform_cb(tianan_obu_regist_rsp & regist_rsp)
{
    m_regist_rsp =  regist_rsp;
}

void tiannan_obu_mqtt_client::ontime_check(long long cur_ms)
{
    m_status_control.call_status_fun(cur_ms);
    m_time_task.on_time(cur_ms); 
}
//=========================================================
// 状态处理函数

//状态处理-等待云平台ID
int tiannan_obu_mqtt_client::proc_status_logout(status_info & status)
{
    if(true==m_regist_rsp.mqtt_info.hostname.empty())
    {
        return RET_OK; 
    }
    init_mqtt_topic_proc_fun(); // 
    m_status_control.enter_status(status_enum::disconnect); 
    return RET_OK;
}

//状态处理-连接断开
int tiannan_obu_mqtt_client::proc_status_disconnect(status_info & status)
{
    destory();
    init();
    return RET_OK;
}

//状态处理-连接成功
int tiannan_obu_mqtt_client::proc_status_connected(status_info & status)
{
    // 进行消息订阅
    if(RET_OK == subcribe_mqtt_topic())
    {
        m_status_control.enter_status(status_enum::heartbeat_send);
    }
    else
    {
        m_status_control.enter_status(status_enum::disconnect);
    }
    return RET_OK;
}

//状态处理-心跳发送
int tiannan_obu_mqtt_client::proc_status_heartbeat_send(status_info & status)
{
    // 发送心跳
    string req;
    if(RET_OK != m_protocal.build_mqtt_heartbeat(m_regist_rsp.meid,req))
    {
        WLE("m_protocal.build_mqtt_heartbeat error ");
        m_status_control.enter_status(status_enum::disconnect);
        return RET_OK;
    }
    string mqtt_topic = get_mqtt_topic("v2x/up/heartbeat/"); 

    WLD("topic %s data %s ",mqtt_topic.c_str(),req.c_str() );
    if(RET_OK!=engine_ref.mqtt_public(m_mqtt_handler,mqtt_topic,req.c_str(),req.size()))
    {
        WLE("engine_ref.mqtt_public error topic %s ", mqtt_topic.c_str());
        m_status_control.enter_status(status_enum::disconnect);
        return RET_OK;
    }
    return RET_OK;
}

void tiannan_obu_mqtt_client::read_cb(engine_conn_handler & handler, const char* msg, int len)   
{
    WLD("msg %s ",msg);
}
void tiannan_obu_mqtt_client::event_cb(int fd, short events )
{
    WLI("fd %d events %d ",fd, events);

    if(events & BEV_EVENT_CONNECTED)
    {// 连接成功
        //向MQTT注册 成功后则进入 connected 
        m_status_control.enter_status(status_enum::connected);

    }
    if(events & BEV_EVENT_ERROR)
    {// 连接断开
        m_mqtt_handler.init();
        m_status_control.enter_status(status_enum::disconnect);
    }
}
void tiannan_obu_mqtt_client::message_cb(const struct mosquitto_message *message,const mosquitto_property *properties)
{
    WLD(" topic %s  payload %s ", message->topic, (char*)message->payload);
    char * data =  (char *)(message->payload);
    int payloadlen = message->payloadlen;
    m_topic_commander.do_command(message->topic,data,payloadlen);
}

//=========================================================
// mqtt 主题处理函数 
int tiannan_obu_mqtt_client::init_mqtt_topic_proc_fun()
{
    m_topic_commander.unregist_all_command(this); 
    m_topic_commander.regist_command(get_mqtt_topic("v2x/down/"),this,std::bind(&tiannan_obu_mqtt_client::proc_topic_v2x_down,FUN_BIND_3)); 
    return RET_OK;
}
// 连接mqtt成功后，每次都需要重新订阅关注的主题
int tiannan_obu_mqtt_client::subcribe_mqtt_topic()
{
    // 运维消息
    list<string> list_topic;
    list_topic.push_back(get_mqtt_topic("v2x/down/"));

    for (auto &topic : list_topic)
    {
        if (RET_OK != engine_ref.mqtt_subscribe(m_mqtt_handler, topic))
        {
            return RET_FAIL;
        }
    }
    return RET_OK;
}


int tiannan_obu_mqtt_client::proc_topic_v2x_down(const string &topic, char * payload, int payload_len)
{
    WLD("recv [%s] data %s ",topic.c_str(),payload); 
    return RET_OK;
}


string tiannan_obu_mqtt_client::get_mqtt_topic(const string & sub_topic)
{
    return sub_topic + m_regist_rsp.meid; 
}
//=========================================================
// v2x 回调处理
int tiannan_obu_mqtt_client::proc_rsu_asn_bin(const rsp_type & type,v2x_client_respond & frame)
{
//  {"direct":"send","type":"asn","msg":"bsm","value":"0025ACEECDEC4EA62686C3EC9862602075E68CD7C1C3400160001F41F41FDFFFC04C81F4500000","aid":111,"verify_sign_errcode":0} 
    if(m_status_control.get_cur_status() < status_enum::connected)
    {
        WLD("mec is offline status %d ",m_status_control.get_cur_status());
        return RET_OK;
    }

    try
    {
        auto j = json::parse(frame.data.asnUper.json);
        auto msg_type = j["msg"].get<string>(); 
        auto msg_direct = j["direct"].get<string>(); 
        if(msg_type !="bsm")
        {
            return RET_OK; 
        }
        if(msg_direct != "send")
        {
            return RET_OK; 
        }
        m_bsm_count++;
        m_bsm_asn = j["value"].get<string>(); 
    }

    catch(exception & e)
    {
        WLE("catch error %s ",e.what());
        return RET_FAIL;
    }

    return RET_OK;
}
void tiannan_obu_mqtt_client::ontime_pub_bsm_msg(long long cur_ms)
{
    if(m_status_control.get_cur_status() < status_enum::connected)
    {
        return ; 
    }

    if(m_bsm_asn.empty() == true)
    {
        return ; 
    }

    if(m_last_bsm_id == m_bsm_count.load())
    {
        return ; 
    }

    m_last_bsm_id = m_bsm_count.load();

    string pub_topic = get_mqtt_topic("v2x/up/deviceInfo/"); 

   
    int type  = config_ref.get_def(CK_TIANAN_OBU_MQTT_CONFIG_TYPE,2); // 为了兼容宜宾项目，宜宾项目不需要向平台获取
    string *pub_data = &m_bsm_asn;// 宜宾项目直接发送 bsm 
    string ps_pub_data; 

    if(type == 1)
    {// 坪山项目-需要带类型等信息
        m_protocal.build_mqtt_deviceinfo_up(m_bsm_asn,m_regist_rsp.meid,ps_pub_data); 
        pub_data =  &ps_pub_data;
    }
     WLD("[%s] %s",pub_topic.c_str(),pub_data->c_str()); 
    if(RET_FAIL==engine_ref.mqtt_public(m_mqtt_handler,pub_topic,pub_data->data(),pub_data->length()))
    {
        WLE("engine_ref.mqtt_public error topic %s ", pub_topic.c_str());
        m_status_control.enter_status(status_enum::disconnect);
    }

}