#include "tianan_obu_platform_client.h"
#include "error_code.h"


tianan_obu_platform_client::tianan_obu_platform_client():m_platform(m_protocal),m_mqtt_client(m_protocal)
{
    auto fun = std::bind(&tiannan_obu_mqtt_client::platform_cb,&m_mqtt_client,std::placeholders::_1); 
    m_platform.regist_platform_cb(fun); 
}

tianan_obu_platform_client::~tianan_obu_platform_client()
{
	
}
	

int tianan_obu_platform_client::init()
{
    int ret = m_protocal.init();
    if(ret != RET_OK)
    {
        return ret; 
    }
    ret = m_platform.init(); 
    if(ret != RET_OK)
    {
        return ret;
    }
    ret =  m_mqtt_client.init(); 
    if(ret != RET_OK)
    {
        return ret; 
    }

    return ret;
}


int tianan_obu_platform_client::destory()
{
    m_mqtt_client.destory();
    m_platform.destory();
    return RET_OK;
}


void tianan_obu_platform_client::get_cloud_platform_id(string &id)
{
    id =  "tianan_obu_id"; 
}


int tianan_obu_platform_client::get_cloud_platform_status()
{
    return 1; 
}

void tianan_obu_platform_client::ontime_check(long long cur_ms)
{
    m_platform.ontime_check(cur_ms); 
    m_mqtt_client.ontime_check(cur_ms); 
}