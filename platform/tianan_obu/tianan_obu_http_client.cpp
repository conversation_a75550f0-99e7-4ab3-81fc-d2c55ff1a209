#include "tianan_obu_http_client.h"
#include "service.h"
#include "config.h"

tianan_obu_http_client::tianan_obu_http_client(tianan_obu_protocal &protocal):m_protocal(protocal),m_status_control(status_enum::logout,"tianan_obu_http_client",this)
{
    m_status_control.regist_status(status_enum::logout, &tianan_obu_http_client::proc_status_logout, "logout", 1000);
    m_status_control.regist_status(status_enum::login, &tianan_obu_http_client::proc_status_login, "login", 0);
    m_status_control.regist_status(status_enum::login_send, &tianan_obu_http_client::proc_status_login_send, "login_send", 10000);
    m_status_control.regist_status(status_enum::keep_alive, &tianan_obu_http_client::proc_status_keep_alive, "keep_alive", 3600000); // 一小时执行一次此时，
    
    m_regist_rsp.meid = create_meid(); 
}

tianan_obu_http_client::~tianan_obu_http_client()
{
	
}
	
bool tianan_obu_http_client::init()
{

    int type  = config_ref.get_def(CK_TIANAN_OBU_MQTT_CONFIG_TYPE,2); // 为了兼容宜宾项目，宜宾项目不需要向平台获取
    WLD("mqtt_config_type %d ",type); 
    if(type == 2 )
    {
        auto & mqtt_info = m_regist_rsp.mqtt_info; 
        mqtt_info.client_id =  config_ref.get_def(GET_TIANAN_OBU_MQTT_KEY(MQTT_CLIENT_ID),""); 
        mqtt_info.username = config_ref.get_def(GET_TIANAN_OBU_MQTT_KEY(MQTT_USER),""); 
        mqtt_info.passwd = config_ref.get_def(GET_TIANAN_OBU_MQTT_KEY(MQTT_PASSWD),""); 
        mqtt_info.hostname = config_ref.get_def(GET_TIANAN_OBU_MQTT_KEY(MQTT_HOST),"127.0.0.1"); 
        mqtt_info.port = config_ref.get_def(GET_TIANAN_OBU_MQTT_KEY(MQTT_PORT),1883); 
        m_status_control.enter_status(status_enum::login);
        return RET_OK;
    }

    // 进行注册 
    string req_url = config_ref.get_def(CK_TIANAN_OBU_HTTP_URL,"") + "/v2x/open/register"; 
    WLI("req_url %s ",req_url.c_str()); 

    http_req req;
    m_protocal.build_http_obu_regist_data(m_regist_rsp.meid,req); 
    if(RET_OK != engine_ref.regist_http_client(this,req_url,m_client_handler))
    {
        WLE("engine_ref.regist_http_client error url %s ",req_url.c_str());
        return RET_FAIL;
    }
    
    if(RET_OK != engine_ref.http_form_submit(m_client_handler,req))
    {
        WLE("engine_ref.http_post error url %s ", req_url.c_str());
        return RET_FAIL; 
    }
    m_status_control.enter_status(status_enum::login_send);
    return true;
}
void tianan_obu_http_client::destory()
{
    WLD("ENTER");
}

void tianan_obu_http_client::ontime_check(long long cur_ms)
{
    m_status_control.call_status_fun(cur_ms);
}



//=========================================================
// 状态处理函数

int tianan_obu_http_client::proc_status_logout(status_info & status)
{
    destory();
    init();
    return RET_OK;
}

int tianan_obu_http_client::proc_status_login_send(status_info & status)
{
    // login 状态超时，刚进行退出来状态，重新发起请求
    m_status_control.enter_status(status_enum::logout);
    return RET_OK;
}


// 成功获取云平台ID后断开连接，不再需要http连接
int tianan_obu_http_client::proc_status_login(status_info & status)
{
    // 登录成功后，调用回调函数 
    if(m_notice_fun)
    {// 几上通知mqtt 相关信息
        m_notice_fun(m_regist_rsp); 
    }

    m_status_control.enter_status(status_enum::keep_alive);
    return RET_OK;
}

// keep_alive 不做什么
int tianan_obu_http_client::proc_status_keep_alive(status_info & status)
{
    m_status_control.enter_status(status_enum::keep_alive);
    return RET_OK;
}


void tianan_obu_http_client::update_conn_handler(engine_conn_handler & new_client_handler)
{
    m_client_handler =  new_client_handler;
}

int tianan_obu_http_client::get_conn_fd(void *conn_handler_data) 
{
    return m_client_handler.fd;
}

void tianan_obu_http_client::event_cb(int fd, short events )
{
    WLD("fd %d evenets %d ",fd, events) ;
    if(events == BEV_EVENT_ERROR )
    {// http 需要处理
        m_client_handler.init();
    }
}

void tianan_obu_http_client::read_cb(engine_conn_handler & handler, const char* msg, int len)
{
    //fetch_mqtt_info(msg,len);
    if(RET_OK!=m_protocal.fetch_http_obu_regist_rsp(msg,len, m_regist_rsp))
    {
        m_status_control.enter_status(status_enum::logout); 
    }
    else 
    {
        m_status_control.enter_status(status_enum::login); 
    }
}
string tianan_obu_http_client::create_meid()
{
    
#if 0 
// 
meid 编码 长度为 40
XXXXYYYYZZZZ...
XXXX 4位为厂家代号, JY00
YYY 3位代表设备类型, OBU为008
ZZZ... 设备唯一识别码
#endif
    string esn = config_ref.get_def(CK_ESN,"");
    int type  = config_ref.get_def(CK_TIANAN_OBU_MQTT_CONFIG_TYPE,2); // 为了兼容宜宾项目，宜宾项目不需要向平台获取
    if(type == 2)
    {
        return esn ; 
    }
    
    string meid =  "GVLB008";  //meid 为 20 位
    size_t esn_max_size =  20 -  meid.size();
    if(esn.size() > esn_max_size)
    {
        string new_esn = esn.substr(esn.size() -  esn_max_size); 
        esn.assign(std::move(new_esn)); 
    }
    meid += esn; 
    WLD("meid[%s] size %d",meid.c_str(),meid.size()); 

    return meid; 
}

