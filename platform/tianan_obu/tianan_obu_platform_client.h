/***********************************************
版本信息： 金溢科技版本权所有，保留一切权利
文件名称： tianan_obu_platform_client.h
作者：张明
开始日期：2024-06-20 11:07:53
完成日期：
当前版本号: v 0.0.1
主要功能: 
版本历史:
***********************************************/

#ifndef _TIANAN_OBU_PLATFORM_CLIENT_H_
#define _TIANAN_OBU_PLATFORM_CLIENT_H_
#include "icloud_platform_client.h"
#include "tiannan_obu_mqtt_client.h"
#include "tianan_obu_http_client.h"
#include "tianan_obu_protocal.h"

class tianan_obu_platform_client:public icloud_platform_client
{

public:
	tianan_obu_platform_client();
	virtual ~tianan_obu_platform_client();
	virtual int init() override;
	virtual int destory() override;
	virtual void get_cloud_platform_id(string &id)override;
	virtual int get_cloud_platform_status()override;
	virtual void ontime_check(long long cur_ms)override;
private:
	tianan_obu_protocal	m_protocal; 
	tianan_obu_http_client	m_platform; 
	tiannan_obu_mqtt_client m_mqtt_client;

};
#endif //_TIANAN_OBU_PLATFORM_CLIENT_H_
