#include "multi_mec_client.h"
#include "error_code.h"

multi_mec_client::multi_mec_client()
{

}

multi_mec_client::~multi_mec_client()
{
	destory();
}
	
int multi_mec_client::add_client(imec_client * client)
{
    m_clients.push_back(client); 
    return RET_OK;
}

int multi_mec_client::init() 
{
    int ret =  RET_OK;
    for(auto & it : m_clients)
    {
        ret = it->init(); 
        if(ret != RET_OK)
        {
            return ret;
        }
    }
    return ret; 
}

void multi_mec_client::stop() 
{
    for(auto & it : m_clients)
    {
        it->stop();
    }
}
void multi_mec_client::destory() 
{
    for(auto &it :m_clients)
    {
        it->destory();
        delete it; 
    }
    m_clients.clear();
}

void multi_mec_client::ontime_check(long long cur_ms)
{
    for(auto &it :  m_clients)
    {
        it->ontime_check(cur_ms); 
    }

}


int multi_mec_client::get_mec_status()
{
    if(m_clients.empty())
    {
        return 0; 
    }
    auto it =  m_clients.begin(); 
    return (*it)->get_mec_status();
}
