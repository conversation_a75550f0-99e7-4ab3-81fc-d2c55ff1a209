/***********************************************
版本信息： 金溢科技版本权所有，保留一切权利
文件名称： multi_mec_client.h
作者：张明
开始日期：2024-06-20 08:52:56
完成日期：
当前版本号: v 0.0.1
主要功能: 
版本历史:
***********************************************/

#ifndef _MULTI_MEC_CLIENT_H_
#define _MULTI_MEC_CLIENT_H_
#include "imec_client.h"
#include <list>
using namespace std;

class multi_mec_client:public imec_client
{
public:
	multi_mec_client();
	virtual ~multi_mec_client();
	int add_client(imec_client * client); 

	virtual int init() override;
    virtual void stop() override;
    virtual void destory() override;
    virtual void ontime_check(long long cur_ms) override;

    //返回值: 1: 表示 mec 在线 ， 0 表示mec 不再线
    virtual int get_mec_status() override;
private:
	list<imec_client*> m_clients;
};
#endif //_MULTI_MEC_CLIENT_H_
