#include "multi_platform_client.h"
#include "error_code.h"

multi_platform_client::multi_platform_client()
{

}

multi_platform_client::~multi_platform_client()
{
	destory(); 
}

int multi_platform_client::add_client(icloud_platform_client * client)
{
    m_clients.push_back(client);
    return RET_OK;
}


	
int multi_platform_client::init()
{
    int ret = RET_OK;
    for(auto & it:m_clients)
    {
        ret = it->init(); 
        if(RET_OK != ret)
        {
            return ret;
        }
    }
    return ret;
}


int multi_platform_client::destory()
{
    int ret = RET_OK;
    for(auto & it:m_clients)
    {
        ret = it->destory(); 
        delete it;
    }
    m_clients.clear();
    return ret;
}


void multi_platform_client::get_cloud_platform_id(string &id)
{
    if(m_clients.empty())
    {
        return ; 
    }
    auto it = m_clients.begin(); 
    (*it)->get_cloud_platform_id(id); 
}

int multi_platform_client::get_cloud_platform_status()
{
    if(m_clients.empty())
    {
        return 0; 
    }
    auto it =  m_clients.begin(); 
    return (*it)->get_cloud_platform_status(); 
}

void multi_platform_client::ontime_check(long long cur_ms)
{
    for(auto &it:m_clients)
    {
        it->ontime_check(cur_ms); 
    }
}