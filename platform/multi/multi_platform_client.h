/***********************************************
版本信息： 金溢科技版本权所有，保留一切权利
文件名称： multi_platform_client.h
作者：张明
开始日期：2024-06-20 08:53:12
完成日期：
当前版本号: v 0.0.1
主要功能: 多平台并行管理
版本历史:
***********************************************/

#ifndef _MULTI_PLATFORM_CLIENT_H_
#define _MULTI_PLATFORM_CLIENT_H_
#include "icloud_platform_client.h"
#include <list>
using namespace std; 
class multi_platform_client:public icloud_platform_client
{
public:
	multi_platform_client();
	virtual ~multi_platform_client();
	int add_client(icloud_platform_client * client); 

	virtual int init() override;
	virtual int destory() override;
	virtual void get_cloud_platform_id(string &id)override;
	virtual int get_cloud_platform_status()override;
	virtual void ontime_check(long long cur_ms)override;
private:
	list<icloud_platform_client*> m_clients; 
	


};
#endif //_MULTI_PLATFORM_CLIENT_H_
