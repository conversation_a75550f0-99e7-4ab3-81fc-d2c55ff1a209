/***********************************************
版本信息： 金溢科技版本权所有，保留一切权利
文件名称： baidu_mec_client.h
作者：张明
开始日期：2022-12-12 14:39:27
完成日期：
当前版本号: v 0.0.1
主要功能: 接入百度MEC ，UDP 默认情况下 RSU 绑定 30500 端口，MEC 绑定 30501 端口
版本历史:
***********************************************/

#ifndef _BAIDU_MEC_CLIENT_H_
#define _BAIDU_MEC_CLIENT_H_
#include "ireceiver.h"
#include "iengine.h"
#include "stdafx.h"
#include "baidu_mec_protocol.h"
#include "imec_client.h"
#include "status_control.hpp"
#include "v2x_protocol.h"


/**
* @brief 百度mec客户端
*/
class baidu_mec_client final :public imec_client,ireceiver
{

    typedef status_control<baidu_mec_client,status_enum> status_control_type;
    typedef status_control_type::status_info  status_info;

public:
    baidu_mec_client();
    virtual ~baidu_mec_client();

public:	//imec_client interface
    virtual int init() override;
    virtual void stop() override;
    virtual void destory() override;
    virtual void ontime_check(long long cur_ms) override;
    virtual int get_mec_status() override;

public: // ireceiver interface
    virtual void read_cb(engine_conn_handler & handler,const char* msg, int len) override;
    virtual void event_cb(int fd, short events ) override ;

private: // 协议桢处理类
    int proc_frame_heartbeat(baidu_mec_frame & frame);

private:
    // 协议状态管理-函数
    int proc_status_disconnect(status_info & status);
    int proc_status_connected(status_info & status);
    int proc_status_heartbeat_send(status_info & status);


private:
    //处理 rsu数据回调
    int proc_rsu_asn_bin(const rsp_type & type,v2x_client_respond & frame);

private:
    engine_conn_handler m_rsu_udp_listen_handler;
    engine_conn_handler m_mec_udp_listen_handler;

    baidu_mec_protocol m_protocol; // 协议分析类
    status_control_type m_status_control; // 状态管理类

    time_t m_last_heartbeat_tm{0};
    int m_offline_sec {15};
    packet_info m_send_buf; // 发送数据缓冲区
};
#endif //_BAIDU_MEC_CLIENT_H_
