/***********************************************
版本信息： 金溢科技版本权所有，保留一切权利
文件名称： baidu_mec_protocol.h
作者：张明
开始日期：2022-12-12 16:37:22
完成日期：
当前版本号: v 0.0.1
主要功能:
版本历史:
***********************************************/

#ifndef _BAIDU_MEC_PROTOCOL_H_
#define _BAIDU_MEC_PROTOCOL_H_

#include "stdafx.h"
#include <arpa/inet.h>
#include "hex_str.h"
#include "imec_client.h"
#include "mec_asn/GoEMECMessageFrame.h"



//baidu_mec_frame::frame_type 定义
#define BDMF_SEND 1  ///< 百度MEC帧类型：发送帧 MEC->RSU
#define BDMF_RECV 3  ///< 百度MEC帧类型：接收帧 RSU->MEC 
#define BDMF_HEARTBEAT 20 ///< 百度MEC帧类型：心跳帧



#pragma pack (0)
/**
* @brief 百度mec协议帧，字段头
*/
struct baidu_head_kv
{

    //转为本地字节序
    void ntoh()
    {
        key =  ntohs(key);
        len =  ntohs(len);
        val =  ntohl(val);
    }

    //转为网络字节序
    void hton()
    {
        key =  htons(key);
        len =  htons(len);
        val =  htonl(val);
    }

    uint16_t key;
    uint16_t len;
    uint32_t val;
};


#pragma pack()

/**
* @brief 百度mec协议帧
*/
struct baidu_mec_frame
{
    uint32_t frame_type {1};   ///< 帧类型
    uint32_t sed {0};          ///< 报文序列号 每次加 1
    uint32_t aid {111};        ///< AID，无符号整型，范围为 0~16511。 应用 ID，默认 111
    uint32_t send_interval_ms {100}; ///< 业务周期，无符号整型，取值 [0,12]， 0---oneshot， 11---20ms， 12--- 50ms 1..10---100ms..1000ms， 默认为 1
    uint32_t protocal_type{4}; ///< 协议类型，无符号整型， 0---保留， 1---保留，2---保留，3---保留，4---DSMP 协议， 5~255 保留 默认 为 4
    uint32_t pri {208};         ///<  优先级，无符号整型，0~255, 默认 208

    uint32_t traffic_id {65535}; ///< traffic id，用于支持业务并发，有效取值范围是 0~9, 65535，默认配置为 65535，表示不区分
    uint32_t ac_mode {1}; ///< 大小模式指示，1---大模式；0---小模式；
    uint32_t ac_type {1}; ///< 指示使用的证书类型，1---假名证书；2---应用证书；3---身份证书
    uint32_t ac_id {0}; ///<  证书id
    uint16_t data_len{0}; ///< 待发送时数据长度
    unsigned char *pdata {nullptr};  ///< 待发送时数据
    MessageFrame_t * asn_v2x_frame {nullptr}; ///< asn 帧
};

/**
* @brief 百度mec客户端协议解释
*/
class baidu_mec_protocol final
{
public:
    baidu_mec_protocol();
    ~baidu_mec_protocol();
    // 协议分析，提取协议帧
    int init();

    int fetch_frame(const char* msg, uint32_t len,baidu_mec_frame & frame);
    int buid_heartbeat_frame(const packet_info & in, packet_info & out);
    int build_report_asn_frame(const packet_info & in, packet_info & out,uint32_t aid,uint32_t verify_sign_errcode,u_char *asn_hex,uint16_t asn_len_hex);

private:
    int set_head(packet_info & out,uint16_t key,uint16_t len,uint32_t val);
private:
    uint32_t m_seq_id{1};






};
#endif //_BAIDU_MEC_PROTOCOL_H_
