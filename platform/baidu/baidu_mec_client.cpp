#include "baidu_mec_client.h"
#include "stdafx.h"
#include "config.h"
#include "service.h"
#include "hex_str.h"
#include "nlohmann/json.hpp"
#include "app_clock.h"
#include "event2/event.h"
#include "event2/bufferevent.h"
using json = nlohmann::json;

baidu_mec_client::baidu_mec_client():m_status_control(status_enum::disconnect,"baidu_mec_client",this)
{
    m_status_control.regist_status(status_enum::disconnect,&baidu_mec_client::proc_status_disconnect,"disconnect",10000);// 断线重连接 为10 秒
    m_status_control.regist_status(status_enum::connected,&baidu_mec_client::proc_status_connected,"connected",0);
    m_status_control.regist_status(status_enum::heartbeat_send,&baidu_mec_client::proc_status_heartbeat_send,"heartbeat_send",1000);// 此状态为中间状态

    int send_pack_size = 64*1024;
    m_send_buf.pdata =  new char[send_pack_size];//udp 包最大 64 K
    m_send_buf.len = send_pack_size;
    m_last_heartbeat_tm = app_clock_ref.now_sec();

}

baidu_mec_client::~baidu_mec_client()
{
    delete[] m_send_buf.pdata;
    m_send_buf.pdata = nullptr;
    m_send_buf.len = 0;
}

int baidu_mec_client::init()
{
    //WLI("ENTER");

    m_protocol.init();
    m_last_heartbeat_tm = app_clock_ref.now_sec();

    string mec_ip,rsu_ip ;
    int mec_port,rsu_port;
    config_ref.get(CK_BAIDU_MEC_UDP_IP,mec_ip);
    config_ref.get(CK_BAIDU_MEC_UDP_PORT,mec_port);
    config_ref.get(CK_BAIDU_RSU_UDP_IP,rsu_ip);
    config_ref.get(CK_BAIDU_RSU_UDP_PORT,rsu_port);
    config_ref.get(CK_BAIDU_OFFLINE_SEC,m_offline_sec);

    int nRet  = engine_ref.regist_udp_service(this,rsu_ip,rsu_port,m_rsu_udp_listen_handler);
    if(nRet !=RET_OK)
    {
        WLE("regist_udp_service error rsu ip %s port %d  ",rsu_ip.c_str(),rsu_port);
        return nRet;
    }

    nRet = engine_ref.regist_udp_client(this,mec_ip,mec_port,m_mec_udp_listen_handler);
    if(nRet !=RET_OK)
    {
        WLE("regist_udp_client error mec ip %s port %d  ",mec_ip.c_str(),mec_port);
        engine_ref.unregist(this,m_rsu_udp_listen_handler);
        return nRet;
    }
    WLI("mec ip %s port %d  fd %d ",mec_ip.c_str(),mec_port,m_mec_udp_listen_handler.fd);
    m_status_control.enter_status(status_enum::connected);
    
    //注册 命令回调
    v2x_client_ref.get_command_pattern().regist_command(rsp_type::RSP_ASN_BIN,this,std::bind(&baidu_mec_client::proc_rsu_asn_bin, FUN_BIND_2));

    return RET_OK;
}

void baidu_mec_client::stop()
{
    WLI("ENTER");
}

void baidu_mec_client::destory()
{
    WLI("ENTER");
    engine_ref.unregist(this,m_rsu_udp_listen_handler);
    engine_ref.unregist(this,m_mec_udp_listen_handler);

    engine_conn_handler init_handel;
    m_rsu_udp_listen_handler = init_handel;
    m_mec_udp_listen_handler = init_handel;
}

void baidu_mec_client::ontime_check(long long cur_ms)
{
    //WLI("ENTER");
    m_status_control.call_status_fun(cur_ms);
}

int baidu_mec_client::get_mec_status()
{
    if(m_status_control.get_cur_status()>=status_enum::connected)
    {
        return 1;
    }
    return 0;
}

void baidu_mec_client::read_cb(engine_conn_handler & handler,const char* msg, int len)
{
    WLD("fd %d len %d ",handler.fd, len );
    baidu_mec_frame frame;
    if(RET_FAIL == m_protocol.fetch_frame(msg,len,frame))
    {
        if(frame.asn_v2x_frame !=nullptr)
        {
            ASN_STRUCT_FREE(asn_DEF_MessageFrame,frame.asn_v2x_frame);
            frame.asn_v2x_frame =  nullptr;
        }
        return ;
    }
    if(frame.frame_type == BDMF_HEARTBEAT)
    {
        proc_frame_heartbeat(frame);
    }
    else if(frame.frame_type == BDMF_SEND)
    {
        v2x_client_ref.send_cv2x_asn_88H(*frame.asn_v2x_frame);
    }
    else
    {
        WLE("not support frame_type %d ",frame.frame_type);
    }

    if(frame.asn_v2x_frame != nullptr)
    {
        ASN_STRUCT_FREE(asn_DEF_MessageFrame,frame.asn_v2x_frame);
        frame.asn_v2x_frame =  nullptr;
    }
}

void baidu_mec_client::event_cb(int fd, short events )
{
    WLD("fd %d,events %d",fd,events);
    
    if(events & BEV_EVENT_ERROR)
    {
        // 处理异常事件
        if(fd == m_mec_udp_listen_handler.fd)
        {
            m_mec_udp_listen_handler.init();
        }

        m_status_control.enter_status(status_enum::disconnect);
    }
}

int baidu_mec_client::proc_frame_heartbeat(baidu_mec_frame & frame)
{
    // 更新 心跳数据
    WLD("ENTER");
    m_last_heartbeat_tm = app_clock_ref.now_sec(); 
    return RET_OK;
}



int baidu_mec_client::proc_status_disconnect(status_info & status)
{
    destory();
    if(RET_OK==init())
    {
        m_status_control.enter_status(status_enum::connected);
    }

    return RET_OK;
}

int baidu_mec_client::proc_status_connected(status_info & status)
{
    m_status_control.enter_status(status_enum::heartbeat_send);
    return RET_OK;
}

int baidu_mec_client::proc_status_heartbeat_send(status_info & status)
{
    //WLD("");

    // 检查对端 心跳 是否正常在发 m_offline_sec值为0时，表示不检查
    if(m_offline_sec > 0 )
    {
        if(m_last_heartbeat_tm + m_offline_sec < app_clock_ref.now_sec())
        {
            WLE("check offline , m_offline_sec %d ",m_offline_sec);
            m_status_control.enter_status(status_enum::disconnect);
            return RET_OK;
        }
    }

    // 发送心跳
    packet_info out;
    m_protocol.buid_heartbeat_frame(m_send_buf,out);
    if(RET_FAIL ==engine_ref.send_udp_data(m_mec_udp_listen_handler,out.pdata,out.len))
    {
        m_status_control.enter_status(status_enum::disconnect);
    }
    return RET_OK;
}

int baidu_mec_client::proc_rsu_asn_bin(const rsp_type & type,v2x_client_respond & frame)
{

    if(m_status_control.get_cur_status() < status_enum::connected)
    {
        WLD("mec is offline status %d ",m_status_control.get_cur_status());
        return RET_OK;
    }

    try
    {
        auto j = json::parse(frame.data.asnUper.json);
        //  {"direct":"send","type":"asn","msg":"bsm","value":"0025ACEECDEC4EA62686C3EC9862602075E68CD7C1C3400160001F41F41FDFFFC04C81F4500000","aid":111,"verify_sign_errcode":0} 
#if  0
        WLD("josn type %s ",j["type"].get<string>().c_str());
        WLD("josn value %s ",j["value"].get<string>().c_str());
        WLD("josn aid %d ",j["aid"].get<int>());
        WLD("josn verify_sign_errcode %d ",j["verify_sign_errcode"].get<int>());
#endif

        string direct = j["direct"].get<string>(); 
        if(direct != "recv")
        {//只上报收到的数据
            return RET_OK; 
        }
        // hex_decode
        auto & hex_value =  j["value"];
        string sAsnValue;
        sAsnValue.resize(hex_value.get<string>().length()/2);

        hex_str ohex;
        ohex.decode((unsigned char *)hex_value.get<string>().c_str(),hex_value.get<string>().length(),(unsigned char *)sAsnValue.c_str());


        if(true==config_ref.is_enable(CK_SYS_PRINT_PROTOCOL))
        {
            MessageFrame_t * asn_v2x_frame  =  nullptr;
            asn_dec_rval_t rval = uper_decode(NULL,&asn_DEF_MessageFrame,
                                              (void **)&asn_v2x_frame,
                                              sAsnValue.c_str(),
                                              sAsnValue.length(),0,0
                                             );
            if(rval.code != RC_OK )
            {

                WLE("uper_decode error rval %d hex_data %s ",rval,hex_value.get<string>().c_str());
                return RET_FAIL;
            }
            asn_fprint(stdout, &asn_DEF_MessageFrame, asn_v2x_frame);
            ASN_STRUCT_FREE(asn_DEF_MessageFrame,asn_v2x_frame);
            asn_v2x_frame = nullptr;
        }

        // 转发给百度服务端
        packet_info out;
        int nRet = m_protocol.build_report_asn_frame(
                       m_send_buf,
                       out,
                       j["aid"].get<int>(),
                       j["verify_sign_errcode"].get<int>(),
                       (unsigned char*)j["value"].get<string>().c_str(),
                       j["value"].get<string>().length()
                   );
        if(nRet != RET_OK)
        {
            WLE("build_report_asn_frame  error  %d ",nRet);
            return nRet;
        }

        if(RET_FAIL ==engine_ref.send_udp_data(m_mec_udp_listen_handler,out.pdata,out.len))
        {
            m_status_control.enter_status(status_enum::disconnect);
        }

    }
    catch(exception & e)
    {
        WLE("catch error %s ",e.what());
        return RET_FAIL;
    }

    return RET_OK;
}
