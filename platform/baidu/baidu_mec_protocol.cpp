#include "baidu_mec_protocol.h"
#include "stdafx.h"
#include "service.h"
#include "mec_asn/GoEMECMessageFrame.h"

const int HEAD_KV_SIZE = sizeof(baidu_head_kv);

baidu_mec_protocol::baidu_mec_protocol()
{

}

baidu_mec_protocol::~baidu_mec_protocol()
{

}

int baidu_mec_protocol::init()
{
    m_seq_id = 1;
    return RET_OK;
}

int baidu_mec_protocol::fetch_frame(const char* msg, uint32_t len,baidu_mec_frame & frame)
{
    //WLI("ENTER");
    bool bError = false;
    bool bFinish=  false;
    uint32_t iProCount = 0;
    while(bError == false && bFinish == false && iProCount < len)
    {
        baidu_head_kv kv =  *(baidu_head_kv*)(msg);
        iProCount += HEAD_KV_SIZE;
        kv.ntoh();
        //WLD("key %d val %d ",kv.key,kv.val);
        switch (kv.key)
        {
        case 0x0100:
        {
            frame.frame_type = kv.val;
            if(frame.frame_type == BDMF_HEARTBEAT)
            {
                //心跳包，只包括头
                bFinish = true;
            }
            break;
        }
        case 0x0001:
        {
            frame.sed = kv.val;
            break;
        }
        case 0x0002:
        {
            frame.aid = kv.val;
            break;
        }
        case 0x0003:
        {

            if(kv.val<=10)
            {
                frame.send_interval_ms = kv.val*100;
            }
            else if(kv.val== 11)
            {
                frame.send_interval_ms = 20;
            }
            else if(kv.val == 12)
            {
                frame.send_interval_ms = 50;
            }
            else
            {
                WLE("error key %d  val %d ",kv.key,kv.val);
                bError =  true;
            }

            break;
        }
        case 0x0004:
        {
            frame.protocal_type = kv.val;
            break;
        }
        case 0x0005:
        {
            frame.pri = kv.val;
            break;
        }
        case 0x0006:
        {
            frame.traffic_id = kv.val;
            break;
        }
        case 0x0007:
        {
            frame.ac_mode = kv.val;
            break;
        }
        case 0x0008:
        {
            frame.ac_type = kv.val;
            break;
        }
        case 0x0009:
        {
            frame.ac_id = kv.val;
            break;
        }
        case 0x000A:
        {
            frame.data_len = kv.len;
            frame.pdata = (unsigned char* )msg + sizeof(kv.key) + sizeof(kv.len);
            bFinish = true;
            break;
        }
        default:
            bError =  true;
            WLE("key is error %d ",kv.key);
            break;
        }
        msg =  msg + HEAD_KV_SIZE;
    }
    if(bError == true)
    {
        WLE("ERROR");
        return RET_FAIL;
    }

    if(frame.frame_type == BDMF_SEND)
    {
        //只对非心跳包，解压
        asn_dec_rval_t rval = uper_decode(NULL,&asn_DEF_MessageFrame,
                                          (void **)&frame.asn_v2x_frame,
                                          frame.pdata,frame.data_len,0,0
                                         );
        if(rval.code != RC_OK )
        {
            hex_str hex;
            string hex_data;
            hex_data.resize(frame.data_len/2);
            hex.encode(frame.pdata,frame.data_len,hex_data);
            WLE("uper_decode error rval %d hex_data %s ",rval.code,hex_data.c_str());
            return RET_FAIL;
        }

        if(true==config_ref.is_enable(CK_SYS_PRINT_PROTOCOL))
        {
            asn_fprint(stdout, &asn_DEF_MessageFrame, frame.asn_v2x_frame);
        }

    }
    //WLI("SUCCESS");
    return RET_OK;
}

int baidu_mec_protocol::buid_heartbeat_frame(const packet_info & in, packet_info & out )
{
    if(in.len < sizeof(baidu_head_kv))
    {
        WLE("Error in buffer too samll %d ", in.len);
        return RET_FAIL;
    }
    out =  in;
    out.len =  sizeof(baidu_head_kv);

    baidu_head_kv &heartbeat_frame  = *((baidu_head_kv*)out.pdata);
    heartbeat_frame.key = BDMF_HEARTBEAT;
    heartbeat_frame.len = 4;
    heartbeat_frame.val = 20;
    heartbeat_frame.hton();
    return RET_OK;
}


int baidu_mec_protocol::set_head(packet_info & out,uint16_t key,uint16_t len,uint32_t val)
{
    baidu_head_kv &type  = *((baidu_head_kv*)(out.pdata+out.len));
    type.key = key;
    type.len = len;
    type.val = val;
    type.hton();
    out.len += HEAD_KV_SIZE;
    return RET_OK;
}

int baidu_mec_protocol::build_report_asn_frame(const packet_info & in, packet_info & out,uint32_t aid,uint32_t verify_sign_errcode,u_char *asn_hex,uint16_t asn_len_hex)
{

    out =  in;
    out.len  = 0;
    set_head(out,0x0100,4,3);                   // type
    set_head(out,0x0001,4,m_seq_id++);          // seq
    set_head(out,0x0002,4,aid);                 // aid
    set_head(out,0x000A,4,0);                   // DSMP 版本
    set_head(out,0x000B,4,verify_sign_errcode); // verify_sign_errcode

    // set data
    int asn_len =  asn_len_hex/2;
    baidu_head_kv & data =  *((baidu_head_kv*)(out.pdata+out.len));
    data.key = htons(0x000C);
    data.len =  htons(asn_len);
    out.len += (sizeof(data.key) + sizeof(data.len));

    u_char *pdata = (u_char *)(out.pdata +  out.len);
    // 将 hex_asn 直接解压到 缓存上
    hex_str hex;
    hex.decode(asn_hex,asn_len_hex,pdata);
    out.len+= asn_len;
    return RET_OK;
}



