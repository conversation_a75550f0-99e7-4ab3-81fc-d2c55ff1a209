/***********************************************
版本信息： 金溢科技版本权所有，保留一切权利
文件名称： gxx_mec_protocol.h
作者：张明
完成日期：
当前版本号: v 0.0.1
主要功能: 协议解释，封包，解包
版本历史:
***********************************************/

#ifndef _GXX_MEC_PROTOCOL_H_
#define _GXX_MEC_PROTOCOL_H_

#include "stdafx.h"
#include "crc16.h"
#include <list>
using namespace std;

struct MessageHead;
struct GoEMECMessageFrame;
struct asn_TYPE_descriptor_s;

/**
* @brief 高兴新mec客户端协议解释类
*/
class gxx_mec_protocol final
{

    /**
    * @brief asn uper 存储结构体
    */
    struct uper_info
    {
        char *encode_buf;
        int encode_size;
        MessageHead *phead;
    };

public:
    gxx_mec_protocol();
    ~gxx_mec_protocol();
    int init();

    // 协议分析，提取协议帧
    int fetch_frame(const char* msg, uint32_t len,list<GoEMECMessageFrame*> &frame_list);

    // build req_data
    int build_login(const packet_info & in, packet_info & out);
    int build_logout(const packet_info & in, packet_info & out);
    int build_heartbeat(const packet_info & in, packet_info & out);

    int build_ack(const packet_info & in, packet_info & out);


    inline void set_devid(uint16_t id)
    {
        m_seq_id =  id;
    }

    inline uint16_t get_max_data_len()
    {
        return m_max_data_len;
    }

private:
    // 提取协议帧 相关函数
    int ntoh_head(MessageHead & head);
    int check_head(const MessageHead & head);
    int check_body(const MessageHead & head,const char *pBody);

    // 封装协议帧 相关函数
    void get_uper_info(const packet_info & in,uper_info &out);
    int build_head(int data_len,int req_type,MessageHead & head);

    int ans_encode(asn_TYPE_descriptor_s & asn_DEF, GoEMECMessageFrame & msg,uper_info & uinfo,
                   const packet_info & in, packet_info & out,int req_type);

private:

    crc16 m_crc16;
    string m_parse_buffer; // 协议解释 缓冲区

    string m_login_username;
    string m_login_passwd;
    uint16_t m_dev_id {0};
    uint16_t m_seq_id;
    uint16_t m_max_data_len {0};


};
#endif //_GXX_MEC_PROTOCOL_H_
