#include "gxx_mec_client.h"
#include "stdafx.h"
#include "config.h"
#include "event2/event.h"
#include "event2/bufferevent.h"
#include "mec_asn/GoEMECMessageFrame.h"
#include <sys/types.h>
#include <sys/socket.h>
#include <unistd.h>
#include <fcntl.h>
#include <arpa/inet.h>
#include <netinet/tcp.h>
#include "service.h"

gxx_mec_client::gxx_mec_client():m_status_control(status_enum::disconnect,"gxx_mec_client_status",this)
{

    int reconnect_interval_ms = 3000;
    int login_wait_ack_ms = 3000;
    int heartbeat_ms = 300;
    int reconnect_timeout_ms = 10000;

    config_ref.get(CK_GXX_MEC_RECONNECT_INTERVAL_MS, reconnect_interval_ms);
    config_ref.get(CK_GXX_MEC_LOGIN_WAIT_ACK_MS, login_wait_ack_ms);
    config_ref.get(CK_GXX_MEC_HEARTBEAT_MS, heartbeat_ms);
    config_ref.get(CK_GXX_MEC_RECONNECT_TIMEOUT_MS, reconnect_timeout_ms);

    // 注册状态
    m_status_control.regist_status(status_enum::disconnect, &gxx_mec_client::proc_status_disconnect, "disconnect", reconnect_interval_ms);
    m_status_control.regist_status(status_enum::connected, &gxx_mec_client::proc_status_connected, "connected", 0);
    m_status_control.regist_status(status_enum::connecting, &gxx_mec_client::proc_status_connecting, "connecting", reconnect_timeout_ms);
    m_status_control.regist_status(status_enum::login_send, &gxx_mec_client::proc_status_login_send, "login_send", 0);
    m_status_control.regist_status(status_enum::login_wait_ack, &gxx_mec_client::proc_status_login_wait_ack, "login_wait_ack", login_wait_ack_ms);
    m_status_control.regist_status(status_enum::login, &gxx_mec_client::proc_status_login, "login", heartbeat_ms);
    m_status_control.regist_status(status_enum::logout, &gxx_mec_client::proc_status_logout, "logout", 0);

    int send_pack_size = 10240;
    config_ref.get(CK_GXX_MEC_SEND_PACK_SIZE, send_pack_size);

    m_send_buf.pdata = new char[send_pack_size];
    m_send_buf.len = send_pack_size;

    // 回包处理函数-LinkFrame
    m_frame_fun_map[GoEMECMessageFrame_PR_linkFrame][LinkFrame_PR_loginAck] = &gxx_mec_client::proc_frame_login_ack;
    m_frame_fun_map[GoEMECMessageFrame_PR_linkFrame][LinkFrame_PR_heartbeat] = &gxx_mec_client::proc_frame_heartbeat;
    m_frame_fun_map[GoEMECMessageFrame_PR_linkFrame][LinkFrame_PR_ack] = &gxx_mec_client::proc_frame_ack;

    // 回包处理函数-cv2xFrame
    m_frame_fun_map[GoEMECMessageFrame_PR_cv2xFrame][MessageFrame_PR_bsmFrame] = &gxx_mec_client::proc_frame_cv2x;
    m_frame_fun_map[GoEMECMessageFrame_PR_cv2xFrame][MessageFrame_PR_mapFrame] = &gxx_mec_client::proc_frame_cv2x;
    m_frame_fun_map[GoEMECMessageFrame_PR_cv2xFrame][MessageFrame_PR_rsmFrame] = &gxx_mec_client::proc_frame_cv2x;
    m_frame_fun_map[GoEMECMessageFrame_PR_cv2xFrame][MessageFrame_PR_spatFrame] = &gxx_mec_client::proc_frame_cv2x;
    m_frame_fun_map[GoEMECMessageFrame_PR_cv2xFrame][MessageFrame_PR_rsiFrame] = &gxx_mec_client::proc_frame_cv2x;
    m_frame_fun_map[GoEMECMessageFrame_PR_cv2xFrame][MessageFrame_PR_msgFrameExt] = &gxx_mec_client::proc_frame_cv2x;
}

gxx_mec_client::~gxx_mec_client()
{
    delete [] m_send_buf.pdata;
    m_send_buf.pdata =  nullptr;
    m_send_buf.len =  0;
}

int gxx_mec_client::init()
{
    m_gxx_mec_protocol.init();

    string ip="**********";
    int port = 2819;

    config_ref.get(CK_GXX_MEC_IP,ip);
    config_ref.get(CK_GXX_MEC_PORT,port);

    return engine_ref.regist_tcp_client(this,ip,port,m_rd_handler );
}

void gxx_mec_client::destory()
{
    engine_ref.unregist(this,m_rd_handler);
}

void gxx_mec_client::stop()
{
    if(m_status_control.get_cur_status() >= status_enum::login)
    {
        // 只有登录状态才能发送登出
        packet_info out;
        m_gxx_mec_protocol.build_logout(m_send_buf,out);
        send_data(out.pdata,out.len);

    }
    m_status_control.enter_status(status_enum::logout);
}


void gxx_mec_client::ontime_check(long long cur_ms)
{
    m_status_control.call_status_fun(cur_ms);
}

void gxx_mec_client::read_cb(engine_conn_handler & handler,const char* msg, int len)
{
    list<GoEMECMessageFrame*> frame_list;
    m_gxx_mec_protocol.fetch_frame(msg,len,frame_list);
    for(auto pframe : frame_list)
    {
        //cout<<"read_cb =============== recv data "<<endl<<endl;
        //asn_fprint(stdout, &asn_DEF_GoEMECMessageFrame, pframe);
        //cout<<endl<<endl;
        proc_frame(*pframe);
        ASN_STRUCT_FREE(asn_DEF_GoEMECMessageFrame,pframe);
    }
}

void gxx_mec_client::event_cb(int fd, short events )
{
    //WLD("event %d ",events);

    if (events & BEV_EVENT_ERROR)
    {
        m_rd_handler.init();
        m_status_control.enter_status(status_enum::disconnect);
        WLE("BEV_EVENT_ERROR fd %d %s ",fd,strerror(EVUTIL_SOCKET_ERROR()) );
        return ;
    }
    if (events & BEV_EVENT_CONNECTED )
    {
        m_status_control.enter_status(status_enum::connected);
        WLI("BEV_EVENT_CONNECTED fd %d ",fd );
        return ;
    }
}

int gxx_mec_client::get_mec_status()
{
    if(m_status_control.get_cur_status()>=status_enum::connected)
    {
        return 1;
    }
    return 0;
}

int gxx_mec_client::send_data(const char *pData,int len)
{
    if(m_status_control.get_cur_status() <status_enum::connected)
    {
        return RET_FAIL;
    }
    return engine_ref.send_tcp_data(m_rd_handler,pData,len);
}

int gxx_mec_client::proc_frame(GoEMECMessageFrame & frame)
{

    auto it_find_map  = m_frame_fun_map.find(frame.present);

    if(it_find_map == m_frame_fun_map.end())
    {
        // 不确认的消息
        //send_ack();
        WLE("not support frame.present %d ",frame.present);
        return RET_FAIL;
    }
    auto fun_map = it_find_map->second;
    if(frame.present == GoEMECMessageFrame_PR_linkFrame)
    {
        auto it_fun = fun_map.find(frame.choice.linkFrame.present);
        if(it_fun == fun_map.end())
        {
            WLE("not support linkFrame.present %d ",frame.choice.linkFrame.present);
            return RET_FAIL;
        }
        return (this->*(it_fun->second))(frame);
    }
    else if(frame.present == GoEMECMessageFrame_PR_cv2xFrame)
    {
        //WLI("proc_frame GoEMECMessageFrame_PR_cv2xFrame");
        //cv2xFrame 先回确认包，再处理
        send_ack();

        auto it_fun = fun_map.find(frame.choice.cv2xFrame.present);
        if(it_fun == fun_map.end())
        {
            WLE("not support cv2xFrame.present %d ",frame.choice.cv2xFrame.present);
            return RET_FAIL;
        }
        return (this->*(it_fun->second))(frame);
    }

    else if(frame.present == GoEMECMessageFrame_PR_opsFrame)
    {
        //send_ack();
        auto it_fun = fun_map.find(frame.choice.opsFrame.present);
        if(it_fun == fun_map.end())
        {
            WLE("not support opsFrame.present %d ",frame.choice.opsFrame.present);
            return RET_FAIL;
        }
        return (this->*(it_fun->second))(frame);
    }
    else
    {
        WLE("not support 2  frame.present %d ",frame.present);
        return RET_FAIL;
    }
    return RET_OK;
}

int gxx_mec_client::proc_frame_login_ack(GoEMECMessageFrame & frame)
{
    LoginAck_t & loginAck = frame.choice.linkFrame.choice.loginAck;

    if(loginAck.errorCode !=  ErrorCode_noError)
    {
        // 登录失败
        WLE("login fail code %d ",loginAck.errorCode);
        service_ref.set_stop_flag("login fail");
        return RET_FAIL;
    }
    m_gxx_mec_protocol.set_devid(loginAck.id);
    m_status_control.enter_status(status_enum::login);

    return RET_OK;
}
// 心跳回包处理
int gxx_mec_client::proc_frame_heartbeat(GoEMECMessageFrame & frame)
{
    HeartBeat_t & heartbeat = frame.choice.linkFrame.choice.heartbeat;
    WLD("%d",heartbeat);
    return RET_OK;
}
// ack 回包处理
int gxx_mec_client::proc_frame_ack(GoEMECMessageFrame & frame)
{
    ACK_t & ack = frame.choice.linkFrame.choice.ack;
    WLD("ack.errorCod %d ",ack.errorCode);
    return RET_OK;
}

int gxx_mec_client::proc_frame_cv2x(GoEMECMessageFrame & frame)
{
    WLD("ENTER");
    return v2x_client_ref.send_cv2x_asn_88H(frame.choice.cv2xFrame);
}

int gxx_mec_client::proc_status_disconnect(status_info & status)
{
    destory();
    init();
    m_status_control.enter_status(status_enum::connecting);
    return RET_OK;
}

int gxx_mec_client::proc_status_connected(status_info & status)
{
    m_status_control.enter_status(status_enum::login_send);
    return RET_OK;
}

int gxx_mec_client::proc_status_connecting(status_info & status)
{
    m_status_control.enter_status(status_enum::disconnect);
    return RET_OK ;
}

int gxx_mec_client::send_ack()
{
    packet_info out;
    m_gxx_mec_protocol.build_ack(m_send_buf,out);
    if(RET_OK != send_data(out.pdata,out.len))
    {
        m_status_control.enter_status(status_enum::disconnect);
    }
    return RET_OK;
}

int gxx_mec_client::proc_status_login_send(status_info & status)
{
    //get login packet
    packet_info out;
    m_gxx_mec_protocol.build_login(m_send_buf,out);

    if(RET_OK == send_data(out.pdata,out.len))
    {
        m_status_control.enter_status(status_enum::login_wait_ack);
    }
    else
    {
        // reconnect
        m_status_control.enter_status(status_enum::disconnect);
    }

    return RET_OK;
}

int gxx_mec_client::proc_status_login_wait_ack(status_info & status)
{
    m_status_control.enter_status(status_enum::disconnect);
    return RET_OK;
}

int gxx_mec_client::proc_status_login(status_info & status)
{
    packet_info out;
    m_gxx_mec_protocol.build_heartbeat(m_send_buf,out);
    if(RET_OK != send_data(out.pdata,out.len))
    {
        m_status_control.enter_status(status_enum::disconnect);
    }


    return RET_OK;
}


int gxx_mec_client::proc_status_logout(status_info & status)
{
    return RET_OK;
}


