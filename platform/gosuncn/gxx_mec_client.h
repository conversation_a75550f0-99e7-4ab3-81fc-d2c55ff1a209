/***********************************************
版本信息： 金溢科技版本权所有，保留一切权利
文件名称： gxx_mec_client.h
作者：张明
完成日期：
当前版本号: v 0.0.1
主要功能: tcp 客户端连接管理，协议状态管理
版本历史:
***********************************************/

#ifndef _GXX_MEC_CLIENT_H_
#define _GXX_MEC_CLIENT_H_

#include "ireceiver.h"
#include "stdafx.h"
#include "gxx_mec_protocol.h"
#include "status_control.hpp"
#include "iengine.h"
#include "imec_client.h"
#include <unordered_map>
#include "log.h"

struct bufferevent ;
struct GoEMECMessageFrame;
struct asn_TYPE_descriptor_s;

/**
* @brief 高兴新mec客户端
*/
class gxx_mec_client final :public imec_client, ireceiver
{
    typedef int (gxx_mec_client::*frame_fun)(GoEMECMessageFrame & frame);

    typedef status_control<gxx_mec_client,status_enum> status_control_type;
    typedef status_control_type::status_info  status_info;

public:
    gxx_mec_client();
    ~gxx_mec_client();

public:	//imec_client interface
    virtual int init() override;
    virtual void stop() override;
    virtual void destory() override;
    virtual void ontime_check(long long cur_ms) override;
    virtual int get_mec_status() override;

public: // ireceiver interface
    virtual void read_cb(engine_conn_handler & handler,const char* msg, int len) override;
    virtual void event_cb(int fd, short events ) override ;

private:
    void init_config();
    int send_ack();
    int send_data(const char *pData,int len);

private:
    // 回包处理函数
    int proc_frame(GoEMECMessageFrame & frame);
    int proc_frame_login_ack(GoEMECMessageFrame & frame);
    int proc_frame_heartbeat(GoEMECMessageFrame & frame);
    int proc_frame_ack(GoEMECMessageFrame & frame);

    // cvx 消息
    int proc_frame_cv2x(GoEMECMessageFrame & frame);

private:
    // 协议状态管理-函数
    int proc_status_disconnect(status_info & status);
    int proc_status_connected(status_info & status);
    int proc_status_connecting(status_info & status);
    int proc_status_login_send(status_info & status);
    int proc_status_login_wait_ack(status_info & status);
    int proc_status_login(status_info & status);
    int proc_status_logout(status_info & status);

private:
    gxx_mec_protocol m_gxx_mec_protocol;
    engine_conn_handler m_rd_handler;
    status_control_type m_status_control;
    packet_info m_send_buf;
    unordered_map<int,unordered_map<int,frame_fun> > m_frame_fun_map;
};
#endif //_GXX_MEC_CLIENT_H_
