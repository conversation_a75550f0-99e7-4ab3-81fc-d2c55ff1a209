#include "gxx_mec_protocol.h"
#include "stdafx.h"
#include "config.h"
#include <arpa/inet.h>
#include "service.h"
#include <sys/time.h>
#include <time.h>
#include "mec_asn/GoEMECMessageFrame.h"

// 消息头定义 包类型定义

//bit 0~1:  00 表示不加密，01表示加密(只加密uper编码后的消息集)，默认00-不加密。
//#define PACKET_TYPE_NOT_ENCRYPT  0 ///< 2进制  00
#define PACKET_TYPE_ENCRYPT 1        ///< 2进制  01
// bit 2~3:  00 表示数据不计算校验，01 表示计算校验。默认01-校验。
//#define PACKET_TYPE_NOT_CRC  0     ///< 2进制  00 00
#define PACKET_TYPE_CRC 4            ///< 2进制  01 00
//bit 4~5:  00 表示非法，01表示请求，10表示响应
#define PACKET_TYPE_REQ 16           ///< 2进制  01 00 00
#define PACKET_TYPE_RSP 32           ///< 2进制  10 00 00





//bit 16~23:  1byte表示设备类型，0x01-GoEMEC；0x02-External。
#define PACKET_DEV_TYPE_GOENEC   1 ///< 1 << 16
#define PACKET_DEV_TYPE_EXTERNAL 2 ///< 1 << 17


#pragma pack (0)

/**
* @brief 高兴新mec协议头
*/
struct  MessageHead
{
    char magic_num[4];    ///< 4 byte	魔术字-协议包开始字段 GXXH
    uint16_t reserve_1;   ///< 2 byte	预留1
    uint16_t data_len ;   ///< 2 byte	数据长度
    uint16_t src_id;      ///< 2 byte	源id 10
    uint16_t des_id;      ///< 2 byte	目的id
    uint32_t time;        ///< 4 byte	时间戳秒数
    uint16_t ms;          ///< 2 byte	时间戳毫秒数
    uint16_t reserve_2;   ///< 2 byte	预留2 20
    uint16_t seq_id;      ///< 2 byte	顺序号 22
    uint16_t reserve_3;   ///< 2 byte	预留3
    //uint32_t packet_type; ///< 4 byte packet_type 改为 四个字段
    uint8_t packet_type;
    uint8_t packet_encrypt_len;
    uint8_t pakcet_devtype;
    uint8_t pakcet_type_reserve;
    uint16_t version;     ///< 2 byte	版本号 30
    uint16_t crc16;       ///< 2 byte	消息头CRC校验（CRC16）
};
#pragma pack()
static const uint32_t MessageHeadSize =  sizeof(MessageHead);

gxx_mec_protocol::gxx_mec_protocol()
{
    m_parse_buffer.reserve(1024);
    config_ref.get(CK_GXX_MEC_USERNAME,m_login_username);
    config_ref.get(CK_GXX_MEC_PASSWD,m_login_passwd);
}

gxx_mec_protocol::~gxx_mec_protocol()
{
}

int gxx_mec_protocol::init()
{
    m_seq_id = 1;
    m_parse_buffer = "";
    return RET_OK;
}

int gxx_mec_protocol::fetch_frame(const char* pdata, uint32_t len,list<GoEMECMessageFrame*> &frame_list)
{
    if(m_parse_buffer.size() != 0)
    {
        m_parse_buffer.append(pdata,len);
        len = m_parse_buffer.size();
        pdata = (const char*)m_parse_buffer.c_str();
    }
    string temp_buf;
    do
    {
        if( MessageHeadSize > len )
        {
            // 不足一帧，缓存
            temp_buf.assign(pdata,len);
            break;
        }

        MessageHead head =  *(MessageHead*)(pdata);
        if( RET_FAIL == check_head(head) )
        {
            break;
        }
        ntoh_head(head);

        // recode max_data_len
        if(m_max_data_len < head.data_len )
        {
            m_max_data_len =  head.data_len;
        }


        if( head.data_len + MessageHeadSize > len )
        {
            // 不足一帧,数据
            temp_buf.assign(pdata,len);
            break;
        }

        const char *pBody = pdata + MessageHeadSize;
        if( RET_OK == check_body(head,pBody) )
        {
            // 解码当前帧
            GoEMECMessageFrame * pframe = NULL;
            asn_dec_rval_t rval = uper_decode(NULL,&asn_DEF_GoEMECMessageFrame,
                                              (void **)&pframe,
                                              pBody,head.data_len-sizeof(uint16_t),0,0);

            if(rval.code == RC_OK)
            {
                if(true==config_ref.is_enable(CK_SYS_PRINT_PROTOCOL))
                {
                    cout<<"recv ===================="<<endl;
                    asn_fprint(stdout, &asn_DEF_GoEMECMessageFrame, pframe);
                    cout<<endl<<endl<<endl;
                }

                frame_list.push_back(pframe);
            }
            else
            {
                if(pframe != NULL)
                {
                    ASN_STRUCT_FREE(asn_DEF_GoEMECMessageFrame,pframe);
                    pframe =  NULL;
                }
            }
        }
        pdata = pdata + MessageHeadSize + head.data_len;
        len = len -  MessageHeadSize - head.data_len;
    }
    while(len > 0);

    m_parse_buffer.assign(move(temp_buf));
    if(frame_list.size() == 0 )
    {
        return RET_FAIL;
    }

    return RET_OK;
}

int gxx_mec_protocol::build_login(const packet_info & in, packet_info & out )
{
    uper_info uinfo;
    get_uper_info(in,uinfo);

    // build - GoEMECMessageFrame_t
    GoEMECMessageFrame_t msg;
    msg.present =  GoEMECMessageFrame_PR::GoEMECMessageFrame_PR_linkFrame;
    msg.choice.linkFrame.present =  LinkFrame_PR::LinkFrame_PR_login;

    Login_t &login  = msg.choice.linkFrame.choice.login;
    memset(&login,0,sizeof(login));
    login.userName.buf = (uint8_t*)m_login_username.c_str();
    login.userName.size = m_login_username.size();
    login.passWord.buf = (uint8_t*)m_login_passwd.c_str();
    login.passWord.size = m_login_passwd.size();

    return ans_encode(asn_DEF_GoEMECMessageFrame,msg,uinfo,in,out,PACKET_TYPE_REQ);
}

int gxx_mec_protocol::build_logout(const packet_info & in, packet_info & out)
{
    uper_info uinfo;
    get_uper_info(in,uinfo);

    // build - GoEMECMessageFrame_t
    GoEMECMessageFrame_t msg;
    msg.present =  GoEMECMessageFrame_PR::GoEMECMessageFrame_PR_linkFrame;
    msg.choice.linkFrame.present =  LinkFrame_PR::LinkFrame_PR_logout;

    Logout_t &logout  = msg.choice.linkFrame.choice.logout;
    memset(&logout,0,sizeof(logout));

    logout.userName.buf = (uint8_t*)m_login_username.c_str();
    logout.userName.size = m_login_username.size();

    return ans_encode(asn_DEF_GoEMECMessageFrame,msg,uinfo,in,out,PACKET_TYPE_REQ);
}

int gxx_mec_protocol::build_heartbeat(const packet_info & in, packet_info & out)
{
    uper_info uinfo;
    get_uper_info(in,uinfo);

    GoEMECMessageFrame_t msg;
    msg.present =  GoEMECMessageFrame_PR::GoEMECMessageFrame_PR_linkFrame;
    msg.choice.linkFrame.present =  LinkFrame_PR::LinkFrame_PR_heartbeat;
    HeartBeat_t & heartbeat = msg.choice.linkFrame.choice.heartbeat;
    
    heartbeat = get_sec();
    return ans_encode(asn_DEF_GoEMECMessageFrame,msg,uinfo,in,out,PACKET_TYPE_REQ);
}

int gxx_mec_protocol::build_ack(const packet_info & in, packet_info & out)
{

    uper_info uinfo;
    get_uper_info(in,uinfo);
    GoEMECMessageFrame_t msg;
    msg.present =  GoEMECMessageFrame_PR::GoEMECMessageFrame_PR_linkFrame;
    msg.choice.linkFrame.present =  LinkFrame_PR::LinkFrame_PR_ack;
    ACK_t & ack = msg.choice.linkFrame.choice.ack;
    ack.errorCode = ErrorCode::ErrorCode_noError ;

    return ans_encode(asn_DEF_GoEMECMessageFrame,msg,uinfo,in,out,PACKET_TYPE_RSP);
}

int gxx_mec_protocol::ans_encode(asn_TYPE_descriptor_t & asn_DEF, GoEMECMessageFrame_t & msg,uper_info & uinfo,
                                 const packet_info & in, packet_info & out,int req_type)
{
    // encode
    asn_enc_rval_t ec;
    ec = uper_encode_to_buffer(&asn_DEF,NULL, &msg, uinfo.encode_buf, uinfo.encode_size);

    if(true==config_ref.is_enable(CK_SYS_PRINT_PROTOCOL))
    {
        cout<<"send ===================="<<endl;
        asn_fprint(stdout, &asn_DEF_GoEMECMessageFrame, &msg);
        cout<<endl<<endl<<endl;
    }


    if(ec.encoded == -1)
    {
        // encode fail;
        WLE("Could not encode build_login %s ",ec.failed_type->name);
        return RET_FAIL;
    }
    uint32_t encode_byte =  (ec.encoded+7) / 8 ;
    // set crc16 packed-end
    uint16_t  *pcrc16 =  (uint16_t *)(uinfo.encode_buf+encode_byte);
    *pcrc16 = htons( m_crc16.encode_arc(uinfo.encode_buf,encode_byte) );

    // build_head
    build_head(encode_byte + sizeof(uint16_t),req_type,*uinfo.phead);
    // set out info
    out.pdata = in.pdata;
    out.len =  MessageHeadSize + encode_byte + sizeof(uint16_t);

    return RET_OK;
}

int gxx_mec_protocol::build_head(int data_len,int req_type,MessageHead & head)
{
    timeval cur;
    gettimeofday(&cur,NULL);
    uint16_t cur_ms =  cur.tv_sec/1000;
    //GXXH
    head.magic_num[0] = 'G';
    head.magic_num[1] = 'X';
    head.magic_num[2] = 'X';
    head.magic_num[3] = 'H';

    head.reserve_1 = htons(0);
    head.data_len = htons(data_len);
    head.src_id =  htons(m_dev_id);
    head.des_id = htons(0) ;
    head.time =  htonl(cur.tv_sec);
    head.ms =  htons(cur_ms);
    head.reserve_2 = htons(0);
    head.seq_id = htons(m_seq_id);
    head.reserve_3 = htons(0);

    // packe_type
    head.packet_type =  PACKET_TYPE_CRC|req_type;
    head.packet_encrypt_len = 0;
    head.pakcet_devtype = PACKET_DEV_TYPE_GOENEC;
    head.pakcet_type_reserve = 0 ;
    head.version =  htons(100);

    // clc16
    char *phead = (char *)&head;
    head.crc16 =  htons( m_crc16.encode_arc(phead, sizeof(head) - sizeof(head.crc16) ) );

    // 自增 seq
    m_seq_id++;
    return RET_OK;
}

int gxx_mec_protocol::ntoh_head(MessageHead & head)
{
    head.reserve_1 =  ntohs(head.reserve_1);
    head.data_len =  ntohs(head.data_len);
    head.src_id =  ntohs(head.src_id);
    head.des_id =  ntohs(head.des_id);
    head.time =  ntohl(head.time);
    head.ms =  ntohs(head.ms);
    head.reserve_2 =  ntohs(head.reserve_2);
    head.seq_id =  ntohs(head.seq_id);
    head.reserve_3 =  ntohs(head.reserve_3);

    //head.packet_type =
    ///head.packet_encrypt_len =
    //head.pakcet_devtype =
    //head.pakcet_type_reserve =

    head.version = ntohs(head.version);
    head.crc16 =  ntohs(head.crc16);

    return RET_OK;
}

int gxx_mec_protocol::check_head(const MessageHead & head)
{

    if(!(head.magic_num[0] == 'G'
            && head.magic_num[1] == 'X'
            && head.magic_num[2] == 'X'
            && head.magic_num[3] == 'H' ))
    {
        return RET_FAIL;
    }

    uint16_t crc16 = ntohs(head.crc16);
    if(head.packet_type & PACKET_TYPE_CRC )
    {
        // crc check
        char *phead = (char *)&head;
        uint16_t cal_crc16 = m_crc16.encode_arc(phead, sizeof(head) - sizeof(head.crc16) ) ;
        if(crc16 !=  cal_crc16)
        {
            WLE("crc_check fail hear.crc16 %d cal_crc16 %d ",crc16,cal_crc16);
            return RET_FAIL;
        }
    }
    return RET_OK;
}

int gxx_mec_protocol::check_body(const MessageHead & head,const char *pBody)
{
    if(head.packet_type & PACKET_TYPE_CRC)
    {
        // check body crc16

        uint16_t body_crc16  =  *(uint16_t*)(pBody+(head.data_len-sizeof(uint16_t)));
        body_crc16 = ntohs(body_crc16);

        // cal body crc16
        uint16_t cal_crc16 = m_crc16.encode_arc(pBody,head.data_len-sizeof(uint16_t)) ;
        if(body_crc16 != cal_crc16)
        {
            WLE("check_body fail body_crc16 %d cal_crc16 %d ",body_crc16,cal_crc16);
            return RET_FAIL;
        }
        return RET_OK;
    }
    return RET_OK;
}

void gxx_mec_protocol::get_uper_info(const packet_info & in,uper_info &sinfo)
{
    sinfo.encode_buf = in.pdata + MessageHeadSize;
    sinfo.encode_size = in.len - MessageHeadSize;
    sinfo.phead =  (MessageHead*)in.pdata;
}
