#include "imec_client.h"
#include "stdafx.h"
#include "config.h"
#include "empty_mec_client.h"
#include "baidu_mec_client.h"
#include "debug_mec_client.h"
#include "gv_mec_client.h"
#include "gxx_mec_client.h"
#include "huawei_mec_client.h"
#include "multi_mec_client.h"
#include "tianan_mec_client.h"
#include "iengine.h"
#include "str_op.h"

imec_client::imec_client()
{

}

imec_client::~imec_client()
{
	
}

imec_client * create_single_mec_client(int nType)
{
    imec_client * p_client{nullptr};
    if(nType == config::PT_GENVICT)
    {
        p_client  = new(std::nothrow) gv_mec_client();
    }
    if(nType ==  config::PT_EMPTY)
    {
        p_client =  new(std::nothrow) empty_mec_client();
    }
    if(nType == config::PT_DEBUG)
    {
        p_client = new(std::nothrow) debug_mec_client();
    }
    if(nType == config::PT_GOSUNCN)
    {
        p_client = new(std::nothrow) gxx_mec_client();
    }
    if(nType == config::PT_BAIDU)
    {
        p_client = new(std::nothrow) baidu_mec_client();
    }
    if(nType == config::PT_HUAWEI)
    {
        p_client = new(std::nothrow) huawei_mec_client(); 
    }
    if(nType == config::PT_TIANAN_RSU)
    {
        
        p_client =   new(std::nothrow) tianan_mec_client();
    }
    return p_client; 
}

imec_client *imec_client::create_mec()
{   
    string def_type = to_string(config::PT_EMPTY); 
    string mul_type = config_ref.get_def(CK_SYS_PLATFORM,def_type.c_str());
    list<string> client_list; 
    string split_str = MULTIC_VAL_SPLIT; 
    ENGINE_MODULE_NAMESPACE::split_string(mul_type,split_str[0],client_list); 
    
    multi_mec_client *p_mul_client = new(std::nothrow) multi_mec_client();
    for(auto & str_type : client_list)
    {
        int nType = std::stoi(str_type); 
        auto p_client = create_single_mec_client(nType); 
        WLI("begin to create mec_client type [%d] name[%s] ",nType,config_ref.get_platform_type_desc(nType).c_str()); 
        if(p_client == nullptr)
        {
            WLI("create mec_client type [%d] name[%s] fail",nType,config_ref.get_platform_type_desc(nType).c_str()); 
            continue;
        }
        WLI("create mec_client type [%d] name[%s] success",nType,config_ref.get_platform_type_desc(nType).c_str()); 
        p_mul_client->add_client(p_client); 
    }
    return p_mul_client;
}
