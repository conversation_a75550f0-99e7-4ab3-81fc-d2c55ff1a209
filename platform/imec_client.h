/***********************************************
版本信息： 金溢科技版本权所有，保留一切权利
文件名称： imec_client.h
作者：张明
开始日期：2022-12-13 13:53:23
完成日期：
当前版本号: v 0.0.1
主要功能: mec_client 的 统一接口
版本历史:
***********************************************/

#ifndef _IMEC_CLIENT_H_
#define _IMEC_CLIENT_H_
#include "iengine.h"
#include <string>
using namespace std;

/**
* @brief mec 客户端接口定义
*/
class imec_client
{
public:
    static imec_client *create_mec();
    imec_client();
    virtual ~imec_client();
    virtual int init()=  0;
    virtual void stop()=  0;
    virtual void destory()=  0;
    virtual void ontime_check(long long cur_ms) = 0;

    //返回值: 1: 表示 mec 在线 ， 0 表示mec 不再线
    virtual int get_mec_status()
    {
        return 0;
    }

    
};


#endif //_IMEC_CLIENT_H_
