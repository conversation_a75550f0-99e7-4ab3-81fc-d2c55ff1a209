/***********************************************
版本信息： 金溢科技版本权所有，保留一切权利
文件名称： huawei_mec_client.h
作者：张明
开始日期：2024-02-05 11:14:18
完成日期：
当前版本号: v 0.0.1
主要功能: 
版本历史:
***********************************************/

#ifndef _HUAWEI_MEC_CLIENT_H_
#define _HUAWEI_MEC_CLIENT_H_

#include "ireceiver.h"
#include "iengine.h"
#include "stdafx.h"
#include "huawei_mec_protocol.h"
#include "imec_client.h"
#include "command_pattern.hpp"
#include "status_control.hpp"
#include "v2x_protocol.h"
#include "time_task.h"

#include "nlohmann/json.hpp"
using json = nlohmann::json;

class huawei_mec_client final :public imec_client,imqttclient_receiver
{
	typedef status_control<huawei_mec_client,status_enum> status_control_type;
    typedef status_control_type::status_info  status_info;
public:
	huawei_mec_client();
	virtual ~huawei_mec_client();

public:	//imec_client interface
    virtual int init() override;
    virtual void stop() override;
    virtual void destory() override;
    virtual void ontime_check(long long cur_ms) override;
    virtual int get_mec_status() override;

public: // ireceiver interface
    virtual void read_cb(engine_conn_handler & handler,const char* msg, int len) override;
    virtual void event_cb(int fd, short events ) override ;	

public:// imqttclient_receiver 
    virtual void message_cb(const struct mosquitto_message *message,const mosquitto_property *properties) override;

public: //状态管理函数
    int proc_status_disconnect(status_info & status);
	int proc_status_connecting(status_info & status); 
    int proc_status_connected(status_info & status);
	int proc_status_login_send(status_info & status);
	int proc_status_login_wait_ack(status_info & status);
	int proc_status_keep_alive(status_info & status);
private:
	int proc_v2x_heartbeat(const rsp_type & type,v2x_client_respond & frame); 
	int proc_v2x_report_05H(const string & type,uint8_t &direct_i,string & s_json,string & str_value_json,shared_ptr<json> & value_json_obj);
	int proc_rsp_obu_alarm_06H(const rsp_type & type,v2x_client_respond & frame);
	int proc_rsu_debug_report_0AH(const rsp_type & type,v2x_client_respond & frame);
	int proc_rsu_config_report_0CH(const rsp_type & type,v2x_client_respond & frame);
	int proc_v2x_report_monitor_0EH(const rsp_type & type,v2x_client_respond & frame); 

private://mqtt 相关命令处理
	string get_mqtt_topic(const string &sub_topic); 
	int init_mqtt_topic_proc();
	int subcribe_mqtt_topic();
	int proc_topic_info_up_ack(const string &topic, char * payload, int payload_len);
	int proc_topic_config_down(const string &topic, char * payload, int payload_len); 
	
	int proc_topic_map_down(const string &topic, char * payload, int payload_len); 
	int proc_topic_map_up_ack(const string &topic, char * payload, int payload_len); 

	int proc_topic_rsm_down(const string &topic, char * payload, int payload_len); 

	int proc_topic_rsi_down(const string &topic, char * payload, int payload_len); 
	int proc_topic_rsi_up_ack(const string &topic, char * payload, int payload_len); 

	int proc_topic_spat_down(const string &topic, char * payload, int payload_len); 
	// 二阶段
	int proc_topic_rtcm_down(const string &topic, char * payload, int payload_len); 
	int proc_topic_om_config_down(const string &topic, char * payload, int payload_len); 

	//发布确认帧
	int mqtt_public_ack(const string &topic_suffix,const hw_req_ack_info_t & ack_info,int error_code,const string &err_desc );
private:
	bool mqtt_public(string &topic,const string &data); 
	bool check_v2x_heartbeat_change(const V2xRspHeartBeat & cur_info); 
	int send_v2x_json(const string & type,const string &json_data);

private://定时上报
	int ontime_mqtt_run_status_up(long long cur_ms); 
	int ontime_mqtt_basic_status_up(long long cur_ms); 

private:
	int regist_mqtt_client();

private:
    ENGINE_MODULE_NAMESPACE::time_task m_time_task;

	status_control_type m_status_control; // 状态管理类
	huawei_mec_protocol m_protocol; 
	command_pattern <string, char*, int > m_topic_commander ; 
	mqtt_regist_info m_regist_info;
	engine_conn_handler m_mqtt_handler;
	time_t m_last_heartbeat_tm{0};
    int m_offline_sec {15};
    packet_info m_send_buf; // 发送数据缓冲区
	thread_worker m_ota_down_thread;
	string m_rsu_id;
	V2xRspHeartBeat m_v2x_heartbeat_info;

	hw_config_t m_hw_config; 
	hw_om_config_t m_hw_om_config;
	time_task_id_t  m_run_status_up_task_id{0};

};
#endif //_HUAWEI_MEC_CLIENT_H_
