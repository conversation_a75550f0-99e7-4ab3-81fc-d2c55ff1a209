/***********************************************
版本信息： 金溢科技版本权所有，保留一切权利
文件名称： huawei_mec_protocol.h
作者：张明
开始日期：2024-02-05 11:14:34
完成日期：
当前版本号: v 0.0.1
主要功能:
版本历史:
***********************************************/

#ifndef _HUAWEI_MEC_PROTOCOL_H_
#define _HUAWEI_MEC_PROTOCOL_H_
#include <string>
#include "v2x_protocol.h"
#include "nlohmann/json.hpp"
using json = nlohmann::json;
using namespace std;

enum HW_ERROR_CODE
{
	HW_OK = 0,	  // 0：表示无错误
	HW_E_PAR = 1, // 1：表示消息中的参数错误（必选参数丢失，参数范围不对）
	HW_E_DEV = 2, // 2：表示由于本端系统错误，没有处理消息，errorDesc描述可能的错误原因
};
struct hw_ack_info_t
{
	string seqNum;
	int errorCode;
	string errorDesc;
};

struct hw_req_ack_info_t
{
	string seqNum;
	bool ack{false};
};


struct hw_bsm_config_t
{
	string sampleMode{"ByAll"};  // "ByAll"：全局采样 "ByID"：RSU解析出车ID，按ID进行采样，保证均匀
	int sampleRate;  // 采样率，按照该采样率进行转发，0表示不需要转发，当达到转发上限（upLimit）时，则自动降低采样率。取值范围：0~10000。
	int upLimit{-1};  // 上行转发上限，中心子系统通过“RSU业务配置下发”决定，表示每秒最多发送多少条消息，0表示不需要发，-1表示不限制。取值范围：0~10000。
	list<string> filters; // 上报的BSM的过滤条件，多个filter之间是或关系，不带表示不过滤。 比如：filters : [{ “id” : “1”}, { “id” : “2”}]，表示只上报id为1和2的BSM消息。

}; 

struct hw_rsi_config_t
{
	list<string> filters;  // 上报的RSI的过滤条件，多个filter之间是或关系，不带表示不过滤。 比如：filters : [{ “signType” : “15”}]，表示只上报signType为15的RSI消息。
}; 
struct hw_spat_config_t
{
	int upLimit{-1};  //上行转发上限，中心子系统提供，每秒最多发送多少条消息，0表示不需要发，-1表示不限制。
	list<string> filters;// 上报的SPAT的过滤条件，MEC提供，多个filter之间是或关系，不带表示不过滤。 比如：filters : [{ “intersectionId” : “1”}]，表示只上报intersectionId为1的SPAT消息。

} ;
struct hw_rsm_config_t
{
	int upLimit{-1};// 上行转发上限，中心子系统提供，每秒最多发送多少条消息，0表示不需要发，-1表示不限制。
	list<string> filters; // 上报的RSM的过滤条件，多个filter之间是或关系，不带表示不过滤，同一个filter之间是“与”的关系。 比如：filters : [{“ptcType” : “3”}, {“source” : “3”}]，表示只上报ptcType为3且source为3的RSM消息。
};
struct hw_map_config_t
{
	int upLimit{-1}; //上报上限，中心子系统提供，每秒最多发送多少条消息，0表示不需要发，-1表示不限制。
	list<string> filters; //上报的MAP的过滤条件，中心子系统提供，多个filter之间是或关系，不带表示不过滤。 比如：filters : [{ “intersectionId” : “1”}]，表示只上报intersectionId为1的Map消息。
};

// 业务配置
struct hw_config_t
{
	hw_bsm_config_t bsm; 
	hw_rsi_config_t rsi;
	hw_spat_config_t spat;
	hw_rsm_config_t rsm; 
	hw_map_config_t map;
}; 

// 运维配置- 只实现runningInfoRate和reboot。其他不实现。
struct hw_om_config_t
{
	int HBRate {30}; ///< 0：不上报心跳信息 >0：表示上报间隔，秒
	int runningInfoRate{60}; ///< 0：不上报设备运行状态信息; >0：表示上报间隔，秒 ;  针对“RSU运行状态上报信息”上报周期的配置
	
	string addressChg_cssUrl; ///<中心子系统地址
	uint64_t addressChg_time{0}; ///< 0：立即生效 >0：UTC时间;

	int logLevel{0} ; ///< 日志级别，DEBUG；INFO；WARN；ERROR；NOLog
	int reboot{0}; ///< 0：不重启1：重启
	string  extendConfig; ///< key:配置名；value：配置值
}; 

class huawei_mec_protocol
{
public:
	huawei_mec_protocol();
	~huawei_mec_protocol();

	typedef int (huawei_mec_protocol::*fun_type_hw2gv)(nlohmann::json &, nlohmann::json &);

public: // 解包
	int fetch_mqtt_ack(const string &s_ack_info, hw_ack_info_t &ack_info);
	int fetch_mqtt_config(const string &s_config_down, hw_req_ack_info_t &ack_info,hw_config_t & config); 
	int fetch_mqtt_om_config(const string & s_om_config,hw_req_ack_info_t &ack_info,hw_om_config_t& config );

private:
	int fetch_mqtt_config_filters(nlohmann::json &hw_json,const string &key,list<string> &filters ); 

public: // 组包
	static string get_msg_seqnum();
	int build_mqtt_info_up(const V2xRspHeartBeat &heartBeatRsp, string &data);
	int build_mqtt_basic_status_up(const V2xRspHeartBeat &heartBeatRsp, string &data);
	int build_mqtt_run_status_up(const V2xRspHeartBeat &heartBeatRsp, string &data);
	int build_mqtt_req_ack_up(const hw_req_ack_info_t &ack_info, int error_code, const string &err_desc, string &data);

	int bsm_gv2hw(shared_ptr<json> &gv_json_ptr, string &hw_data);

	int map_hw2gv(const string &hw_map, string &gv_map, hw_req_ack_info_t &ack_info);
	int map_gv2hw(shared_ptr<json> &gv_json_ptr, string &hw_data);

	int spat_hw2gv(const string &hw_spat, string &gv_spat, hw_req_ack_info_t &ack_info);
	int spat_gv2hw(shared_ptr<json> &gv_json_ptr, string &hw_data);

	int rsm_gv2hw(shared_ptr<json> &gv_json_ptr, string &hw_data);
	int rsm_hw2gv(const string &hw_rsm, string &gv_rsm, hw_req_ack_info_t &ack_info);

	int rsi_gv2hw(shared_ptr<json> &gv_json_ptr, string &hw_data);
	int rsi_hw2gv(const string &hw_rsi, string &gv_rsi, hw_req_ack_info_t &ack_info);
#ifdef TEST_CODE
public:
#else
private:
#endif

	int rte_hw2gv(nlohmann::json &hw_json, nlohmann::json &gv_json);
	int rte_gv2hw(nlohmann::json &gv_json, nlohmann::json &hw_json);

	int rts_hw2gv(nlohmann::json &hw_json, nlohmann::json &gv_json);
	int rts_gv2hw(nlohmann::json &gv_json, nlohmann::json &hw_json);

	int pos_hw2gv(nlohmann::json &hw_json, nlohmann::json &gv_json);
	int pos_gv2hw(nlohmann::json &gv_json, nlohmann::json &hw_json);

	int point_gv2hw(nlohmann::json &gv_json, nlohmann::json &hw_json);
	int point_hw2gv(nlohmann::json &hw_json, nlohmann::json &gv_json);

	int posoffset_hw2gv(nlohmann::json &hw_json, nlohmann::json &gv_json);
	int posoffset_gv2hw(nlohmann::json &gv_json, nlohmann::json &hw_json);

	int Descript_hw2gv(const string &desc, nlohmann::json &gv_json);
	int Descript_gv2hw(nlohmann::json &gv_json, string &hw_desc);

	int TimeDetail_hw2gv(nlohmann::json &hw_json, nlohmann::json &gv_json);
	int TimeDetail_gv2hw(nlohmann::json &gv_json, nlohmann::json &hw_json);

	int referencePath_hw2gv(nlohmann::json &hw_json, nlohmann::json &gv_json);
	int referencePath_gv2hw(nlohmann::json &gv_json, nlohmann::json &hw_json);

	int referenceLink_hw2gv(nlohmann::json &hw_json, nlohmann::json &gv_json);
	int referenceLink_gv2hw(nlohmann::json &gv_json, nlohmann::json &hw_json);

	int ref_lane_hw2gv(nlohmann::json &hw_json, nlohmann::json &gv_json);
	int ref_lane_gv2hw(nlohmann::json &gv_json, nlohmann::json &hw_json);

	int participant_hw2gv(nlohmann::json &hw_json, nlohmann::json &gv_json);
	int participant_gv2hw(nlohmann::json &gv_json, nlohmann::json &hw_json);

	int node_hw2gv(nlohmann::json &hw_json, nlohmann::json &gv_json);
	int node_gv2hw(nlohmann::json &gv_json, nlohmann::json &hw_json);

	int link_hw2gv(nlohmann::json &hw_json, nlohmann::json &gv_json);
	int link_gv2hw(nlohmann::json &gv_json, nlohmann::json &hw_json);

	int speedlimit_hw2gv(nlohmann::json &hw_json, nlohmann::json &gv_json);
	int speedlimit_gv2hw(nlohmann::json &gv_json, nlohmann::json &hw_json);

	int lane_hw2gv(nlohmann::json &hw_json, nlohmann::json &gv_json);
	int lane_gv2hw(nlohmann::json &gv_json, nlohmann::json &hw_json);

	int lane_attributes_hw2gv(nlohmann::json &hw_json, nlohmann::json &gv_json);
	int lane_attributes_gv2hw(nlohmann::json &gv_json, nlohmann::json &hw_json);

	int maneuver_hw2gv(uint16_t hw_maneuver, nlohmann::json &gv_json);
	int maneuver_gv2hw(nlohmann::json &gv_json, uint16_t &hw_maneuver);

	int connection_hw2gv(nlohmann::json &hw_json, nlohmann::json &gv_json);
	int connection_gv2hw(nlohmann::json &gv_json, nlohmann::json &hw_json);

	int connectingLane_hw2gv(nlohmann::json &hw_json, nlohmann::json &gv_json);
	int connectingLane_gv2hw(nlohmann::json &gv_json, nlohmann::json &hw_json);

	int intersection_hw2gv(nlohmann::json &hw_json, nlohmann::json &gv_json);
	int intersection_gv2hw(nlohmann::json &gv_json, nlohmann::json &hw_json);

	int intersection_status_hw2gv(uint16_t status, nlohmann::json &gv_json);
	int intersection_status_gv2hw(nlohmann::json &gv_json, uint16_t &status);

	int phase_hw2gv(nlohmann::json &hw_json, nlohmann::json &gv_json);
	int phase_gv2hw(nlohmann::json &gv_json, nlohmann::json &hw_json);

	int phase_state_hw2gv(nlohmann::json &hw_json, nlohmann::json &gv_json);
	int phase_state_gv2hw(nlohmann::json &gv_json, nlohmann::json &hw_json);

	int shareWith_hw2gv(uint16_t shareWith, nlohmann::json &gv_json);
	int shareWith_gv2hw(nlohmann::json &gv_json, uint16_t &shareWith);

	int posConfidence_hw2gv(nlohmann::json &hw_json, nlohmann::json &gv_json);
	int posConfidence_gv2hw(nlohmann::json &gv_json, nlohmann::json &hw_json);

	int motionConfidence_hw2gv(nlohmann::json &hw_json, nlohmann::json &gv_json);
	int motionConfidence_gv2hw(nlohmann::json &gv_json, nlohmann::json &hw_json);

	int accelSet_hw2gv(nlohmann::json &hw_json, nlohmann::json &gv_json);
	int accelSet_gv2hw(nlohmann::json &gv_json, nlohmann::json &hw_json);

	int brakes_gv2hw(nlohmann::json &gv_json, nlohmann::json &hw_json);
	int brakes_hw2gv(nlohmann::json &hw_json, nlohmann::json &gv_json);

	int wheelBrakeStatus_gv2hw(nlohmann::json &gv_json, nlohmann::json &hw_json);
	int wheelBrakeStatus_hw2gv(nlohmann::json &hw_json, nlohmann::json &gv_json);

	int vehicleClass_gv2hw(nlohmann::json &gv_json, nlohmann::json &hw_json);
	int vehicleClass_hw2gv(nlohmann::json &hw_json, nlohmann::json &gv_json);

	int rsi_priority_hw2gv(int hw_priority, string &gv_priority);
	int rsi_priority_gv2hw(const string &gv_priority, int &hw_priority);

	int arrays_convert(nlohmann::json &hw_json, nlohmann::json &gv_json, fun_type_hw2gv fun);

private:
	void fetch_ack_seq_num(nlohmann::json &json, hw_req_ack_info_t &ack_info);

private:
	string m_rsu_id;
};
#endif //_HUAWEI_MEC_PROTOCOL_H_
