#include "huawei_mec_client.h"
#include "stdafx.h"
#include "config.h"
#include "service.h"
#include "hex_str.h"
#include "app_clock.h"
#include "event2/event.h"
#include "event2/bufferevent.h"
#include <sstream>
#include "dir_op.h"
#include <time.h>
#include "hmac_code.h"

huawei_mec_client::huawei_mec_client() : m_status_control(status_enum::disconnect, "huawei_mec_client", this)
{

    m_rsu_id = config_ref.get_def(CK_ESN,"");
    m_regist_info.tls = config_ref.get_def(GET_HUAWEI_MQTT_KEY(MQTT_TLS), false);
    m_regist_info.tls_version = config_ref.get_def(GET_HUAWEI_MQTT_KEY(MQTT_TLS_VERSION),""); 
    m_regist_info.cafile = config_ref.get_def(GET_HUAWEI_MQTT_KEY(MQTT_CA_FILE),""); 
    m_regist_info.capath =  config_ref.get_def(GET_HUAWEI_MQTT_KEY(MQTT_CA_PATH),""); 
    m_regist_info.certfile =  config_ref.get_def(GET_HUAWEI_MQTT_KEY(MQTT_CERT_FILE),""); 
    m_regist_info.keyfile =  config_ref.get_def(GET_HUAWEI_MQTT_KEY(MQTT_KEY_FILE),""); 
    m_regist_info.hostname = config_ref.get_def(GET_HUAWEI_MQTT_KEY(MQTT_HOST),"");
    m_regist_info.port = config_ref.get_def(GET_HUAWEI_MQTT_KEY(MQTT_PORT),0);
    m_regist_info.username = config_ref.get_def(GET_HUAWEI_MQTT_KEY(MQTT_USER),"");

    int reconnect_interval_ms = 10 * 1000;
    int login_send_ms = 5 * 1000;
    int login_wait_ack_timeout_ms = 60 * 1000;
    int keep_alive_timeout_ms = 1 * 1000;

    m_status_control.regist_status(status_enum::disconnect, &huawei_mec_client::proc_status_disconnect, "disconnect", reconnect_interval_ms);
    m_status_control.regist_status(status_enum::connecting, &huawei_mec_client::proc_status_connecting, "connecting", reconnect_interval_ms);
    m_status_control.regist_status(status_enum::connected, &huawei_mec_client::proc_status_connected, "connected", 0);
    m_status_control.regist_status(status_enum::login_send, &huawei_mec_client::proc_status_login_send, "login_send", login_send_ms);
    m_status_control.regist_status(status_enum::login_wait_ack, &huawei_mec_client::proc_status_login_wait_ack, "login_wait_ack", login_wait_ack_timeout_ms);
    m_status_control.regist_status(status_enum::keep_alive, &huawei_mec_client::proc_status_keep_alive, "keep_alive", keep_alive_timeout_ms);

    // 向 rsu 注册 回调
    auto &v2x_command = v2x_client_ref.get_command_pattern();
    v2x_command.regist_command(rsp_type::RSP_HEARTBEAT, this, std::bind(&huawei_mec_client::proc_v2x_heartbeat, FUN_BIND_2));

    auto &rsu_report_command = v2x_client_ref.get_rsp_report_command();
    rsu_report_command.regist_all_command(this, std::bind(&huawei_mec_client::proc_v2x_report_05H, FUN_BIND_5));

    m_run_status_up_task_id = m_time_task.regist_task(std::bind(&huawei_mec_client::ontime_mqtt_run_status_up, FUN_BIND_1), 60 * 1000);        // RSU上报基本信息 RSU基本信息上报。开机、建立链路、或信息变更上报 1 分钟
    m_time_task.regist_task(std::bind(&huawei_mec_client::ontime_mqtt_basic_status_up, FUN_BIND_1), 10 * 60 * 1000); // RSU运行状态上报信息 默认：周期性上报RSU的运行状态，周期10分钟

    init_mqtt_topic_proc();
}



int huawei_mec_client::regist_mqtt_client()
{
    // Mqtt协议的cleintId，由4个部分组成：设备 ID、设备身份标识类型、密码签名类型、时间戳。通过下划线“_”分隔，设备身份标识类型固定值为0。
        // 时间戳：为设备连接平台时的UTC时间，格式为YYYYMMDDHH 时间戳：为设备连接平台时的UTC时间，格式为YYYYMMDDHH，如UTC 时间2018/7/24 17:56:20 则应表示为2018072417。
        // 密码签名类型 0"代表HMACSHA256不校验时间戳。 "1"代表HMACSHA256校验时间戳。
    // Mqtt password的值为使用“HMACSHA256”算法以时间戳为密钥，对secret进行加密后的值。secret
    string client_id = config_ref.get_def(GET_HUAWEI_MQTT_KEY(MQTT_CLIENT_ID),"");
                       
    char tm_tmp[20] = {0}; 
    auto cur  = time(nullptr); 
    auto cur_tm = localtime(&cur);
    strftime(tm_tmp,sizeof(tm_tmp),"%Y%m%d%M",cur_tm);
    string cur_tm_foramt = tm_tmp;

    stringstream  ss;  
    ss <<m_rsu_id<<"_0_1_"<<cur_tm_foramt;
    m_regist_info.client_id =  ss.str();

    string config_passwd = config_ref.get_def(GET_HUAWEI_MQTT_KEY(MQTT_PASSWD) ,"");
    string sha256_passwd; 
    hmac_code::sha256(cur_tm_foramt,config_passwd,sha256_passwd ); 
    hex_str ohex;
    ohex.encode(sha256_passwd,m_regist_info.passwd);
    if (RET_OK != engine_ref.regist_mqtt_client(this, m_regist_info, m_mqtt_handler))
    {
        WLE("engine_ref.regist_mqtt_client error ");
        return RET_FAIL;
    }
    return RET_OK;
}


huawei_mec_client::~huawei_mec_client()
{
    auto &v2x_command = v2x_client_ref.get_command_pattern();
    v2x_command.unregist_all_command(this);

    auto &rsu_report_command = v2x_client_ref.get_rsp_report_command();
    rsu_report_command.unregist_all_command(this);
}

//=========================================================
// icloud_platform_client
int huawei_mec_client::init()
{
   
    {
        // 业务配置
        string config_config_path = config_ref.get_def(CK_HUIWEI_MEC_CONFIG_PATH, "");
        string config_data;
        if(RET_OK== dir_op::read_file_data(config_config_path,config_data))
        {
            hw_req_ack_info_t ack_info;
            m_protocol.fetch_mqtt_config(config_data,ack_info,m_hw_config);
        }
    }
    {
        //运维配置
        string config_path = config_ref.get_def(CK_HUIWEI_MEC_OM_CONFIG_PATH,""); 
        string config_data;
        if(RET_OK == dir_op::read_file_data(config_path,config_data))
        {
            hw_req_ack_info_t ack_info;
            m_protocol.fetch_mqtt_om_config(config_data,ack_info,m_hw_om_config);
            m_hw_om_config.reboot = 0;
            if(m_hw_om_config.runningInfoRate > 0)
            {
                m_time_task.set_task_interval_ms(m_run_status_up_task_id,m_hw_om_config.runningInfoRate*1000);
            }
            
        }
    }
    // 注册 MQTT
    if (RET_OK != this->regist_mqtt_client())
    {
        WLE("regist_mqtt_client error ");
        return RET_FAIL;
    }

    m_status_control.enter_status(status_enum::connecting);

    return RET_OK;
}
void huawei_mec_client::stop()
{
    destory();
}
void huawei_mec_client::destory()
{
    m_v2x_heartbeat_info.dev_type = 0;
    engine_ref.unregist(this, m_mqtt_handler);
}

int huawei_mec_client::get_mec_status()
{
    return 1;
}

void huawei_mec_client::ontime_check(long long cur_ms)
{
    m_status_control.call_status_fun(cur_ms);
}

//=========================================================
// ireceiver
void huawei_mec_client::read_cb(engine_conn_handler &handler, const char *msg, int len)
{
    WLD("msg %s ", msg);
}

// 事件处理
void huawei_mec_client::event_cb(int fd, short events)
{
    WLI("fd %d events %d ", fd, events);

    if (events & BEV_EVENT_CONNECTED)
    { // 连接成功
        // 向MQTT注册 成功后则进入 connected
        m_status_control.enter_status(status_enum::connected);
    }
    if (events & BEV_EVENT_ERROR)
    { // 连接断开
        m_mqtt_handler.init();
        m_status_control.enter_status(status_enum::disconnect);
    }
}

void huawei_mec_client::message_cb(const struct mosquitto_message *message,const mosquitto_property *properties)
{
    WLD(" topic %s  payload %s ", message->topic, (char*)message->payload);
    char * data =  (char *)(message->payload);
    int payloadlen = message->payloadlen;
    m_topic_commander.do_command(message->topic,data,payloadlen);

}


//=========================================================
// 状态处理函数

// 状态处理-连接断开
int huawei_mec_client::proc_status_disconnect(status_info &status)
{
    auto info = v2x_client_ref.get_heartbeat_info();
    if (info.dev_type == 0)
    { // 表示 还没有与 设备 建立连接
        return RET_OK;
    }
    // 连接成功后才进行mqtt连接
    destory();
    init();
    return RET_OK;
}
// 状态处理-连接中,如果超过一定时间一直处于 连接状态，则进入重连接状态
int huawei_mec_client::proc_status_connecting(status_info & status)
{
    m_status_control.enter_status(status_enum::disconnect); 
    return RET_OK;
}

// 状态处理-连接成功
int huawei_mec_client::proc_status_connected(status_info &status)
{
    // 进行消息订阅
    if (RET_OK == subcribe_mqtt_topic())
    {
        m_status_control.enter_status(status_enum::login_send);
    }
    else
    {
        m_status_control.enter_status(status_enum::disconnect);
    }
    return RET_OK;
}

int huawei_mec_client::proc_status_login_send(status_info &status)
{
    m_v2x_heartbeat_info = v2x_client_ref.get_heartbeat_info();
    WLD("m_v2x_heartbeat_info.dev_type %d ",m_v2x_heartbeat_info.dev_type);
    if (m_v2x_heartbeat_info.dev_type == 0)
    {
        return RET_OK;
    }

    string data;
    m_protocol.build_mqtt_info_up(m_v2x_heartbeat_info, data);
    string topic = get_mqtt_topic("/info/up");
    mqtt_public(topic,data);
    return RET_OK;
}

// 等待超时后，再次进入 login_send
int huawei_mec_client::proc_status_login_wait_ack(status_info &status)
{
    m_status_control.enter_status(status_enum::login_send);
    return RET_OK;
}

/// @brief 保活后，发送心跳包，检测基本信息上报
/// @param status
/// @return
int huawei_mec_client::proc_status_keep_alive(status_info &status)
{
    m_time_task.on_time(status.enter_tm_ms);
    return RET_OK;
}

//=========================================================================
// v2x_service  消息处理
int huawei_mec_client::proc_v2x_heartbeat(const rsp_type &type, v2x_client_respond &frame)
{
    if (m_status_control.get_cur_status() < status_enum::connected)
    { // mqtt 断开连接
        return RET_OK;
    }
    auto &s = frame.data.heartBeatRsp;
    if (true == check_v2x_heartbeat_change(s))
    { // 检查到状态发行改变
        string data;
        m_protocol.build_mqtt_info_up(s, data);
        string topic = get_mqtt_topic("/info/up");
        if (true == mqtt_public(topic, data))
        {
            m_status_control.enter_status(status_enum::login_send);
        }
    }
    m_v2x_heartbeat_info = s;
    return RET_OK;
}

int huawei_mec_client::proc_v2x_report_05H(const string &type, uint8_t &direct_i, string &s_json, string &str_value_json, shared_ptr<json> &value_json_obj)
{

    if (m_status_control.get_cur_status() != status_enum::keep_alive)
    {
        return RET_OK;
    }
    
    string hw_json;
    string topic;
    bool need_pub =  true;
    if (type == "bsm")
    {
        if(m_hw_config.bsm.upLimit == 0)
        {
            need_pub =  false;
        }
        else
        {
            topic = get_mqtt_topic("/bsm/up");
            m_protocol.bsm_gv2hw(value_json_obj, hw_json);
        }
        
    }
    else if (type == "map")
    {
        if(m_hw_config.map.upLimit == 0 )
        {
            need_pub = false;
        }
        else
        {
            topic = get_mqtt_topic("/map/up");
            m_protocol.map_gv2hw(value_json_obj, hw_json);
        }
        
    }
    else if (type == "spat")
    {
        if(m_hw_config.spat.upLimit == 0)
        {
            need_pub =  false;
        }
        else
        {
            topic = get_mqtt_topic("/spat/up");
            m_protocol.spat_gv2hw(value_json_obj, hw_json);
        }
        
    }
    else if (type == "rsm")
    {
        if(m_hw_config.rsm.upLimit == 0)
        {
            need_pub = false;
        }
        else
        {
            topic = get_mqtt_topic("/rsm/up");
            m_protocol.rsm_gv2hw(value_json_obj, hw_json);
        }
    }
    else if (type == "rsi")
    {
        topic = get_mqtt_topic("/rsi/up");
        m_protocol.rsi_gv2hw(value_json_obj, hw_json);
    }
    else
    {
        WLE("not support type[%s]", type.c_str());
        return RET_OK;
    }
    
    //WLD(" type[%s] need_pub %d ", type.c_str(),need_pub);

    if(need_pub == true)
    {
        WLD("topic %s data %s ", topic.c_str(), hw_json.c_str());
        mqtt_public(topic, hw_json);    
    }
    return RET_OK;
}

//=========================================================================
// mqtt 订阅
string huawei_mec_client::get_mqtt_topic(const string &sub_topic)
{
    stringstream ss;
    ss << "v2x/v1/rsu/" << m_rsu_id << sub_topic;
    return ss.str();
}

int huawei_mec_client::init_mqtt_topic_proc()
{
// 注册 MQTT回调处理
#define REGIST_TOPIC_COMMAND(sub_topic, func) m_topic_commander.regist_command(get_mqtt_topic(sub_topic), this, std::bind(&func, FUN_BIND_3))

    // 运维消息
    REGIST_TOPIC_COMMAND("/info/up/ack", huawei_mec_client::proc_topic_info_up_ack);
    REGIST_TOPIC_COMMAND("/config/down", huawei_mec_client::proc_topic_config_down);
    REGIST_TOPIC_COMMAND("/om-config/down", huawei_mec_client::proc_topic_om_config_down);
    // 一阶段
    REGIST_TOPIC_COMMAND("/map/down", huawei_mec_client::proc_topic_map_down);
    REGIST_TOPIC_COMMAND("/map/up/ack",huawei_mec_client::proc_topic_map_up_ack); 

    REGIST_TOPIC_COMMAND("/rsm/down", huawei_mec_client::proc_topic_rsm_down);

    REGIST_TOPIC_COMMAND("/rsi/down", huawei_mec_client::proc_topic_rsi_down);
    REGIST_TOPIC_COMMAND("/rsi/up/ack", huawei_mec_client::proc_topic_rsi_up_ack);

    REGIST_TOPIC_COMMAND("/spat/down", huawei_mec_client::proc_topic_spat_down);
    // 二阶段
    REGIST_TOPIC_COMMAND("/rtcm/down", huawei_mec_client::proc_topic_rtcm_down);


    return RET_OK;
}

// 连接mqtt成功后，每次都需要重新订阅关注的主题
int huawei_mec_client::subcribe_mqtt_topic()
{
    // 运维消息
    list<string> list_topic;
    list_topic.push_back(get_mqtt_topic("/info/up/ack"));
    list_topic.push_back(get_mqtt_topic("/config/down"));
    list_topic.push_back(get_mqtt_topic("/om-config/down/down"));
    
    // 一阶段
    list_topic.push_back(get_mqtt_topic("/map/down"));
    list_topic.push_back(get_mqtt_topic("/map/up/ack"));

    list_topic.push_back(get_mqtt_topic("/rsm/down"));

    list_topic.push_back(get_mqtt_topic("/rsi/down"));
    list_topic.push_back(get_mqtt_topic("/rsi/up/ack"));

    list_topic.push_back(get_mqtt_topic("/spat/down"));
    // 二阶段
    list_topic.push_back(get_mqtt_topic("/rtcm/down"));
    for (auto &topic : list_topic)
    {
        if (RET_OK != engine_ref.mqtt_subscribe(m_mqtt_handler, topic))
        {
            WLE(" mqtt_subscribe error topic %s ", topic.c_str());
            return RET_FAIL;
        }
    }
    return RET_OK;
}

/// @brief 收到 info_up_ack 后，才进入最后 keep_alive
/// @param topic
/// @param payload
/// @param payload_len
/// @return
int huawei_mec_client::proc_topic_info_up_ack(const string &topic, char *payload, int payload_len)
{
    hw_ack_info_t info;
    if (RET_OK != m_protocol.fetch_mqtt_ack(payload, info))
    {
        return RET_OK;
    }
    if (info.errorCode != 0)
    {
        WLE(" err desc %s", info.errorDesc.c_str());
        return RET_OK;
    }
    m_status_control.enter_status(status_enum::keep_alive);
    return RET_OK;
}

int huawei_mec_client::proc_topic_config_down(const string &topic, char *payload, int payload_len)
{
    hw_req_ack_info_t ack_info;
    string ack_topic_suffix = "/config/down/ack";
    int error_code = HW_ERROR_CODE::HW_OK;
    try
    {
        if(RET_OK != m_protocol.fetch_mqtt_config(payload,ack_info,m_hw_config))
        {
            error_code = HW_ERROR_CODE::HW_E_PAR;
            throw logic_error("lost par");
        }
        
        string config_config_path = config_ref.get_def(CK_HUIWEI_MEC_CONFIG_PATH, "");

        if(RET_OK == dir_op::write_file_data(config_config_path,payload))
        {// 保存文件失败
            error_code = HW_ERROR_CODE::HW_E_DEV;
            throw logic_error("dir_op::write_file_data");
        }

        mqtt_public_ack(ack_topic_suffix, ack_info, HW_ERROR_CODE::HW_OK, "");
        return RET_OK;
    }
    catch (logic_error &e)
    {
        WLE("Error[%s] topic %s payload %s", e.what(), topic.c_str(), payload);
        mqtt_public_ack(ack_topic_suffix, ack_info, error_code, e.what());
        return RET_FAIL;
    }
}

int huawei_mec_client::proc_topic_map_down(const string &topic, char *payload, int payload_len)
{
    hw_req_ack_info_t ack_info;
    string gv_map;
    string ack_topic_suffix = "/map/down/ack";
    int error_code = HW_ERROR_CODE::HW_OK;
    try
    {
        if (RET_OK != m_protocol.map_hw2gv(payload, gv_map, ack_info))
        {
            error_code = HW_ERROR_CODE::HW_E_PAR;
            throw logic_error("lost par");
        }

        if(RET_OK != send_v2x_json("map",gv_map))
        {
            error_code = HW_ERROR_CODE::HW_E_DEV;
            throw logic_error("error send_v2x_json");
        }
        mqtt_public_ack(ack_topic_suffix, ack_info, HW_ERROR_CODE::HW_OK, "");
        return RET_OK;
    }

    catch (logic_error &e)
    {
        WLE("Error[%s] topic %s payload %s", e.what(), topic.c_str(), payload);
        mqtt_public_ack(ack_topic_suffix, ack_info, error_code, e.what());
        return RET_FAIL;
    }
}


int huawei_mec_client::proc_topic_map_up_ack(const string &topic, char *payload, int payload_len)
{
    hw_ack_info_t ack_info;
    m_protocol.fetch_mqtt_ack(payload,ack_info); 
    WLD(" seqNum %s errorCode %d error %s " ,ack_info.seqNum.c_str(),ack_info.errorCode,ack_info.errorDesc.c_str()); 
    return RET_OK;
}


int huawei_mec_client::proc_topic_rsm_down(const string &topic, char *payload, int payload_len)
{
    hw_req_ack_info_t ack_info;
    string gv_rsm;

    if (RET_OK != m_protocol.rsm_hw2gv(payload, gv_rsm, ack_info))
    {
        WLE("m_protocol.rsm_hw2gv topic %s payload %s ", topic.c_str(), payload);
        return RET_FAIL;
    }

    if(RET_OK != send_v2x_json("rsm",gv_rsm))
    {
        WLE("v2x_client_ref.send_v2x_json Error topic %s , payload %s ", topic.c_str(), payload);
        return RET_FAIL;
    }
    return RET_OK;
}


/// @brief  MEC下发的 rsi消息
/// @param topic
/// @param payload
/// @param payload_len
/// @return
int huawei_mec_client::proc_topic_rsi_down(const string &topic, char *payload, int payload_len)
{
    hw_req_ack_info_t ack_info;
    string gv_rsi;
    string ack_topic_suffix = "/rsi/down/ack";
    int error_code = HW_ERROR_CODE::HW_OK;
    try
    {
        if (RET_OK != m_protocol.rsi_hw2gv(payload, gv_rsi, ack_info))
        {
            error_code = HW_ERROR_CODE::HW_E_PAR;
            throw logic_error("lost par");
        }
        if (RET_OK != send_v2x_json("rsi",gv_rsi))
        {
            error_code = HW_ERROR_CODE::HW_E_DEV;
            throw logic_error("send_v2x_json");
        }
        mqtt_public_ack(ack_topic_suffix, ack_info, HW_ERROR_CODE::HW_OK, "");
        return RET_OK;
    }
    catch (logic_error &e)
    {
        WLE("Error[%s] topic %s payload %s", e.what(), topic.c_str(), payload);
        mqtt_public_ack(ack_topic_suffix, ack_info, error_code, e.what());
        return RET_FAIL;
    }
    return RET_OK;
}

int huawei_mec_client::proc_topic_rsi_up_ack(const string &topic, char * payload, int payload_len)
{
    hw_ack_info_t ack_info;
    m_protocol.fetch_mqtt_ack(payload,ack_info); 
    WLD(" seqNum %s errorCode %d error %s " ,ack_info.seqNum.c_str(),ack_info.errorCode,ack_info.errorDesc.c_str()); 
    return RET_OK;
}


int huawei_mec_client::proc_topic_spat_down(const string &topic, char *payload, int payload_len)
{
    hw_req_ack_info_t ack_info;
    string gv_spat;
    string err_desc;
    try
    {
        if (RET_OK != m_protocol.spat_hw2gv(payload, gv_spat, ack_info))
        {
            throw logic_error("lost par");
        }
        if(RET_OK != send_v2x_json("spat",gv_spat))
        {
            throw logic_error("send_v2x_json");
        }
        return RET_OK;
    }
    catch (logic_error &e)
    {
        WLE("Error[%s] topic %s payload %s", e.what(), topic.c_str(), payload);
        return RET_FAIL;
    }
}

int huawei_mec_client::proc_topic_rtcm_down(const string &topic, char *payload, int payload_len)
{
    return RET_OK;
}
/// @brief 运维管理配置信息
/// @param topic 
/// @param payload 
/// @param payload_len 
/// @return 
int huawei_mec_client::proc_topic_om_config_down(const string &topic, char *payload, int payload_len)
{
    hw_req_ack_info_t ack_info;
    string ack_topic_suffix = "/om-config/down/ack";
    int error_code = HW_ERROR_CODE::HW_OK;
    try
    {
        if (RET_OK != m_protocol.fetch_mqtt_om_config(payload,ack_info,m_hw_om_config))
        {
            error_code = HW_ERROR_CODE::HW_E_PAR;
            throw logic_error("lost par");
        }
        // 保存文件 
        string path = config_ref.get_def(CK_HUIWEI_MEC_OM_CONFIG_PATH,"");
        if(RET_OK != dir_op::write_file_data(path,payload))
        {
            error_code = HW_ERROR_CODE::HW_E_DEV;
            throw logic_error("write_file_data");
        }
        mqtt_public_ack(ack_topic_suffix, ack_info, HW_ERROR_CODE::HW_OK, "");
        if(m_hw_om_config.reboot == 1)
        {// 如果配置了重启，则2秒后执行重启
            service_ref.delay_reboot(2000);
        }

        if(m_hw_om_config.runningInfoRate > 0 )
        {// 变更状态处理时间
            m_time_task.set_task_interval_ms(m_run_status_up_task_id,m_hw_om_config.runningInfoRate*1000);
        }

        return RET_OK;
    }
    catch (logic_error &e)
    {
        WLE("Error[%s] topic %s payload %s", e.what(), topic.c_str(), payload);
        mqtt_public_ack(ack_topic_suffix, ack_info, error_code, e.what());
        return RET_FAIL;
    }
    return RET_OK;
}
//=========================================================================
// 其它辅助函数
bool huawei_mec_client::check_v2x_heartbeat_change(const V2xRspHeartBeat &cur_info)
{
    if (m_v2x_heartbeat_info.dev_type != cur_info.dev_type ||
        m_v2x_heartbeat_info.pid != cur_info.pid ||
        m_v2x_heartbeat_info.dev_status != cur_info.dev_status ||
        m_v2x_heartbeat_info.gnss_status != cur_info.gnss_status ||
        m_v2x_heartbeat_info.v2x_status != cur_info.v2x_status)
    {
        return true;
    }
    return false;
}

bool huawei_mec_client::mqtt_public(string &topic, const string &data)
{
    if (m_status_control.get_cur_status() < status_enum::connected)
    { // mqtt 断开连接
        return false;
    }
    if (RET_OK != engine_ref.mqtt_public(m_mqtt_handler, topic, data.c_str(), data.length()))
    {
        m_status_control.enter_status(status_enum::disconnect);
        return false;
    }
    return true;
}

int huawei_mec_client::mqtt_public_ack(const string &topic_suffix, const hw_req_ack_info_t &ack_info, int error_code, const string &err_desc)
{
    if (ack_info.ack == false)
    {
        return RET_OK;
    }

    string topic = get_mqtt_topic(topic_suffix);
    string data;
    m_protocol.build_mqtt_req_ack_up(ack_info, error_code, err_desc, data);
    mqtt_public(topic, data);
    return RET_OK;
}

int huawei_mec_client::ontime_mqtt_run_status_up(long long cur_ms)
{
    if(m_hw_om_config.runningInfoRate > 0)
    {// 当此值大于 0 上报
        string topic = get_mqtt_topic("/run-status/up");
        string data;
        m_protocol.build_mqtt_run_status_up(m_v2x_heartbeat_info, data);
        mqtt_public(topic, data);
    }

    return RET_OK;
}
int huawei_mec_client::ontime_mqtt_basic_status_up(long long cur_ms)
{
    string topic = get_mqtt_topic("/basic-status/up");
    string data;
    m_protocol.build_mqtt_basic_status_up(m_v2x_heartbeat_info, data);
    mqtt_public(topic, data);

    return RET_OK;
}

int huawei_mec_client::send_v2x_json(const string & type,const string &json_data)
{
    auto json_send =  nlohmann::json{
        {"type", type}
    }; 
    json_send["value"] =  std::move(json_data);
    string json_send_data =  json_send.dump();
    if (RET_OK != v2x_client_ref.send_cv2x_json_81H((char *)json_send_data.c_str(), json_send_data.size()))
    {
        return RET_FAIL;
    }
    return RET_OK;
}