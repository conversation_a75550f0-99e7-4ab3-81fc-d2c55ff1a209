#include "huawei_mec_protocol.h"
#include "stdafx.h"
#include "engine_module_log.h"
#include "config.h"
#include "service.h"
#include <atomic>
#include "common_utility.h"

#define CHECK_JSON_KEY(hw_json,key) hw_json.end() != hw_json.find(key) && !hw_json[key].is_null()

#define CHECK_BIT_BOOL(v,i) (v&(1<<i))? true:false
#define CHECK_BIT_STRING_BOOL(v,bit_size,i) (v & (1 << (bit_size-i-1) ) )? true:false

#define CHECK_JSON_BIT_STRING_SET(j,k,v,bit_size,i) if(j[k].get<bool>()){ v |= 1<<(bit_size-i-1) ; }

huawei_mec_protocol::huawei_mec_protocol()
{
    m_rsu_id = config_ref.get_def(CK_ESN,"");    
}

huawei_mec_protocol::~huawei_mec_protocol()
{
	
}
string huawei_mec_protocol::get_msg_seqnum()
{
    static atomic<uint16_t> seqnum(0);
    return std::to_string(seqnum.fetch_add(1)); 
}


int huawei_mec_protocol::fetch_mqtt_ack(const string &s_ack_info,hw_ack_info_t &ack_info)
{
    try
    {
        auto j_ack = json::parse(s_ack_info);
        ack_info.seqNum = j_ack["seqNum"].get<string>(); 
        ack_info.errorCode = j_ack["errorCode"].get<int>(); 
        if(j_ack.find("errorDesc") != j_ack.end())
        {
            ack_info.errorDesc =  j_ack["errorDesc"].get<string>();
        }
        return RET_OK;
    }
    catch(const std::exception& e)
    {
        //std::cerr << e.what() << '\n';
        WLE("json::parse error %s  s_ack_info %s  ",e.what(),s_ack_info.c_str()); 
        return RET_FAIL;
    }
}

int huawei_mec_protocol::fetch_mqtt_config(const string &s_config_down, hw_req_ack_info_t &ack_info,hw_config_t & config)
{

#if 0 
{
    "name": null,
    "id": "dabd1163-6108-411e-a3d8-fc73f40d3e2f",
    "content": {
        "mapConfig": {
            "upLimit": 10000
        },
        "bsmConfig": {
            "sampleMode": "ByAll",
            "sampleRate": 1000,
            "upLimit": 10000
        },
        "rsiConfig": {},
        "spatConfig": {
            "upLimit": 0
        },
        "rsmConfig": {
            "upLimit": -1
        },
        "ack": true,
        "seqNum": "3"
    }
}

#endif 

    try
    {
        auto j_config =  nlohmann::json::parse(s_config_down); 
        //auto j_config =  j["content"]; 
        fetch_ack_seq_num(j_config,ack_info); 
        if(CHECK_JSON_KEY(j_config,"bsmConfig"))
        {
            auto & bsmConfig = j_config["bsmConfig"];
            config.bsm.sampleMode =  bsmConfig["sampleMode"].get<string>();
            config.bsm.sampleRate =  bsmConfig["sampleRate"].get<int>();
            config.bsm.upLimit =  bsmConfig["upLimit"].get<int>();
            fetch_mqtt_config_filters(bsmConfig,"upFilters",config.bsm.filters);

        }
        if(CHECK_JSON_KEY(j_config,"rsiConfig"))
        {
            auto & rsiConfig = j_config["rsiConfig"];
            fetch_mqtt_config_filters(rsiConfig,"upFilters",config.rsi.filters);
        }

        if(CHECK_JSON_KEY(j_config,"spatConfig"))
        {
            auto& spatConfig = j_config["spatConfig"];
            config.spat.upLimit =  spatConfig["upLimit"].get<int>();
            fetch_mqtt_config_filters(spatConfig,"upFilters",config.spat.filters);

        }
        if(CHECK_JSON_KEY(j_config,"rsmConfig"))
        {
            auto & rsmConfig = j_config["rsmConfig"];
            config.rsm.upLimit = rsmConfig["upLimit"].get<int>();
            fetch_mqtt_config_filters(rsmConfig,"upFilters",config.rsm.filters);
        }

        if(CHECK_JSON_KEY(j_config,"mapConfig"))
        {
            auto & mapConfig = j_config["mapConfig"];
            config.map.upLimit = mapConfig["upLimit"].get<int>();
            fetch_mqtt_config_filters(mapConfig,"upFilters",config.spat.filters);
        }
        return RET_OK;
    }
    catch(const std::exception & e)
    {
        WLE("json::parse error %s  s_config_down %s  ",e.what(),s_config_down.c_str()); 
        return RET_FAIL;
    }
}
int huawei_mec_protocol::fetch_mqtt_om_config(const string & s_om_config,hw_req_ack_info_t &ack_info,hw_om_config_t& config )
{
    try
    {
        auto j_config =  nlohmann::json::parse(s_om_config); 
        //auto j_config =  j["content"]; 
        fetch_ack_seq_num(j_config,ack_info);

        if(CHECK_JSON_KEY(j_config,"HBRate"))
        {
           config.HBRate =  j_config["HBRate"].get<int>();
        }

        if(CHECK_JSON_KEY(j_config,"runningInfoRate"))
        {
            config.runningInfoRate =  j_config["runningInfoRate"].get<int>();
        }

        if(CHECK_JSON_KEY(j_config,"addressChg"))
        {
            auto& addressChg = j_config["addressChg"];
            config.addressChg_cssUrl = addressChg["cssUrl"].get<string>();
            config.addressChg_time = addressChg["time"].get<uint64_t>();
        }
        if(CHECK_JSON_KEY(j_config,"logLevel"))
        {
            config.logLevel =  j_config["logLevel"].get<int>();
        }
        if(CHECK_JSON_KEY(j_config,"reboot"))
        {
            config.reboot = j_config["reboot"].get<int>();
        }
        if(CHECK_JSON_KEY(j_config,"extendConfig"))
        {
            config.extendConfig = j_config["extendConfig"].get<string>();
        }
        return RET_OK;
    }
    catch(const std::exception & e)
    {
        WLE("json::parse error %s  s_config_down %s  ",e.what(),s_om_config.c_str()); 
        return RET_FAIL;
    }
}
int huawei_mec_protocol::fetch_mqtt_config_filters(nlohmann::json &hw_json,const string &key,list<string> &filters )
{
    if(hw_json.find(key)== hw_json.end())
    {
        return RET_FAIL;
    }
    for(auto & filter_item :hw_json[key])
    {
        filters.emplace_back(filter_item.get<string>());
    }

    return RET_OK; 
}
int huawei_mec_protocol::build_mqtt_info_up(const V2xRspHeartBeat &heartBeatRsp,string &data)
{
    auto j = nlohmann::json{
        {"seqNum",get_msg_seqnum()},
        {"rsuId", m_rsu_id},
        {"rsuEsn",m_rsu_id},
        {"version",heartBeatRsp.soft_ver},
        {"rsuName",m_rsu_id},
        {"rsuStatus",to_string(heartBeatRsp.dev_type)},
        {"location",
            {
                {"lon",heartBeatRsp.longitude*1.0/LONGITUDE_RES},
                {"lat",heartBeatRsp.latitude*1.0/LATITUDE_RES}
            }
        },
        {"ack",true},
        
    };
    data = j.dump(); 
    return RET_OK;
}

int huawei_mec_protocol::build_mqtt_basic_status_up(const V2xRspHeartBeat &heartBeatRsp,string &data)
{
    auto j =  nlohmann::json{
        {"seqNum",get_msg_seqnum()},
        {"rsuId", m_rsu_id},
        {"rsuEsn",m_rsu_id},
        {"timestamp",ENGINE_MODULE_NAMESPACE::get_ms()},
        {"protocolVersion","v1"},
        {"rsuStatus",to_string(heartBeatRsp.dev_type)},
        {"location",
            {
                {"lon",heartBeatRsp.longitude*1.0/LONGITUDE_RES},
                {"lat",heartBeatRsp.latitude*1.0/LATITUDE_RES}
            }
        },
        {"transProtocal","http"}
    }; 
    data = j.dump(); 
    return RET_OK;
}

/// @brief 
/// @param heartBeatRsp 
/// @param data 
/// @return 
int huawei_mec_protocol::build_mqtt_run_status_up(const V2xRspHeartBeat &heartBeatRsp,string &data)
{
    cpu_info_t cpu_info; 
    dev_performance_ref.get_cpu_info(cpu_info); 

    mem_info_t  mem_info;
    dev_performance_ref.get_mem_info(mem_info);
    
    disk_info_t  disk_info;
    dev_performance_ref.get_disk_info(disk_info);

    net_info_t net_info; 
    dev_performance_ref.get_net_info(net_info); 
    auto j =  nlohmann::json{
        {"seqNum",get_msg_seqnum()},
        {"rsuId", m_rsu_id},
        {"rsuEsn",m_rsu_id},
        {"timestamp",ENGINE_MODULE_NAMESPACE::get_ms()},
        {"protocolVersion","v1"},
        {"runningInfo",{
            {"cpu",{
                {"load",cpu_info.load1},
                {"uti",std::to_string(cpu_info.occupancy)}
            }},
            {"mem",{
                {"total",mem_info.total/1024},
                {"used",mem_info.use/1024},
                {"free",(mem_info.total - mem_info.use)/1024}

            }},
            {"net",{
                {"rx",net_info.rx},
                {"tx",net_info.tx},
                {"rxByte",net_info.rxByte},
                {"txByte",net_info.rxByte}
            }},
        }},
        {"ack",0}
    }; 
    data =  j.dump();
    return RET_OK;
}

/// @brief 搭建确认帧回包
/// @param ack_info 
/// @param error_code 
/// @param err_desc 
/// @param data 
/// @return 
int huawei_mec_protocol::build_mqtt_req_ack_up(const hw_req_ack_info_t & ack_info,int error_code,const string &err_desc,string & data)
{
    auto j =  nlohmann::json{
        {"seqNum",ack_info.seqNum},
        {"errorCode",error_code}
    }; 

    if(error_code != 0 )
    {
        j["errorDesc"] = err_desc; 
    }
    data = j.dump();
    return RET_OK;
}


/// @brief 
/// @param heartBeatRsp 
/// @param gv_json 
/// @param hw_data 
/// @return 
int huawei_mec_protocol::bsm_gv2hw(shared_ptr<json> & gv_json_ptr,string &hw_data)
{
#if 0 
///////////////////////
// gv 
{
	"msgCnt": 62,
	"id": "10000000",
	"secMark": 30281,
	"pos": {
		"lat": 0,
		"long": 0
	},
	"transmission": 0,
	"speed": 0,
	"heading": 0,
	"angle": 0,
	"accelSet": {
		"Long": 0,
		"lat": 0,
		"vert": 0,
		"yaw": 0
	},
    
	"brakes": {},
	"size": {
		"width": 200,
		"length": 500,
		"height": 40
	},
	"vehicleClass": {
		"classification": 0
	}
}

// motionCfd 
    ret = decode_motion_cfd(sub_json, "motionCfd", &value->motionCfd, &value->motionCfd_option, err_buf, err_size);
    CHECK_ASN_JSON_RETVALUE(ret);
    // accelSet 
    ret = decode_accel_set(sub_json, "accelSet", &value->accelSet, nullptr, err_buf, err_size);
    CHECK_ASN_JSON_RETVALUE(ret);
    // brakes 
    ret = decode_brakes(sub_json, "brakes", &value->brakes, nullptr, err_buf, err_size);
    CHECK_ASN_JSON_RETVALUE(ret);
    // size 
    ret = decode_veh_size(sub_json, "size", &value->size, nullptr, err_buf, err_size);
    CHECK_ASN_JSON_RETVALUE(ret);
    // vehicleClass 
    ret = decode_veh_class(sub_json, "vehicleClass", &value->vehicleClass, nullptr, err_buf, err_size);
    CHECK_ASN_JSON_RETVALUE(ret);


    // safetyExt
    ret = decode_safety_ext(sub_json, "safetyExt", &value->safetyExt, &value->safetyExt_option, err_buf, err_size);
    CHECK_ASN_JSON_RETVALUE(ret);
    // emergencyExt
    ret = decode_emergency_ext(sub_json, "emergencyExt", &value->emergencyExt, &value->emergencyExt_option, err_buf, err_size);
    CHECK_ASN_JSON_RETVALUE(ret);

///////////////////////
// hw 
{
    "bsmDatas": [
        {
            "vehicleId": "12345678",
            "timeStamp": 52280,
            "timeConfidence": 2,
            "pos": {
                "lon": 108.75,
                "lat": 34.03,
                "ele": 1888.8
            },
            "posAccuracy": {
                "semiMajor": 6,
                "semiMinor": 6,
                "orientation": 6
            },
            "posConfidence": {
                "positionConfidence": 6,
                "eleConfidence": 6
            },
            "transmission": 6,
            "speed": 1350,
            "heading": 0,
            "angle": -108,
            "motionConfidence": {
                "speedConfidence": 6,
                "headingConfidence": 6,
                "steerConfidence": 3
            },
            "accelSet": {
                "longAccel": -1000,
                "latAccel": 1000,
                "vertAccel": 108,
                "yawRate": 18888
            },
            "brakes": {
                "brakePadelStatus": 1,
                "wheelBrakeStatus": {
                    "setStatus": false,
                    "leftFront": false,
                    "leftRear": true,
                    "rightFront": false,
                    "rightRear": true
                },
                "tractionStatus": 1,
                "absStatus": 1,
                "scsStatus": 1,
                "brakeBoostStatus": 1,
                "auxBrakesStatus": 1
            },
            
            "size": {
                "width": 166,
                "length": 1666,
                "height": 66
            },
            "vehicleClass": {
                "basicVehicleClass": 188,
                "fuelType": 1
            }
        }
    ]
}
#endif

    try
    {
        auto &gv_bsm =  *(gv_json_ptr.get());

        auto hw_bsm  =  nlohmann::json{
            {"vehicleId",gv_bsm["id"]},
            {"timeStamp",ENGINE_MODULE_NAMESPACE::get_ms()},
            {"transmission",gv_bsm["transmission"]},
            {"speed",gv_bsm["speed"]},
            {"heading",gv_bsm["heading"]},
            {"size",gv_bsm["size"]}
        };

        if(CHECK_JSON_KEY(gv_bsm,"timeConfidence"))
        {
            hw_bsm["timeConfidence"] = gv_bsm["timeConfidence"];     
        }
        pos_gv2hw(gv_bsm["pos"],hw_bsm["pos"] ); 

        if(CHECK_JSON_KEY(gv_bsm,"posAccuracy"))
        {
            hw_bsm["posAccuracy"] = gv_bsm["posAccuracy"]; 
        }
        if(CHECK_JSON_KEY(gv_bsm,"posConfidence"))
        {
            posConfidence_gv2hw(gv_bsm["posConfidence"],hw_bsm["posConfidence"]); 
        }

        if(CHECK_JSON_KEY(gv_bsm,"angle"))
        {
            hw_bsm["angle"] =  gv_bsm["angle"];
        }

        if(CHECK_JSON_KEY(gv_bsm,"motionCfd"))
        {
            motionConfidence_gv2hw(gv_bsm["motionCfd"],hw_bsm["motionConfidence"]);
        }
        accelSet_gv2hw(gv_bsm["accelSet"],hw_bsm["accelSet"] ); 
        hw_bsm["brakes"]=  nlohmann::json::object(); 
        brakes_gv2hw(gv_bsm["brakes"],hw_bsm["brakes"]); 
        vehicleClass_gv2hw(gv_bsm["vehicleClass"],hw_bsm["vehicleClass"]); 

        auto hw_json = nlohmann::json(); 
        hw_json["bsmDatas"].emplace_back(::move(hw_bsm)); 
        hw_data = hw_json.dump();
    }
    catch(const std::exception& e)
    {
        WLE("json::parse error %s ",e.what()); 
        return RET_FAIL;
    } 
    return RET_OK;
}




int huawei_mec_protocol::rsi_gv2hw(shared_ptr<json> & gv_json_ptr,string &hw_data) 
{
#if 0
////////////////////////////////////////////////////////
// gv 
{
	"msgCnt": 60,
	"moy": 7364,
	"id": "XtMClS0h",
	"refPos": {
		"lat": 231693386,
		"long": 1133994943,
		"elevation": 730
	},
	"rtes": [{
		"rteId": 1,
		"eventType": 408,
		"eventSource": 1,
		"eventPos": {
			"offsetLL": {
				"choiceID": 7,
				"position_LatLon": {
					"long": 1133997280,
					"lat": 231693420
				}
			}
		},
		"eventRadius": 10000,
		"description": {
			"choiceID": 2,
			"description": "rteWarning"
		},
		"timeDetails": {
			"startTime": 428192,
			"endTime": 428192
		},
		"priority": " "
	}, {
		"rteId": 2,
		"eventType": 409,
		"eventSource": 1,
		"eventPos": {
			"offsetLL": {
				"choiceID": 7,
				"position_LatLon": {
					"long": 1133990600,
					"lat": 231692140
				}
			}
		},
		"eventRadius": 10000,
		"description": {
			"choiceID": 2,
			"description": "rteWarning"
		},
		"timeDetails": {
			"startTime": 428203,
			"endTime": 428203
		},
		"priority": " "
	}],
	"rtss": [{
		"rtsId": 1,
		"signType": 2,
		"signPos": {
			"offsetLL": {
				"choiceID": 7,
				"position_LatLon": {
					"long": 1133926960,
					"lat": 231693420
				}
			}
		},
		"description": {
			"choiceID": 2,
			"description": "rtsWarning"
		},
		"timeDetails": {
			"startTime": 345851,
			"endTime": 345851
		},
		"priority": " ",
		"referencePaths": [{
			"activePath": [{
				"offsetLL": {
					"choiceID": 7,
					"position_LatLon": {
						"long": 1133902330,
						"lat": 231592220
					}
				}
			}, {
				"offsetLL": {
					"choiceID": 7,
					"position_LatLon": {
						"long": 1134102330,
						"lat": 231792220
					}
				}
			}],
			"pathRadius": 10000
		}]
	}]
}

////////////////////////////////////////////////////////
// hw
{
    "rsiDatas": [
        {
            "id": "00000025",
            "refPos": {
                "lon": 108.835639,
                "lat": 34.201457
            },
            "rtss": [
                {
                    "rtsId": 0,
                    "signType": 1,
                    "signPosition": {
                        "lon": 108,
                        "lat": 34
                    },
                    "signDescription": "123",
                    "signPriority": 7,
                    "referencePaths": [
                        {
                            "activePath": [
                                {
                                    "lon": 108,
                                    "lat": 34
                                },
                                {
                                    "lon": 108.001,
                                    "lat": 34.0002
                                }
                            ],
                            "pathRadius": 1024
                        }
                    ]
                }
            ]
        }
    ],
    "ack": false
}
#endif 
    try
    {
        auto & gv_json =  *(gv_json_ptr.get());
        auto hw_rsiDatas =  nlohmann::json::array();
        
        auto hw_rsiDatas_item =  nlohmann::json{
            {"id",gv_json["id"]}
        };
        pos_gv2hw(gv_json["refPos"],hw_rsiDatas_item["refPos"]); 
        
        if(CHECK_JSON_KEY(gv_json,"rtes"))
        {
            arrays_convert(gv_json["rtes"],hw_rsiDatas_item["rtes"],&huawei_mec_protocol::rte_gv2hw); 
        }
        if(CHECK_JSON_KEY(gv_json,"rtss"))
        {
            arrays_convert(gv_json["rtss"],hw_rsiDatas_item["rtss"],&huawei_mec_protocol::rts_gv2hw); 
        }
        hw_rsiDatas.emplace_back(std::move(hw_rsiDatas_item));
        auto hw_json =  nlohmann::json{
            {"ack",false},
            {"rsiDatas",hw_rsiDatas}
        }; 
        hw_data =  hw_json.dump();
    }
    catch(const std::exception& e)
    {
        WLE("json::parse error %s ",e.what()); 
        return RET_FAIL;
    } 
    return RET_OK;
}


/// @brief 协议转换，华为的rsi 转换成 金溢 rsi
/// @param hw_rsi 
/// @param gv_rsi 
/// @return 
int huawei_mec_protocol::rsi_hw2gv(const string & hw_rsi, string &gv_rsi,hw_req_ack_info_t & ack_info)
{
    try
    {
        auto v2x_heartbeat_info  = v2x_client_ref.get_heartbeat_info();
        auto j = json::parse(hw_rsi);
        fetch_ack_seq_num(j,ack_info);  
        auto &rsiDatas = j["rsiDatas"]; 
        auto j_gv =  nlohmann::json{
            {"msgCnt",0},
            {"moy",0},
            {"id",""},
            {"refPos",{
              {"lat",v2x_heartbeat_info.latitude},  
              {"long",v2x_heartbeat_info.longitude}
            }}
        };

        for(auto & rsi_item:rsiDatas)
        {
            
            if(CHECK_JSON_KEY(rsi_item,"rtes"))
            {// rtes
                auto &rtes = rsi_item["rtes"]; 
                for(auto & hw_rte_item:rtes)
                {
                    auto gv_rte_item  = nlohmann::json(); 
                    rte_hw2gv(hw_rte_item,gv_rte_item); 
                    j_gv["rtes"].emplace_back(std::move(gv_rte_item)); 
                }
            }
            if(CHECK_JSON_KEY(rsi_item,"rtss"))
            {// rtss
                auto & rtss =  rsi_item["rtss"]; 
                for(auto &hw_rts_item:rtss)
                {
                    auto gv_rts_item =  nlohmann::json(); 
                    rts_hw2gv(hw_rts_item,gv_rts_item); 
                    j_gv["rtss"].emplace_back(std::move(gv_rts_item)); 
                }
            }
        }
        gv_rsi = j_gv.dump(); 
        return RET_OK;
    }
    catch(const std::exception& e)
    {
        WLE("json::parse exception %s  hw_rsi %s ",e.what(),hw_rsi.c_str()); 
        return RET_FAIL;
    } 
    return RET_OK;
}

/// @brief 暂没有实现 
/// @param hw_spat 
/// @param gv_spat 
/// @param ack_info 
/// @return 
int huawei_mec_protocol::spat_hw2gv(const string & hw_spat, string &gv_spat,hw_req_ack_info_t & ack_info)
{
    try
    {
        auto j_spat_hw = json::parse(hw_spat);
        auto j_spat_gv =  nlohmann::json
        {
            {"msgCnt",0}
        };

        if(CHECK_JSON_KEY(j_spat_hw,"name"))
        {
            j_spat_gv["name"] =  j_spat_hw["name"].get<string>();
        }
        arrays_convert(j_spat_hw["intersections"],j_spat_gv["intersections"],&huawei_mec_protocol::intersection_hw2gv); 
        gv_spat =  j_spat_gv.dump(); 
        return RET_OK;
    }
    catch(const std::exception& e)
    {
        WLE("json::parse error %s  hw_spat %s ",e.what(),hw_spat.c_str()); 
        return RET_FAIL;
    } 
    return RET_OK;
}

int huawei_mec_protocol::spat_gv2hw(shared_ptr<json> & gv_json_ptr,string &hw_data)
{
#if 0
////////////////////////////////
// gv
{
	"msgCnt": 114,
	"moy": 56739,
	"timeStamp": 42920,
	"name": "FS-CCQ-SZQD",
	"intersections": [{
		"intersectionId": {
			"region": 21,
			"id": 13
		},
		"status": {
			"manualControlIsEnabled": false,
			"stopTimeIsActivated": false,
			"failureFlash": false,
			"preemptIsActive": false,
			"signalPriorityIsActive": true,
			"fixedTimeOperation": false,
			"trafficDependentOperation": false,
			"standbyOperation": false,
			"failureMode": false,
			"off": false,
			"recentMAPmessageUpdate": false,
			"recentChangeInMAPassignedLanesIDsUsed": false,
			"noValidMAPisAvailableAtThisTime": false,
			"noValidSPATisAvailableAtThisTime": false
		},
		"moy": 56739,
		"timeStamp": 42920,
		"timeConfidence": 15,
		"phases": [{
			"id": 4,
			"phaseStates": [{
				"light": 3,
				"timing": {
					"choiceID": 1,
					"counting": {
						"startTime": 0,
						"minEndTime": 0,
						"maxEndTime": 0,
						"likelyEndTime": 430,
						"timeConfidence": 100,
						"nextStartTime": 43,
						"nextDuration": 0
					}
				}
			}]
		},
////////////////////////////////
//hw 
{
    "intersections": [
        {
            "intersectionId": {
                "region": 0,
                "id": 1
            },
            "status": 256,
            "phases": [
                {
                    "phaseId": 1,
                    "phaseStates": [
                        {
                            "light": "3",
                            "timing": {
                                "counting": {
                                    "startTime": {
                                        "timeMark": 0
                                    },
                                    "likelyEndTime": {
                                        "timeMark": 450
                                    },
                                    "timeConfidence": 0
                                },
                                "startTime": 0,
                                "likelyEndTime": 450
                            }
                        }
                    ]
                },

#endif
    try
    {
        auto &gv_json =  *(gv_json_ptr.get());
        auto hw_json =  nlohmann::json();
        if(CHECK_JSON_KEY(gv_json,"name"))
        {
            hw_json["name"] = gv_json["name"];
        }
        arrays_convert(gv_json["intersections"],hw_json["intersections"] ,&huawei_mec_protocol::intersection_gv2hw); 
        hw_data =  hw_json.dump();
    }
    catch(const std::exception& e)
    {
        WLE("json::parse error %s ",e.what()); 
        return RET_FAIL;
    } 
    return RET_OK;
}

/// @brief rsm_hw2gv  
/// @param hw_spat 
/// @param gv_spat 
/// @param ack_info 
/// @return 
int huawei_mec_protocol::rsm_hw2gv(const string & hw_rsm, string &gv_rsm,hw_req_ack_info_t & ack_info)
{
#if 0 

/// gv 
{
	"msgCnt": 88,
	"id": "GJMBqX4Q",
	"refPos": {
		"lat": 0,
		"long": 0
	},
	"participants": [{
		"ptcType": 1,
		"ptcId": 51,
		"source": 7,
		"secMark": 42320,
		"pos": {
			"offsetLL": {
				"choiceID": 7,
				"position_LatLon": {
					"long": 1131520807,
					"lat": 229875548
				}
			}
		},
		"posConfidence": {
			"pos": 0
		},
		"speed": 0,
		"heading": 8155,
		"angle": 0,
		"size": {
			"width": 100,
			"length": 400,
			"height": 20
		}
	}]
}

//// hv 

{
	"ack": false,
	"rsms": [{
		"participants": [{
			"heading": 14400,
			"pos": {
				"lat": 22.529654183444283,
				"lon": 113.94782102104044
			},
			"ptcId": 1,
			"ptcType": 1,
			"secMark": 56456,
			"size": {
				"height": 127,
				"length": 4095,
				"width": 1023
			},
			"source": 3,
			"speed": 833
		}]
	}],
	"seqNum": "47"
}

#endif 
    try
    {
        auto v2x_heartbeat_info  = v2x_client_ref.get_heartbeat_info();
        auto j = json::parse(hw_rsm);
        auto &j_rsms = j["rsms"]; 
        auto j_gv_rsm =  nlohmann::json{
            {"msgCnt",0},
            {"id",""},
            {"refPos",{
              {"lat",v2x_heartbeat_info.latitude},  
              {"long",v2x_heartbeat_info.longitude}
            }}
        };
        auto gv_participants =  nlohmann::json::array(); 
    
        for(auto & rsm_item:j_rsms)
        {
            arrays_convert(rsm_item["participants"],j_gv_rsm["participants"] ,&huawei_mec_protocol::participant_hw2gv); 
        }
        gv_rsm = j_gv_rsm.dump(); 
        return RET_OK;
    }
    catch(const std::exception& e)
    {
        WLE("catch exception %s hw_rsm %s ",e.what(),hw_rsm.c_str()); 
        return RET_FAIL;
    } 
    return RET_OK;
}


int huawei_mec_protocol::rsm_gv2hw(shared_ptr<json> & gv_json_ptr,string &hw_data)
{
#if 0
////////////////////////////////
// gv 
{
	"msgCnt": 88,
	"id": "GJMBqX4Q",
	"refPos": {
		"lat": 0,
		"long": 0
	},
	"participants": [{
		"ptcType": 1,
		"ptcId": 51,
		"source": 7,
		"secMark": 42320,
		"pos": {
			"offsetLL": {
				"choiceID": 7,
				"position_LatLon": {
					"long": 1131520807,
					"lat": 229875548
				}
			}
		},
		"posConfidence": {
			"pos": 0
		},
		"speed": 0,
		"heading": 8155,
		"angle": 0,
		"size": {
			"width": 100,
			"length": 400,
			"height": 20
		}
	}
	}]
}
///////////////////////////////
// hw 
{
	"ack": false,
	"rsms": [{
		"participants": [{
			"heading": 14400,
			"pos": {
				"lat": 22.529654183444283,
				"lon": 113.94782102104044
			},
			"ptcId": 1,
			"ptcType": 1,
			"secMark": 56456,
			"size": {
				"height": 127,
				"length": 4095,
				"width": 1023
			},
			"source": 3,
			"speed": 833
		}]
	}],
	"seqNum": "47"
}
#endif
    try
    {
        auto & gv_json =  *(gv_json_ptr.get());
        auto hw_json =  nlohmann::json{
           {"name",nullptr},
           {"id",get_msg_seqnum()},
        }; 
        auto hw_rsms_item = nlohmann::json();

        pos_gv2hw(gv_json["refPos"],hw_rsms_item["refPos"]); 

        arrays_convert(gv_json["participants"],hw_rsms_item["participants"],&huawei_mec_protocol::participant_gv2hw); 

        hw_json["rsms"].emplace_back(std::move(hw_rsms_item));

        hw_data =  hw_json.dump();
    }
    catch(const std::exception& e)
    {
        WLE("catch exception %s ",e.what()); 
        return RET_FAIL;
    }
    return RET_OK;
}


/// @brief map_hw2gv
/// @param hw_map 
/// @param gv_map 
/// @param ack_info 
/// @return 
int huawei_mec_protocol::map_hw2gv(const string & hw_map, string &gv_map,hw_req_ack_info_t & ack_info)
{

#if 0
////////////////////////
// gv 
    ret =  decode_uft8string(sub_json, (char *)JSON_MAP_NAME, (char **)&value->name.buf, &value->name.len, &value->name_option, err_buf, err_size);
    
    CHECK_ASN_JSON_RETVALUE(ret);
    ret = decode_node_ref_id(sub_json, (char *)JSON_MAP_ID, &value->id, nullptr, err_buf, err_size);
    CHECK_ASN_JSON_RETVALUE(ret);
    ret = decode_position(sub_json, (char *)JSON_MAP_REFPOS, &value->refPos, nullptr, err_buf, err_size);
    CHECK_ASN_JSON_RETVALUE(ret);
    ret = decode_link_list(sub_json, (char *)JSON_MAP_INLINKS, &value->inLinks, &value->inLinks_option, err_buf, err_size);
    CHECK_ASN_JSON_RETVALUE(ret);
    ret =  decode_list(sub_json,JSON_MAP_INLINKS_EX,&value->inLinks_ex,&value->inLinks_ex_option,err_buf,err_size,MIN_LIST_NUM,MAX_LINKSEX_NUM,this,&json_mapMsg::decode_link_ex);
    CHECK_ASN_JSON_RETVALUE(ret);
    ret = decode_prohibited_zone(sub_json, (char *)JSON_MAP_PROHIBITEDZONE, &value->prohibitedzone, &value->prohibitedzone_option, err_buf, err_size);
    CHECK_ASN_JSON_RETVALUE(ret);

////////////////////////
// hw 
{
	"ack": false,
	"eTag": "test",
	"map": {
		"nodes": [{
			"id": {
				"id": 1,
				"region": 21
			},
			"inLinks": [{
				"lanes": [{
					"connectsTo": [{
						"connectingLane": {
							"laneId": 5,
							"maneuvers": 2048
						},
						"remoteIntersection": {
							"id": 4,
							"region": 24
						}
					}],
					"laneAttributes": {
						"laneType": "01000000",
						"shareWith": 512
					},
					"laneId": 5,
					"laneWidth": 500,
					"maneuvers": 2048,
					"points": [{
						"lat": 38.033353,
						"lon": 107.112237
					}, {
						"lat": 38.033354,
						"lon": 107.112238
					}, {
						"lat": 38.033355,
						"lon": 107.112239
					}],
					"speedLimits": [{
						"speed": 600,
						"type": "5"
					}]
				}],
				"linkWidth": 1,
				"movements": [{
					"phaseId": 4,
					"remoteIntersection": {
						"id": 3,
						"region": 23
					}
				}],
				"name": "link_name",
				"points": [{
					"lat": 39.033353,
					"lon": 117.112237
				}, {
					"lat": 39.033354,
					"lon": 117.112238
				}, {
					"lat": 39.033355,
					"lon": 117.112239
				}],
				"speedLimits": [{
					"speed": 500,
					"type": "4"
				}],
				"upstreamNodeId": {
					"id": 2,
					"region": 22
				}
			}],
			"refPos": {
				"ele": 15,
				"lat": 39.0321787,
				"lon": 117.1057227
			}
		}],
		"timeStamp": 36492
	},
	"mapSlice": "test",
	"seqNum": "65"
}
#endif 
    try
    {   
        auto j = json::parse(hw_map);
        fetch_ack_seq_num(j,ack_info);  
        auto & j_hw_map =  j["map"]; 
        auto j_gv_map =  nlohmann::json{
            {"msgCnt",0}
        };

        if(CHECK_JSON_KEY(j_hw_map,"timeStamp"))
        {
            j_gv_map["timeStamp"] =  j_hw_map["timeStamp"].get<int>(); 
        }
        arrays_convert(j_hw_map["nodes"],j_gv_map["nodes"],&huawei_mec_protocol::node_hw2gv); 
        gv_map = j_gv_map.dump(); 
    }
    catch(const std::exception& e)
    {
        WLE("catch exception error %s hw_map %s ",e.what(),hw_map.c_str()); 
        return RET_FAIL;
    } 
    return RET_OK;
}

int huawei_mec_protocol::map_gv2hw(shared_ptr<json> & gv_json_ptr,string &hw_data)
{
#if 0


/////////////////////////////////
// hw 

{
    "mapSlice": "mapSlice",
    "map": {
        "nodes": [
            {
                "id": {
                    "region": 0,
                    "id": 1
                },
                "refPos": {
                    "lon": 123.45217,
                    "lat": 41.69551
                },
                "inLinks": [
                    {
                        "upstreamNodeId": {
                            "region": 0,


#endif
    try
    {
        auto & gv_json = *(gv_json_ptr.get()); 
        auto hw_nodes =  nlohmann::json::array(); 
        auto hw_json =  nlohmann::json{
            {"seqNum",get_msg_seqnum()},
            {"ack",false}
        }; 
        arrays_convert(gv_json["nodes"],hw_json["map"]["nodes"],&huawei_mec_protocol::node_gv2hw); 
        hw_data = hw_json.dump();
    }
    catch(const std::exception& e)
    {
        WLE("catch exception error %s ",e.what()); 
        return RET_FAIL;
    } 
    return RET_OK;
}



void huawei_mec_protocol::fetch_ack_seq_num(nlohmann::json & j,hw_req_ack_info_t & ack_info)
{
    if(j.end() != j.find("ack"))
    {
        ack_info.ack=  j["ack"].get<bool>();
    }
    if(j.end() != j.find("seqNum"))
    {
        ack_info.seqNum = j["seqNum"].get<string>(); 
    }
}


int huawei_mec_protocol::rte_hw2gv(nlohmann::json & hw_json,nlohmann::json & gv_json )
{
#if 0 //gv_rte
{
              "rteId": 1,
              "eventType": 408,
              "eventSource": 1,
              "eventPos": {
                     "offsetLL": {
                            "choiceID": 7,
                            "position_LatLon": {
                                   "long": 1133997280,
                                   "lat": 231693420
                            }
                     }
              },
              "eventRadius": 10000,
              "description": {
                     "choiceID": 2,
                     "description": "rteWarning"
              },
              "timeDetails": {
                     "startTime": 428192,
                     "endTime": 428192
              },
              "priority": ""
       }
#endif 
    gv_json["rteId"] =  hw_json["rteId"];
    gv_json["eventType"] = hw_json["eventType"];
    gv_json["eventSource"] =  hw_json["eventSource"];
    
    
    if(CHECK_JSON_KEY(hw_json,"eventPosition"))
    {
        posoffset_hw2gv(hw_json["eventPosition"],gv_json["eventPos"]); 
    }

    if(CHECK_JSON_KEY(hw_json,"eventRadius"))
    {
        gv_json["eventRadius"] = hw_json["eventRadius"]; //与标准相同 0.1米，  hw 单位分米
    }
    if(CHECK_JSON_KEY(hw_json,"eventDescription"))
    {
        Descript_hw2gv(hw_json["eventDescription"].get<string>(),gv_json["description"]); 
    }
    
    if(CHECK_JSON_KEY(hw_json,"timeDetails"))
    {
        TimeDetail_hw2gv(hw_json["timeDetails"],gv_json["timeDetails"]); 
    }
    if(CHECK_JSON_KEY(hw_json,"eventPriority"))
    {
        string priority; 
        rsi_priority_hw2gv(hw_json["eventPriority"].get<int>(),priority);
        gv_json["priority"] = std::move(priority); 
    }
    if(CHECK_JSON_KEY(hw_json,"referencePaths"))
    {
        arrays_convert(hw_json["referencePaths"],gv_json["referencePaths"],&huawei_mec_protocol::referencePath_hw2gv); 
    }

    if(CHECK_JSON_KEY(hw_json,"referenceLinks"))
    {
        arrays_convert(hw_json["referenceLinks"],gv_json["referenceLinks"],&huawei_mec_protocol::referenceLink_hw2gv); 
    }
    if(CHECK_JSON_KEY(hw_json,"eventConfidence"))
    {
        gv_json["eventConfidence"] = hw_json["eventConfidence"]; 
    }
    if(CHECK_JSON_KEY(hw_json,"duration"))
    {
        gv_json["duration"] = hw_json["duration"];
    }
    if(CHECK_JSON_KEY(hw_json,"eventStatus"))
    {
        gv_json["alertStatus"] = hw_json["eventStatus"];
    }
    //===================================================
    // 以下没有实现
    #if 0
    if(CHECK_JSON_KEY(hw_json,"vehL"))
    {
        
    }
    if(CHECK_JSON_KEY(hw_json,"vehW"))
    {
        
    }

    if(CHECK_JSON_KEY(hw_json,"vehH"))
    {
        
    }
    if(CHECK_JSON_KEY(hw_json,"picUrl"))
    {
        
    }
    if(CHECK_JSON_KEY(hw_json,"videoUrl"))
    {
        
    }
    #endif 

    return RET_OK;
}

int huawei_mec_protocol::rte_gv2hw(nlohmann::json & gv_json,nlohmann::json & hw_json )
{    
    hw_json["rteId"] =  gv_json["rteId"];
    hw_json["eventType"] = gv_json["eventType"];
    hw_json["eventSource"] =  gv_json["eventSource"];
    if(CHECK_JSON_KEY(gv_json,"eventPos"))
    {
        posoffset_gv2hw(gv_json["eventPos"],hw_json["eventPosition"]);  
    }

    if(CHECK_JSON_KEY(gv_json,"eventRadius"))
    {
        hw_json["eventRadius"] = gv_json["eventRadius"]; //与标准相同 0.1米，  hw 单位分米
    }
    if(CHECK_JSON_KEY(gv_json,"description"))
    {
        string description; 
        Descript_gv2hw(gv_json["description"],description); 
        hw_json["eventDescription"] =  description;
    }
    
    if(CHECK_JSON_KEY(gv_json,"timeDetails"))
    {
        TimeDetail_gv2hw(gv_json["timeDetails"],hw_json["timeDetails"] ); 
    }
    if(CHECK_JSON_KEY(gv_json,"priority"))
    {
        int eventPriority =  0; 
        rsi_priority_gv2hw(gv_json["priority"].get<string>(),eventPriority);
        hw_json["eventPriority"] = eventPriority;
    }
    if(CHECK_JSON_KEY(gv_json,"referencePaths"))
    {
        arrays_convert(gv_json["referencePaths"],hw_json["referencePaths"],&huawei_mec_protocol::referencePath_gv2hw); 
    }

    if(CHECK_JSON_KEY(gv_json,"referenceLinks"))
    {
        arrays_convert(gv_json["referenceLinks"],hw_json["referenceLinks"],&huawei_mec_protocol::referenceLink_gv2hw); 
    }
    if(CHECK_JSON_KEY(gv_json,"eventConfidence"))
    {
        hw_json["eventConfidence"] = gv_json["eventConfidence"];
    }
    if(CHECK_JSON_KEY(gv_json,"duration"))
    {
        hw_json["duration"] = gv_json["duration"];
    }
    if(CHECK_JSON_KEY(gv_json,"alertStatus"))
    {
        hw_json["eventStatus"] = gv_json["alertStatus"];
    }

    return RET_OK;
}

int huawei_mec_protocol::rts_hw2gv(nlohmann::json & hw_json,nlohmann::json & gv_json )
{
#if 0  // gv_rts
{
	"msgCnt": 48,
	"moy": 56260,
	"id": "GJMBqX4Q",
	"refPos": {
		"lat": 229875904,
		"long": 1131522560
	},
	"rtes": [{
		"rteId": 121,
		"eventType": 901,
		"eventSource": 5,
		"eventPos": {
			"offsetLL": {
				"choiceID": 7,
				"position_LatLon": {
					"long": 1131522560,
					"lat": 229875904
				}
			}
		},
		"eventRadius": 1000,
		"timeDetails": {
			"startTime": 56260
		},
		"priority": "8"
	}]
}
#endif

    gv_json["rtsId"] =  hw_json["rtsId"].get<int>();
    gv_json["signType"] = hw_json["signType"].get<int>();
    if(CHECK_JSON_KEY(hw_json,"signPosition"))
    {
        posoffset_hw2gv(hw_json["signPosition"],gv_json["signPos"]); 
    }

    if(CHECK_JSON_KEY(hw_json,"signDescription"))
    {
        auto description =  nlohmann::json(); 
        Descript_hw2gv(hw_json["signDescription"].get<string>(),description); 
        gv_json["description"] =  description;
    }

    if(CHECK_JSON_KEY(hw_json,"timeDetails"))
    {
        TimeDetail_hw2gv(hw_json["timeDetails"],gv_json["timeDetails"]); 
    }
    if(CHECK_JSON_KEY(hw_json,"signPriority"))
    {
        string priority;  
        rsi_priority_hw2gv(hw_json["signPriority"].get<int>(),priority);
        gv_json["priority"] = priority; 
    }

    if(CHECK_JSON_KEY(hw_json,"referencePaths"))
    {
        arrays_convert(hw_json["referencePaths"],gv_json["referencePaths"],&huawei_mec_protocol::referencePath_hw2gv); 
    }

    if(CHECK_JSON_KEY(hw_json,"referenceLinks"))
    {
        arrays_convert(hw_json["referenceLinks"],gv_json["referenceLinks"],&huawei_mec_protocol::referenceLink_hw2gv); 
    }

    /// 
    int duration_rsi_waring_delete ;
    if(CHECK_JSON_KEY(hw_json,"duration"))
    {
        gv_json["duration"] = hw_json["duration"];
    }
    if(CHECK_JSON_KEY(hw_json,"eventStatus"))
    {
        gv_json["alertStatus"] = hw_json["eventStatus"];
    }

    return RET_OK;
}

int huawei_mec_protocol::rts_gv2hw(nlohmann::json & gv_json,nlohmann::json & hw_json )
{

#if 0
/////////////////////////////////////////
// gv 
"rtss": [{
    "rtsId": 1,
    "signType": 2,
    "signPos": {
        "offsetLL": {
            "choiceID": 7,
            "position_LatLon": {
                "long": 1133926960,
                "lat": 231693420
            }
        }
    },
    "description": {
        "choiceID": 2,
        "description": "rtsWarning"
    },
    "timeDetails": {
        "startTime": 345851,
        "endTime": 345851
    },
    "priority": " ",
    "referencePaths": [{
        "activePath": [{
            "offsetLL": {
                "choiceID": 7,
                "position_LatLon": {
                    "long": 1133902330,
                    "lat": 231592220
                }
            }
        }, {
            "offsetLL": {
                "choiceID": 7,
                "position_LatLon": {
                    "long": 1134102330,
                    "lat": 231792220
                }
            }
        }],
        "pathRadius": 10000
    }]
/////////////////////////////////////////
// hw 
"rtss": [
{
    "rtsId": 0,
    "signType": 1,
    "signPosition": {
        "lon": 108,
        "lat": 34
    },
    "signDescription": "123",
    "signPriority": 7,
    "referencePaths": [
        {
            "activePath": [
                {
                    "lon": 108,
                    "lat": 34
                },
                {
                    "lon": 108.001,
                    "lat": 34.0002
                }
            ],
            "pathRadius": 1024
        }
    ]
}
]
#endif 
    hw_json["rtsId"] = gv_json["rtsId"]; 
    hw_json["signType"] = gv_json["signType"]; 
    
    if(CHECK_JSON_KEY(gv_json,"signPos"))
    {
        posoffset_gv2hw(gv_json["signPos"],hw_json["signPosition"]); 
    }

    if(CHECK_JSON_KEY(gv_json,"description"))
    {
        string signDescription; 
        Descript_gv2hw(gv_json["description"],signDescription); 
        hw_json["signDescription"] = signDescription; 
    }
    
    if(CHECK_JSON_KEY(gv_json,"timeDetails"))
    {
        TimeDetail_gv2hw(gv_json["timeDetails"],hw_json["timeDetails"]); 
    }
    
    if(CHECK_JSON_KEY(gv_json,"priority"))
    {
        int signPriority = 0; 
        rsi_priority_gv2hw(gv_json["priority"].get<string>(),signPriority);
        hw_json["signPriority"] = signPriority;
    }
    if(CHECK_JSON_KEY(gv_json,"referencePaths"))
    {
        arrays_convert(gv_json["referencePaths"],hw_json["referencePaths"],&huawei_mec_protocol::referencePath_gv2hw); 
    }
    
    if(CHECK_JSON_KEY(gv_json,"referenceLinks"))
    {
        arrays_convert(gv_json["referenceLinks"],hw_json["referenceLinks"],&huawei_mec_protocol::referenceLink_gv2hw);
    }


    return RET_OK;
}

int huawei_mec_protocol::pos_hw2gv(nlohmann::json & hw_json,nlohmann::json & gv_json )
{
    uint32_t log = hw_json["lon"].get<float>()*LONGITUDE_RES; 
    uint32_t lat =  hw_json["lat"].get<float>()*LATITUDE_RES; 
    gv_json["long"] =  log; 
    gv_json["lat"] =  lat; 
    if(CHECK_JSON_KEY(hw_json,"ele"))
    {
        uint32_t ele =  hw_json["ele"].get<uint32_t>();  /// 此处需要确认hw高度值单位
        gv_json["elevation"] =  ele;
    }
    return RET_OK;
}

int huawei_mec_protocol::pos_gv2hw(nlohmann::json & gv_json,nlohmann::json & hw_json )
{
    hw_json["lon"] =  gv_json["long"].get<uint32_t>()*1.0/LONGITUDE_RES; 
    hw_json["lat"] =  gv_json["lat"].get<uint32_t>()*1.0/LATITUDE_RES; 
    if(CHECK_JSON_KEY(gv_json,"elevation")) 
    {
        hw_json["ele"] =  gv_json["elevation"]; 
    }
    return RET_OK;
}

int huawei_mec_protocol::point_gv2hw(nlohmann::json & gv_json,nlohmann::json & hw_json)
{
#if 0
///////////////////////////////////
// gv 
"points": [{
        "posOffset": {
            "offsetLL": {
                "choiceID": 7,
                "position_LatLon": {
                    "long": 1131288064,
                    "lat": 229786835
                }
            },
            "offsetV": {
                "choiceID": 7,
                "elevation": 128
            }
        }
    }
///////////////////////////////////
// hw 

"points": [
{
    "lon": 116.345657348633,
    "lat": 40.0139617919922,
    "ele": 0
}
],
#endif
    auto & posOffset =  gv_json["posOffset"];
    auto & offsetLL =  posOffset["offsetLL"]; 
    auto & position_LatLon =  offsetLL["position_LatLon"]; 
    hw_json["lon"] = position_LatLon["long"].get<uint32_t>()*(double)1.0/LONGITUDE_RES; 
    hw_json["lat"] = position_LatLon["lat"].get<uint32_t>()*(double)1.0/LATITUDE_RES; 

    if(CHECK_JSON_KEY(posOffset,"offsetV"))
    {
        auto & offsetV = posOffset["offsetV"]; 
        hw_json["ele"] = offsetV["elevation"]; 
    }
    return RET_OK;
}


int huawei_mec_protocol::point_hw2gv(nlohmann::json & hw_json,nlohmann::json & gv_json)
{
    uint32_t lon =   hw_json["lon"].get<double>()*LONGITUDE_RES; 
    uint32_t lat =  hw_json["lat"].get<double>()*LATITUDE_RES; 

    gv_json =  nlohmann::json{
        {"posOffset",{
            {"offsetLL",{
                {"choiceID",7},
                {"position_LatLon",{
                    {"long",lon},
                    {"lat",lat}
                }}
            }}
        }}
    }; 
    if(CHECK_JSON_KEY(hw_json,"ele"))
    {
        gv_json["posOffset"]["offsetV"] =  nlohmann::json{
            {"choiceID",7},
            {"elevation",hw_json["ele"]}
        };
    }
    return RET_OK; 
}
// pos 转成 pos_offse 
int huawei_mec_protocol::posoffset_hw2gv(nlohmann::json & hw_json,nlohmann::json & gv_json )
{
#if 0


/////////////////////////
// hw 
"pos": {
        "lon": 111,
        "lat": 11,
        "ele": 55
    },

#endif
    uint32_t log =   hw_json["lon"].get<double>()*LONGITUDE_RES; 
    uint32_t lat =  hw_json["lat"].get<double>()*LATITUDE_RES; 
    gv_json["offsetLL"] =  nlohmann::json{
        {"choiceID",7},
        {"position_LatLon",{
            {"long",log},
            {"lat",lat}
        }}
    };

    if(CHECK_JSON_KEY(hw_json,"ele"))
    {
        gv_json["offsetV"] =  nlohmann::json{
            {"choiceID",7},
            {"elevation",hw_json["ele"]}
        };
    }
    return RET_OK;
}

int huawei_mec_protocol::posoffset_gv2hw(nlohmann::json & gv_json,nlohmann::json & hw_json )
{// gv 转 华为的  posoffse 只支持 choiceID 7 原始坐标
#if 0
/////////////////////////
// gv 
"posOffset": {
    "offsetLL": {
        "choiceID": 7,
        "position_LatLon": {
            "long": 1131288064,
            "lat": 229786835
        }
    },
    "offsetV": {
        "choiceID": 7,
        "elevation": 128
    }

#endif
    //cout<<"huawei_mec_protocol::posoffset_gv2hw gv_json "<<gv_json.dump()<<endl;
    //WLD("%s",gv_json.dump().c_str());
    //string debug_info =  gv_json.dump();
    //int debug_info_warning ; /// debug info must delete 

    auto & offsetLL =  gv_json["offsetLL"]; 
    auto & position_LatLon =  offsetLL["position_LatLon"]; 
    hw_json["lon"] = position_LatLon["long"].get<uint32_t>()*(double)1.0/LONGITUDE_RES; 
    hw_json["lat"] = position_LatLon["lat"].get<uint32_t>()*(double)1.0/LATITUDE_RES; 
   
    if(CHECK_JSON_KEY(gv_json,"offsetV"))
    {
        auto & offsetV = gv_json["offsetV"]; 
        hw_json["ele"] = offsetV["elevation"]; 
    }
    return RET_OK;
}

int huawei_mec_protocol::Descript_hw2gv(const string & desc, nlohmann::json & gv_json)
{
    gv_json["choiceID"] = 2; 
    gv_json["description"] = desc; 
    return RET_OK;
}

int huawei_mec_protocol::Descript_gv2hw(nlohmann::json & gv_json, string & hw_desc )
{
    hw_desc =  gv_json["description"]; 
    return RET_OK;
}

int huawei_mec_protocol::TimeDetail_hw2gv(nlohmann::json & hw_json,nlohmann::json & gv_json)
{
    if(CHECK_JSON_KEY(hw_json,"startTime"))
    {
        gv_json["startTime"] =  hw_json["startTime"].get<int>(); 
    }
    if(CHECK_JSON_KEY(hw_json,"endTime"))
    {
        gv_json["endTime"] =  hw_json["endTime"].get<int>(); 
    }
    return RET_OK;
}

int huawei_mec_protocol::TimeDetail_gv2hw(nlohmann::json & gv_json,nlohmann::json & hw_json)
{
    if(CHECK_JSON_KEY(gv_json,"startTime"))
    {
        hw_json["startTime"] =  gv_json["startTime"].get<int>(); 
    }
    if(CHECK_JSON_KEY(gv_json,"endTime"))
    {
        hw_json["endTime"] =  gv_json["endTime"].get<int>(); 
    }  
    return RET_OK; 
}

int huawei_mec_protocol::referencePath_hw2gv(nlohmann::json & hw_json,nlohmann::json & gv_json)
{
    gv_json["pathRadius"] = hw_json["pathRadius"].get<int>(); 
    arrays_convert(hw_json["activePath"],gv_json["activePath"],&huawei_mec_protocol::posoffset_hw2gv);   
    return RET_OK;
}

int huawei_mec_protocol::referencePath_gv2hw(nlohmann::json & gv_json,nlohmann::json & hw_json)
{
#if 0
///////////////////////////////////
// gv 
"referencePaths": [{
    "activePath": [{
        "offsetLL": {
            "choiceID": 7,
            "position_LatLon": {
                "long": 1133902330,
                "lat": 231592220
            }
        }
    }, {
        "offsetLL": {
            "choiceID": 7,
            "position_LatLon": {
                "long": 1134102330,
                "lat": 231792220
            }
        }
    }],
    "pathRadius": 10000

///////////////////////////////////
// hw
"referencePaths": [
    {
    "activePath": [
        {
            "lon": 108,
            "lat": 34
        },
        {
            "lon": 108.001,
            "lat": 34.0002
        }
    ],
    "pathRadius": 1024
    }
    ]
#endif 
    arrays_convert(gv_json["activePath"],hw_json["activePath"],&huawei_mec_protocol::posoffset_gv2hw); 
    hw_json["pathRadius"] = gv_json["pathRadius"].get<int>(); 
    return RET_OK;
}



int huawei_mec_protocol::referenceLink_hw2gv(nlohmann::json & hw_json,nlohmann::json & gv_json)
{
    gv_json["upstreamNodeId"] = hw_json["upStreamNodeId"]; 
    gv_json["downstreamNodeId"] = hw_json["downStreamNodeId"]; 

    if(CHECK_JSON_KEY(hw_json,"referenceLane"))
    {
        ref_lane_hw2gv(hw_json["referenceLane"],gv_json["referenceLanes"]) ; 
    }
    return RET_OK;
}

int huawei_mec_protocol::referenceLink_gv2hw(nlohmann::json & gv_json,nlohmann::json & hw_json)
{
    hw_json["upstreamNodeId"] = gv_json["upstreamNodeId"];
    hw_json["downStreamNodeId"] = gv_json["downStreamNodeId"];
    if(CHECK_JSON_KEY(gv_json,"referenceLanes"))
    {
        ref_lane_gv2hw(gv_json["referenceLanes"],hw_json["referenceLane"]) ;
    }
    return RET_OK;
}



int huawei_mec_protocol::ref_lane_hw2gv(nlohmann::json & hw_json,nlohmann::json & gv_json)
{
    if(CHECK_JSON_KEY(hw_json,"reserve0"))
    {
        gv_json["reserved"] =  hw_json["reserve0"]; 
    }
    char key[12]={0}; 
    for(int i = 1; i <=15; ++i)
    {
        sprintf(key,"lane%d",i); 
        if(CHECK_JSON_KEY(hw_json,key))
        {
            gv_json[key] = hw_json[key];
        }
    }
    return RET_OK;
}
int huawei_mec_protocol::ref_lane_gv2hw(nlohmann::json & gv_json,nlohmann::json & hw_json)
{
    hw_json["reserve0"] = gv_json["reserved"]; 
    char key[12]={0}; 
    for(int i = 1; i <=15; ++i)
    {
        sprintf(key,"lane%d",i); 
        hw_json[key] = gv_json[key];
  
    }
    return RET_OK;
}


int huawei_mec_protocol::participant_hw2gv(nlohmann::json & hw_json,nlohmann::json & gv_json)
{
#if 0 
{
	"msgCnt": 88,
	"id": "GJMBqX4Q",
	"refPos": {
		"lat": 0,
		"long": 0
	},
	"participants": [{
		"ptcType": 1,
		"ptcId": 51,
		"source": 7,
		"secMark": 42320,
		"pos": {
			"offsetLL": {
				"choiceID": 7,
				"position_LatLon": {
					"long": 1131520807,
					"lat": 229875548
				}
			}
		},
		"posConfidence": {
			"pos": 0
		},
		"speed": 0,
		"heading": 8155,
		"angle": 0,
		"size": {
			"width": 100,
			"length": 400,
			"height": 20
		}
	}]
}

///  hv 

{
    "name": null,
    "id": "1234546789",
    "content": {
        "rsms": [
            {
                "refPos": {
                    "lon": 111,
                    "lat": 11,
                    "ele": 23
                },
                "participants": [
                    {
                        "ptcType": 4,
                        "ptcId": 65535,
                        "source": 7,
                        "secMark": 59999,
                        "pos": {
                            "lon": 111,
                            "lat": 11,
                            "ele": 55
                        },
                        "accuracy": "15",
                        "speed": 8191,
                        "heading": 28800,
                        "size": {
                            "width": 1023,
                            "length": 4095,
                            "height": 127
                        }
                    }
                ]
            }
        ]
    }
}

#endif 
    gv_json["ptcType"] =  hw_json["ptcType"];
    gv_json["ptcId"] = hw_json["ptcId"];
    gv_json["source"] = hw_json["source"];
    if(CHECK_JSON_KEY(hw_json,"secMark"))
    {
        gv_json["secMark"] = hw_json["secMark"];
    }

    posoffset_hw2gv(hw_json["pos"],gv_json["pos"]); 

    auto posConfidence =  nlohmann::json{
            {"pos",0}
    }; 

    if(CHECK_JSON_KEY(hw_json,"accuracy"))
    {
        int posConfidence_warning ; ///< 华为协议说明为  如果没有该字段，空口填unavailable(0)
        string accuracy =  hw_json["accuracy"].get<string>(); 
        posConfidence["pos"] =  std::stoi(accuracy); 
    }
    gv_json["posConfidence"] = posConfidence;

    gv_json["speed"] = hw_json["speed"]; 
    gv_json["heading"] = hw_json["heading"];
    gv_json["size"] = hw_json["size"];
    
    return RET_OK;
}


int huawei_mec_protocol::participant_gv2hw(nlohmann::json & gv_json,nlohmann::json & hw_json)
{
    hw_json["ptcType"] =  gv_json["ptcType"]; 
    hw_json["ptcId"] = gv_json["ptcId"];
    hw_json["source"] = gv_json["source"];
    if(CHECK_JSON_KEY(gv_json,"secMark"))
    {
        hw_json["secMark"] = gv_json["secMark"];
    }

    posoffset_gv2hw(gv_json["pos"],hw_json["pos"]); 

    int accuracy_waing ; ///< 华为协议说明为  如果没有该字段，空口填unavailable(0)
    //hw_json["accuracy"] = 0;

    hw_json["speed"] = gv_json["speed"]; 
    hw_json["heading"] = gv_json["heading"];
    hw_json["size"] = gv_json["size"];


    return RET_OK;
}

int huawei_mec_protocol::node_hw2gv(nlohmann::json & hw_json,nlohmann::json & gv_json)
{
    if(CHECK_JSON_KEY(hw_json,"name"))
    {
        gv_json["name"] =  hw_json["name"];
    }

    gv_json["id"] = hw_json["id"];

    pos_hw2gv(hw_json["refPos"],gv_json["refPos"] );

    if(CHECK_JSON_KEY(hw_json,"inLinks"))
    {
        arrays_convert(hw_json["inLinks"],gv_json["inLinks"],&huawei_mec_protocol::link_hw2gv); 
    }

    return RET_OK;
}


int huawei_mec_protocol::node_gv2hw(nlohmann::json & gv_json,nlohmann::json & hw_json)
{
    if(CHECK_JSON_KEY(gv_json,"name"))
    {
        hw_json["name"] =  gv_json["name"];
    }

    hw_json["id"] = gv_json["id"];

    pos_gv2hw(gv_json["refPos"],hw_json["refPos"] );

    if(CHECK_JSON_KEY(gv_json,"inLinks"))
    {
        arrays_convert(gv_json["inLinks"],hw_json["inLinks"],&huawei_mec_protocol::link_gv2hw); 
    }   
    return RET_OK;
}

int huawei_mec_protocol::link_hw2gv(nlohmann::json & hw_json,nlohmann::json & gv_json)
{
    if(CHECK_JSON_KEY(hw_json,"name"))
    {
        gv_json["name"] = hw_json["name"];
    }
    gv_json["upstreamNodeId"] =  hw_json["upstreamNodeId"];

    if(CHECK_JSON_KEY(hw_json,"speedLimits"))
    {
        arrays_convert(hw_json["speedLimits"],gv_json["speedLimits"],&huawei_mec_protocol::speedlimit_hw2gv );  
    }


    gv_json["linkWidth"] =  hw_json["linkWidth"];

    if(CHECK_JSON_KEY(hw_json,"points"))
    {
        arrays_convert(hw_json["points"],gv_json["points"],&huawei_mec_protocol::point_hw2gv); 
    }

    if(CHECK_JSON_KEY(hw_json,"movements"))
    {
        gv_json["movements"] = hw_json["movements"];
    }
    arrays_convert(hw_json["lanes"],gv_json["lanes"],&huawei_mec_protocol::lane_hw2gv); 
    return RET_OK;
}

int huawei_mec_protocol::link_gv2hw(nlohmann::json & gv_json,nlohmann::json & hw_json)
{

    if(CHECK_JSON_KEY(gv_json,"name"))
    {
        hw_json["name"] = gv_json["name"];
    }
    hw_json["upstreamNodeId"] =  gv_json["upstreamNodeId"];

    if(CHECK_JSON_KEY(gv_json,"speedLimits"))
    {
        arrays_convert(gv_json["speedLimits"],hw_json["speedLimits"],&huawei_mec_protocol::speedlimit_gv2hw ); 
    }


    hw_json["linkWidth"] =  gv_json["linkWidth"];

    if(CHECK_JSON_KEY(gv_json,"points"))
    {
        arrays_convert(gv_json["points"],hw_json["points"] ,&huawei_mec_protocol::point_gv2hw); 
    }

    if(CHECK_JSON_KEY(gv_json,"movements"))
    {
        hw_json["movements"] = gv_json["movements"];
    }
    arrays_convert(gv_json["lanes"],hw_json["lanes"],&huawei_mec_protocol::lane_gv2hw); 
    return RET_OK;
}



int huawei_mec_protocol::speedlimit_hw2gv(nlohmann::json & hw_json,nlohmann::json & gv_json)
{
    gv_json["type"] =  ::stoi(hw_json["type"].get<string>()); 
    gv_json["speed"] = hw_json["speed"];
    return RET_OK; 
}

int huawei_mec_protocol::speedlimit_gv2hw(nlohmann::json & gv_json,nlohmann::json & hw_json)
{
    hw_json["type"] = std::to_string(gv_json["type"].get<int>()); 
    hw_json["speed"] = gv_json["speed"]; 
    return RET_OK; 
}

int huawei_mec_protocol::lane_hw2gv(nlohmann::json & hw_json,nlohmann::json & gv_json)
{
// gv 
#if 0
{
                            "laneID": 1,
                            "laneWidth": 450,
                            "laneAttributes": {
                                   "laneType": {
                                          "choiceID": 1,
                                          "vehicle": {
                                                 "isVehicleRevocableLane": false,
                                                 "isRampLane": false,
                                                 "hovLaneUseOnly": false,
                                                 "restrictedToBusUse": false,
                                                 "restrictedToTaxiUse": false,
                                                 "restrictedFromPublicUse": false,
                                                 "emergencyLane": true,
                                                 "permissionOnRequest": false
                                          }
                                   }
                            },
                            "maneuvers": {
                                   "maneuverStraightAllowed": true,
                                   "maneuverLeftAllowed": true,
                                   "maneuverRightAllowed": false,
                                   "maneuverUTurnAllowed": false,
                                   "maneuverLeftTurnOnRedAllowed": false,
                                   "maneuverRightTurnOnRedAllowed": false,
                                   "maneuverLaneChangeAllowed": false,
                                   "maneuverNoStoppingAllowed": false,
                                   "yieldAllwaysRequired": false,
                                   "goWithHalt": false,
                                   "caution": false,
                                   "AllowedManeuvers_reserved1": false
                            },
                            "connectsTo": [{
                                   "remoteIntersection": {
                                          "region": 21,
                                          "id": 17
                                   },
                                   "connectingLane": {
                                          "lane": 1,
                                          "maneuver": {
                                                 "maneuverStraightAllowed": true,
                                                 "maneuverLeftAllowed": false,
                                                 "maneuverRightAllowed": false,
                                                 "maneuverUTurnAllowed": false,
                                                 "maneuverLeftTurnOnRedAllowed": false,
                                                 "maneuverRightTurnOnRedAllowed": false,
                                                 "maneuverLaneChangeAllowed": false,
                                                 "maneuverNoStoppingAllowed": false,
                                                 "yieldAllwaysRequired": false,
                                                 "goWithHalt": false,
                                                 "caution": false,
                                                 "AllowedManeuvers_reserved1": false
                                          }
                                   },
                                   "phaseId": 6
                            }


//////////////////////////////////
// hw 

"lanes": [
    {
        "laneId": 1,
        "laneWidth": 0,
        "laneAttributes": {
            "laneType": "0",
            "shareWith": 0
        },
        "maneuvers": 0
    }
    ],

#endif 
    gv_json["laneID"] =hw_json["laneId"].get<int>(); 
    if(CHECK_JSON_KEY(hw_json,"laneWidth"))
    {
        gv_json["laneWidth"] = hw_json["laneWidth"];
    }
    if(CHECK_JSON_KEY(hw_json,"laneAttributes"))
    {
        lane_attributes_hw2gv(hw_json["laneAttributes"],gv_json["laneAttributes"]) ; 
    }
    if(CHECK_JSON_KEY(hw_json,"maneuvers"))
    {
        maneuver_hw2gv(hw_json["maneuvers"].get<uint16_t>(),gv_json["maneuvers"]); 
    }

    if(CHECK_JSON_KEY(hw_json,"connectsTo"))
    {
        arrays_convert(hw_json["connectsTo"],gv_json["connectsTo"],&huawei_mec_protocol::connection_hw2gv); 
    }
    if(CHECK_JSON_KEY(hw_json,"speedLimits"))
    {
        arrays_convert(hw_json["speedLimits"],gv_json["speedLimits"],&huawei_mec_protocol::speedlimit_hw2gv); 
    }

    if(CHECK_JSON_KEY(hw_json,"points"))
    {
        arrays_convert(hw_json["points"],gv_json["points"],&huawei_mec_protocol::point_hw2gv); 
    }
    return RET_OK;
}

int huawei_mec_protocol::lane_gv2hw(nlohmann::json & gv_json,nlohmann::json & hw_json)
{
    hw_json["laneId"] =gv_json["laneID"].get<int>(); 

    if(CHECK_JSON_KEY(gv_json,"laneWidth"))
    {
        hw_json["laneWidth"] = gv_json["laneWidth"];
    }
    if(CHECK_JSON_KEY(gv_json,"laneAttributes"))
    {
        lane_attributes_gv2hw(gv_json["laneAttributes"],hw_json["laneAttributes"]) ; 
    }
    if(CHECK_JSON_KEY(gv_json,"maneuvers"))
    {
        uint16_t maneuvers = 0; 
        maneuver_gv2hw(gv_json["maneuvers"],maneuvers); 
        hw_json["maneuvers"] = maneuvers;
    }

    if(CHECK_JSON_KEY(gv_json,"connectsTo"))
    {
        arrays_convert(gv_json["connectsTo"],hw_json["connectsTo"] ,&huawei_mec_protocol::connection_gv2hw); 
    }
    if(CHECK_JSON_KEY(gv_json,"speedLimits"))
    {
        arrays_convert(gv_json["speedLimits"],hw_json["speedLimits"],&huawei_mec_protocol::speedlimit_gv2hw); 
    }

    if(CHECK_JSON_KEY(gv_json,"points"))
    {
        arrays_convert(gv_json["points"],hw_json["points"] ,&huawei_mec_protocol::point_gv2hw); 
    }
    return RET_OK;
}


int huawei_mec_protocol::lane_attributes_hw2gv(nlohmann::json & hw_json,nlohmann::json & gv_json)
{

#if 0
//// gv
#define JSON_MAP_LANEATTRIBUTES "laneAttributes"
#define JSON_MAP_SHAREWITH "shareWith"
#define JSON_MAP_LANETYPE "laneType"
"laneAttributes": {
"laneType": {
        "choiceID": 1,
        "vehicle": {
                "isVehicleRevocableLane": false,
                "isRampLane": false,
                "hovLaneUseOnly": false,
                "restrictedToBusUse": false,
                "restrictedToTaxiUse": false,
                "restrictedFromPublicUse": false,
                "emergencyLane": true,
                "permissionOnRequest": false
        }
},
},

ret = decode_lane_attributes(sub_json, (char *)JSON_MAP_LANEATTRIBUTES, &value->laneAttributes, &value->laneAttributes_option, err_buf, err_size);
    ret = decode_lane_sharing(sub_json, (char *)JSON_MAP_SHAREWITH, &value->shareWith, &value->shareWith_option, err_buf, err_size);
    CHECK_ASN_JSON_RETVALUE(ret);
    ret = decode_lane_type_attributes(sub_json, (char *)JSON_MAP_LANETYPE, &value->laneType, nullptr, err_buf, err_size);
    CHECK_ASN_JSON_RETVALUE(ret);

//// hw 

"laneAttributes": {
    "laneType": {
            "choiceID": 1,
            "vehicle": {
                    "isVehicleRevocableLane": false,
                    "isRampLane": false,
                    "hovLaneUseOnly": false,
                    "restrictedToBusUse": false,
                    "restrictedToTaxiUse": false,
                    "restrictedFromPublicUse": false,
                    "emergencyLane": true,
                    "permissionOnRequest": false
            }
}
    "shareWith": 0
},
                                  
#endif 
    if(CHECK_JSON_KEY(hw_json,"shareWith"))
    {
        shareWith_hw2gv(hw_json["shareWith"].get<uint16_t>(),gv_json["shareWith"] ); 
    }
    gv_json["laneType"] = hw_json["laneType"];
    return RET_OK;
}

int huawei_mec_protocol::lane_attributes_gv2hw(nlohmann::json & gv_json,nlohmann::json & hw_json)
{
    if(CHECK_JSON_KEY(gv_json,"shareWith"))
    {
        uint16_t shareWith = 0; 
        shareWith_gv2hw(gv_json["shareWith"],shareWith); 
        hw_json["shareWith"] = shareWith; 
    }
    hw_json["laneType"] = gv_json["laneType"];
    return RET_OK;
}


int huawei_mec_protocol::shareWith_hw2gv(uint16_t shareWith,nlohmann::json & gv_json)
{
    gv_json["overlappingLaneDescriptionProvided"] = CHECK_BIT_STRING_BOOL(shareWith,10,0); 
    gv_json["multipleLanesTreatedAsOneLane"] = CHECK_BIT_STRING_BOOL(shareWith,10,1); 
    gv_json["otherNonMotorizedTrafficTypes"] = CHECK_BIT_STRING_BOOL(shareWith,10,2); 
    gv_json["individualMotorizedVehicleTraffic"] = CHECK_BIT_STRING_BOOL(shareWith,10,3); 
    gv_json["busVehicleTraffic"] = CHECK_BIT_STRING_BOOL(shareWith,10,4); 
    gv_json["taxiVehicleTraffic"] = CHECK_BIT_STRING_BOOL(shareWith,10,5); 
    gv_json["pedestriansTraffic"] = CHECK_BIT_STRING_BOOL(shareWith,10,6); 
    gv_json["cyclistVehicleTraffic"] = CHECK_BIT_STRING_BOOL(shareWith,10,7); 
    gv_json["trackedVehicleTraffic"] = CHECK_BIT_STRING_BOOL(shareWith,10,8); 
    gv_json["pedestrianTraffic"] = CHECK_BIT_STRING_BOOL(shareWith,10,9); 
    return RET_OK;
}



int huawei_mec_protocol::shareWith_gv2hw(nlohmann::json & gv_json,uint16_t &shareWith)
{
    shareWith = 0; 
    CHECK_JSON_BIT_STRING_SET(gv_json,"overlappingLaneDescriptionProvided",shareWith,10,0); 
    CHECK_JSON_BIT_STRING_SET(gv_json,"multipleLanesTreatedAsOneLane",shareWith,10,1); 
    CHECK_JSON_BIT_STRING_SET(gv_json,"otherNonMotorizedTrafficTypes",shareWith,10,2);
    CHECK_JSON_BIT_STRING_SET(gv_json,"individualMotorizedVehicleTraffic",shareWith,10,3);
    CHECK_JSON_BIT_STRING_SET(gv_json,"busVehicleTraffic",shareWith,10,4);
    CHECK_JSON_BIT_STRING_SET(gv_json,"taxiVehicleTraffic",shareWith,10,5);
    CHECK_JSON_BIT_STRING_SET(gv_json,"pedestriansTraffic",shareWith,10,6);
    CHECK_JSON_BIT_STRING_SET(gv_json,"cyclistVehicleTraffic",shareWith,10,7);
    CHECK_JSON_BIT_STRING_SET(gv_json,"trackedVehicleTraffic",shareWith,10,8);
    CHECK_JSON_BIT_STRING_SET(gv_json,"pedestrianTraffic",shareWith,10,9);
    return RET_OK;
}


int huawei_mec_protocol::maneuver_hw2gv(uint16_t hw_maneuver, nlohmann::json & gv_json)
{

    gv_json["maneuverStraightAllowed"] = CHECK_BIT_STRING_BOOL(hw_maneuver,12,0); 
    gv_json["maneuverLeftAllowed"] = CHECK_BIT_STRING_BOOL(hw_maneuver,12,1); 
    gv_json["maneuverRightAllowed"] = CHECK_BIT_STRING_BOOL(hw_maneuver,12,2); 
    gv_json["maneuverUTurnAllowed"] = CHECK_BIT_STRING_BOOL(hw_maneuver,12,3); 
    gv_json["maneuverLeftTurnOnRedAllowed"] = CHECK_BIT_STRING_BOOL(hw_maneuver,12,4); 
    gv_json["maneuverRightTurnOnRedAllowed"] = CHECK_BIT_STRING_BOOL(hw_maneuver,12,5); 
    gv_json["maneuverLaneChangeAllowed"] = CHECK_BIT_STRING_BOOL(hw_maneuver,12,6); 
    gv_json["maneuverNoStoppingAllowed"] = CHECK_BIT_STRING_BOOL(hw_maneuver,12,7); 
    gv_json["yieldAllwaysRequired"] = CHECK_BIT_STRING_BOOL(hw_maneuver,12,8); 
    gv_json["goWithHalt"] = CHECK_BIT_STRING_BOOL(hw_maneuver,12,9); 
    gv_json["caution"] = CHECK_BIT_STRING_BOOL(hw_maneuver,12,10); 
    gv_json["AllowedManeuvers_reserved1"] = CHECK_BIT_STRING_BOOL(hw_maneuver,12,11); 
    return RET_OK;
}

int huawei_mec_protocol::maneuver_gv2hw( nlohmann::json & gv_json,uint16_t &hw_maneuver)
{
    hw_maneuver = 0; 
    CHECK_JSON_BIT_STRING_SET(gv_json,"maneuverStraightAllowed",hw_maneuver,12,0); 
    CHECK_JSON_BIT_STRING_SET(gv_json,"maneuverLeftAllowed",hw_maneuver,12,1);
    CHECK_JSON_BIT_STRING_SET(gv_json,"maneuverRightAllowed",hw_maneuver,12,2); 
    CHECK_JSON_BIT_STRING_SET(gv_json,"maneuverUTurnAllowed",hw_maneuver,12,3);
    CHECK_JSON_BIT_STRING_SET(gv_json,"maneuverLeftTurnOnRedAllowed",hw_maneuver,12,4); 
    CHECK_JSON_BIT_STRING_SET(gv_json,"maneuverRightTurnOnRedAllowed",hw_maneuver,12,5);
    CHECK_JSON_BIT_STRING_SET(gv_json,"maneuverLaneChangeAllowed",hw_maneuver,12,6); 
    CHECK_JSON_BIT_STRING_SET(gv_json,"maneuverNoStoppingAllowed",hw_maneuver,12,7);
    CHECK_JSON_BIT_STRING_SET(gv_json,"yieldAllwaysRequired",hw_maneuver,12,8); 
    CHECK_JSON_BIT_STRING_SET(gv_json,"goWithHalt",hw_maneuver,12,9);
    CHECK_JSON_BIT_STRING_SET(gv_json,"caution",hw_maneuver,12,10); 
    CHECK_JSON_BIT_STRING_SET(gv_json,"AllowedManeuvers_reserved1",hw_maneuver,12,11);

    return RET_OK;
}


int huawei_mec_protocol::connection_hw2gv(nlohmann::json & hw_json, nlohmann::json & gv_json)
{
#if 0
//////////////////////////////////////////////
// gv 

"connectsTo": [{
"remoteIntersection": {
    "region": 21,
    "id": 10
},
"connectingLane": {
    "lane": 2,
    "maneuver": {
            "maneuverStraightAllowed": true,
            "maneuverLeftAllowed": false,
            "maneuverRightAllowed": false,
            "maneuverUTurnAllowed": false,
            "maneuverLeftTurnOnRedAllowed": false,
            "maneuverRightTurnOnRedAllowed": false,
            "maneuverLaneChangeAllowed": false,
            "maneuverNoStoppingAllowed": false,
            "yieldAllwaysRequired": false,
            "goWithHalt": false,
            "caution": false,
            "AllowedManeuvers_reserved1": false
    }
},
"phaseId": 14
}
//////////////////////////////////////////////
// hw 

"connectsTo": [
{
    "remoteIntersection": {
        "region": 0,
        "id": 2
    },
    "connectingLane": {
        "laneId": 2
    },
    "phaseId": 1
}
],
"points": [
{
    "lon": 123.45152,
    "lat": 41.69912
},
{
    "lon": 123.451756,
    "lat": 41.698559
}
]


#endif 
    gv_json["remoteIntersection"] = hw_json["remoteIntersection"]; 
    if(CHECK_JSON_KEY(hw_json,"connectingLane"))
    {
        connectingLane_hw2gv(hw_json["connectingLane"],gv_json["connectingLane"]); 
    }

    if(CHECK_JSON_KEY(hw_json,"phaseId"))
    {
        gv_json["phaseId"] =  hw_json["phaseId"];
    }
    return RET_OK;
}

int huawei_mec_protocol::connection_gv2hw(nlohmann::json & gv_json,nlohmann::json & hw_json)
{
    hw_json["remoteIntersection"] = gv_json["remoteIntersection"]; 
    if(CHECK_JSON_KEY(gv_json,"connectingLane"))
    {
        connectingLane_gv2hw(gv_json["connectingLane"],hw_json["connectingLane"]); 
    }

    if(CHECK_JSON_KEY(gv_json,"phaseId"))
    {
        hw_json["phaseId"] =  gv_json["phaseId"];
    }

    return RET_OK;
}

int huawei_mec_protocol::connectingLane_hw2gv(nlohmann::json & hw_json,nlohmann::json & gv_json)
{
#if 0
////////////////////////////////////////////////
// gv 
"connectingLane": {
        "lane": 2,
        "maneuver": {
            "maneuverStraightAllowed": true,
            "maneuverLeftAllowed": false,
            "maneuverRightAllowed": false,
            "maneuverUTurnAllowed": false,
            "maneuverLeftTurnOnRedAllowed": false,
            "maneuverRightTurnOnRedAllowed": false,
            "maneuverLaneChangeAllowed": false,
            "maneuverNoStoppingAllowed": false,
            "yieldAllwaysRequired": false,
            "goWithHalt": false,
            "caution": false,
            "AllowedManeuvers_reserved1": false
        }
    },

////////////////////////////////////////////////
// hw
"connectingLane": {
    "laneId": 2
},
#endif 

    gv_json["lane"] =  hw_json["laneId"];
    if(CHECK_JSON_KEY(hw_json,"maneuvers"))
    {
        auto maneuvers =  nlohmann::json(); 
        maneuver_hw2gv(hw_json["maneuvers"].get<uint16_t>(),gv_json["maneuver"] );
    }
    return RET_OK;
}

int huawei_mec_protocol::connectingLane_gv2hw(nlohmann::json & gv_json,nlohmann::json & hw_json)
{
    hw_json["laneId"] =  gv_json["lane"];
    if(CHECK_JSON_KEY(gv_json,"maneuvers"))
    {
        uint16_t maneuvers = 0; 
        maneuver_gv2hw(gv_json["maneuvers"],maneuvers);
        hw_json["maneuver"] = maneuvers; 
    }
    return RET_OK;  
}

int huawei_mec_protocol::intersection_hw2gv(nlohmann::json & hw_json,nlohmann::json & gv_json)
{
 
    gv_json["intersectionId"] = hw_json["intersectionId"];
    intersection_status_hw2gv(hw_json["status"].get<uint16_t>(),gv_json["status"] ); 

    arrays_convert(hw_json["phases"],gv_json["phases"],&huawei_mec_protocol::phase_hw2gv); 
    return RET_OK;
}

int huawei_mec_protocol::intersection_gv2hw(nlohmann::json & gv_json,nlohmann::json & hw_json)
{
    hw_json["intersectionId"] = gv_json["intersectionId"];
    uint16_t status = 0; 
    intersection_status_gv2hw(gv_json["status"],status); 
    hw_json["status"] = status; 

    arrays_convert(gv_json["phases"],hw_json["phases"],&huawei_mec_protocol::phase_gv2hw); 
    return RET_OK;
}

int huawei_mec_protocol::phase_hw2gv(nlohmann::json & hw_json,nlohmann::json & gv_json)
{
    gv_json["id"] = hw_json["phaseId"]; 
    arrays_convert(hw_json["phaseStates"],gv_json["phaseStates"],&huawei_mec_protocol::phase_state_hw2gv); 
    return RET_OK;
}


int huawei_mec_protocol::phase_gv2hw(nlohmann::json & gv_json,nlohmann::json & hw_json)
{
    hw_json["phaseId"] = gv_json["id"] ;
    arrays_convert(gv_json["phaseStates"],hw_json["phaseStates"], &huawei_mec_protocol::phase_state_gv2hw ); 
    return RET_OK;    
}

int huawei_mec_protocol::phase_state_hw2gv(nlohmann::json & hw_json,nlohmann::json & gv_json)
{
    gv_json["light"] =   std::stoi(hw_json["light"].get<string>()); 

    auto & hw_counting =  hw_json["timing"]["counting"]; 
    auto timing = nlohmann::json{
        {"choiceID",1},
        {"counting",{
            {"startTime",hw_counting["startTime"]["timeMark"].get<int>()},
            {"minEndTime",hw_counting["minEndTime"]["timeMark"].get<int>()},
            {"maxEndTime",hw_counting["maxEndTime"]["timeMark"].get<int>()},
            {"likelyEndTime",hw_counting["likelyEndTime"]["timeMark"].get<int>()},
            {"timeConfidence",hw_counting["timeConfidence"].get<int>()},
            {"nextStartTime",hw_counting["nextStartTime"]["timeMark"].get<int>()},
            {"nextDuration",hw_counting["nextDuration"]["timeMark"].get<int>()}
        }} 
    }; 
    gv_json["timing"] =  timing; 
    return RET_OK;
}

int huawei_mec_protocol::phase_state_gv2hw(nlohmann::json & gv_json,nlohmann::json & hw_json)
{
    hw_json["light"] = std::to_string(gv_json["light"].get<int>());
    auto &gv_counting = gv_json["timing"]["counting"]; 
    auto timing  = nlohmann::json{
        {"counting",{
            {"startTime",{{"timeMark",gv_counting["startTime"]}}},
            {"minEndTime",{{"timeMark",gv_counting["minEndTime"]}}},
            {"maxEndTime",{{"timeMark",gv_counting["maxEndTime"]}}},
            {"likelyEndTime",{{"timeMark",gv_counting["likelyEndTime"]}}},
            {"timeConfidence",{{"timeMark",gv_counting["timeConfidence"]}}},
            {"nextStartTime",{{"timeMark",gv_counting["nextStartTime"]}}},
            {"nextDuration",{{"timeMark",gv_counting["nextDuration"]}}}
        }}
    }; 
    hw_json["timing"] =  timing; 
    return RET_OK;
}


int huawei_mec_protocol::intersection_status_hw2gv(uint16_t status, nlohmann::json & gv_json)
{
    gv_json["manualControlIsEnabled"] = CHECK_BIT_STRING_BOOL(status,16,0); 
    gv_json["stopTimeIsActivated"] = CHECK_BIT_STRING_BOOL(status,16,1); 
    gv_json["failureFlash"] = CHECK_BIT_STRING_BOOL(status,16,2); 
    gv_json["preemptIsActive"] = CHECK_BIT_STRING_BOOL(status,16,3); 
    gv_json["signalPriorityIsActive"] = CHECK_BIT_STRING_BOOL(status,16,4); 
    gv_json["fixedTimeOperation"] = CHECK_BIT_STRING_BOOL(status,16,5); 
    gv_json["trafficDependentOperation"] = CHECK_BIT_STRING_BOOL(status,16,6); 
    gv_json["standbyOperation"] = CHECK_BIT_STRING_BOOL(status,16,7); 
    gv_json["failureMode"] = CHECK_BIT_STRING_BOOL(status,16,8); 
    gv_json["off"] = CHECK_BIT_STRING_BOOL(status,16,9); 
    gv_json["recentMAPmessageUpdate"] = CHECK_BIT_STRING_BOOL(status,16,10); 
    gv_json["recentChangeInMAPassignedLanesIDsUsed"] = CHECK_BIT_STRING_BOOL(status,16,11); 
    gv_json["noValidMAPisAvailableAtThisTime"] = CHECK_BIT_STRING_BOOL(status,16,12); 
    gv_json["noValidSPATisAvailableAtThisTime"] = CHECK_BIT_STRING_BOOL(status,16,13); 
    return RET_OK;
}

int huawei_mec_protocol::intersection_status_gv2hw( nlohmann::json & gv_json,uint16_t &status)
{
    status = 0; 
    CHECK_JSON_BIT_STRING_SET(gv_json,"manualControlIsEnabled",status,16,0); 
    CHECK_JSON_BIT_STRING_SET(gv_json,"stopTimeIsActivated",status,16,1); 
    CHECK_JSON_BIT_STRING_SET(gv_json,"failureFlash",status,16,2); 
    CHECK_JSON_BIT_STRING_SET(gv_json,"preemptIsActive",status,16,3); 
    CHECK_JSON_BIT_STRING_SET(gv_json,"signalPriorityIsActive",status,16,4); 
    CHECK_JSON_BIT_STRING_SET(gv_json,"fixedTimeOperation",status,16,5); 
    CHECK_JSON_BIT_STRING_SET(gv_json,"trafficDependentOperation",status,16,6); 
    CHECK_JSON_BIT_STRING_SET(gv_json,"standbyOperation",status,16,7); 
    CHECK_JSON_BIT_STRING_SET(gv_json,"failureMode",status,16,8); 
    CHECK_JSON_BIT_STRING_SET(gv_json,"off",status,16,9); 
    CHECK_JSON_BIT_STRING_SET(gv_json,"recentMAPmessageUpdate",status,16,10); 
    CHECK_JSON_BIT_STRING_SET(gv_json,"recentChangeInMAPassignedLanesIDsUsed",status,16,11); 
    CHECK_JSON_BIT_STRING_SET(gv_json,"noValidMAPisAvailableAtThisTime",status,16,12); 
    CHECK_JSON_BIT_STRING_SET(gv_json,"noValidSPATisAvailableAtThisTime",status,16,13); 

    return RET_OK;
}



int huawei_mec_protocol::posConfidence_hw2gv(nlohmann::json & hw_json,nlohmann::json & gv_json)
{
    gv_json["pos"] = hw_json["positionConfidence"]; 
    gv_json["elevation"] = hw_json["eleConfidence"]; 
    return RET_OK;
}
int huawei_mec_protocol::posConfidence_gv2hw(nlohmann::json & gv_json,nlohmann::json & hw_json)
{
    hw_json["positionConfidence"] = gv_json["pos"]; 
    hw_json["eleConfidence"] = gv_json["elevation"]; 
    return RET_OK;
}


int huawei_mec_protocol::motionConfidence_hw2gv(nlohmann::json & hw_json,nlohmann::json & gv_json)
{
#if 0
/// hw 
            "motionConfidence": {
                "speedConfidence": 6,
                "headingConfidence": 6,
                "steerConfidence": 3
            },
// gv         
#define JSON_SPEEDCFD 				("speedCfd")
#define JSON_HEADINGCFD 			("headingCfd")
#define JSON_STEERCFD 				("steerCfd")

ret = decode_motion_cfd(sub_json, "motionCfd", &value->motionCfd, &value->motionCfd_option, err_buf, err_size);

    ret = decode_get_int(sub_json, (char *)JSON_SPEEDCFD, (int *)&value->speedCfd, &value->speedCfd_option, err_buf, err_size);
    CHECK_ASN_JSON_RETVALUE(ret);
    ret = decode_get_int(sub_json, (char *)JSON_HEADINGCFD, (int *)&value->headingCfd, &value->headingCfd_option, err_buf, err_size);
    CHECK_ASN_JSON_RETVALUE(ret);
    ret = decode_get_int(sub_json, (char *)JSON_STEERCFD, (int *)&value->steerCfd, &value->steerCfd_option, err_buf, err_size);
    CHECK_ASN_JSON_RETVALUE(ret);
#endif 
    if(CHECK_JSON_KEY(hw_json,"speedConfidence"))
    {
        gv_json["speedCfd"] = hw_json["speedConfidence"]; 
    }
    if(CHECK_JSON_KEY(hw_json,"headingConfidence"))
    {
        gv_json["headingCfd"] = hw_json["headingConfidence"]; 
    }
    if(CHECK_JSON_KEY(hw_json,"steerConfidence"))
    {
        gv_json["steerCfd"]  = hw_json["steerConfidence"]; 
    }
    
    return RET_OK;
}

int huawei_mec_protocol::motionConfidence_gv2hw(nlohmann::json & gv_json,nlohmann::json & hw_json)
{
    if(CHECK_JSON_KEY(gv_json,"speedCfd"))
    {
        hw_json["speedConfidence"] = gv_json["speedCfd"]; 
    }
    if(CHECK_JSON_KEY(gv_json,"headingCfd"))
    {
        hw_json["headingConfidence"] = gv_json["headingCfd"]; 
    }
    if(CHECK_JSON_KEY(gv_json,"steerCfd"))
    {
        hw_json["steerConfidence"]  = gv_json["steerCfd"]; 
    }
    return RET_OK;
}


int huawei_mec_protocol::accelSet_hw2gv(nlohmann::json & hw_json,nlohmann::json & gv_json)
{
#if  0
// gv
	"accelSet": {
		"long": 0,
		"lat": 0,
		"vert": 0,
		"yaw": 0
	},
// hw 
    "accelSet": {
        "longAccel": -1000,
        "latAccel": 1000,
        "vertAccel": 108,
        "yawRate": 18888
    },

#endif
    gv_json["long"] =  hw_json["longAccel"]; 
    gv_json["lat"] =  hw_json["latAccel"]; 
    gv_json["vert"] =  hw_json["vertAccel"]; 
    gv_json["yaw"] =  hw_json["yawRate"]; 
    return RET_OK;
}
int huawei_mec_protocol::accelSet_gv2hw(nlohmann::json & gv_json,nlohmann::json & hw_json)
{
    hw_json["longAccel"] = gv_json["long"];
    hw_json["latAccel"] = gv_json["lat"];
    hw_json["vertAccel"] = gv_json["vert"];
    hw_json["yawRate"] = gv_json["yaw"];

    return RET_OK;
}

int huawei_mec_protocol::brakes_gv2hw(nlohmann::json & gv_json,nlohmann::json & hw_json)
{
#if 0
//gv 
    ret = decode_brakes(sub_json, "brakes", &value->brakes, nullptr, err_buf, err_size);
        // brakePadel 
        ret = decode_get_int(sub_json, "brakePadel", (int*)&value->brakePadel, &value->brakePadel_option, err_buf, err_size);
        CHECK_ASN_JSON_RETVALUE(ret);
        // wheelBrakes 
        ret = decode_wheel_brakes(sub_json, "wheelBrakes", &value->wheelBrakes, &value->wheelBrakes_option, err_buf, err_size);
        // traction 
        ret = decode_get_int(sub_json, "traction", (int*)&value->traction, &value->traction_option, err_buf, err_size);
        CHECK_ASN_JSON_RETVALUE(ret);
        // abs 
        ret = decode_get_int(sub_json, "abs", (int*)&value->abs, &value->abs_option, err_buf, err_size);
        CHECK_ASN_JSON_RETVALUE(ret);
        // scs 
        ret = decode_get_int(sub_json, "scs", (int*)&value->scs, &value->scs_option, err_buf, err_size);
        CHECK_ASN_JSON_RETVALUE(ret);
        // brakeBoost 
        ret = decode_get_int(sub_json, "brakeBoost", (int*)&value->brakeBoost, &value->brakeBoost_option, err_buf, err_size);
        CHECK_ASN_JSON_RETVALUE(ret);
        // auxBrakes 
        ret = decode_get_int(sub_json, "auxBrakes", (int*)&value->auxBrakes, &value->auxBrakes_option, err_buf, err_size);
        CHECK_ASN_JSON_RETVALUE(ret);
// hw 
            "brakes": {
                "brakePadelStatus": 1,
                "wheelBrakeStatus": {
                    "setStatus": false,
                    "leftFront": false,
                    "leftRear": true,
                    "rightFront": false,
                    "rightRear": true
                },
                "tractionStatus": 1,
                "absStatus": 1,
                "scsStatus": 1,
                "brakeBoostStatus": 1,
                "auxBrakesStatus": 1
            },
#endif
    if(CHECK_JSON_KEY(gv_json,"brakePadel"))
    {
       hw_json["brakePadelStatus"] = gv_json["brakePadel"]; 
    }
    if(CHECK_JSON_KEY(gv_json,"wheelBrakes"))
    {
        wheelBrakeStatus_gv2hw(gv_json["wheelBrakes"],hw_json["wheelBrakeStatus"]);
    }
    if(CHECK_JSON_KEY(gv_json,"traction"))
    {
        hw_json["tractionStatus"] = gv_json["traction"]; 
    }
    if(CHECK_JSON_KEY(gv_json,"abs"))
    {
        hw_json["absStatus"] = gv_json["abs"]; 
    }
    if(CHECK_JSON_KEY(gv_json,"scs"))
    {
        hw_json["scsStatus"] = gv_json["scs"]; 
    }
    if(CHECK_JSON_KEY(gv_json,"brakeBoost"))
    {
        hw_json["brakeBoostStatus"] = gv_json["brakeBoost"]; 
    }
    if(CHECK_JSON_KEY(gv_json,"auxBrakes"))
    {
        hw_json["auxBrakesStatus"] = gv_json["auxBrakes"]; 
    }

    return RET_OK;
}
int huawei_mec_protocol::brakes_hw2gv(nlohmann::json & hw_json,nlohmann::json & gv_json)
{
    if(CHECK_JSON_KEY(hw_json,"brakePadelStatus"))
    {
       gv_json["brakePadel"] = hw_json["brakePadelStatus"]; 
    }
    if(CHECK_JSON_KEY(hw_json,"wheelBrakeStatus"))
    {
        wheelBrakeStatus_hw2gv(hw_json["wheelBrakeStatus"],gv_json["wheelBrakes"]);
    }
    if(CHECK_JSON_KEY(hw_json,"tractionStatus"))
    {
        gv_json["traction"] = hw_json["tractionStatus"]; 
    }
    if(CHECK_JSON_KEY(hw_json,"absStatus"))
    {
        gv_json["abs"] = hw_json["absStatus"]; 
    }
    if(CHECK_JSON_KEY(hw_json,"scsStatus"))
    {
        gv_json["scs"] = hw_json["scsStatus"]; 
    }
    if(CHECK_JSON_KEY(hw_json,"brakeBoostStatus"))
    {
        gv_json["brakeBoost"] = hw_json["brakeBoostStatus"]; 
    }
    if(CHECK_JSON_KEY(hw_json,"auxBrakesStatus"))
    {
        gv_json["auxBrakes"] = hw_json["auxBrakesStatus"]; 
    }
    return RET_OK;
}


int huawei_mec_protocol::wheelBrakeStatus_gv2hw(nlohmann::json & gv_json,nlohmann::json & hw_json)
{
#if  0 
// gv 

ret = decode_wheel_brakes(sub_json, "wheelBrakes", &value->wheelBrakes, &value->wheelBrakes_option, err_buf, err_size);
CHECK_ASN_JSON_RETVALUE(ret);
        int inside_bits = 0;
        ret = decode_get_bit(sub_json, (char *)"unavailable", 0, &inside_bits, nullptr, err_buf, err_size);
        CHECK_ASN_JSON_RETVALUE(ret);
        ret = decode_get_bit(sub_json, (char *)"leftFront", 1, &inside_bits, nullptr, err_buf, err_size);
        CHECK_ASN_JSON_RETVALUE(ret);
        ret = decode_get_bit(sub_json, (char *)"leftRear", 2, &inside_bits, nullptr, err_buf, err_size);
        CHECK_ASN_JSON_RETVALUE(ret);
        ret = decode_get_bit(sub_json, (char *)"rightFront", 3, &inside_bits, nullptr, err_buf, err_size);
        CHECK_ASN_JSON_RETVALUE(ret);
        ret = decode_get_bit(sub_json, (char *)"rightRear", 4, &inside_bits, nullptr, err_buf, err_size);
// hw 
"wheelBrakeStatus": {
    "setStatus": false,
    "leftFront": false,
    "leftRear": true,
    "rightFront": false,
    "rightRear": true
},
#endif 

    hw_json["setStatus"] =  gv_json["unavailable"].get<bool>()?false:true;
    hw_json["leftFront"] =  gv_json["leftFront"];
    hw_json["leftRear"] =  gv_json["leftRear"];
    hw_json["rightFront"] =  gv_json["rightFront"];
    hw_json["rightRear"] =  gv_json["rightRear"];
    return RET_OK;
}


int huawei_mec_protocol::wheelBrakeStatus_hw2gv(nlohmann::json & hw_json,nlohmann::json & gv_json)
{
    
    gv_json["unavailable"] = hw_json["setStatus"].get<bool>()?false:true; 
    gv_json["leftFront"] = hw_json["leftFront"];
    gv_json["leftRear"] = hw_json["leftRear"];
    gv_json["rightFront"] = hw_json["rightFront"];
    gv_json["rightRear"] = hw_json["rightRear"];
    return RET_OK;
}


int huawei_mec_protocol::vehicleClass_gv2hw(nlohmann::json & gv_json,nlohmann::json & hw_json)
{
#if 0
// gv 
"vehicleClass": {
    "classification": 0
}
#define JSON_CLASSIFICATION 		("classification")
#define JSON_FUELTYPE 				("fuelType")

ret = decode_get_int(sub_json, (char *)JSON_CLASSIFICATION, &value->classification, NULL, err_buf, err_size);
CHECK_ASN_JSON_RETVALUE(ret);
ret = decode_get_int(sub_json, (char *)JSON_FUELTYPE, &value->fuelType, &value->fuelType_option, err_buf, err_size);
CHECK_ASN_JSON_RETVALUE(ret);

// hw 
"vehicleClass": {
    "basicVehicleClass": 188,
    "fuelType": 1
}
#endif 
    hw_json["basicVehicleClass"] = gv_json["classification"] ; 
    if(CHECK_JSON_KEY(gv_json,"fuelType"))
    {
        hw_json["fuelType"] = gv_json["fuelType"]; 
    }
    return RET_OK;
}

int huawei_mec_protocol::vehicleClass_hw2gv(nlohmann::json & hw_json,nlohmann::json & gv_json)
{
    gv_json["classification"] = hw_json["basicVehicleClass"];
    if(CHECK_JSON_KEY(hw_json,"fuelType"))
    {
        gv_json["fuelType"] = hw_json["fuelType"];    
    }
    return RET_OK;
}

int huawei_mec_protocol::rsi_priority_hw2gv(int hw_priority,string &gv_priority)
{
    // 按标准只取 3 位
    //hw_priority =  hw_priority & 7; 
    //hw_priority =  hw_priority << 5;
    //char c = hw_priority;
    //gv_priority.push_back(c);

    if(hw_priority>=10)
    {//rsi asn 编码目前只能编一位
        hw_priority = 7;
    }

    gv_priority =std::to_string(hw_priority); 
    return RET_OK;
}

int huawei_mec_protocol::rsi_priority_gv2hw(const string & gv_priority,int &hw_priority)
{

    try
    {
        hw_priority = std::stoi(gv_priority); 
    }
    catch(std::exception & e)
    {

        hw_priority = 7; 
    }
    return RET_OK;
}

int huawei_mec_protocol::arrays_convert(nlohmann::json & hw_json,nlohmann::json & gv_json,fun_type_hw2gv fun)
{
    for(auto & hw_json_item:hw_json)
    {
        auto gv_json_item =  nlohmann::json(); 
        (this->*fun)(hw_json_item,gv_json_item); 
        gv_json.emplace_back(std::move(gv_json_item)); 
    }
    return RET_OK;
}


