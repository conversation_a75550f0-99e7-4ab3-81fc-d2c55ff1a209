#include "tianan_http_client.h"
#include "service.h"
#include "stdafx.h"

tianan_http_client::tianan_http_client():m_status_control(status_enum::logout,"tianan_http_client",this)
{
    m_status_control.regist_status(status_enum::logout, &tianan_http_client::proc_status_logout, "logout", 0);
    m_status_control.regist_status(status_enum::login, &tianan_http_client::proc_status_login, "login", 0);
    m_status_control.regist_status(status_enum::login_send, &tianan_http_client::proc_status_login_send, "login_send", 10000);
    m_status_control.regist_status(status_enum::keep_alive, &tianan_http_client::proc_status_keep_alive, "keep_alive", 3600000); // 一小时执行一次此时， 

}

tianan_http_client::~tianan_http_client()
{
	destory();
}
	
int tianan_http_client::init()
{
    //向云平台申请ID
    string req_url;
    build_req_url(req_url);
    WLI("req_url %s",req_url.c_str()); 

    if(RET_OK != engine_ref.regist_http_client(this,req_url,m_client_handler))
    {
        WLE("engine_ref.regist_http_client error url %s ",req_url.c_str());
        return RET_FAIL;
    }
    http_req req; 
    // 配置 get head 
    req.heads.push_back("Accept: application/json") ; 
    req.heads.push_back("Content-Type: application/json") ; 
    req.heads.push_back("charsets: utf-8") ; 


    if(RET_OK != engine_ref.http_get(m_client_handler,req))
    {
        WLE("engine_ref.http_post error url %s ", req_url.c_str());
        return RET_FAIL; 
    }
    m_status_control.enter_status(status_enum::login_send);
    return RET_OK;
}

void tianan_http_client::build_req_url(string & url)
{
    string client_id ; 
    config_ref.get(CK_TIANAN_RSU_HTTP_CLIENT_ID,client_id); 
    auto data_j = nlohmann::json{
        {"client_id",client_id},
        {"device_type","rsu"},
        {"network","1"} 
    }; 
    auto data_v =  data_j.dump(); 
    auto pData = curl_easy_escape(NULL,data_v.c_str(),data_v.length()); 

    config_ref.get(CK_TIANAN_RSU_REGIST_URL,url); 
    url += "?action=TakePushServer&data="; 
    url += pData;
    curl_free(pData); 
}


int tianan_http_client::destory()
{
    engine_ref.unregist(this,m_client_handler);
    return RET_OK;
}
void tianan_http_client::ontime_check(long long cur_ms)
{
    m_status_control.call_status_fun(cur_ms);
}


void tianan_http_client::update_conn_handler(engine_conn_handler & new_client_handler)
{
    m_client_handler =  new_client_handler;
}

int tianan_http_client::get_conn_fd(void *conn_handler_data) 
{
    return m_client_handler.fd;
}

void tianan_http_client::read_cb(engine_conn_handler & handler, const char* msg, int len)
{
    fetch_mqtt_info(msg,len);
}

int tianan_http_client::fetch_mqtt_info( const char* msg, int len)
{
    try
    {
        auto j = json::parse(msg);
        string key = "X_RESULTCODE"; 
        if(j.end() == j.find(key))
        {
            WLE("json error  %s  has not key  %s ", msg,key.c_str());
            return RET_FAIL;
        }
        auto j_result_code =  j[key.c_str()];
        auto s_resut_code = j_result_code.get<string>();
        if(s_resut_code !="0")
        {
            WLE("service return error info  %s ", msg);
            return RET_FAIL; 
        }
        string mqtt_host = j["host"].get<string>();
        int mqtt_port = j["port"].get<int>();
        string devicesecret =  j["devicesecret"].get<string>(); 
        //WLI(" success mqtt_host %s mqtt_port %d ",mqtt_host.c_str(),mqtt_port);
        if(m_take_push_server_cb_fun)
        {
            m_take_push_server_cb_fun(mqtt_host,mqtt_port,devicesecret); 
        }

        m_status_control.enter_status(status_enum::login);
        return RET_OK;
    }
    catch(exception & e)
    {
        WLE("parse json error %s errdesc %s ", msg,e.what());
        return RET_FAIL;
    }
}

void tianan_http_client::event_cb(int fd, short events )
{
    WLD("fd %d evenets %d ",fd, events) ;
    if(events == BEV_EVENT_ERROR )
    {// http 需要处理
        m_client_handler.init();
    }
}


//=========================================================
// 状态处理函数

int tianan_http_client::proc_status_logout(status_info & status)
{
    destory();
    init();
    return RET_OK;
}

int tianan_http_client::proc_status_login_send(status_info & status)
{
    m_status_control.enter_status(status_enum::logout);
    return RET_OK;
}


// 成功获取云平台ID后断开连接，不再需要http连接
int tianan_http_client::proc_status_login(status_info & status)
{
    //destory();
    engine_ref.unregist(this,m_client_handler);
    m_status_control.enter_status(status_enum::keep_alive);
    return RET_OK;
}

// keep_alive 不做什么
int tianan_http_client::proc_status_keep_alive(status_info & status)
{
    m_status_control.enter_status(status_enum::keep_alive);
    return RET_OK;
}

