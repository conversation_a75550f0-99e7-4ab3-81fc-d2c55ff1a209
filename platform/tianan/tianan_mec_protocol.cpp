#include "tianan_mec_protocol.h"
#include "error_code.h"
#include "common_utility.h"
#include "MessageFrame.h"
#include "hex_str.h"
using namespace ENGINE_MODULE_NAMESPACE;


tianan_mec_protocol::tianan_mec_protocol()
{

}

tianan_mec_protocol::~tianan_mec_protocol()
{
	
}
	
int tianan_mec_protocol::rsi_ta2gv(const perception::PerceptionMsg & msg, string & gv_json)
{
    //auto rtes = nlohmann::json::array(); 

    perception::Position refPos ; 
    refPos.set_lon(0);
    refPos.set_lat(0); 
    refPos.set_elev(0); 
    
    auto &events_msg =  msg.event_msg();
    auto j  = nlohmann::json{
        {"msgCnt",get_v2x_msgcnt()},
        {"moy",v2x_get_MinuteOfTheYear(msg.time())},
        {"id",get_v2x_id()}
    }; 
    pos_ta2gv(refPos,j["refPos"]);

    if(events_msg.event_size()>0)
    {
        array_ta2gv(events_msg.event(),j["rtes"],std::bind(&tianan_mec_protocol::rsi_rte_ta2gv,FUN_BIND_2)); 
    }
    gv_json = j.dump();
    auto j_ret = nlohmann::json{
        {"type","rsi"},
        {"value", gv_json}
    }; 
    gv_json =  j_ret.dump();
    return RET_OK;
}

int tianan_mec_protocol::rsm_ta2gv(const perception::PerceptionMsg & msg, string & gv_json)
{
#if 0
    auto j  =nlohmann::json{
        {"msgCnt",0},
        {"id",get_id()},
        {"refPos",test_build_json::get_Position3D(231692256,1133998326,677)},
        {"participants",participants}
    };

// 目标类型                                     
    message Targets{    
    string                  device_sn       = 1;    // 设备序列号,用于标识数据来源,来自哪种传感器设备
    string                  device_ip       = 2;    // 设备编号,对于设备序列号相同的设备,通过设备IP进行区分    
    int32                   fusion_state    = 3;    // 1 代表融合输出,2 代表单一传感器输出                                      
    repeated Target         array           = 4;    // 融合目标列表        
}  
#endif 
    perception::Position refPos ; 
    refPos.set_lon(0);
    refPos.set_lat(0); 
    refPos.set_elev(0); 
    
    auto &target_msg =  msg.target_msg();

    auto j  = nlohmann::json{
        {"msgCnt",get_v2x_msgcnt()},
        {"id",get_v2x_id()}
    }; 
    pos_ta2gv(refPos,j["refPos"]);

    if(target_msg.array_size() > 0)
    {
        array_ta2gv(target_msg.array(),j["participants"],std::bind(&tianan_mec_protocol::participant_ta2gv,FUN_BIND_2)); 
    }
    gv_json =  j.dump();
    
    auto j_ret = nlohmann::json{
        {"type","rsm"},
        {"value", gv_json}
    }; 
    gv_json =  j_ret.dump();

    return RET_OK;
}



int tianan_mec_protocol::pos_ta2gv(const perception::Position & pos,nlohmann::json &gv_json)
{
    gv_json["lat"] = pos.lat(); 
    gv_json["long"] = pos.lon(); 
    gv_json["elevation"] = pos.elev(); 
    return RET_OK;
}
int tianan_mec_protocol::posoffset_ta2gv(const perception::Position  & pos,nlohmann::json &gv_json)
{
    gv_json["offsetLL"] =  nlohmann::json{
        {"choiceID",7},
        {"position_LatLon",{
            {"lat",pos.lat()},
            {"long",pos.lon()}
        }}
    };
    gv_json["offsetV"] = nlohmann::json{
        {"choiceID",7},
        {"elevation",pos.elev() }
    }; 
    return RET_OK;
}

int tianan_mec_protocol::descript_ta2gv(const string &desc, nlohmann::json & gv_json)
{
    gv_json["choiceID"] = 2; // 1  ascii ; 2 gb2312
    gv_json["description"] =  desc; 
    return RET_OK;
}

int tianan_mec_protocol::rsi_rte_ta2gv(const perception::Event & event, nlohmann::json *gv_json_par)
{
#if 0
        auto  ret = nlohmann::json{
            {"rteId",1},
            {"eventType",2},
            {"eventSource",3},
            {"eventPos",get_PositionOffsetLLV(231692256,1133998326,123)},
            {"eventRadius",1},
            {"description",get_Description("rte_test")},
            {"timeDetails",get_RSITimeDetails()},
            {"priority","7"},
            {"referencePaths",referencePaths},
            {"referenceLinks",referenceLinks},
            {"eventConfidence",1},
            {"duration",1},
            {"alertStatus",1}
        }; 

// 一个事件
message Event {
    int32               event_type              = 1;    // 事件类型,十进制,高2位为主事件,低2位为子事件(参看事件字典)
    int32               level                   = 2;    // 事件等级
    string              device_sn               = 3;    // 检测的设备id
    int32               lane_id                 = 4;    // 车道号,0xff表示不在车道内
    int32               fusion_state            = 5;    // 1代表融合输出,2代表单一传感器输出
    int32               source                  = 6;    // 数据来源，bit0:毫米波雷达，bit1:摄像头，bit2:激光雷达，bit3:V2X，见perception_content.h 
    repeated Position   pos                     = 7;    // 区域类型或者点类型位置坐标
    string              license_plate           = 8;    // 车牌,造成事件的车牌
    int64               track_time              = 9;    // 事件起始时间
    int64               ttl                     = 10;   // time to live；预计事件消散的时间,单位ms
    repeated AreaInfo   refpath                 = 11;   // 触发路径,预留,可多条
    oneof DataType{
        string          string_data             = 12;   // 字符串附加属性,根据事件不同赋值
        int32           int_data                = 13;   // int型附加属性,根据事件不同赋值
    }
} 
#endif
    auto & gv_json =  *gv_json_par; 
    gv_json["duration"] = 0; 
    gv_json["alertStatus"] = 1; 
    gv_json["rteId"] =  get_v2x_rteid(event);
    gv_json["eventType"] = event.event_type();
    gv_json["eventSource"] =  EventSource::EventSource_detection;  // 写死为检测到的来源
    if(event.pos_size()== 1)
    {// 只有一个点，则认为是事件点
        auto &first_pos = *event.pos().begin();
        posoffset_ta2gv(first_pos,gv_json["eventPos"]); 
    }
    if(event.pos_size() > 1 )
    {// 如果是多个点，刚认为是引用路径
        int warning_pos_size_more ; 
    }
   

    if(event.DataType_case() ==  perception::Event::kStringData)
    {
        descript_ta2gv(event.string_data(),gv_json["description"] );
    }
    get_rsitimedetails(event.track_time(),event.track_time()+event.ttl(),gv_json["timeDetails"]);
    

    //gv_json["eventRadius"] 无此对应项，且为可选项，不处理
    //gv_json["priority"] ; // 是否根据 evetn.level 来计算，如何计算
    //gv_json["referencePaths"]; 
    //gv_json["referenceLinks"]; 
    //gv_json["eventConfidence"] 无此对应项，且为可选项，不处理

    int warning_eventRadius_priority_referencePaths_referenceLinks; 

    return RET_OK;
}

int tianan_mec_protocol::get_participant_posconfidence(const perception::Target &target,nlohmann::json &gv_json)
{
    int warning_posconfidence; 
    gv_json["pos"] = PositionConfidence::PositionConfidence_unavailable ; 
    //gv_json["elevation"] = ;  // option 
    return RET_OK;

}
int tianan_mec_protocol::size_ta2gv(const perception::Size & size, nlohmann::json &gv_json)
{
    gv_json["width"] =  size.width(); 
    gv_json["length"] =  size.width(); 
    gv_json["height"] =  size.height(); 

    return RET_OK;
}

int tianan_mec_protocol::vehicleClass_ta2gv(int car_type,nlohmann::json &gv_json)
{
    gv_json["classification"] =  car_type; 
    //gv_json["fuelType"] ;  option 
    return RET_OK; 
}

int tianan_mec_protocol::participant_ta2gv(const perception::Target &target, nlohmann::json *gv_json_par)
{
#if 0
    auto  ret = nlohmann::json{
        {"ptcType",ptcType},
        {"ptcId",ptcId},
        {"source",1},
        {"id",id},
        {"secMark",v2x_get_DSecond()},
        {"pos",get_PositionOffsetLLV()},
        {"posConfidence",get_PositionConfidenceSet()},
        {"transmission",1},
        {"speed",2},
        {"heading",3},
        {"angle",4},
        {"motionCfd",get_MotionConfidenceSet(1,2,3)},
        {"accelSet",get_AccelerationSet4Way(1,2,3,4)},
        {"size",get_size(7,8,9)},
        {"vehicleClass",get_VehicleClassification(1,2)}
    }; 

    	ParticipantType ::= ENUMERATED {
		unknown (0), -- Unknown
		motor (1), -- motor
		non-motor (2), -- non-motor
		pedestrian (3), -- pedestrian
		rsu (4), -- rsu
		...
		}
        Unknown：0; Motor：1; non-motor：2; Pedestrian：3; rsu obstacle：4


int32          car_type               = 12;       // 未知：0 ; 乘用车：10; 轻卡：20;卡车：25; 摩托车 :40; 紧急车:60; 细分车辆类型参考asn标准定义 ******* 章节
#endif
    auto & gv_json =  *gv_json_par; 
    gv_json["ptcType"] = target.type(); 
    gv_json["ptcId"] = target.id()%65536 ; //ptcId INTEGER (0..65535),
    gv_json["source"] = target.type();
    gv_json["id"] = get_bsm_id(target.id());
    gv_json["secMark"] =  v2x_get_DSecond();
    posoffset_ta2gv(target.pos(),gv_json["pos"]);
    get_participant_posconfidence(target,gv_json["posConfidence"]);
    //gv_json["transmission"]  //option ; 
    gv_json["speed"] = target.speed(); 
    gv_json["heading"] = target.heading();
    //gv_json["angle"]   // option 
    //gv_json["motionCfd"] //option 
    //gv_json["accelSet"] // option 
    size_ta2gv(target.size(),gv_json["size"]);
    vehicleClass_ta2gv(target.car_type(),gv_json["vehicleClass"]); 
    return RET_OK;
}
int tianan_mec_protocol::get_rsitimedetails(int64_t beg_ms, int64_t end_ms, nlohmann::json& gv_json)
{
    gv_json["startTime"] = v2x_get_MinuteOfTheYear(beg_ms); 
    gv_json["endTime"] = v2x_get_MinuteOfTheYear(end_ms); 
    //gv_json["endTimeConfidence"];  
    return RET_OK;

}

string tianan_mec_protocol::get_bsm_id(int64_t id)
{
    hex_str ohex; 
    string ret_id; 
    ohex.encode((unsigned char  *)&id,8,ret_id); 
    return ret_id;
}

string tianan_mec_protocol::get_v2x_id()
{
    return "ta123456";
}

int tianan_mec_protocol::get_v2x_msgcnt()
{
    static int msgcnt = 0; // 0..127
    msgcnt++; 
    if(msgcnt >= 128)
    {
        msgcnt = 0; 
    }
    return msgcnt; // 
}

int tianan_mec_protocol::get_v2x_rteid(const perception::Event &event)
{
    auto retid =  (event.event_type()+event.level()+event.source()+event.track_time())%256;
    return retid; 
}
