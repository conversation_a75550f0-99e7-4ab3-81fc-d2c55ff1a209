/***********************************************
版本信息： 金溢科技版本权所有，保留一切权利
文件名称： tianan_mqtt_client.h
作者：张明
开始日期：2024-01-29 13:45:16
完成日期：
当前版本号: v 0.0.1
主要功能: 
版本历史:
***********************************************/

#ifndef _TIANAN_MQTT_CLIENT_H_
#define _TIANAN_MQTT_CLIENT_H_

#include "icloud_platform_client.h"
#include "iengine.h"
#include "ireceiver.h"
#include "tianan_mqtt_protocol.h"
#include "v2x_client.h"
#include "status_control.hpp"// 引入 状态管理，进行重新，关注，上报各种状态处理
#include "command_pattern.hpp"
#include "time_task.h"
class tianan_mqtt_client : public imqttclient_receiver
{
	typedef status_control<tianan_mqtt_client,status_enum> status_control_type;
    typedef status_control_type::status_info  status_info;

	using TOPIC_SUB_PROC_FUN = function<int(const string &,char * , int,const mosquitto_property *)>; 
	
	struct mqtt_sub_pub_info
	{
		string pub_topic ; 
		string pub_ack_topic;
		TOPIC_SUB_PROC_FUN pub_ack_topic_fun; 
		string sub_topic ;
		TOPIC_SUB_PROC_FUN sub_topic_fun; 
	}; 

public:
	tianan_mqtt_client();
	~tianan_mqtt_client();
	void ontime_check(long long cur_ms) ;

	int init();
	int destory();

public:
	int on_http_push_server_info_cb(const string& mqtt_host, int port,const string& devicesecret ); 

public: // interface ireceiver
    virtual void read_cb(engine_conn_handler & handler, const char* msg, int len) override;
    virtual void event_cb(int fd, short events ) override; 	
public: // imqttclient_receiver
	virtual void message_cb(const struct mosquitto_message *message,const mosquitto_property *properties) override; 

public: //状态管理函数
	int proc_status_logout(status_info & status);
    int proc_status_disconnect(status_info & status);
    int proc_status_connected(status_info & status);
	int proc_status_keep_alive(status_info & status); 
private://mqtt 相关命令处理
	void init_mqtt_topic_info();
	int regist_mqtt_topic_proc_fun();
	
	int proc_topic_rsi_up_ack_down(const string &topic, char * payload, int payload_len,const mosquitto_property *properties ); 
	int proc_topic_spat_up_ack_down(const string &topic, char * payload, int payload_len,const mosquitto_property *properties); 
	int proc_topic_rsm_up_ack_down(const string &topic, char * payload, int payload_len,const mosquitto_property * properties); 
	int proc_topic_base_up_ack_down(const string &topic, char * payload, int payload_len,const mosquitto_property * properties);
	int proc_topic_run_up_ack_down(const string &topic, char * payload, int payload_len,const mosquitto_property * properties);

	int proc_topic_heartbeat_ack(const string &topic, char * payload, int payload_len,const mosquitto_property *properties ); 
	int proc_topic_conf_down(const string &topic, char * payload, int payload_len,const mosquitto_property *properties); 
	int proc_topic_upgrade_down(const string &topic, char * payload, int payload_len,const mosquitto_property *properties); 
	int mqtt_rsu_version_up(); 

	int mqtt_public(string &topic,string &data, string &respose_topic,string &msg_type); 
	int mqtt_public_response(const mosquitto_property *properties,
                                    bool ack,const string seqNum,int errorCode, const string &errdesc);

	int proc_topic_rsi_down(const string &topic, char * payload, int payload_len,const mosquitto_property * properties); 
	int proc_topic_map_down(const string &topic, char * payload, int payload_len,const mosquitto_property * properties); 
private:
	int proc_rsu_asn_bin(const rsp_type & type,v2x_client_respond & frame);
	string get_public_topic(const string &type);
	string get_subscribe_topic(const string &type); 

	// 定时任务
	void on_report_heartbeat(long long cur_ms); 
	void on_report_run(long long cur_ms); 
	void on_report_base(long long cur_ms); 
private:
	time_task m_time_task;
	command_pattern <string, char*, int,const mosquitto_property * > m_topic_commander; 
	map<string,mqtt_sub_pub_info> m_mqtt_topic_info; // 订阅主题处理函数;

	tianan_mqtt_protocol  m_protocol;
	status_control_type m_status_control;
	engine_conn_handler m_mqtt_handler;
	
	mqtt_regist_info m_mqtt_regist_info; 
	string m_devicesecret;
	string m_rsu_id;
	int m_v2x_dev_type;

	ta_down_conf_data m_ta_config_down_data;

	time_task_id_t m_mqtt_report_heartbeat_id{0};
	time_task_id_t m_mqtt_report_run_id{0};

 
};
#endif //_TIANAN_MQTT_CLIENT_H_
