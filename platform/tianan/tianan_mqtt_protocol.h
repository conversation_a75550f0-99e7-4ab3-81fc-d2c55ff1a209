/***********************************************
版本信息： 金溢科技版本权所有，保留一切权利
文件名称： tianan_mqtt_protocol.h
作者：张明
开始日期：2024-01-29 14:12:09
完成日期：
当前版本号: v 0.0.1
主要功能: 
版本历史:
***********************************************/

#ifndef _TIANAN_MQTT_PROTOCOL_H_
#define _TIANAN_MQTT_PROTOCOL_H_

#include "hex_str.h"
#include "mec_asn/GoEMECMessageFrame.h"
#include "uncopyable.hpp"
#include "nlohmann/json.hpp"
using json = nlohmann::json;	

#include <string>
using namespace std;

struct ta_down_mqtt_data:public uncopyable<ta_down_mqtt_data>
{
	~ta_down_mqtt_data()
	{
		if(asn_v2x_frame!=nullptr)
		{
			ASN_STRUCT_FREE(asn_DEF_MessageFrame,asn_v2x_frame);
			asn_v2x_frame = nullptr;
		}
	}
	uint64_t start_time;
	uint64_t end_time; 
	string data;
	bool ack{false}; 
	string seqNum; 
	int status;
	MessageFrame_t * asn_v2x_frame {nullptr}; ///< asn 帧
}; 
struct ta_AddressChg
{
	string cssUrl; 
	int64_t time{-1}; 
};

struct ta_down_conf_data
{
	string seqNum; 
	int HBRate{10} ; //0：不上报心跳信息 ; >0：表示上报间隔，秒
	int runningInfoRate{3}; // 0：不上报设备运行状态信息  ; >0：表示上报间隔，秒
	ta_AddressChg addressChg; 
	string logLevel; 
	int reboot {0}; 
	string extendConfig; 
	bool ack; 
}; 

struct ta_upgrade_data
{
	string seqNum; 
	bool ack;
	string softwareVersion; // 软件原版本
	string updateVersion;
	string downloadUrl;
	string OTAUserId;
	string OTApassword; 
	string downloadMd5;
	int64_t updateTime;
	int authMode {1}; // 校验模式。0：不使用；1-token； 注：当前固定使用token模式
	string token ; 
}; 



class tianan_mqtt_protocol
{
public:
	tianan_mqtt_protocol();
	~tianan_mqtt_protocol();
	void init(const string &rus_id); 
public:
	int fetch_rsi_data(const char* pdata, uint32_t len,ta_down_mqtt_data & rsi_data);
	int fetch_map_data(const char* pdata, uint32_t len,ta_down_mqtt_data & map_data);
	int fetch_conf_data(const char * pdata, uint32_t len , ta_down_conf_data & ta_conf); 
	int fetch_upgrade_data(const char *pdata, uint32_t len,ta_upgrade_data & ta_upgrade);
public:
	int build_heartbeat(string & req); 
	int build_runinfo(string & req); 
	int build_baseinfo(string & req); 
	int build_version_up(string & req); 

	static string get_mqtt_seqnum(); 

	int build_mqtt_public_msg(nlohmann::json &asn_json, string & mqtt_json); 
private:
	int asn_decode(ta_down_mqtt_data & data); 
private:
	string m_rsu_id;
	hex_str m_hex_str; 

	


};
#endif //_TIANAN_MQTT_PROTOCOL_H_
