#include "tianan_mec_client.h"
#include "service.h"


tianan_mec_client::tianan_mec_client():m_status_control(status_enum::disconnect,"tianan_mec_client",this)
{
    m_pb_commander.regist_command(PerceptionMsg::MsgTypeCase::kTargetMsg,this,std::bind(&tianan_mec_client::proc_target_msg,FUN_BIND_2)); 
    m_pb_commander.regist_command(PerceptionMsg::MsgTypeCase::kEventMsg,this,std::bind(&tianan_mec_client::proc_event_msg,FUN_BIND_2)); 
    m_pb_commander.regist_command(PerceptionMsg::MsgTypeCase::kHeartBeatMsg,this,std::bind(&tianan_mec_client::proc_heartbeat_msg,FUN_BIND_2)); 
    m_pb_commander.regist_command(PerceptionMsg::MsgTypeCase::kDevInfo,this,std::bind(&tianan_mec_client::proc_devinfo_msg,FUN_BIND_2)); 
    m_pb_commander.regist_command(PerceptionMsg::MsgTypeCase::kRoadCount,this,std::bind(&tianan_mec_client::proc_roadcount_msg,FUN_BIND_2)); 
    m_pb_commander.regist_command(PerceptionMsg::MsgTypeCase::kRoadInfo,this,std::bind(&tianan_mec_client::proc_roadinfo_msg,FUN_BIND_2)); 

    m_status_control.regist_status(status_enum::disconnect,&tianan_mec_client::proc_status_disconnect,"disconnect",10000);// 断线重连接 为10 秒
    m_status_control.regist_status(status_enum::connected,&tianan_mec_client::proc_status_connected,"connected",0);
    m_status_control.regist_status(status_enum::heartbeat_send,&tianan_mec_client::proc_status_heartbeat_send,"heartbeat_send",1000000);// 此状态为中间状态

}

tianan_mec_client::~tianan_mec_client()
{
	m_pb_commander.unregist_all_command(this);
}
	
int tianan_mec_client::init()
{
    int udp_port=6000; 
    if(RET_OK != engine_ref.regist_udp_service(this,"127.0.0.1",6000,m_mec_handler))
    {
        WLE("regist_udp_service udp_port %d fail ", udp_port); 
    }
    m_status_control.enter_status(status_enum::connected);
    return RET_OK;
}


void tianan_mec_client::stop()
{
    WLD("ENTER");
}


void tianan_mec_client::destory()
{
    WLD("ENTER");
    engine_ref.unregist(this,m_mec_handler);
}


void tianan_mec_client::ontime_check(long long cur_ms)
{
    m_status_control.call_status_fun(cur_ms);
}

int tianan_mec_client::get_mec_status()
{
    return 1; 
}

void tianan_mec_client::read_cb(engine_conn_handler & handler, const char* msg, int len) 
{
    perception::PerceptionMsg perceptionMsg; 
    if(false==perceptionMsg.ParseFromArray(msg,len))
    {
        WLE("Error upd msg "); 
        return ;
    }
    //perceptionMsg.SerializeAsString(); 
    m_pb_commander.do_command(perceptionMsg.MsgType_case(),perceptionMsg);
}

void tianan_mec_client::event_cb(int fd, short events ) 
{
    WLD("fd %d,events %d",fd,events);
    if(events & BEV_EVENT_ERROR)
    {
        // 处理异常事件
        if(fd == m_mec_handler.fd)
        {
            m_mec_handler.init();
        }
        m_status_control.enter_status(status_enum::disconnect);
    }
}


//=======================================================
// pb 消息处理函数 
int tianan_mec_client::proc_target_msg(const PerceptionMsg::MsgTypeCase & msgType, PerceptionMsg & msg )
{
    WLD(" Eneter");
    auto target_msg = msg.target_msg();
    string rsm_json ;
    m_protocol.rsm_ta2gv(msg,rsm_json); 
    WLD(" rsm_json %s ",rsm_json.c_str());
    v2x_client_ref.send_cv2x_json_81H((char *)rsm_json.c_str(),rsm_json.length());
    return RET_OK;
}

int tianan_mec_client::proc_event_msg(const PerceptionMsg::MsgTypeCase & msgType, PerceptionMsg & msg )
{
    WLD(" Eneter");
    string rsi_rtes_json; 
    m_protocol.rsi_ta2gv(msg,rsi_rtes_json); 
    WLD(" rsi_rtes_json %s ",rsi_rtes_json.c_str());
    v2x_client_ref.send_cv2x_json_81H((char *)rsi_rtes_json.c_str(),rsi_rtes_json.length());
    return RET_OK;
}
int tianan_mec_client::proc_heartbeat_msg(const PerceptionMsg::MsgTypeCase & msgType, PerceptionMsg & msg )
{
    WLD(" Eneter");
    return RET_OK;
}
int tianan_mec_client::proc_devinfo_msg(const PerceptionMsg::MsgTypeCase & msgType, PerceptionMsg & msg )
{
    WLD(" Eneter");
    return RET_OK;
}
int tianan_mec_client::proc_roadcount_msg(const PerceptionMsg::MsgTypeCase & msgType, PerceptionMsg & msg )
{
    WLD(" Eneter");
    return RET_OK;
}
int tianan_mec_client::proc_roadinfo_msg(const PerceptionMsg::MsgTypeCase & msgType, PerceptionMsg & msg ) 
{
    WLD(" Eneter");
    return RET_OK;
}


//============================================================================
// 状态管理函数 
int tianan_mec_client::proc_status_disconnect(status_info & status)
{
    destory();
    if(RET_OK==init())
    {
        m_status_control.enter_status(status_enum::connected);
    }

    return RET_OK;
}

int tianan_mec_client::proc_status_connected(status_info & status)
{
    m_status_control.enter_status(status_enum::heartbeat_send);
    return RET_OK;
}

int tianan_mec_client::proc_status_heartbeat_send(status_info & status)
{
    return RET_OK;
}
