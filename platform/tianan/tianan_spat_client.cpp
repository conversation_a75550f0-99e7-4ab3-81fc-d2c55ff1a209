#include "tianan_spat_client.h"
#include "error_code.h"
#include "stdafx.h"
#include "service.h"

tianan_spat_client::tianan_spat_client()
{

}

tianan_spat_client::~tianan_spat_client()
{
	
}

int tianan_spat_client::init()
{
    int ret_code  =  engine_ref.regist_udp_service(this,m_ip,m_port,m_udp_handler); 
    if(RET_OK != ret_code)
    {
        WLE("engine_ref.regist_udp_service Error %s %d",m_ip.c_str(),m_port);
        return RET_FAIL; 
    }

    return RET_OK;
}

int tianan_spat_client::destory()
{
    engine_ref.unregist(this,m_udp_handler); 
    return RET_OK; 
}


void tianan_spat_client::read_cb(engine_conn_handler & handler, const char* msg, int len)
{
    
}
void tianan_spat_client::event_cb(int fd, short events )
{
    WLD("fd %d event %d",fd,events);
}