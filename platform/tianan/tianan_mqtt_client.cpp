#include "tianan_mqtt_client.h"
#include "stdafx.h"
#include "service.h"
#include "common_utility.h"
#include "dir_op.h"
tianan_mqtt_client::tianan_mqtt_client():m_status_control(status_enum::logout,"tianan_mqtt_client",this)
{
    config_ref.get(CK_V2X_TYPE,m_v2x_dev_type);
    config_ref.get(CK_ESN,m_rsu_id);
    m_protocol.init(m_rsu_id); 

    int reconnect_interval_ms = 3000;

    m_status_control.regist_status(status_enum::logout, &tianan_mqtt_client::proc_status_logout, "logout", reconnect_interval_ms);
    m_status_control.regist_status(status_enum::disconnect, &tianan_mqtt_client::proc_status_disconnect, "disconnect", reconnect_interval_ms);
    m_status_control.regist_status(status_enum::connected, &tianan_mqtt_client::proc_status_connected, "connected", 1000);
    m_status_control.regist_status(status_enum::keep_alive, &tianan_mqtt_client::proc_status_keep_alive, "keep_alive", 1000);

    // 向 rsu 注册 回调
    v2x_client_ref.get_command_pattern().regist_command(rsp_type::RSP_ASN_BIN,this,std::bind(&tianan_mqtt_client::proc_rsu_asn_bin, FUN_BIND_2));
    
    

    init_mqtt_topic_info();


    m_mqtt_regist_info.version = MQTT_PROTOCOL_V5; // 天安mqtt 采用v5 协议

    string ta_down_conf_path;
    string conf_data;
    config_ref.get(CK_TIANAN_RSU_CONF_DOWN_PATH,ta_down_conf_path); 
    if(RET_OK == dir_op::read_file_data(ta_down_conf_path,conf_data))
    {
        m_protocol.fetch_conf_data(conf_data.c_str(),conf_data.length(),m_ta_config_down_data); 
        m_ta_config_down_data.reboot = 0; 
    }


    m_mqtt_report_heartbeat_id = m_time_task.regist_task(std::bind(&tianan_mqtt_client::on_report_heartbeat,FUN_BIND_1),10000); //10 秒 心跳 HEART
    m_mqtt_report_run_id = m_time_task.regist_task(std::bind(&tianan_mqtt_client::on_report_run,FUN_BIND_1),3000); //3 秒 运行状态上报信息 RUN
    m_time_task.regist_task(std::bind(&tianan_mqtt_client::on_report_base,FUN_BIND_1),3000); //3 秒 基本信息上报 BASE

    if(m_ta_config_down_data.HBRate >0 )
    {
        m_time_task.set_task_interval_ms(m_mqtt_report_heartbeat_id,m_ta_config_down_data.HBRate * 1000); 
    }
    if(m_ta_config_down_data.runningInfoRate > 0 )
    {
        m_time_task.set_task_interval_ms(m_mqtt_report_run_id,m_ta_config_down_data.runningInfoRate * 1000); 
    }

}

tianan_mqtt_client::~tianan_mqtt_client()
{

}

int tianan_mqtt_client::init()
{
    if(m_v2x_dev_type != config::v2x_type::VT_RSU )
    {
        WLE(" dev type is not RSU  !!!! "); 
        return RET_FAIL;
    }
    if(m_status_control.get_cur_status() == status_enum::logout)
    {// 表示还没有获取到登录的 ip port
        return RET_OK;
    }
    if(RET_OK != engine_ref.regist_mqtt_client(this,m_mqtt_regist_info,m_mqtt_handler))
    {
        WLE("engine_ref.regist_mqtt_client error ");
        return RET_FAIL;
    }
    return RET_OK;
}

int tianan_mqtt_client::destory()
{
    engine_ref.unregist(this,m_mqtt_handler);
    return RET_OK;
}

void tianan_mqtt_client::ontime_check(long long cur_ms)
{
    m_status_control.call_status_fun(cur_ms);
}

//=========================================================
// ireceiver
void tianan_mqtt_client::read_cb(engine_conn_handler & handler, const char* msg, int len)
{
    WLD("msg %s ",msg);
}
// 事件处理
void tianan_mqtt_client::event_cb(int fd, short events )
{
    WLI("fd %d events %d ",fd, events);

    if(events & BEV_EVENT_CONNECTED)
    {// 连接成功
        //向MQTT注册 成功后则进入 connected 
        m_status_control.enter_status(status_enum::connected);
    }
    if(events & BEV_EVENT_ERROR)
    {// 连接断开
        m_mqtt_handler.init();
        m_status_control.enter_status(status_enum::disconnect);
    }
}
// mqtt topic call back 
void tianan_mqtt_client::message_cb(const struct mosquitto_message *message,const mosquitto_property *properties)
{
    WLD(" topic %s  payload %s ", message->topic, (char*)message->payload);
    char * data =  (char *)(message->payload);
    int payloadlen =  message->payloadlen;
    m_topic_commander.do_command(message->topic,data,payloadlen,properties);
}


//=========================================================
// 状态处理函数

//状态处理-等待云平台ID
int tianan_mqtt_client::proc_status_logout(status_info & status)
{
    if(false == m_mqtt_regist_info.hostname.empty())
    {// 如果取的云平台ID，刚进入连接mqtt 状态
        regist_mqtt_topic_proc_fun(); 
        m_status_control.enter_status(status_enum::disconnect); 
    }
    return RET_OK;
}

//状态处理-连接断开
int tianan_mqtt_client::proc_status_disconnect(status_info & status)
{
    destory();
    init();
    return RET_OK;
}
void tianan_mqtt_client::init_mqtt_topic_info()
{
    // 初始化连接信息
    int val = 0;
    config_ref.get(GET_TIANAN_RSU_MQTT_KEY(MQTT_TLS),val);
    if(val == 1)
    {
        m_mqtt_regist_info.tls =  true;
    }
    else 
    {
        m_mqtt_regist_info.tls =  false;
    }
    config_ref.get(GET_TIANAN_RSU_MQTT_KEY(MQTT_CLIENT_ID),m_mqtt_regist_info.client_id);     
    config_ref.get(GET_TIANAN_RSU_MQTT_KEY(MQTT_CA_PATH),m_mqtt_regist_info.cafile);


    mqtt_sub_pub_info rsi_info;
    rsi_info.pub_topic = "V2X/RSU/RSI/%s"; 
    rsi_info.pub_ack_topic = "V2X/RSU/RSI_ACK/%s"; 
    rsi_info.pub_ack_topic_fun = std::bind(&tianan_mqtt_client::proc_topic_rsi_up_ack_down,FUN_BIND_4); 
    rsi_info.sub_topic = "V2X.RSU.RSI.%s.DOWN"; 
    rsi_info.sub_topic_fun = std::bind(&tianan_mqtt_client::proc_topic_rsi_down,FUN_BIND_4);
    m_mqtt_topic_info["rsi"] = rsi_info;

    mqtt_sub_pub_info map_info;
    map_info.sub_topic = "V2X.RSU.MAP.%s.DOWN"; 
    map_info.sub_topic_fun = std::bind(&tianan_mqtt_client::proc_topic_map_down,FUN_BIND_4);
    m_mqtt_topic_info["map"] =  map_info;

    mqtt_sub_pub_info spat_info;
    spat_info.pub_topic = "V2X/RSU/SPAT/%s"; 
    spat_info.pub_ack_topic = "V2X/RSU/SPAT_ACK/%s"; 
    spat_info.pub_ack_topic_fun = std::bind(&tianan_mqtt_client::proc_topic_spat_up_ack_down,FUN_BIND_4); 
    m_mqtt_topic_info["spat"] = spat_info;

    mqtt_sub_pub_info rsm_info;
    rsm_info.pub_topic = "V2X/RSU/RSM/%s"; 
    rsm_info.pub_ack_topic = "V2X/RSU/RSM_ACK/%s"; 
    rsm_info.pub_ack_topic_fun = std::bind(&tianan_mqtt_client::proc_topic_rsm_up_ack_down,FUN_BIND_4); 
    m_mqtt_topic_info["rsm"] = rsm_info;

    mqtt_sub_pub_info heart_info;
    heart_info.pub_topic = "V2X/RSU/HEART/%s"; 
    heart_info.pub_ack_topic = "V2X/RSU/HEART_ACK/%s"; 
    heart_info.pub_ack_topic_fun = std::bind(&tianan_mqtt_client::proc_topic_heartbeat_ack,FUN_BIND_4); 
    m_mqtt_topic_info["heart"] = heart_info;

    mqtt_sub_pub_info base_info;
    base_info.pub_topic = "V2X/RSU/BASE/%s"; 
    base_info.pub_ack_topic = "V2X/RSU/BASE_ACK/%s"; 
    base_info.pub_ack_topic_fun = std::bind(&tianan_mqtt_client::proc_topic_base_up_ack_down,FUN_BIND_4); 
    m_mqtt_topic_info["base"] = base_info;

    mqtt_sub_pub_info run_info;
    run_info.pub_topic = "V2X/RSU/RUN/%s"; 
    run_info.pub_ack_topic = "V2X/RSU/RUN_ACK/%s"; 
    run_info.pub_ack_topic_fun = std::bind(&tianan_mqtt_client::proc_topic_run_up_ack_down,FUN_BIND_4); 
    m_mqtt_topic_info["run"] = run_info;

    mqtt_sub_pub_info conf_info;
    conf_info.sub_topic = "V2X.RSU.CONF.%s.DOWN"; 
    conf_info.sub_topic_fun = std::bind(&tianan_mqtt_client::proc_topic_conf_down,FUN_BIND_4);
    m_mqtt_topic_info["conf"] =  conf_info;

    mqtt_sub_pub_info upgrade_info;
    upgrade_info.sub_topic = "v2x.v1.rsu.%s.upgrade.down"; 
    upgrade_info.sub_topic_fun = std::bind(&tianan_mqtt_client::proc_topic_upgrade_down,FUN_BIND_4);
    m_mqtt_topic_info["upgrade"] =  upgrade_info;



    char topic_format[256]={0}; 
    for(auto & it:m_mqtt_topic_info)
    {
        if(!it.second.pub_topic.empty())
        {
            sprintf(topic_format,it.second.pub_topic.c_str(),m_rsu_id.c_str()); 
            it.second.pub_topic = topic_format; 

        }
        if(!it.second.pub_ack_topic.empty())
        {
            sprintf(topic_format,it.second.pub_ack_topic.c_str(),m_rsu_id.c_str()); 
            it.second.pub_ack_topic =  topic_format; 
        }
        if(!it.second.sub_topic.empty())
        {
            sprintf(topic_format,it.second.sub_topic.c_str(),m_rsu_id.c_str()); 
            it.second.sub_topic =  topic_format; 
        }
    }

}
int tianan_mqtt_client::regist_mqtt_topic_proc_fun()
{
    m_topic_commander.unregist_all_command(this); 
    for(auto& item : m_mqtt_topic_info)
    {
        if(!item.second.pub_ack_topic.empty())
        {
            m_topic_commander.regist_command(item.second.pub_ack_topic,this,item.second.pub_ack_topic_fun);
        }
        if(!item.second.sub_topic.empty())
        {
            m_topic_commander.regist_command(item.second.sub_topic,this, item.second.sub_topic_fun); 
        }
            
    }

    return RET_OK;
}

//状态处理-连接成功
int tianan_mqtt_client::proc_status_connected(status_info & status)
{

    if(v2x_client_ref.connect_is_ok() == false)
    {
        WLE(" v2x_client_ref.connect_is_ok() == false"); 
        return RET_OK;
    }
    WLI(" v2x_client_ref.connect_is_ok subscribe_topic begin"); 

    // 进行消息订阅
    for(auto& item : m_mqtt_topic_info)
    {
        if(!item.second.pub_ack_topic.empty())
        {
            if(RET_OK != engine_ref.mqtt_subscribe(m_mqtt_handler,item.second.pub_ack_topic))
            {
                m_status_control.enter_status(status_enum::disconnect);
                WLE("mqtt_subscribe topic %s",item.second.pub_ack_topic.c_str()); 
                return RET_FAIL;
            }
        }
        if(!item.second.sub_topic.empty())
        {
            if(RET_OK != engine_ref.mqtt_subscribe(m_mqtt_handler,item.second.sub_topic))
            {
                m_status_control.enter_status(status_enum::disconnect);
                WLE("mqtt_subscribe topic %s",item.second.sub_topic.c_str()); 
                return RET_FAIL;
            }
        }
    }

    // 连接成功后上报 版本信息
    mqtt_rsu_version_up();
    m_status_control.enter_status(status_enum::keep_alive);
    return RET_OK;
}

//状态处理-保活，用于处理定时上报任务
int tianan_mqtt_client::proc_status_keep_alive(status_info & status)
{
    m_time_task.on_time(status.enter_tm_ms);
    return RET_OK;
}


int tianan_mqtt_client::on_http_push_server_info_cb(const string& mqtt_host, int port,const string&devicesecret )
{
    if(mqtt_host != m_mqtt_regist_info.hostname || port!= m_mqtt_regist_info.port || devicesecret != m_devicesecret)
    {// 检测到配置变后，重新连接 mqtt
        m_mqtt_regist_info.tls = false;
        m_mqtt_regist_info.keepalivesec = 10; 

        m_mqtt_regist_info.hostname =  mqtt_host;
        m_mqtt_regist_info.port =  port;
        m_devicesecret =  devicesecret;
        destory(); 
        init();
    }
    return RET_OK;
}
int tianan_mqtt_client::proc_rsu_asn_bin(const rsp_type & type,v2x_client_respond & frame)
{
    // 只上报收到的。
    //WLD("json : %s " , frame.data.asnUper.json);
    auto j = nlohmann::json::parse(frame.data.asnUper.json);
    auto direct =  j["direct"].get<string>();

    if(direct == "recv")
    {// 天安只上报 RSU发出的数据
        return RET_OK;
    }
    auto msg_type =  j["msg"].get<string>();
    auto it_find =  m_mqtt_topic_info.find(msg_type); 
    if(it_find == m_mqtt_topic_info.end())
    {
        WLI("not need to report msg %s ",msg_type.c_str()); 
        return RET_OK;
    }
    string mqtt_data;
    if(RET_OK != m_protocol.build_mqtt_public_msg(j,mqtt_data))
    {
        return RET_OK;
    }
    
    if(it_find->second.pub_topic.empty())
    {
        return RET_OK;
    }

    mqtt_public(it_find->second.pub_topic,mqtt_data,it_find->second.pub_ack_topic,msg_type); 
    return RET_OK;
}

int tianan_mqtt_client::mqtt_public(string &topic,string &data, string &respose_topic,string &msg_type)
{
    if( m_status_control.get_cur_status()< status_enum::connected )
    {
        return RET_FAIL; 
    }

    mosquitto_property * property = nullptr;
    if(!respose_topic.empty())
    {
        mosquitto_property_add_string(&property,mqtt5_property::MQTT_PROP_RESPONSE_TOPIC,respose_topic.c_str());
    }
    if(!msg_type.empty())
    {
        mosquitto_property_add_binary(&property,mqtt5_property::MQTT_PROP_CORRELATION_DATA,msg_type.data(),msg_type.length());// 非必须数据
    }
    int ret = engine_ref.mqtt_public(m_mqtt_handler,topic,data.c_str(),data.length(),1,false,property); 
    return ret;
}

string tianan_mqtt_client::get_public_topic(const string &type)
{
    char sTemp[256] = {}; 
    sprintf(sTemp,"V2X/RSU/%s/%s",str_toupper(type).c_str(),m_rsu_id.c_str()); 
    string topic =  sTemp;
    return topic;
}
string tianan_mqtt_client::get_subscribe_topic(const string &type)
{
    char sTemp[256] = {}; 
    sprintf(sTemp,"V2X.RSU.%s.%s.DOWN",str_toupper(type).c_str(),m_rsu_id.c_str()); 
    string topic = sTemp;
    return topic;
}



int tianan_mqtt_client::proc_topic_rsi_down(const string &topic, char * payload, int payload_len,const mosquitto_property *properties)
{
    ta_down_mqtt_data rsi_data;
    if(RET_OK != m_protocol.fetch_rsi_data(payload,payload_len,rsi_data))
    {
        mqtt_public_response(properties,rsi_data.ack,rsi_data.seqNum,1,"rsi_down json decode error "); 
        return RET_FAIL;
    }
    
    int ret_code = v2x_client_ref.send_cv2x_asn_88H(*(rsi_data.asn_v2x_frame)); 
    if(ret_code != RET_OK)
    {
        mqtt_public_response(properties,rsi_data.ack,rsi_data.seqNum,2,"rsi_down send_cv2x_asn_88H error "); 
        return ret_code;
    }
    mqtt_public_response(properties,rsi_data.ack,rsi_data.seqNum,0,"");
    return RET_OK;
}


int tianan_mqtt_client::proc_topic_map_down(const string &topic, char * payload, int payload_len,const mosquitto_property *properties)
{
    ta_down_mqtt_data map_data;
    if(RET_OK != m_protocol.fetch_map_data(payload,payload_len,map_data))
    {
        mqtt_public_response(properties,map_data.ack,map_data.seqNum,1,"map_down json decode error "); 
        return RET_FAIL;
    }
    int ret_code = v2x_client_ref.send_cv2x_asn_88H(*(map_data.asn_v2x_frame)); 
    if(ret_code != RET_OK)
    {
        mqtt_public_response(properties,map_data.ack,map_data.seqNum,2,"map_down send_cv2x_asn_88H error "); 
        return ret_code;
    }
    mqtt_public_response(properties,map_data.ack,map_data.seqNum,0,""); 
    return RET_OK;
}

int tianan_mqtt_client::proc_topic_conf_down(const string &topic, char * payload, int payload_len,const mosquitto_property *properties)
{

    auto old_config =  m_ta_config_down_data; 
    if(RET_OK !=  m_protocol.fetch_conf_data(payload,payload_len,m_ta_config_down_data))
    {
        mqtt_public_response(properties,m_ta_config_down_data.ack,m_ta_config_down_data.seqNum,1,"map_down json decode error "); 
        return RET_FAIL;
    }
    // 保存下发的配置文件 
    string path;
    config_ref.get(CK_TIANAN_RSU_CONF_DOWN_PATH,path); 
    dir_op::write_file_data(path,payload); 

    if(m_ta_config_down_data.addressChg.cssUrl != old_config.addressChg.cssUrl)
    {// 表示平台地址有更新
        config_ref.set(CK_TIANAN_RSU_REGIST_URL,m_ta_config_down_data.addressChg.cssUrl); 
        config_ref.save_config();
    }

    if(m_ta_config_down_data.HBRate >0 )
    {
        m_time_task.set_task_interval_ms(m_mqtt_report_heartbeat_id,m_ta_config_down_data.HBRate * 1000); 
    }
    if(m_ta_config_down_data.runningInfoRate > 0 )
    {
        m_time_task.set_task_interval_ms(m_mqtt_report_run_id,m_ta_config_down_data.runningInfoRate * 1000); 
    }

    if(1 == m_ta_config_down_data.reboot)
    {// 执行 5 秒后重启
        service_ref.delay_reboot(5000); 
    }
    
    return RET_OK;
}

// 升级下载
int tianan_mqtt_client::proc_topic_upgrade_down(const string &topic, char * payload, int payload_len,const mosquitto_property *properties)
{

    ta_upgrade_data ota_down; 
    if(RET_OK!= m_protocol.fetch_upgrade_data(payload,payload_len,ota_down))
    {
        mqtt_public_response(properties,ota_down.ack,ota_down.seqNum,1,"upgrade_down json decode error "); 
        return RET_FAIL;
    }
    
    updage_info info; 
    info.downloadMd5 =  ota_down.downloadMd5; 
    info.downloadUrl =  ota_down.downloadUrl; 
    info.local_path = "/tmp/update.gv"; 
    info.passwd = ota_down.OTApassword; 
    info.user =  ota_down.OTAUserId; 
    if(ota_down.authMode == 1)
    {
        info.token =  ota_down.token;
    }

    ota_upgade_ref.upgade(info);
   
    return RET_OK;
}


int tianan_mqtt_client::proc_topic_rsi_up_ack_down(const string &topic, char * payload, int payload_len,const mosquitto_property *properties)
{
    WLI(" topic[%s] data:%s ",topic.c_str(),payload); 
    return RET_OK;
}

int tianan_mqtt_client::proc_topic_spat_up_ack_down(const string &topic, char * payload, int payload_len,const mosquitto_property *properties)
{
    WLI(" topic[%s] data:%s ",topic.c_str(),payload); 
    return RET_OK;
}
int tianan_mqtt_client::proc_topic_rsm_up_ack_down(const string &topic, char * payload, int payload_len,const mosquitto_property *properties)
{
    WLI(" topic[%s] data:%s ",topic.c_str(),payload); 
    return RET_OK;
}

int tianan_mqtt_client::proc_topic_heartbeat_ack(const string &topic, char * payload, int payload_len,const mosquitto_property *properties )
{
    WLI(" topic[%s] data:%s ",topic.c_str(),payload); 
    return RET_OK;
}


int tianan_mqtt_client::proc_topic_base_up_ack_down(const string &topic, char * payload, int payload_len,const mosquitto_property *properties)
{
    WLI(" topic[%s] data:%s ",topic.c_str(),payload); 
    return RET_OK;
}


int tianan_mqtt_client::proc_topic_run_up_ack_down(const string &topic, char * payload, int payload_len,const mosquitto_property *properties)
{
    WLI(" topic[%s] data:%s ",topic.c_str(),payload); 
    return RET_OK;
}


int tianan_mqtt_client::mqtt_public_response(const mosquitto_property *properties,
                                    bool ack,const string seqNum,int errorCode, const string &errdesc)
{
    if(properties == nullptr)
    {
        return RET_OK;
    }
    if(ack == false)
    {
        return RET_OK;
    }

    char* r_temp =  nullptr; 
    mosquitto_property_read_string(properties,mqtt5_property::MQTT_PROP_RESPONSE_TOPIC,&r_temp,false); 
    if(r_temp == nullptr)
    {
        return RET_OK;
    }
    string response_topic =  r_temp;
    free(r_temp); 
    r_temp = nullptr;

    auto ret_json = nlohmann::json{
        {"seqNum",seqNum},
        {"errorCode",errorCode}
    };
    
    if(errorCode != 0)
    {
        ret_json["errorDesc"] = errdesc;
    }
    string response_data =  ret_json.dump();
    engine_ref.mqtt_public(m_mqtt_handler,response_topic,response_data.c_str(),response_data.size()); 
    return RET_OK;
}

void tianan_mqtt_client::on_report_heartbeat(long long cur_ms)
{
    if(m_ta_config_down_data.HBRate <= 0 )
    {
        return ; 
    }
// 发送心跳
    string req_data;
    if(RET_OK != m_protocol.build_heartbeat(req_data))
    {
        WLE("m_protocol.build_heartbeat error ");
        m_status_control.enter_status(status_enum::disconnect);
        return ;
    }
    string msg_type = "heart"; 
    auto it_find = m_mqtt_topic_info.find(msg_type); 
    if(it_find == m_mqtt_topic_info.end())
    {
        return ; 
    }
    mqtt_public(it_find->second.pub_topic,req_data,it_find->second.pub_ack_topic,msg_type);
}


void tianan_mqtt_client::on_report_run(long long cur_ms)
{
    if(m_ta_config_down_data.runningInfoRate <= 0 )
    {
        return ; 
    }

    // 发送运行信息
    string req_data;
    if(RET_OK != m_protocol.build_runinfo(req_data))
    {
        WLE("m_protocol.build_runinfo error ");
        return ;
    }
    string msg_type = "run"; 
    auto it_find = m_mqtt_topic_info.find(msg_type); 
    if(it_find == m_mqtt_topic_info.end())
    {
        return ; 
    }
    mqtt_public(it_find->second.pub_topic,req_data,it_find->second.pub_ack_topic,msg_type);
}

void tianan_mqtt_client::on_report_base(long long cur_ms) 
{
    // 发送运行信息
    string req_data;
    if(RET_OK != m_protocol.build_baseinfo(req_data))
    {
        WLE("m_protocol.build_runinfo error ");
        return ;
    }
    string msg_type = "base"; 
    auto it_find = m_mqtt_topic_info.find(msg_type); 
    if(it_find == m_mqtt_topic_info.end())
    {
        return ; 
    }
    mqtt_public(it_find->second.pub_topic,req_data,it_find->second.pub_ack_topic,msg_type);
}


int tianan_mqtt_client::mqtt_rsu_version_up()
{
    string topic = "v2x.v1.rsu.versions.up"; 
    string req_data;
    if(RET_OK != m_protocol.build_version_up(req_data))
    {
        WLE("m_protocol.build_version_up error ");
        return RET_FAIL;
    }
    string response_topic ; 
    string msg_type = "rsu_version_up"; 
    mqtt_public(topic,req_data,response_topic,msg_type);
    return RET_OK;
}
