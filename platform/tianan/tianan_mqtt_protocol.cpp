#include "tianan_mqtt_protocol.h"
#include "stdafx.h"
#include "engine_module_macro.h"
#include "engine_module_log.h"
#include "common_utility.h"
#include "service.h"
tianan_mqtt_protocol::tianan_mqtt_protocol()
{

}

tianan_mqtt_protocol::~tianan_mqtt_protocol()
{
	
}


void tianan_mqtt_protocol::init(const string &rus_id)
{
    m_rsu_id =  rus_id;
}

	
int tianan_mqtt_protocol::build_heartbeat(string & req)
{
#if 0
{
"seqNum": "3456778",
"rsuId": "rsu_0001",
"timestamp":  //UTC时间戳(毫秒数)
"rsuEsn": "2930993833",
"protocolVersion": "v1.0","faultCodes":[{"faultCode":"00000","faultDesc":"正常"}]
"ack":FALSE
}
#endif 
    try
    {
        auto heartbeat_info = v2x_client_ref.get_heartbeat_info(); 
        auto mqtt_j = nlohmann::json{
            {"rsuId",m_rsu_id},
            {"timestamp",ENGINE_MODULE_NAMESPACE::get_ms()},
            {"seqNum",get_mqtt_seqnum()},
            {"protocolVersion","v1.0"},
            {"ack",false}
        };
        string faultCode = "00000"; 
        //string faultDesc = "正常"; 
        string faultDesc = "OK"; 
        if(heartbeat_info.dev_status != 0)
        {
            faultCode = "50008"; 
            faultDesc = "模组状态异常"; 
        }

        auto faultCodes_j_i  = nlohmann::json{
            {"faultCode",faultCode},
            {"faultDesc",faultDesc}
        };
        mqtt_j["faultCodes"].emplace_back(std::move(faultCodes_j_i)); 
        req =  mqtt_j.dump();
        return RET_OK;
    }

    catch(const std::exception& e)
    {
        WLE("catch exception %s ",e.what()); 
        return RET_FAIL;
    }
}

int tianan_mqtt_protocol::build_runinfo(string & req)
{
    try
    {
        auto mqtt_j = nlohmann::json{
            {"seqNum",get_mqtt_seqnum()},
            {"rsuId",m_rsu_id},
            {"rsuEsn",m_rsu_id},
            {"timestamp",ENGINE_MODULE_NAMESPACE::get_ms()},
            {"protocolVersion","v1.0"},
            {"ack",false}
        };
        cpu_info_t cpu_info ; 
        mem_info_t mem_info; 
        disk_info_t disk_info; 
        net_info_t net_info; 
        dev_performance_ref.get_cpu_info(cpu_info);
        dev_performance_ref.get_mem_info(mem_info);
        dev_performance_ref.get_disk_info(disk_info);
        dev_performance_ref.get_net_info(net_info);
        

        auto runningInfo = nlohmann::json{
            {"cpu",{
                {"Load",cpu_info.load1},
                {"Uti",std::to_string(cpu_info.load1)}
            }},
            {"mem",{
                {"Total",mem_info.total/1024.0},
                {"Used",mem_info.use/1024.0},
                {"Free",(mem_info.total - mem_info.use)/1024.0}
            }},
            {"disk",{
                {"total",disk_info.total/1024.0},
                {"used",disk_info.use/1024.0},
                {"free",(disk_info.total-disk_info.use)/1024.0},
                {"tps",disk_info.tps},
                {"write",disk_info.write},
                {"read",disk_info.read}
            }},
            {"net",{
                {"rx",net_info.rx},
                {"tx",net_info.tx},
                {"rxByte",net_info.rxByte},
                {"txByte",net_info.txByte}
            }}
        }; 

        mqtt_j["runningInfo"] =  runningInfo;
        req =  mqtt_j.dump();
        return RET_OK;
    }

    catch(const std::exception& e)
    {
        WLE("catch exception %s ",e.what()); 
        return RET_FAIL;
    }
}
int tianan_mqtt_protocol::build_baseinfo(string & req)
{
    try
    {
        auto heartbeat_info = v2x_client_ref.get_heartbeat_info(); 
        
        auto mqtt_j = nlohmann::json{
            {"rsuId",m_rsu_id},
            {"timestamp",ENGINE_MODULE_NAMESPACE::get_ms()},
            {"seqNum",get_mqtt_seqnum()},
            {"protocolVersion","v1.0"},
            {"ack",false},
            {"rsuStatus",(heartbeat_info.dev_status == 0?"ok":"error")},
            {"location",{
                {"lon",heartbeat_info.longitude},
                {"lat",heartbeat_info.latitude}
            }},
            {"transProtocal","https"},
            {"softwareVersion",heartbeat_info.soft_ver},
            {"hardwareVersion","V1.0"}
        };
        req =  mqtt_j.dump();
        return RET_OK;
    }
    catch(const std::exception& e)
    {
        WLE("catch exception %s ",e.what()); 
        return RET_FAIL;
    }
}

int tianan_mqtt_protocol::build_version_up(string & req)
{
    try
    {
        auto heartbeat_info = v2x_client_ref.get_heartbeat_info(); 
        
        auto mqtt_j = nlohmann::json{
            {"seqNum",get_mqtt_seqnum()},
            {"ack",false},
            {"deviceId",m_rsu_id},
            {"deviceType","001"},
            {"timestamp",ENGINE_MODULE_NAMESPACE::get_ms()},
            {"protocolVersion","v1.0"},
            {"softwareVersion",heartbeat_info.soft_ver},
            {"hardwareVersion","V1.0"}
        };
        req =  mqtt_j.dump();
        return RET_OK;
    }
    catch(const std::exception& e)
    {
        WLE("catch exception %s ",e.what()); 
        return RET_FAIL;
    }    
}

int tianan_mqtt_protocol::build_mqtt_public_msg(nlohmann::json &asn_json, string & mqtt_json)
{
#if 0
////////////////////////////////////////////////////
// gv 
{
	"direct": "recv",
	"type": "asn",
	"msg": "bsm",
	"value": "002BCCEECDEC4EA626060539386E880635DC290A8273E00000001EDDF41FDFFFC04C91F5A01E00",
	"aid": 111,
	"verify_sign_errcode": 0
}
////////////////////////////////////////////////////
// hw 
{
	"rsuId" : "rsu00001" ,
	"timestamp" : "1608703317000" ,
    " seqNum " : "123456" ,
    " ack " : true,
    " length " : 58 ,
    " rsiDatas " : "200636964695f52534db5a4e9016b49d201100300020004600c87181ce1264f064  012c0f6390"
}

{
    "rsuId" : "rsu00001" ,
    "timestamp" : "1608703317000" ,
    "seqNum" : "123456" ,
    "ack" : true,
    "length" : 58 ,
    "spatDatas" : "200636964695f52534db5a4e9016b49d201100300020004600c87181ce1264f064  012c0f6390"
}
{
"rsuId" : "rsu00001" ,
"timestamp" : "1608703317000" ,
"seqNum" : "123456" ,
"ack" : true,
"length" : 58 ,
"rsmDatas" : "200636964695f52534db5a4e9016b49d201100300020004600c87181ce1264f064  012c0f6390"
}
#endif
    try
    {
        string data =  asn_json["value"].get<string>(); 
        string msg_key = asn_json["msg"].get<string>()+"Datas";

        auto mqtt_j = nlohmann::json{
            {"rsuId",m_rsu_id},
            {"timestamp",ENGINE_MODULE_NAMESPACE::get_ms()},
            {"seqNum",get_mqtt_seqnum()},
            {"length",data.length()},
            {msg_key,data},
            {"ack",false}
        };
        mqtt_json =  mqtt_j.dump(); 
        return RET_OK;
    }
    catch(exception & e)
    {
        WLE(" catch exception %s ",e.what()); 
        return RET_FAIL; 
    }

}


int tianan_mqtt_protocol::fetch_rsi_data(const char* pdata, uint32_t len,ta_down_mqtt_data & rsi_data)
{
    try
    {
        auto j = json::parse(pdata);
        if(j.end() == j.find("rsiDatas"))
        {
            WLE(" lost rsiDatas key %s ",pdata); 
            return RET_FAIL;
        }
        rsi_data.start_time =  j["startTime"].get<uint64_t>(); 
        rsi_data.end_time = j["endTime"].get<uint64_t>(); 
        rsi_data.status = j["status"].get<int>();
        rsi_data.data = j["rsiDatas"].get<string>(); 

        rsi_data.ack =  false;
        GET_JSON_OPTION(j,"ack",bool,rsi_data.ack); 
        GET_JSON_OPTION(j,"seqNum",string,rsi_data.seqNum); 
        return asn_decode(rsi_data); 
    }
    catch(exception & e)
    {
        WLE("parse json error %s errdesc %s ", pdata,e.what());
        return RET_FAIL;
    }
    return RET_OK;
}

int tianan_mqtt_protocol::fetch_map_data(const char* pdata, uint32_t len,ta_down_mqtt_data & map_data)
{
    try
    {
        auto j = json::parse(pdata);
        if(j.end() == j.find("mapDatas"))
        {
            WLE(" lost mapDatas key %s ",pdata); 
            return RET_FAIL;
        }
        //map_data.slice =  j["mapSlice"].get<string>(); 
        map_data.status = j["status"].get<int>();
        map_data.data = j["mapDatas"].get<string>(); 
        map_data.ack =  false;
        GET_JSON_OPTION(j,"ack",bool,map_data.ack); 
        GET_JSON_OPTION(j,"seqNum",string,map_data.seqNum); 
        return asn_decode(map_data); 
    }

    catch(exception & e)
    {
        WLE("parse json error %s errdesc %s ", pdata,e.what());
        return RET_FAIL;
    }    
}

int tianan_mqtt_protocol::fetch_conf_data(const char * pdata, uint32_t len , ta_down_conf_data & ta_conf)
{
    try
    {
        auto j = json::parse(pdata);
        GET_JSON_OPTION(j,"HBRate",int,ta_conf.HBRate); 
        GET_JSON_OPTION(j,"runningInfoRate",int,ta_conf.runningInfoRate); 
        GET_JSON_OPTION(j,"logLevel",string,ta_conf.logLevel); 
        GET_JSON_OPTION(j,"reboot",int,ta_conf.reboot); 
        GET_JSON_OPTION(j,"extendConfig",string,ta_conf.extendConfig); 
        GET_JSON_OPTION(j,"ack",bool,ta_conf.ack);
        GET_JSON_OPTION(j,"seqNum",string,ta_conf.seqNum); 
        if(j.end()!= j.find("runningInfoRate"))
        {
            ta_conf.addressChg.cssUrl = j["runningInfoRate"]["cssUrl"].get<string>(); 
            ta_conf.addressChg.time = j["runningInfoRate"]["time"].get<int64_t>(); 
        }
        
        return RET_OK;
    }
    catch(const std::exception& e)
    {
        WLE("parse json error %s errdesc %s ", pdata,e.what());
        return RET_FAIL;
    }

}
int tianan_mqtt_protocol::fetch_upgrade_data(const char *pdata, uint32_t len,ta_upgrade_data & ta_upgrade)
{
    try
    {
        auto j = json::parse(pdata);
        GET_JSON_OPTION(j,"seqNum",string,ta_upgrade.seqNum); 
        GET_JSON_OPTION(j,"ack",bool,ta_upgrade.ack);
        GET_JSON_OPTION(j,"softwareVersion",string,ta_upgrade.softwareVersion);
        GET_JSON_OPTION(j,"updateVersion",string,ta_upgrade.updateVersion); 
        GET_JSON_OPTION(j,"downloadUrl",string,ta_upgrade.downloadUrl); 
        GET_JSON_OPTION(j,"OTAUserId",string,ta_upgrade.OTAUserId); 
        GET_JSON_OPTION(j,"OTApassword",string,ta_upgrade.OTApassword); 
        GET_JSON_OPTION(j,"downloadMd5",string,ta_upgrade.downloadMd5); 
        GET_JSON_OPTION(j,"updateTime",int64_t,ta_upgrade.updateTime); 
        GET_JSON_OPTION(j,"authMode",int,ta_upgrade.authMode); 
        GET_JSON_OPTION(j,"token",string,ta_upgrade.token); 
        return RET_OK;
    }

    catch(const std::exception& e)
    {
        WLE("parse json error %s errdesc %s ", pdata,e.what());
        return RET_FAIL;
    }
}


string tianan_mqtt_protocol::get_mqtt_seqnum()
{
    static atomic<uint32_t> seq{0}; 
    auto i =  seq.fetch_add(1); 
    return std::to_string(i); 
}

int tianan_mqtt_protocol::asn_decode(ta_down_mqtt_data & down_data)
{
    string asn_bin_data;
    asn_bin_data.resize(down_data.data.length()/2); 
    m_hex_str.decode((unsigned char*)down_data.data.c_str(),down_data.data.length(),(unsigned char*)asn_bin_data.data());
    asn_dec_rval_t rval = uper_decode(NULL,&asn_DEF_MessageFrame,
                                        (void **)&down_data.asn_v2x_frame,
                                        asn_bin_data.c_str(),asn_bin_data.size(),0,0
                                        );
    if(rval.code != RC_OK )
    {
        WLE("uper_decode error rval %d hex_data %s ",rval.code,down_data.data.c_str());
        return RET_FAIL;
    }

    if(true==config_ref.is_enable(CK_SYS_PRINT_PROTOCOL))
    {
        asn_fprint(stdout, &asn_DEF_MessageFrame, down_data.asn_v2x_frame);
    }

    return RET_OK;
}
