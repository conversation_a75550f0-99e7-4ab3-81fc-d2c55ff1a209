/***********************************************
版本信息： 金溢科技版本权所有，保留一切权利
文件名称： tianan_mec_client.h
作者：张明
开始日期：2024-01-29 10:49:41
完成日期：
当前版本号: v 0.0.1
主要功能: 
版本历史:
***********************************************/

#ifndef _TIANAN_MEC_CLIENT_H_
#define _TIANAN_MEC_CLIENT_H_
#include "imec_client.h"
#include "command_pattern.hpp"
#include "tianan_mec_protocol.h"
#include "status_control.hpp"
#include "mec_protocol/perception.pb.h"
using namespace perception; 

class tianan_mec_client:public imec_client, public ireceiver
{
    typedef status_control<tianan_mec_client,status_enum> status_control_type;
    typedef status_control_type::status_info  status_info;

public:
	tianan_mec_client();
	virtual ~tianan_mec_client();
    void ontime_check(long long cur_ms);

public: //imec_client interface ;
    virtual int init() override;
    virtual void stop() override;
    virtual void destory() override;
    virtual int get_mec_status() override; 
public://ireceiver interface; 
	virtual void read_cb(engine_conn_handler & handler, const char* msg, int len) override;
    virtual void event_cb(int fd, short events ) override;
public:
    int proc_target_msg(const PerceptionMsg::MsgTypeCase & msgType, PerceptionMsg & msg ); 
    int proc_event_msg(const PerceptionMsg::MsgTypeCase & msgType, PerceptionMsg & msg ); 
    int proc_heartbeat_msg(const PerceptionMsg::MsgTypeCase & msgType, PerceptionMsg & msg ); 
    int proc_devinfo_msg(const PerceptionMsg::MsgTypeCase & msgType, PerceptionMsg & msg ); 
    int proc_roadcount_msg(const PerceptionMsg::MsgTypeCase & msgType, PerceptionMsg & msg ); 
    int proc_roadinfo_msg(const PerceptionMsg::MsgTypeCase & msgType, PerceptionMsg & msg ); 

private:
    // 协议状态管理-函数
    int proc_status_disconnect(status_info & status);
    int proc_status_connected(status_info & status);
    int proc_status_heartbeat_send(status_info & status);


private:
    status_control_type m_status_control; // 状态管理类

	engine_conn_handler m_mec_handler;
    command_pattern<PerceptionMsg::MsgTypeCase,PerceptionMsg> m_pb_commander; 
    tianan_mec_protocol m_protocol; 

};
#endif //_TIANAN_MEC_CLIENT_H_
