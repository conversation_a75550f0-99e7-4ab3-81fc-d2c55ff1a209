#include "tianan_platform_client.h"
#include "stdafx.h"

tianan_platform_client::tianan_platform_client()
{
    auto fun = std::bind(&tianan_mqtt_client::on_http_push_server_info_cb,&m_tianan_mqtt_client,std::placeholders::_1,std::placeholders::_2,std::placeholders::_3); 
    m_tianan_http_client.regist_push_server_info(fun); 
}

tianan_platform_client::~tianan_platform_client()
{
	
}
	

int tianan_platform_client::init()
{
    auto  ret_code =  m_tianan_mqtt_client.init(); 
    
    if(ret_code != RET_OK )
    {
        WLE("m_tianan_mqtt_client.init error ret_code %d ",ret_code); 
        return ret_code;
    }    

    ret_code = m_tianan_http_client.init(); 
    if(ret_code != RET_OK )
    {
        WLE("m_tianan_http_client.init error ret_code %d ",ret_code); 
        return ret_code;
    }
    
    return RET_OK;
}

int tianan_platform_client::destory()
{
    m_tianan_http_client.destory();
    m_tianan_mqtt_client.destory();
    return RET_OK;
}

void tianan_platform_client::ontime_check(long long cur_ms)
{
   m_tianan_http_client.ontime_check(cur_ms); 
   m_tianan_mqtt_client.ontime_check(cur_ms); 
}


void tianan_platform_client::get_cloud_platform_id(string &id)
{
    
}
// 云平台连接状态，0：未连接  非0:连接
int tianan_platform_client::get_cloud_platform_status()
{
    return 1; 
}
