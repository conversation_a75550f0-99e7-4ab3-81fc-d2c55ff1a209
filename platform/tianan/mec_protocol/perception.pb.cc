// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: perception.proto

#include "perception.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG

namespace _pb = ::PROTOBUF_NAMESPACE_ID;
namespace _pbi = _pb::internal;

namespace perception {
PROTOBUF_CONSTEXPR Position::Position(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.lat_)*/int64_t{0}
  , /*decltype(_impl_.lon_)*/int64_t{0}
  , /*decltype(_impl_.elev_)*/0
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct PositionDefaultTypeInternal {
  PROTOBUF_CONSTEXPR PositionDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~PositionDefaultTypeInternal() {}
  union {
    Position _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 PositionDefaultTypeInternal _Position_default_instance_;
PROTOBUF_CONSTEXPR Size::Size(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.length_)*/0
  , /*decltype(_impl_.width_)*/0
  , /*decltype(_impl_.height_)*/0
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct SizeDefaultTypeInternal {
  PROTOBUF_CONSTEXPR SizeDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~SizeDefaultTypeInternal() {}
  union {
    Size _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 SizeDefaultTypeInternal _Size_default_instance_;
PROTOBUF_CONSTEXPR LaneInfo::LaneInfo(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.area_type_)*/0
  , /*decltype(_impl_.lane_count_)*/0
  , /*decltype(_impl_.lane_heading_)*/0
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct LaneInfoDefaultTypeInternal {
  PROTOBUF_CONSTEXPR LaneInfoDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~LaneInfoDefaultTypeInternal() {}
  union {
    LaneInfo _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 LaneInfoDefaultTypeInternal _LaneInfo_default_instance_;
PROTOBUF_CONSTEXPR Target::Target(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.license_plate_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.pos_)*/nullptr
  , /*decltype(_impl_.size_)*/nullptr
  , /*decltype(_impl_.lane_info_)*/nullptr
  , /*decltype(_impl_.id_)*/int64_t{0}
  , /*decltype(_impl_.source_)*/0
  , /*decltype(_impl_.confidence_)*/0
  , /*decltype(_impl_.type_)*/0
  , /*decltype(_impl_.speed_)*/0
  , /*decltype(_impl_.heading_)*/0
  , /*decltype(_impl_.lane_id_)*/0
  , /*decltype(_impl_.car_type_)*/0
  , /*decltype(_impl_.color_)*/0
  , /*decltype(_impl_.status_)*/0
  , /*decltype(_impl_.distance_)*/0
  , /*decltype(_impl_.track_time_)*/int64_t{0}
  , /*decltype(_impl_.rcs_)*/0
  , /*decltype(_impl_.vehicle_pose_)*/0
  , /*decltype(_impl_.vehicle_type_)*/0
  , /*decltype(_impl_.vehicle_marker_)*/0
  , /*decltype(_impl_.vehicle_other_)*/0
  , /*decltype(_impl_.vehicle_roof_)*/0
  , /*decltype(_impl_.vehicle_carcolor_)*/0
  , /*decltype(_impl_.vehicle_make_)*/0
  , /*decltype(_impl_.vehicle_model_)*/0
  , /*decltype(_impl_.vehicle_year_)*/0
  , /*decltype(_impl_.vehicle_plate_color_)*/0
  , /*decltype(_impl_.vehicle_plate_type_)*/0
  , /*decltype(_impl_.face_age_)*/0
  , /*decltype(_impl_.face_gender_)*/0
  , /*decltype(_impl_.face_mask_)*/0
  , /*decltype(_impl_.face_hat_)*/0
  , /*decltype(_impl_.face_glass_)*/0
  , /*decltype(_impl_.pedestrian_shoecolor_)*/0
  , /*decltype(_impl_.pedestrian_shoestyle_)*/0
  , /*decltype(_impl_.pedestrian_sleeve_)*/0
  , /*decltype(_impl_.pedestrian_hair_)*/0
  , /*decltype(_impl_.pedestrian_nation_)*/0
  , /*decltype(_impl_.pedestrian_gender_)*/0
  , /*decltype(_impl_.pedestrian_lowerstyle_)*/0
  , /*decltype(_impl_.pedestrian_upperstyle_)*/0
  , /*decltype(_impl_.pedestrian_age_)*/0
  , /*decltype(_impl_.pedestrian_lower_)*/0
  , /*decltype(_impl_.pedestrian_upper_)*/0
  , /*decltype(_impl_.pedestrian_backpack_)*/0
  , /*decltype(_impl_.nonmotor_attitude_)*/0
  , /*decltype(_impl_.nonmotor_gender_)*/0
  , /*decltype(_impl_.nonmotor_nation_)*/0
  , /*decltype(_impl_.nonmotor_transportation_)*/0
  , /*decltype(_impl_.nonmotor_uppercolor_)*/0
  , /*decltype(_impl_.nonmotor_upper_)*/0
  , /*decltype(_impl_.nonmotor_transportationcolor_)*/0
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct TargetDefaultTypeInternal {
  PROTOBUF_CONSTEXPR TargetDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~TargetDefaultTypeInternal() {}
  union {
    Target _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 TargetDefaultTypeInternal _Target_default_instance_;
PROTOBUF_CONSTEXPR Targets::Targets(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.array_)*/{}
  , /*decltype(_impl_.device_sn_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.device_ip_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.fusion_state_)*/0
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct TargetsDefaultTypeInternal {
  PROTOBUF_CONSTEXPR TargetsDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~TargetsDefaultTypeInternal() {}
  union {
    Targets _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 TargetsDefaultTypeInternal _Targets_default_instance_;
PROTOBUF_CONSTEXPR AreaInfo::AreaInfo(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.dot_)*/{}
  , /*decltype(_impl_.id_)*/0
  , /*decltype(_impl_.width_)*/0
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct AreaInfoDefaultTypeInternal {
  PROTOBUF_CONSTEXPR AreaInfoDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~AreaInfoDefaultTypeInternal() {}
  union {
    AreaInfo _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 AreaInfoDefaultTypeInternal _AreaInfo_default_instance_;
PROTOBUF_CONSTEXPR LaneCount::LaneCount(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.laneno_)*/0
  , /*decltype(_impl_.volume_)*/0
  , /*decltype(_impl_.pcu_)*/0
  , /*decltype(_impl_.avspeed_)*/0
  , /*decltype(_impl_.occupancy_)*/0
  , /*decltype(_impl_.headway_)*/0
  , /*decltype(_impl_.gap_)*/0
  , /*decltype(_impl_.avdelay_)*/0
  , /*decltype(_impl_.avstop_)*/0
  , /*decltype(_impl_.speed85_)*/0
  , /*decltype(_impl_.queuelength_)*/0
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct LaneCountDefaultTypeInternal {
  PROTOBUF_CONSTEXPR LaneCountDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~LaneCountDefaultTypeInternal() {}
  union {
    LaneCount _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 LaneCountDefaultTypeInternal _LaneCount_default_instance_;
PROTOBUF_CONSTEXPR RoadCount::RoadCount(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.lanecount_)*/{}
  , /*decltype(_impl_.deviceid_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.roadname_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.roadincoord_)*/nullptr
  , /*decltype(_impl_.heading_)*/0
  , /*decltype(_impl_.volume_)*/0
  , /*decltype(_impl_.pcu_)*/0
  , /*decltype(_impl_.avspeed_)*/0
  , /*decltype(_impl_.occupancy_)*/0
  , /*decltype(_impl_.headway_)*/0
  , /*decltype(_impl_.gap_)*/0
  , /*decltype(_impl_.avdelay_)*/0
  , /*decltype(_impl_.avstop_)*/0
  , /*decltype(_impl_.speed85_)*/0
  , /*decltype(_impl_.queuelength_)*/0
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct RoadCountDefaultTypeInternal {
  PROTOBUF_CONSTEXPR RoadCountDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~RoadCountDefaultTypeInternal() {}
  union {
    RoadCount _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 RoadCountDefaultTypeInternal _RoadCount_default_instance_;
PROTOBUF_CONSTEXPR IntersectionCount::IntersectionCount(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.roadcount_)*/{}
  , /*decltype(_impl_.volume_)*/0
  , /*decltype(_impl_.pcu_)*/0
  , /*decltype(_impl_.avspeed_)*/0
  , /*decltype(_impl_.occupancy_)*/0
  , /*decltype(_impl_.headway_)*/0
  , /*decltype(_impl_.gap_)*/0
  , /*decltype(_impl_.avdelay_)*/0
  , /*decltype(_impl_.avstop_)*/0
  , /*decltype(_impl_.speed85_)*/0
  , /*decltype(_impl_.queuelength_)*/0
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct IntersectionCountDefaultTypeInternal {
  PROTOBUF_CONSTEXPR IntersectionCountDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~IntersectionCountDefaultTypeInternal() {}
  union {
    IntersectionCount _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 IntersectionCountDefaultTypeInternal _IntersectionCount_default_instance_;
PROTOBUF_CONSTEXPR Event::Event(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.pos_)*/{}
  , /*decltype(_impl_.refpath_)*/{}
  , /*decltype(_impl_.device_sn_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.license_plate_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.event_type_)*/0
  , /*decltype(_impl_.level_)*/0
  , /*decltype(_impl_.lane_id_)*/0
  , /*decltype(_impl_.fusion_state_)*/0
  , /*decltype(_impl_.track_time_)*/int64_t{0}
  , /*decltype(_impl_.ttl_)*/int64_t{0}
  , /*decltype(_impl_.source_)*/0
  , /*decltype(_impl_.DataType_)*/{}
  , /*decltype(_impl_._cached_size_)*/{}
  , /*decltype(_impl_._oneof_case_)*/{}} {}
struct EventDefaultTypeInternal {
  PROTOBUF_CONSTEXPR EventDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~EventDefaultTypeInternal() {}
  union {
    Event _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 EventDefaultTypeInternal _Event_default_instance_;
PROTOBUF_CONSTEXPR Events::Events(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.event_)*/{}
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct EventsDefaultTypeInternal {
  PROTOBUF_CONSTEXPR EventsDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~EventsDefaultTypeInternal() {}
  union {
    Events _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 EventsDefaultTypeInternal _Events_default_instance_;
PROTOBUF_CONSTEXPR HeartBeat::HeartBeat(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.err_code_)*/{}
  , /*decltype(_impl_._err_code_cached_byte_size_)*/{0}
  , /*decltype(_impl_.device_sn_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.time_)*/int64_t{0}
  , /*decltype(_impl_.seq_count_)*/0
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct HeartBeatDefaultTypeInternal {
  PROTOBUF_CONSTEXPR HeartBeatDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~HeartBeatDefaultTypeInternal() {}
  union {
    HeartBeat _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 HeartBeatDefaultTypeInternal _HeartBeat_default_instance_;
PROTOBUF_CONSTEXPR DeviceInfo::DeviceInfo(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.perceptual_area_)*/{}
  , /*decltype(_impl_.device_sn_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.description_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.location_)*/nullptr
  , /*decltype(_impl_.orientation_)*/0
  , /*decltype(_impl_.type_)*/0
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct DeviceInfoDefaultTypeInternal {
  PROTOBUF_CONSTEXPR DeviceInfoDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~DeviceInfoDefaultTypeInternal() {}
  union {
    DeviceInfo _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 DeviceInfoDefaultTypeInternal _DeviceInfo_default_instance_;
PROTOBUF_CONSTEXPR RoadCounts::RoadCounts(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.rsuid_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.seqnum_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.rsuesn_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.protocolversion_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.intersectioncount_)*/nullptr
  , /*decltype(_impl_.cycle_)*/0
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct RoadCountsDefaultTypeInternal {
  PROTOBUF_CONSTEXPR RoadCountsDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~RoadCountsDefaultTypeInternal() {}
  union {
    RoadCounts _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 RoadCountsDefaultTypeInternal _RoadCounts_default_instance_;
PROTOBUF_CONSTEXPR LaneCountInfo::LaneCountInfo(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.laneno_)*/0
  , /*decltype(_impl_.volume1_)*/0
  , /*decltype(_impl_.volume2_)*/0
  , /*decltype(_impl_.volume3_)*/0
  , /*decltype(_impl_.volume4_)*/0
  , /*decltype(_impl_.volume5_)*/0
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct LaneCountInfoDefaultTypeInternal {
  PROTOBUF_CONSTEXPR LaneCountInfoDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~LaneCountInfoDefaultTypeInternal() {}
  union {
    LaneCountInfo _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 LaneCountInfoDefaultTypeInternal _LaneCountInfo_default_instance_;
PROTOBUF_CONSTEXPR RoadCountInfo::RoadCountInfo(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.lanecount_)*/{}
  , /*decltype(_impl_.deviceid_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.roadname_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.roadincoord_)*/nullptr
  , /*decltype(_impl_.heading_)*/0
  , /*decltype(_impl_.volume1_)*/0
  , /*decltype(_impl_.volume2_)*/0
  , /*decltype(_impl_.volume3_)*/0
  , /*decltype(_impl_.volume4_)*/0
  , /*decltype(_impl_.volume5_)*/0
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct RoadCountInfoDefaultTypeInternal {
  PROTOBUF_CONSTEXPR RoadCountInfoDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~RoadCountInfoDefaultTypeInternal() {}
  union {
    RoadCountInfo _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 RoadCountInfoDefaultTypeInternal _RoadCountInfo_default_instance_;
PROTOBUF_CONSTEXPR IntersectionInfo::IntersectionInfo(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.roadcount_)*/{}
  , /*decltype(_impl_.volume1_)*/0
  , /*decltype(_impl_.volume2_)*/0
  , /*decltype(_impl_.volume3_)*/0
  , /*decltype(_impl_.volume4_)*/0
  , /*decltype(_impl_.volume5_)*/0
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct IntersectionInfoDefaultTypeInternal {
  PROTOBUF_CONSTEXPR IntersectionInfoDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~IntersectionInfoDefaultTypeInternal() {}
  union {
    IntersectionInfo _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 IntersectionInfoDefaultTypeInternal _IntersectionInfo_default_instance_;
PROTOBUF_CONSTEXPR RoadInfo::RoadInfo(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.rsuid_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.seqnum_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.rsuesn_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.protocolversion_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.intersectioninfo_)*/nullptr
  , /*decltype(_impl_.cycle_)*/0
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct RoadInfoDefaultTypeInternal {
  PROTOBUF_CONSTEXPR RoadInfoDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~RoadInfoDefaultTypeInternal() {}
  union {
    RoadInfo _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 RoadInfoDefaultTypeInternal _RoadInfo_default_instance_;
PROTOBUF_CONSTEXPR PerceptionMsg::PerceptionMsg(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.time_)*/int64_t{0}
  , /*decltype(_impl_.MsgType_)*/{}
  , /*decltype(_impl_._cached_size_)*/{}
  , /*decltype(_impl_._oneof_case_)*/{}} {}
struct PerceptionMsgDefaultTypeInternal {
  PROTOBUF_CONSTEXPR PerceptionMsgDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~PerceptionMsgDefaultTypeInternal() {}
  union {
    PerceptionMsg _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 PerceptionMsgDefaultTypeInternal _PerceptionMsg_default_instance_;
}  // namespace perception
static ::_pb::Metadata file_level_metadata_perception_2eproto[19];
static constexpr ::_pb::EnumDescriptor const** file_level_enum_descriptors_perception_2eproto = nullptr;
static constexpr ::_pb::ServiceDescriptor const** file_level_service_descriptors_perception_2eproto = nullptr;

const uint32_t TableStruct_perception_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::perception::Position, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::perception::Position, _impl_.lat_),
  PROTOBUF_FIELD_OFFSET(::perception::Position, _impl_.lon_),
  PROTOBUF_FIELD_OFFSET(::perception::Position, _impl_.elev_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::perception::Size, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::perception::Size, _impl_.length_),
  PROTOBUF_FIELD_OFFSET(::perception::Size, _impl_.width_),
  PROTOBUF_FIELD_OFFSET(::perception::Size, _impl_.height_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::perception::LaneInfo, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::perception::LaneInfo, _impl_.area_type_),
  PROTOBUF_FIELD_OFFSET(::perception::LaneInfo, _impl_.lane_count_),
  PROTOBUF_FIELD_OFFSET(::perception::LaneInfo, _impl_.lane_heading_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::perception::Target, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::perception::Target, _impl_.id_),
  PROTOBUF_FIELD_OFFSET(::perception::Target, _impl_.source_),
  PROTOBUF_FIELD_OFFSET(::perception::Target, _impl_.pos_),
  PROTOBUF_FIELD_OFFSET(::perception::Target, _impl_.size_),
  PROTOBUF_FIELD_OFFSET(::perception::Target, _impl_.confidence_),
  PROTOBUF_FIELD_OFFSET(::perception::Target, _impl_.type_),
  PROTOBUF_FIELD_OFFSET(::perception::Target, _impl_.speed_),
  PROTOBUF_FIELD_OFFSET(::perception::Target, _impl_.heading_),
  PROTOBUF_FIELD_OFFSET(::perception::Target, _impl_.lane_id_),
  PROTOBUF_FIELD_OFFSET(::perception::Target, _impl_.lane_info_),
  PROTOBUF_FIELD_OFFSET(::perception::Target, _impl_.license_plate_),
  PROTOBUF_FIELD_OFFSET(::perception::Target, _impl_.car_type_),
  PROTOBUF_FIELD_OFFSET(::perception::Target, _impl_.color_),
  PROTOBUF_FIELD_OFFSET(::perception::Target, _impl_.status_),
  PROTOBUF_FIELD_OFFSET(::perception::Target, _impl_.distance_),
  PROTOBUF_FIELD_OFFSET(::perception::Target, _impl_.rcs_),
  PROTOBUF_FIELD_OFFSET(::perception::Target, _impl_.track_time_),
  PROTOBUF_FIELD_OFFSET(::perception::Target, _impl_.vehicle_pose_),
  PROTOBUF_FIELD_OFFSET(::perception::Target, _impl_.vehicle_type_),
  PROTOBUF_FIELD_OFFSET(::perception::Target, _impl_.vehicle_marker_),
  PROTOBUF_FIELD_OFFSET(::perception::Target, _impl_.vehicle_other_),
  PROTOBUF_FIELD_OFFSET(::perception::Target, _impl_.vehicle_roof_),
  PROTOBUF_FIELD_OFFSET(::perception::Target, _impl_.vehicle_carcolor_),
  PROTOBUF_FIELD_OFFSET(::perception::Target, _impl_.vehicle_make_),
  PROTOBUF_FIELD_OFFSET(::perception::Target, _impl_.vehicle_model_),
  PROTOBUF_FIELD_OFFSET(::perception::Target, _impl_.vehicle_year_),
  PROTOBUF_FIELD_OFFSET(::perception::Target, _impl_.vehicle_plate_color_),
  PROTOBUF_FIELD_OFFSET(::perception::Target, _impl_.vehicle_plate_type_),
  PROTOBUF_FIELD_OFFSET(::perception::Target, _impl_.face_age_),
  PROTOBUF_FIELD_OFFSET(::perception::Target, _impl_.face_gender_),
  PROTOBUF_FIELD_OFFSET(::perception::Target, _impl_.face_mask_),
  PROTOBUF_FIELD_OFFSET(::perception::Target, _impl_.face_hat_),
  PROTOBUF_FIELD_OFFSET(::perception::Target, _impl_.face_glass_),
  PROTOBUF_FIELD_OFFSET(::perception::Target, _impl_.pedestrian_shoecolor_),
  PROTOBUF_FIELD_OFFSET(::perception::Target, _impl_.pedestrian_shoestyle_),
  PROTOBUF_FIELD_OFFSET(::perception::Target, _impl_.pedestrian_sleeve_),
  PROTOBUF_FIELD_OFFSET(::perception::Target, _impl_.pedestrian_hair_),
  PROTOBUF_FIELD_OFFSET(::perception::Target, _impl_.pedestrian_nation_),
  PROTOBUF_FIELD_OFFSET(::perception::Target, _impl_.pedestrian_gender_),
  PROTOBUF_FIELD_OFFSET(::perception::Target, _impl_.pedestrian_lowerstyle_),
  PROTOBUF_FIELD_OFFSET(::perception::Target, _impl_.pedestrian_upperstyle_),
  PROTOBUF_FIELD_OFFSET(::perception::Target, _impl_.pedestrian_age_),
  PROTOBUF_FIELD_OFFSET(::perception::Target, _impl_.pedestrian_lower_),
  PROTOBUF_FIELD_OFFSET(::perception::Target, _impl_.pedestrian_upper_),
  PROTOBUF_FIELD_OFFSET(::perception::Target, _impl_.pedestrian_backpack_),
  PROTOBUF_FIELD_OFFSET(::perception::Target, _impl_.nonmotor_attitude_),
  PROTOBUF_FIELD_OFFSET(::perception::Target, _impl_.nonmotor_gender_),
  PROTOBUF_FIELD_OFFSET(::perception::Target, _impl_.nonmotor_nation_),
  PROTOBUF_FIELD_OFFSET(::perception::Target, _impl_.nonmotor_transportation_),
  PROTOBUF_FIELD_OFFSET(::perception::Target, _impl_.nonmotor_uppercolor_),
  PROTOBUF_FIELD_OFFSET(::perception::Target, _impl_.nonmotor_upper_),
  PROTOBUF_FIELD_OFFSET(::perception::Target, _impl_.nonmotor_transportationcolor_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::perception::Targets, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::perception::Targets, _impl_.device_sn_),
  PROTOBUF_FIELD_OFFSET(::perception::Targets, _impl_.device_ip_),
  PROTOBUF_FIELD_OFFSET(::perception::Targets, _impl_.fusion_state_),
  PROTOBUF_FIELD_OFFSET(::perception::Targets, _impl_.array_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::perception::AreaInfo, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::perception::AreaInfo, _impl_.id_),
  PROTOBUF_FIELD_OFFSET(::perception::AreaInfo, _impl_.width_),
  PROTOBUF_FIELD_OFFSET(::perception::AreaInfo, _impl_.dot_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::perception::LaneCount, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::perception::LaneCount, _impl_.laneno_),
  PROTOBUF_FIELD_OFFSET(::perception::LaneCount, _impl_.volume_),
  PROTOBUF_FIELD_OFFSET(::perception::LaneCount, _impl_.pcu_),
  PROTOBUF_FIELD_OFFSET(::perception::LaneCount, _impl_.avspeed_),
  PROTOBUF_FIELD_OFFSET(::perception::LaneCount, _impl_.occupancy_),
  PROTOBUF_FIELD_OFFSET(::perception::LaneCount, _impl_.headway_),
  PROTOBUF_FIELD_OFFSET(::perception::LaneCount, _impl_.gap_),
  PROTOBUF_FIELD_OFFSET(::perception::LaneCount, _impl_.avdelay_),
  PROTOBUF_FIELD_OFFSET(::perception::LaneCount, _impl_.avstop_),
  PROTOBUF_FIELD_OFFSET(::perception::LaneCount, _impl_.speed85_),
  PROTOBUF_FIELD_OFFSET(::perception::LaneCount, _impl_.queuelength_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::perception::RoadCount, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::perception::RoadCount, _impl_.deviceid_),
  PROTOBUF_FIELD_OFFSET(::perception::RoadCount, _impl_.heading_),
  PROTOBUF_FIELD_OFFSET(::perception::RoadCount, _impl_.roadname_),
  PROTOBUF_FIELD_OFFSET(::perception::RoadCount, _impl_.roadincoord_),
  PROTOBUF_FIELD_OFFSET(::perception::RoadCount, _impl_.volume_),
  PROTOBUF_FIELD_OFFSET(::perception::RoadCount, _impl_.pcu_),
  PROTOBUF_FIELD_OFFSET(::perception::RoadCount, _impl_.avspeed_),
  PROTOBUF_FIELD_OFFSET(::perception::RoadCount, _impl_.occupancy_),
  PROTOBUF_FIELD_OFFSET(::perception::RoadCount, _impl_.headway_),
  PROTOBUF_FIELD_OFFSET(::perception::RoadCount, _impl_.gap_),
  PROTOBUF_FIELD_OFFSET(::perception::RoadCount, _impl_.avdelay_),
  PROTOBUF_FIELD_OFFSET(::perception::RoadCount, _impl_.avstop_),
  PROTOBUF_FIELD_OFFSET(::perception::RoadCount, _impl_.speed85_),
  PROTOBUF_FIELD_OFFSET(::perception::RoadCount, _impl_.queuelength_),
  PROTOBUF_FIELD_OFFSET(::perception::RoadCount, _impl_.lanecount_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::perception::IntersectionCount, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::perception::IntersectionCount, _impl_.volume_),
  PROTOBUF_FIELD_OFFSET(::perception::IntersectionCount, _impl_.pcu_),
  PROTOBUF_FIELD_OFFSET(::perception::IntersectionCount, _impl_.avspeed_),
  PROTOBUF_FIELD_OFFSET(::perception::IntersectionCount, _impl_.occupancy_),
  PROTOBUF_FIELD_OFFSET(::perception::IntersectionCount, _impl_.headway_),
  PROTOBUF_FIELD_OFFSET(::perception::IntersectionCount, _impl_.gap_),
  PROTOBUF_FIELD_OFFSET(::perception::IntersectionCount, _impl_.avdelay_),
  PROTOBUF_FIELD_OFFSET(::perception::IntersectionCount, _impl_.avstop_),
  PROTOBUF_FIELD_OFFSET(::perception::IntersectionCount, _impl_.speed85_),
  PROTOBUF_FIELD_OFFSET(::perception::IntersectionCount, _impl_.queuelength_),
  PROTOBUF_FIELD_OFFSET(::perception::IntersectionCount, _impl_.roadcount_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::perception::Event, _internal_metadata_),
  ~0u,  // no _extensions_
  PROTOBUF_FIELD_OFFSET(::perception::Event, _impl_._oneof_case_[0]),
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::perception::Event, _impl_.event_type_),
  PROTOBUF_FIELD_OFFSET(::perception::Event, _impl_.level_),
  PROTOBUF_FIELD_OFFSET(::perception::Event, _impl_.device_sn_),
  PROTOBUF_FIELD_OFFSET(::perception::Event, _impl_.lane_id_),
  PROTOBUF_FIELD_OFFSET(::perception::Event, _impl_.fusion_state_),
  PROTOBUF_FIELD_OFFSET(::perception::Event, _impl_.source_),
  PROTOBUF_FIELD_OFFSET(::perception::Event, _impl_.pos_),
  PROTOBUF_FIELD_OFFSET(::perception::Event, _impl_.license_plate_),
  PROTOBUF_FIELD_OFFSET(::perception::Event, _impl_.track_time_),
  PROTOBUF_FIELD_OFFSET(::perception::Event, _impl_.ttl_),
  PROTOBUF_FIELD_OFFSET(::perception::Event, _impl_.refpath_),
  ::_pbi::kInvalidFieldOffsetTag,
  ::_pbi::kInvalidFieldOffsetTag,
  PROTOBUF_FIELD_OFFSET(::perception::Event, _impl_.DataType_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::perception::Events, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::perception::Events, _impl_.event_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::perception::HeartBeat, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::perception::HeartBeat, _impl_.seq_count_),
  PROTOBUF_FIELD_OFFSET(::perception::HeartBeat, _impl_.time_),
  PROTOBUF_FIELD_OFFSET(::perception::HeartBeat, _impl_.device_sn_),
  PROTOBUF_FIELD_OFFSET(::perception::HeartBeat, _impl_.err_code_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::perception::DeviceInfo, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::perception::DeviceInfo, _impl_.device_sn_),
  PROTOBUF_FIELD_OFFSET(::perception::DeviceInfo, _impl_.location_),
  PROTOBUF_FIELD_OFFSET(::perception::DeviceInfo, _impl_.orientation_),
  PROTOBUF_FIELD_OFFSET(::perception::DeviceInfo, _impl_.type_),
  PROTOBUF_FIELD_OFFSET(::perception::DeviceInfo, _impl_.perceptual_area_),
  PROTOBUF_FIELD_OFFSET(::perception::DeviceInfo, _impl_.description_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::perception::RoadCounts, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::perception::RoadCounts, _impl_.rsuid_),
  PROTOBUF_FIELD_OFFSET(::perception::RoadCounts, _impl_.seqnum_),
  PROTOBUF_FIELD_OFFSET(::perception::RoadCounts, _impl_.rsuesn_),
  PROTOBUF_FIELD_OFFSET(::perception::RoadCounts, _impl_.protocolversion_),
  PROTOBUF_FIELD_OFFSET(::perception::RoadCounts, _impl_.cycle_),
  PROTOBUF_FIELD_OFFSET(::perception::RoadCounts, _impl_.intersectioncount_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::perception::LaneCountInfo, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::perception::LaneCountInfo, _impl_.laneno_),
  PROTOBUF_FIELD_OFFSET(::perception::LaneCountInfo, _impl_.volume1_),
  PROTOBUF_FIELD_OFFSET(::perception::LaneCountInfo, _impl_.volume2_),
  PROTOBUF_FIELD_OFFSET(::perception::LaneCountInfo, _impl_.volume3_),
  PROTOBUF_FIELD_OFFSET(::perception::LaneCountInfo, _impl_.volume4_),
  PROTOBUF_FIELD_OFFSET(::perception::LaneCountInfo, _impl_.volume5_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::perception::RoadCountInfo, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::perception::RoadCountInfo, _impl_.deviceid_),
  PROTOBUF_FIELD_OFFSET(::perception::RoadCountInfo, _impl_.heading_),
  PROTOBUF_FIELD_OFFSET(::perception::RoadCountInfo, _impl_.roadname_),
  PROTOBUF_FIELD_OFFSET(::perception::RoadCountInfo, _impl_.roadincoord_),
  PROTOBUF_FIELD_OFFSET(::perception::RoadCountInfo, _impl_.volume1_),
  PROTOBUF_FIELD_OFFSET(::perception::RoadCountInfo, _impl_.volume2_),
  PROTOBUF_FIELD_OFFSET(::perception::RoadCountInfo, _impl_.volume3_),
  PROTOBUF_FIELD_OFFSET(::perception::RoadCountInfo, _impl_.volume4_),
  PROTOBUF_FIELD_OFFSET(::perception::RoadCountInfo, _impl_.volume5_),
  PROTOBUF_FIELD_OFFSET(::perception::RoadCountInfo, _impl_.lanecount_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::perception::IntersectionInfo, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::perception::IntersectionInfo, _impl_.volume1_),
  PROTOBUF_FIELD_OFFSET(::perception::IntersectionInfo, _impl_.volume2_),
  PROTOBUF_FIELD_OFFSET(::perception::IntersectionInfo, _impl_.volume3_),
  PROTOBUF_FIELD_OFFSET(::perception::IntersectionInfo, _impl_.volume4_),
  PROTOBUF_FIELD_OFFSET(::perception::IntersectionInfo, _impl_.volume5_),
  PROTOBUF_FIELD_OFFSET(::perception::IntersectionInfo, _impl_.roadcount_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::perception::RoadInfo, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::perception::RoadInfo, _impl_.rsuid_),
  PROTOBUF_FIELD_OFFSET(::perception::RoadInfo, _impl_.seqnum_),
  PROTOBUF_FIELD_OFFSET(::perception::RoadInfo, _impl_.rsuesn_),
  PROTOBUF_FIELD_OFFSET(::perception::RoadInfo, _impl_.protocolversion_),
  PROTOBUF_FIELD_OFFSET(::perception::RoadInfo, _impl_.cycle_),
  PROTOBUF_FIELD_OFFSET(::perception::RoadInfo, _impl_.intersectioninfo_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::perception::PerceptionMsg, _internal_metadata_),
  ~0u,  // no _extensions_
  PROTOBUF_FIELD_OFFSET(::perception::PerceptionMsg, _impl_._oneof_case_[0]),
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::perception::PerceptionMsg, _impl_.time_),
  ::_pbi::kInvalidFieldOffsetTag,
  ::_pbi::kInvalidFieldOffsetTag,
  ::_pbi::kInvalidFieldOffsetTag,
  ::_pbi::kInvalidFieldOffsetTag,
  ::_pbi::kInvalidFieldOffsetTag,
  ::_pbi::kInvalidFieldOffsetTag,
  PROTOBUF_FIELD_OFFSET(::perception::PerceptionMsg, _impl_.MsgType_),
};
static const ::_pbi::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::perception::Position)},
  { 9, -1, -1, sizeof(::perception::Size)},
  { 18, -1, -1, sizeof(::perception::LaneInfo)},
  { 27, -1, -1, sizeof(::perception::Target)},
  { 85, -1, -1, sizeof(::perception::Targets)},
  { 95, -1, -1, sizeof(::perception::AreaInfo)},
  { 104, -1, -1, sizeof(::perception::LaneCount)},
  { 121, -1, -1, sizeof(::perception::RoadCount)},
  { 142, -1, -1, sizeof(::perception::IntersectionCount)},
  { 159, -1, -1, sizeof(::perception::Event)},
  { 179, -1, -1, sizeof(::perception::Events)},
  { 186, -1, -1, sizeof(::perception::HeartBeat)},
  { 196, -1, -1, sizeof(::perception::DeviceInfo)},
  { 208, -1, -1, sizeof(::perception::RoadCounts)},
  { 220, -1, -1, sizeof(::perception::LaneCountInfo)},
  { 232, -1, -1, sizeof(::perception::RoadCountInfo)},
  { 248, -1, -1, sizeof(::perception::IntersectionInfo)},
  { 260, -1, -1, sizeof(::perception::RoadInfo)},
  { 272, -1, -1, sizeof(::perception::PerceptionMsg)},
};

static const ::_pb::Message* const file_default_instances[] = {
  &::perception::_Position_default_instance_._instance,
  &::perception::_Size_default_instance_._instance,
  &::perception::_LaneInfo_default_instance_._instance,
  &::perception::_Target_default_instance_._instance,
  &::perception::_Targets_default_instance_._instance,
  &::perception::_AreaInfo_default_instance_._instance,
  &::perception::_LaneCount_default_instance_._instance,
  &::perception::_RoadCount_default_instance_._instance,
  &::perception::_IntersectionCount_default_instance_._instance,
  &::perception::_Event_default_instance_._instance,
  &::perception::_Events_default_instance_._instance,
  &::perception::_HeartBeat_default_instance_._instance,
  &::perception::_DeviceInfo_default_instance_._instance,
  &::perception::_RoadCounts_default_instance_._instance,
  &::perception::_LaneCountInfo_default_instance_._instance,
  &::perception::_RoadCountInfo_default_instance_._instance,
  &::perception::_IntersectionInfo_default_instance_._instance,
  &::perception::_RoadInfo_default_instance_._instance,
  &::perception::_PerceptionMsg_default_instance_._instance,
};

const char descriptor_table_protodef_perception_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\020perception.proto\022\nperception\"2\n\010Positi"
  "on\022\013\n\003lat\030\001 \001(\003\022\013\n\003lon\030\002 \001(\003\022\014\n\004elev\030\003 \001"
  "(\005\"5\n\004Size\022\016\n\006length\030\001 \001(\005\022\r\n\005width\030\002 \001("
  "\005\022\016\n\006height\030\003 \001(\005\"G\n\010LaneInfo\022\021\n\tarea_ty"
  "pe\030\001 \001(\005\022\022\n\nlane_count\030\002 \001(\005\022\024\n\014lane_hea"
  "ding\030\003 \001(\002\"\333\t\n\006Target\022\n\n\002id\030\001 \001(\003\022\016\n\006sou"
  "rce\030\002 \001(\005\022!\n\003pos\030\003 \001(\0132\024.perception.Posi"
  "tion\022\036\n\004size\030\004 \001(\0132\020.perception.Size\022\022\n\n"
  "confidence\030\005 \001(\002\022\014\n\004type\030\006 \001(\005\022\r\n\005speed\030"
  "\007 \001(\005\022\017\n\007heading\030\010 \001(\005\022\017\n\007lane_id\030\t \001(\005\022"
  "\'\n\tlane_info\030\n \001(\0132\024.perception.LaneInfo"
  "\022\025\n\rlicense_plate\030\013 \001(\t\022\020\n\010car_type\030\014 \001("
  "\005\022\r\n\005color\030\r \001(\005\022\016\n\006status\030\016 \001(\005\022\020\n\010dist"
  "ance\030\017 \001(\002\022\013\n\003rcs\030\020 \001(\002\022\022\n\ntrack_time\030\021 "
  "\001(\003\022\024\n\014vehicle_pose\030\022 \001(\005\022\024\n\014vehicle_typ"
  "e\030\023 \001(\005\022\026\n\016vehicle_marker\030\024 \001(\005\022\025\n\rvehic"
  "le_other\030\025 \001(\005\022\024\n\014vehicle_roof\030\026 \001(\005\022\030\n\020"
  "vehicle_carColor\030\027 \001(\005\022\024\n\014vehicle_make\030\030"
  " \001(\005\022\025\n\rvehicle_model\030\031 \001(\005\022\024\n\014vehicle_y"
  "ear\030\032 \001(\005\022\033\n\023vehicle_plate_color\030\033 \001(\005\022\032"
  "\n\022vehicle_plate_type\030\034 \001(\005\022\020\n\010face_age\030\035"
  " \001(\005\022\023\n\013face_gender\030\036 \001(\005\022\021\n\tface_mask\030\037"
  " \001(\005\022\020\n\010face_hat\030  \001(\005\022\022\n\nface_glass\030! \001"
  "(\005\022\034\n\024pedestrian_shoecolor\030\" \001(\005\022\034\n\024pede"
  "strian_shoestyle\030# \001(\005\022\031\n\021pedestrian_sle"
  "eve\030$ \001(\005\022\027\n\017pedestrian_hair\030% \001(\005\022\031\n\021pe"
  "destrian_nation\030& \001(\005\022\031\n\021pedestrian_gend"
  "er\030\' \001(\005\022\035\n\025pedestrian_lowerstyle\030( \001(\005\022"
  "\035\n\025pedestrian_upperstyle\030) \001(\005\022\026\n\016pedest"
  "rian_age\030* \001(\005\022\030\n\020pedestrian_lower\030+ \001(\005"
  "\022\030\n\020pedestrian_upper\030, \001(\005\022\033\n\023pedestrian"
  "_backpack\030- \001(\005\022\031\n\021nonmotor_attitude\030. \001"
  "(\005\022\027\n\017nonmotor_gender\030/ \001(\005\022\027\n\017nonmotor_"
  "nation\0300 \001(\005\022\037\n\027nonmotor_transportation\030"
  "1 \001(\005\022\033\n\023nonmotor_upperColor\0302 \001(\005\022\026\n\016no"
  "nmotor_upper\0303 \001(\005\022$\n\034nonmotor_transport"
  "ationColor\0304 \001(\005\"h\n\007Targets\022\021\n\tdevice_sn"
  "\030\001 \001(\t\022\021\n\tdevice_ip\030\002 \001(\t\022\024\n\014fusion_stat"
  "e\030\003 \001(\005\022!\n\005array\030\004 \003(\0132\022.perception.Targ"
  "et\"H\n\010AreaInfo\022\n\n\002id\030\001 \001(\005\022\r\n\005width\030\002 \001("
  "\002\022!\n\003dot\030\003 \003(\0132\024.perception.Position\"\301\001\n"
  "\tLaneCount\022\016\n\006laneNo\030\001 \001(\005\022\016\n\006volume\030\002 \001"
  "(\005\022\013\n\003pcu\030\003 \001(\005\022\017\n\007avSpeed\030\004 \001(\002\022\021\n\toccu"
  "pancy\030\005 \001(\002\022\017\n\007headWay\030\006 \001(\002\022\013\n\003gap\030\007 \001("
  "\002\022\017\n\007avDelay\030\010 \001(\002\022\016\n\006avStop\030\t \001(\005\022\017\n\007sp"
  "eed85\030\n \001(\002\022\023\n\013queueLength\030\013 \001(\002\"\273\002\n\tRoa"
  "dCount\022\020\n\010deviceId\030\001 \001(\t\022\017\n\007heading\030\002 \001("
  "\002\022\020\n\010roadName\030\003 \001(\t\022)\n\013roadInCoord\030\004 \001(\013"
  "2\024.perception.Position\022\016\n\006volume\030\005 \001(\005\022\013"
  "\n\003pcu\030\006 \001(\005\022\017\n\007avSpeed\030\007 \001(\002\022\021\n\toccupanc"
  "y\030\010 \001(\002\022\017\n\007headWay\030\t \001(\002\022\013\n\003gap\030\n \001(\002\022\017\n"
  "\007avDelay\030\013 \001(\002\022\016\n\006avStop\030\014 \001(\005\022\017\n\007speed8"
  "5\030\r \001(\002\022\023\n\013queueLength\030\016 \001(\002\022(\n\tlaneCoun"
  "t\030\017 \003(\0132\025.perception.LaneCount\"\343\001\n\021Inter"
  "sectionCount\022\016\n\006volume\030\001 \001(\005\022\013\n\003pcu\030\002 \001("
  "\005\022\017\n\007avSpeed\030\003 \001(\002\022\021\n\toccupancy\030\004 \001(\002\022\017\n"
  "\007headWay\030\005 \001(\002\022\013\n\003gap\030\006 \001(\002\022\017\n\007avDelay\030\007"
  " \001(\002\022\016\n\006avStop\030\010 \001(\005\022\017\n\007speed85\030\t \001(\002\022\023\n"
  "\013queueLength\030\n \001(\002\022(\n\troadCount\030\013 \003(\0132\025."
  "perception.RoadCount\"\255\002\n\005Event\022\022\n\nevent_"
  "type\030\001 \001(\005\022\r\n\005level\030\002 \001(\005\022\021\n\tdevice_sn\030\003"
  " \001(\t\022\017\n\007lane_id\030\004 \001(\005\022\024\n\014fusion_state\030\005 "
  "\001(\005\022\016\n\006source\030\006 \001(\005\022!\n\003pos\030\007 \003(\0132\024.perce"
  "ption.Position\022\025\n\rlicense_plate\030\010 \001(\t\022\022\n"
  "\ntrack_time\030\t \001(\003\022\013\n\003ttl\030\n \001(\003\022%\n\007refpat"
  "h\030\013 \003(\0132\024.perception.AreaInfo\022\025\n\013string_"
  "data\030\014 \001(\tH\000\022\022\n\010int_data\030\r \001(\005H\000B\n\n\010Data"
  "Type\"*\n\006Events\022 \n\005event\030\001 \003(\0132\021.percepti"
  "on.Event\"Q\n\tHeartBeat\022\021\n\tseq_count\030\001 \001(\005"
  "\022\014\n\004time\030\002 \001(\003\022\021\n\tdevice_sn\030\003 \001(\t\022\020\n\010err"
  "_code\030\004 \003(\005\"\256\001\n\nDeviceInfo\022\021\n\tdevice_sn\030"
  "\001 \001(\t\022&\n\010location\030\002 \001(\0132\024.perception.Pos"
  "ition\022\023\n\013orientation\030\003 \001(\002\022\014\n\004type\030\004 \001(\005"
  "\022-\n\017perceptual_area\030\005 \003(\0132\024.perception.A"
  "reaInfo\022\023\n\013description\030\006 \001(\t\"\235\001\n\nRoadCou"
  "nts\022\r\n\005rsuId\030\001 \001(\t\022\016\n\006seqNum\030\002 \001(\t\022\016\n\006rs"
  "uEsn\030\003 \001(\t\022\027\n\017protocolVersion\030\004 \001(\t\022\r\n\005c"
  "ycle\030\005 \001(\005\0228\n\021intersectionCount\030\006 \001(\0132\035."
  "perception.IntersectionCount\"t\n\rLaneCoun"
  "tInfo\022\016\n\006laneNo\030\001 \001(\005\022\017\n\007volume1\030\002 \001(\005\022\017"
  "\n\007volume2\030\003 \001(\005\022\017\n\007volume3\030\004 \001(\005\022\017\n\007volu"
  "me4\030\005 \001(\005\022\017\n\007volume5\030\006 \001(\005\"\362\001\n\rRoadCount"
  "Info\022\020\n\010deviceId\030\001 \001(\t\022\017\n\007heading\030\002 \001(\002\022"
  "\020\n\010roadName\030\003 \001(\t\022)\n\013roadInCoord\030\004 \001(\0132\024"
  ".perception.Position\022\017\n\007volume1\030\005 \001(\005\022\017\n"
  "\007volume2\030\006 \001(\005\022\017\n\007volume3\030\007 \001(\005\022\017\n\007volum"
  "e4\030\010 \001(\005\022\017\n\007volume5\030\t \001(\005\022,\n\tlaneCount\030\n"
  " \003(\0132\031.perception.LaneCountInfo\"\225\001\n\020Inte"
  "rsectionInfo\022\017\n\007volume1\030\001 \001(\005\022\017\n\007volume2"
  "\030\002 \001(\005\022\017\n\007volume3\030\003 \001(\005\022\017\n\007volume4\030\004 \001(\005"
  "\022\017\n\007volume5\030\005 \001(\005\022,\n\troadCount\030\006 \003(\0132\031.p"
  "erception.RoadCountInfo\"\231\001\n\010RoadInfo\022\r\n\005"
  "rsuId\030\001 \001(\t\022\016\n\006seqNum\030\002 \001(\t\022\016\n\006rsuEsn\030\003 "
  "\001(\t\022\027\n\017protocolVersion\030\004 \001(\t\022\r\n\005cycle\030\005 "
  "\001(\005\0226\n\020intersectionInfo\030\006 \001(\0132\034.percepti"
  "on.IntersectionInfo\"\262\002\n\rPerceptionMsg\022\014\n"
  "\004time\030\001 \001(\003\022)\n\ntarget_msg\030\002 \001(\0132\023.percep"
  "tion.TargetsH\000\022\'\n\tevent_msg\030\003 \001(\0132\022.perc"
  "eption.EventsH\000\022/\n\016heart_beat_msg\030\004 \001(\0132"
  "\025.perception.HeartBeatH\000\022*\n\010dev_info\030\005 \001"
  "(\0132\026.perception.DeviceInfoH\000\022,\n\nroad_cou"
  "nt\030\006 \001(\0132\026.perception.RoadCountsH\000\022)\n\tro"
  "ad_info\030\007 \001(\0132\024.perception.RoadInfoH\000B\t\n"
  "\007MsgTypeb\006proto3"
  ;
static ::_pbi::once_flag descriptor_table_perception_2eproto_once;
const ::_pbi::DescriptorTable descriptor_table_perception_2eproto = {
    false, false, 4136, descriptor_table_protodef_perception_2eproto,
    "perception.proto",
    &descriptor_table_perception_2eproto_once, nullptr, 0, 19,
    schemas, file_default_instances, TableStruct_perception_2eproto::offsets,
    file_level_metadata_perception_2eproto, file_level_enum_descriptors_perception_2eproto,
    file_level_service_descriptors_perception_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::_pbi::DescriptorTable* descriptor_table_perception_2eproto_getter() {
  return &descriptor_table_perception_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY2 static ::_pbi::AddDescriptorsRunner dynamic_init_dummy_perception_2eproto(&descriptor_table_perception_2eproto);
namespace perception {

// ===================================================================

class Position::_Internal {
 public:
};

Position::Position(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:perception.Position)
}
Position::Position(const Position& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  Position* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.lat_){}
    , decltype(_impl_.lon_){}
    , decltype(_impl_.elev_){}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&_impl_.lat_, &from._impl_.lat_,
    static_cast<size_t>(reinterpret_cast<char*>(&_impl_.elev_) -
    reinterpret_cast<char*>(&_impl_.lat_)) + sizeof(_impl_.elev_));
  // @@protoc_insertion_point(copy_constructor:perception.Position)
}

inline void Position::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.lat_){int64_t{0}}
    , decltype(_impl_.lon_){int64_t{0}}
    , decltype(_impl_.elev_){0}
    , /*decltype(_impl_._cached_size_)*/{}
  };
}

Position::~Position() {
  // @@protoc_insertion_point(destructor:perception.Position)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void Position::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void Position::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void Position::Clear() {
// @@protoc_insertion_point(message_clear_start:perception.Position)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&_impl_.lat_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&_impl_.elev_) -
      reinterpret_cast<char*>(&_impl_.lat_)) + sizeof(_impl_.elev_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* Position::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // int64 lat = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          _impl_.lat_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int64 lon = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          _impl_.lon_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 elev = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          _impl_.elev_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* Position::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:perception.Position)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // int64 lat = 1;
  if (this->_internal_lat() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt64ToArray(1, this->_internal_lat(), target);
  }

  // int64 lon = 2;
  if (this->_internal_lon() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt64ToArray(2, this->_internal_lon(), target);
  }

  // int32 elev = 3;
  if (this->_internal_elev() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(3, this->_internal_elev(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:perception.Position)
  return target;
}

size_t Position::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:perception.Position)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // int64 lat = 1;
  if (this->_internal_lat() != 0) {
    total_size += ::_pbi::WireFormatLite::Int64SizePlusOne(this->_internal_lat());
  }

  // int64 lon = 2;
  if (this->_internal_lon() != 0) {
    total_size += ::_pbi::WireFormatLite::Int64SizePlusOne(this->_internal_lon());
  }

  // int32 elev = 3;
  if (this->_internal_elev() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_elev());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData Position::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    Position::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*Position::GetClassData() const { return &_class_data_; }


void Position::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<Position*>(&to_msg);
  auto& from = static_cast<const Position&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:perception.Position)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_lat() != 0) {
    _this->_internal_set_lat(from._internal_lat());
  }
  if (from._internal_lon() != 0) {
    _this->_internal_set_lon(from._internal_lon());
  }
  if (from._internal_elev() != 0) {
    _this->_internal_set_elev(from._internal_elev());
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void Position::CopyFrom(const Position& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:perception.Position)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Position::IsInitialized() const {
  return true;
}

void Position::InternalSwap(Position* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(Position, _impl_.elev_)
      + sizeof(Position::_impl_.elev_)
      - PROTOBUF_FIELD_OFFSET(Position, _impl_.lat_)>(
          reinterpret_cast<char*>(&_impl_.lat_),
          reinterpret_cast<char*>(&other->_impl_.lat_));
}

::PROTOBUF_NAMESPACE_ID::Metadata Position::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_perception_2eproto_getter, &descriptor_table_perception_2eproto_once,
      file_level_metadata_perception_2eproto[0]);
}

// ===================================================================

class Size::_Internal {
 public:
};

Size::Size(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:perception.Size)
}
Size::Size(const Size& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  Size* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.length_){}
    , decltype(_impl_.width_){}
    , decltype(_impl_.height_){}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&_impl_.length_, &from._impl_.length_,
    static_cast<size_t>(reinterpret_cast<char*>(&_impl_.height_) -
    reinterpret_cast<char*>(&_impl_.length_)) + sizeof(_impl_.height_));
  // @@protoc_insertion_point(copy_constructor:perception.Size)
}

inline void Size::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.length_){0}
    , decltype(_impl_.width_){0}
    , decltype(_impl_.height_){0}
    , /*decltype(_impl_._cached_size_)*/{}
  };
}

Size::~Size() {
  // @@protoc_insertion_point(destructor:perception.Size)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void Size::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void Size::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void Size::Clear() {
// @@protoc_insertion_point(message_clear_start:perception.Size)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&_impl_.length_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&_impl_.height_) -
      reinterpret_cast<char*>(&_impl_.length_)) + sizeof(_impl_.height_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* Size::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // int32 length = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          _impl_.length_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 width = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          _impl_.width_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 height = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          _impl_.height_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* Size::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:perception.Size)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 length = 1;
  if (this->_internal_length() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(1, this->_internal_length(), target);
  }

  // int32 width = 2;
  if (this->_internal_width() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(2, this->_internal_width(), target);
  }

  // int32 height = 3;
  if (this->_internal_height() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(3, this->_internal_height(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:perception.Size)
  return target;
}

size_t Size::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:perception.Size)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // int32 length = 1;
  if (this->_internal_length() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_length());
  }

  // int32 width = 2;
  if (this->_internal_width() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_width());
  }

  // int32 height = 3;
  if (this->_internal_height() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_height());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData Size::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    Size::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*Size::GetClassData() const { return &_class_data_; }


void Size::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<Size*>(&to_msg);
  auto& from = static_cast<const Size&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:perception.Size)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_length() != 0) {
    _this->_internal_set_length(from._internal_length());
  }
  if (from._internal_width() != 0) {
    _this->_internal_set_width(from._internal_width());
  }
  if (from._internal_height() != 0) {
    _this->_internal_set_height(from._internal_height());
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void Size::CopyFrom(const Size& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:perception.Size)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Size::IsInitialized() const {
  return true;
}

void Size::InternalSwap(Size* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(Size, _impl_.height_)
      + sizeof(Size::_impl_.height_)
      - PROTOBUF_FIELD_OFFSET(Size, _impl_.length_)>(
          reinterpret_cast<char*>(&_impl_.length_),
          reinterpret_cast<char*>(&other->_impl_.length_));
}

::PROTOBUF_NAMESPACE_ID::Metadata Size::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_perception_2eproto_getter, &descriptor_table_perception_2eproto_once,
      file_level_metadata_perception_2eproto[1]);
}

// ===================================================================

class LaneInfo::_Internal {
 public:
};

LaneInfo::LaneInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:perception.LaneInfo)
}
LaneInfo::LaneInfo(const LaneInfo& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  LaneInfo* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.area_type_){}
    , decltype(_impl_.lane_count_){}
    , decltype(_impl_.lane_heading_){}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&_impl_.area_type_, &from._impl_.area_type_,
    static_cast<size_t>(reinterpret_cast<char*>(&_impl_.lane_heading_) -
    reinterpret_cast<char*>(&_impl_.area_type_)) + sizeof(_impl_.lane_heading_));
  // @@protoc_insertion_point(copy_constructor:perception.LaneInfo)
}

inline void LaneInfo::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.area_type_){0}
    , decltype(_impl_.lane_count_){0}
    , decltype(_impl_.lane_heading_){0}
    , /*decltype(_impl_._cached_size_)*/{}
  };
}

LaneInfo::~LaneInfo() {
  // @@protoc_insertion_point(destructor:perception.LaneInfo)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void LaneInfo::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void LaneInfo::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void LaneInfo::Clear() {
// @@protoc_insertion_point(message_clear_start:perception.LaneInfo)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&_impl_.area_type_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&_impl_.lane_heading_) -
      reinterpret_cast<char*>(&_impl_.area_type_)) + sizeof(_impl_.lane_heading_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* LaneInfo::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // int32 area_type = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          _impl_.area_type_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 lane_count = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          _impl_.lane_count_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // float lane_heading = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 29)) {
          _impl_.lane_heading_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* LaneInfo::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:perception.LaneInfo)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 area_type = 1;
  if (this->_internal_area_type() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(1, this->_internal_area_type(), target);
  }

  // int32 lane_count = 2;
  if (this->_internal_lane_count() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(2, this->_internal_lane_count(), target);
  }

  // float lane_heading = 3;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_lane_heading = this->_internal_lane_heading();
  uint32_t raw_lane_heading;
  memcpy(&raw_lane_heading, &tmp_lane_heading, sizeof(tmp_lane_heading));
  if (raw_lane_heading != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(3, this->_internal_lane_heading(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:perception.LaneInfo)
  return target;
}

size_t LaneInfo::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:perception.LaneInfo)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // int32 area_type = 1;
  if (this->_internal_area_type() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_area_type());
  }

  // int32 lane_count = 2;
  if (this->_internal_lane_count() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_lane_count());
  }

  // float lane_heading = 3;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_lane_heading = this->_internal_lane_heading();
  uint32_t raw_lane_heading;
  memcpy(&raw_lane_heading, &tmp_lane_heading, sizeof(tmp_lane_heading));
  if (raw_lane_heading != 0) {
    total_size += 1 + 4;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData LaneInfo::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    LaneInfo::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*LaneInfo::GetClassData() const { return &_class_data_; }


void LaneInfo::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<LaneInfo*>(&to_msg);
  auto& from = static_cast<const LaneInfo&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:perception.LaneInfo)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_area_type() != 0) {
    _this->_internal_set_area_type(from._internal_area_type());
  }
  if (from._internal_lane_count() != 0) {
    _this->_internal_set_lane_count(from._internal_lane_count());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_lane_heading = from._internal_lane_heading();
  uint32_t raw_lane_heading;
  memcpy(&raw_lane_heading, &tmp_lane_heading, sizeof(tmp_lane_heading));
  if (raw_lane_heading != 0) {
    _this->_internal_set_lane_heading(from._internal_lane_heading());
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void LaneInfo::CopyFrom(const LaneInfo& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:perception.LaneInfo)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool LaneInfo::IsInitialized() const {
  return true;
}

void LaneInfo::InternalSwap(LaneInfo* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(LaneInfo, _impl_.lane_heading_)
      + sizeof(LaneInfo::_impl_.lane_heading_)
      - PROTOBUF_FIELD_OFFSET(LaneInfo, _impl_.area_type_)>(
          reinterpret_cast<char*>(&_impl_.area_type_),
          reinterpret_cast<char*>(&other->_impl_.area_type_));
}

::PROTOBUF_NAMESPACE_ID::Metadata LaneInfo::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_perception_2eproto_getter, &descriptor_table_perception_2eproto_once,
      file_level_metadata_perception_2eproto[2]);
}

// ===================================================================

class Target::_Internal {
 public:
  static const ::perception::Position& pos(const Target* msg);
  static const ::perception::Size& size(const Target* msg);
  static const ::perception::LaneInfo& lane_info(const Target* msg);
};

const ::perception::Position&
Target::_Internal::pos(const Target* msg) {
  return *msg->_impl_.pos_;
}
const ::perception::Size&
Target::_Internal::size(const Target* msg) {
  return *msg->_impl_.size_;
}
const ::perception::LaneInfo&
Target::_Internal::lane_info(const Target* msg) {
  return *msg->_impl_.lane_info_;
}
Target::Target(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:perception.Target)
}
Target::Target(const Target& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  Target* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.license_plate_){}
    , decltype(_impl_.pos_){nullptr}
    , decltype(_impl_.size_){nullptr}
    , decltype(_impl_.lane_info_){nullptr}
    , decltype(_impl_.id_){}
    , decltype(_impl_.source_){}
    , decltype(_impl_.confidence_){}
    , decltype(_impl_.type_){}
    , decltype(_impl_.speed_){}
    , decltype(_impl_.heading_){}
    , decltype(_impl_.lane_id_){}
    , decltype(_impl_.car_type_){}
    , decltype(_impl_.color_){}
    , decltype(_impl_.status_){}
    , decltype(_impl_.distance_){}
    , decltype(_impl_.track_time_){}
    , decltype(_impl_.rcs_){}
    , decltype(_impl_.vehicle_pose_){}
    , decltype(_impl_.vehicle_type_){}
    , decltype(_impl_.vehicle_marker_){}
    , decltype(_impl_.vehicle_other_){}
    , decltype(_impl_.vehicle_roof_){}
    , decltype(_impl_.vehicle_carcolor_){}
    , decltype(_impl_.vehicle_make_){}
    , decltype(_impl_.vehicle_model_){}
    , decltype(_impl_.vehicle_year_){}
    , decltype(_impl_.vehicle_plate_color_){}
    , decltype(_impl_.vehicle_plate_type_){}
    , decltype(_impl_.face_age_){}
    , decltype(_impl_.face_gender_){}
    , decltype(_impl_.face_mask_){}
    , decltype(_impl_.face_hat_){}
    , decltype(_impl_.face_glass_){}
    , decltype(_impl_.pedestrian_shoecolor_){}
    , decltype(_impl_.pedestrian_shoestyle_){}
    , decltype(_impl_.pedestrian_sleeve_){}
    , decltype(_impl_.pedestrian_hair_){}
    , decltype(_impl_.pedestrian_nation_){}
    , decltype(_impl_.pedestrian_gender_){}
    , decltype(_impl_.pedestrian_lowerstyle_){}
    , decltype(_impl_.pedestrian_upperstyle_){}
    , decltype(_impl_.pedestrian_age_){}
    , decltype(_impl_.pedestrian_lower_){}
    , decltype(_impl_.pedestrian_upper_){}
    , decltype(_impl_.pedestrian_backpack_){}
    , decltype(_impl_.nonmotor_attitude_){}
    , decltype(_impl_.nonmotor_gender_){}
    , decltype(_impl_.nonmotor_nation_){}
    , decltype(_impl_.nonmotor_transportation_){}
    , decltype(_impl_.nonmotor_uppercolor_){}
    , decltype(_impl_.nonmotor_upper_){}
    , decltype(_impl_.nonmotor_transportationcolor_){}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  _impl_.license_plate_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.license_plate_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_license_plate().empty()) {
    _this->_impl_.license_plate_.Set(from._internal_license_plate(), 
      _this->GetArenaForAllocation());
  }
  if (from._internal_has_pos()) {
    _this->_impl_.pos_ = new ::perception::Position(*from._impl_.pos_);
  }
  if (from._internal_has_size()) {
    _this->_impl_.size_ = new ::perception::Size(*from._impl_.size_);
  }
  if (from._internal_has_lane_info()) {
    _this->_impl_.lane_info_ = new ::perception::LaneInfo(*from._impl_.lane_info_);
  }
  ::memcpy(&_impl_.id_, &from._impl_.id_,
    static_cast<size_t>(reinterpret_cast<char*>(&_impl_.nonmotor_transportationcolor_) -
    reinterpret_cast<char*>(&_impl_.id_)) + sizeof(_impl_.nonmotor_transportationcolor_));
  // @@protoc_insertion_point(copy_constructor:perception.Target)
}

inline void Target::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.license_plate_){}
    , decltype(_impl_.pos_){nullptr}
    , decltype(_impl_.size_){nullptr}
    , decltype(_impl_.lane_info_){nullptr}
    , decltype(_impl_.id_){int64_t{0}}
    , decltype(_impl_.source_){0}
    , decltype(_impl_.confidence_){0}
    , decltype(_impl_.type_){0}
    , decltype(_impl_.speed_){0}
    , decltype(_impl_.heading_){0}
    , decltype(_impl_.lane_id_){0}
    , decltype(_impl_.car_type_){0}
    , decltype(_impl_.color_){0}
    , decltype(_impl_.status_){0}
    , decltype(_impl_.distance_){0}
    , decltype(_impl_.track_time_){int64_t{0}}
    , decltype(_impl_.rcs_){0}
    , decltype(_impl_.vehicle_pose_){0}
    , decltype(_impl_.vehicle_type_){0}
    , decltype(_impl_.vehicle_marker_){0}
    , decltype(_impl_.vehicle_other_){0}
    , decltype(_impl_.vehicle_roof_){0}
    , decltype(_impl_.vehicle_carcolor_){0}
    , decltype(_impl_.vehicle_make_){0}
    , decltype(_impl_.vehicle_model_){0}
    , decltype(_impl_.vehicle_year_){0}
    , decltype(_impl_.vehicle_plate_color_){0}
    , decltype(_impl_.vehicle_plate_type_){0}
    , decltype(_impl_.face_age_){0}
    , decltype(_impl_.face_gender_){0}
    , decltype(_impl_.face_mask_){0}
    , decltype(_impl_.face_hat_){0}
    , decltype(_impl_.face_glass_){0}
    , decltype(_impl_.pedestrian_shoecolor_){0}
    , decltype(_impl_.pedestrian_shoestyle_){0}
    , decltype(_impl_.pedestrian_sleeve_){0}
    , decltype(_impl_.pedestrian_hair_){0}
    , decltype(_impl_.pedestrian_nation_){0}
    , decltype(_impl_.pedestrian_gender_){0}
    , decltype(_impl_.pedestrian_lowerstyle_){0}
    , decltype(_impl_.pedestrian_upperstyle_){0}
    , decltype(_impl_.pedestrian_age_){0}
    , decltype(_impl_.pedestrian_lower_){0}
    , decltype(_impl_.pedestrian_upper_){0}
    , decltype(_impl_.pedestrian_backpack_){0}
    , decltype(_impl_.nonmotor_attitude_){0}
    , decltype(_impl_.nonmotor_gender_){0}
    , decltype(_impl_.nonmotor_nation_){0}
    , decltype(_impl_.nonmotor_transportation_){0}
    , decltype(_impl_.nonmotor_uppercolor_){0}
    , decltype(_impl_.nonmotor_upper_){0}
    , decltype(_impl_.nonmotor_transportationcolor_){0}
    , /*decltype(_impl_._cached_size_)*/{}
  };
  _impl_.license_plate_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.license_plate_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

Target::~Target() {
  // @@protoc_insertion_point(destructor:perception.Target)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void Target::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  _impl_.license_plate_.Destroy();
  if (this != internal_default_instance()) delete _impl_.pos_;
  if (this != internal_default_instance()) delete _impl_.size_;
  if (this != internal_default_instance()) delete _impl_.lane_info_;
}

void Target::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void Target::Clear() {
// @@protoc_insertion_point(message_clear_start:perception.Target)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.license_plate_.ClearToEmpty();
  if (GetArenaForAllocation() == nullptr && _impl_.pos_ != nullptr) {
    delete _impl_.pos_;
  }
  _impl_.pos_ = nullptr;
  if (GetArenaForAllocation() == nullptr && _impl_.size_ != nullptr) {
    delete _impl_.size_;
  }
  _impl_.size_ = nullptr;
  if (GetArenaForAllocation() == nullptr && _impl_.lane_info_ != nullptr) {
    delete _impl_.lane_info_;
  }
  _impl_.lane_info_ = nullptr;
  ::memset(&_impl_.id_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&_impl_.nonmotor_transportationcolor_) -
      reinterpret_cast<char*>(&_impl_.id_)) + sizeof(_impl_.nonmotor_transportationcolor_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* Target::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // int64 id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          _impl_.id_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 source = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          _impl_.source_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .perception.Position pos = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          ptr = ctx->ParseMessage(_internal_mutable_pos(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .perception.Size size = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 34)) {
          ptr = ctx->ParseMessage(_internal_mutable_size(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // float confidence = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 45)) {
          _impl_.confidence_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // int32 type = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 48)) {
          _impl_.type_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 speed = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 56)) {
          _impl_.speed_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 heading = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 64)) {
          _impl_.heading_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 lane_id = 9;
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 72)) {
          _impl_.lane_id_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .perception.LaneInfo lane_info = 10;
      case 10:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 82)) {
          ptr = ctx->ParseMessage(_internal_mutable_lane_info(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string license_plate = 11;
      case 11:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 90)) {
          auto str = _internal_mutable_license_plate();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          CHK_(::_pbi::VerifyUTF8(str, "perception.Target.license_plate"));
        } else
          goto handle_unusual;
        continue;
      // int32 car_type = 12;
      case 12:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 96)) {
          _impl_.car_type_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 color = 13;
      case 13:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 104)) {
          _impl_.color_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 status = 14;
      case 14:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 112)) {
          _impl_.status_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // float distance = 15;
      case 15:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 125)) {
          _impl_.distance_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float rcs = 16;
      case 16:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 133)) {
          _impl_.rcs_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // int64 track_time = 17;
      case 17:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 136)) {
          _impl_.track_time_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 vehicle_pose = 18;
      case 18:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 144)) {
          _impl_.vehicle_pose_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 vehicle_type = 19;
      case 19:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 152)) {
          _impl_.vehicle_type_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 vehicle_marker = 20;
      case 20:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 160)) {
          _impl_.vehicle_marker_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 vehicle_other = 21;
      case 21:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 168)) {
          _impl_.vehicle_other_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 vehicle_roof = 22;
      case 22:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 176)) {
          _impl_.vehicle_roof_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 vehicle_carColor = 23;
      case 23:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 184)) {
          _impl_.vehicle_carcolor_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 vehicle_make = 24;
      case 24:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 192)) {
          _impl_.vehicle_make_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 vehicle_model = 25;
      case 25:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 200)) {
          _impl_.vehicle_model_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 vehicle_year = 26;
      case 26:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 208)) {
          _impl_.vehicle_year_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 vehicle_plate_color = 27;
      case 27:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 216)) {
          _impl_.vehicle_plate_color_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 vehicle_plate_type = 28;
      case 28:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 224)) {
          _impl_.vehicle_plate_type_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 face_age = 29;
      case 29:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 232)) {
          _impl_.face_age_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 face_gender = 30;
      case 30:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 240)) {
          _impl_.face_gender_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 face_mask = 31;
      case 31:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 248)) {
          _impl_.face_mask_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 face_hat = 32;
      case 32:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 0)) {
          _impl_.face_hat_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 face_glass = 33;
      case 33:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          _impl_.face_glass_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 pedestrian_shoecolor = 34;
      case 34:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          _impl_.pedestrian_shoecolor_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 pedestrian_shoestyle = 35;
      case 35:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          _impl_.pedestrian_shoestyle_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 pedestrian_sleeve = 36;
      case 36:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 32)) {
          _impl_.pedestrian_sleeve_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 pedestrian_hair = 37;
      case 37:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 40)) {
          _impl_.pedestrian_hair_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 pedestrian_nation = 38;
      case 38:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 48)) {
          _impl_.pedestrian_nation_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 pedestrian_gender = 39;
      case 39:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 56)) {
          _impl_.pedestrian_gender_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 pedestrian_lowerstyle = 40;
      case 40:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 64)) {
          _impl_.pedestrian_lowerstyle_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 pedestrian_upperstyle = 41;
      case 41:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 72)) {
          _impl_.pedestrian_upperstyle_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 pedestrian_age = 42;
      case 42:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 80)) {
          _impl_.pedestrian_age_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 pedestrian_lower = 43;
      case 43:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 88)) {
          _impl_.pedestrian_lower_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 pedestrian_upper = 44;
      case 44:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 96)) {
          _impl_.pedestrian_upper_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 pedestrian_backpack = 45;
      case 45:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 104)) {
          _impl_.pedestrian_backpack_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 nonmotor_attitude = 46;
      case 46:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 112)) {
          _impl_.nonmotor_attitude_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 nonmotor_gender = 47;
      case 47:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 120)) {
          _impl_.nonmotor_gender_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 nonmotor_nation = 48;
      case 48:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 128)) {
          _impl_.nonmotor_nation_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 nonmotor_transportation = 49;
      case 49:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 136)) {
          _impl_.nonmotor_transportation_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 nonmotor_upperColor = 50;
      case 50:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 144)) {
          _impl_.nonmotor_uppercolor_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 nonmotor_upper = 51;
      case 51:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 152)) {
          _impl_.nonmotor_upper_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 nonmotor_transportationColor = 52;
      case 52:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 160)) {
          _impl_.nonmotor_transportationcolor_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* Target::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:perception.Target)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // int64 id = 1;
  if (this->_internal_id() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt64ToArray(1, this->_internal_id(), target);
  }

  // int32 source = 2;
  if (this->_internal_source() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(2, this->_internal_source(), target);
  }

  // .perception.Position pos = 3;
  if (this->_internal_has_pos()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(3, _Internal::pos(this),
        _Internal::pos(this).GetCachedSize(), target, stream);
  }

  // .perception.Size size = 4;
  if (this->_internal_has_size()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(4, _Internal::size(this),
        _Internal::size(this).GetCachedSize(), target, stream);
  }

  // float confidence = 5;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_confidence = this->_internal_confidence();
  uint32_t raw_confidence;
  memcpy(&raw_confidence, &tmp_confidence, sizeof(tmp_confidence));
  if (raw_confidence != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(5, this->_internal_confidence(), target);
  }

  // int32 type = 6;
  if (this->_internal_type() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(6, this->_internal_type(), target);
  }

  // int32 speed = 7;
  if (this->_internal_speed() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(7, this->_internal_speed(), target);
  }

  // int32 heading = 8;
  if (this->_internal_heading() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(8, this->_internal_heading(), target);
  }

  // int32 lane_id = 9;
  if (this->_internal_lane_id() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(9, this->_internal_lane_id(), target);
  }

  // .perception.LaneInfo lane_info = 10;
  if (this->_internal_has_lane_info()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(10, _Internal::lane_info(this),
        _Internal::lane_info(this).GetCachedSize(), target, stream);
  }

  // string license_plate = 11;
  if (!this->_internal_license_plate().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_license_plate().data(), static_cast<int>(this->_internal_license_plate().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "perception.Target.license_plate");
    target = stream->WriteStringMaybeAliased(
        11, this->_internal_license_plate(), target);
  }

  // int32 car_type = 12;
  if (this->_internal_car_type() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(12, this->_internal_car_type(), target);
  }

  // int32 color = 13;
  if (this->_internal_color() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(13, this->_internal_color(), target);
  }

  // int32 status = 14;
  if (this->_internal_status() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(14, this->_internal_status(), target);
  }

  // float distance = 15;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_distance = this->_internal_distance();
  uint32_t raw_distance;
  memcpy(&raw_distance, &tmp_distance, sizeof(tmp_distance));
  if (raw_distance != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(15, this->_internal_distance(), target);
  }

  // float rcs = 16;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_rcs = this->_internal_rcs();
  uint32_t raw_rcs;
  memcpy(&raw_rcs, &tmp_rcs, sizeof(tmp_rcs));
  if (raw_rcs != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(16, this->_internal_rcs(), target);
  }

  // int64 track_time = 17;
  if (this->_internal_track_time() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt64ToArray(17, this->_internal_track_time(), target);
  }

  // int32 vehicle_pose = 18;
  if (this->_internal_vehicle_pose() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(18, this->_internal_vehicle_pose(), target);
  }

  // int32 vehicle_type = 19;
  if (this->_internal_vehicle_type() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(19, this->_internal_vehicle_type(), target);
  }

  // int32 vehicle_marker = 20;
  if (this->_internal_vehicle_marker() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(20, this->_internal_vehicle_marker(), target);
  }

  // int32 vehicle_other = 21;
  if (this->_internal_vehicle_other() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(21, this->_internal_vehicle_other(), target);
  }

  // int32 vehicle_roof = 22;
  if (this->_internal_vehicle_roof() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(22, this->_internal_vehicle_roof(), target);
  }

  // int32 vehicle_carColor = 23;
  if (this->_internal_vehicle_carcolor() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(23, this->_internal_vehicle_carcolor(), target);
  }

  // int32 vehicle_make = 24;
  if (this->_internal_vehicle_make() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(24, this->_internal_vehicle_make(), target);
  }

  // int32 vehicle_model = 25;
  if (this->_internal_vehicle_model() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(25, this->_internal_vehicle_model(), target);
  }

  // int32 vehicle_year = 26;
  if (this->_internal_vehicle_year() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(26, this->_internal_vehicle_year(), target);
  }

  // int32 vehicle_plate_color = 27;
  if (this->_internal_vehicle_plate_color() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(27, this->_internal_vehicle_plate_color(), target);
  }

  // int32 vehicle_plate_type = 28;
  if (this->_internal_vehicle_plate_type() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(28, this->_internal_vehicle_plate_type(), target);
  }

  // int32 face_age = 29;
  if (this->_internal_face_age() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(29, this->_internal_face_age(), target);
  }

  // int32 face_gender = 30;
  if (this->_internal_face_gender() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(30, this->_internal_face_gender(), target);
  }

  // int32 face_mask = 31;
  if (this->_internal_face_mask() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(31, this->_internal_face_mask(), target);
  }

  // int32 face_hat = 32;
  if (this->_internal_face_hat() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(32, this->_internal_face_hat(), target);
  }

  // int32 face_glass = 33;
  if (this->_internal_face_glass() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(33, this->_internal_face_glass(), target);
  }

  // int32 pedestrian_shoecolor = 34;
  if (this->_internal_pedestrian_shoecolor() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(34, this->_internal_pedestrian_shoecolor(), target);
  }

  // int32 pedestrian_shoestyle = 35;
  if (this->_internal_pedestrian_shoestyle() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(35, this->_internal_pedestrian_shoestyle(), target);
  }

  // int32 pedestrian_sleeve = 36;
  if (this->_internal_pedestrian_sleeve() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(36, this->_internal_pedestrian_sleeve(), target);
  }

  // int32 pedestrian_hair = 37;
  if (this->_internal_pedestrian_hair() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(37, this->_internal_pedestrian_hair(), target);
  }

  // int32 pedestrian_nation = 38;
  if (this->_internal_pedestrian_nation() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(38, this->_internal_pedestrian_nation(), target);
  }

  // int32 pedestrian_gender = 39;
  if (this->_internal_pedestrian_gender() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(39, this->_internal_pedestrian_gender(), target);
  }

  // int32 pedestrian_lowerstyle = 40;
  if (this->_internal_pedestrian_lowerstyle() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(40, this->_internal_pedestrian_lowerstyle(), target);
  }

  // int32 pedestrian_upperstyle = 41;
  if (this->_internal_pedestrian_upperstyle() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(41, this->_internal_pedestrian_upperstyle(), target);
  }

  // int32 pedestrian_age = 42;
  if (this->_internal_pedestrian_age() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(42, this->_internal_pedestrian_age(), target);
  }

  // int32 pedestrian_lower = 43;
  if (this->_internal_pedestrian_lower() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(43, this->_internal_pedestrian_lower(), target);
  }

  // int32 pedestrian_upper = 44;
  if (this->_internal_pedestrian_upper() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(44, this->_internal_pedestrian_upper(), target);
  }

  // int32 pedestrian_backpack = 45;
  if (this->_internal_pedestrian_backpack() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(45, this->_internal_pedestrian_backpack(), target);
  }

  // int32 nonmotor_attitude = 46;
  if (this->_internal_nonmotor_attitude() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(46, this->_internal_nonmotor_attitude(), target);
  }

  // int32 nonmotor_gender = 47;
  if (this->_internal_nonmotor_gender() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(47, this->_internal_nonmotor_gender(), target);
  }

  // int32 nonmotor_nation = 48;
  if (this->_internal_nonmotor_nation() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(48, this->_internal_nonmotor_nation(), target);
  }

  // int32 nonmotor_transportation = 49;
  if (this->_internal_nonmotor_transportation() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(49, this->_internal_nonmotor_transportation(), target);
  }

  // int32 nonmotor_upperColor = 50;
  if (this->_internal_nonmotor_uppercolor() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(50, this->_internal_nonmotor_uppercolor(), target);
  }

  // int32 nonmotor_upper = 51;
  if (this->_internal_nonmotor_upper() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(51, this->_internal_nonmotor_upper(), target);
  }

  // int32 nonmotor_transportationColor = 52;
  if (this->_internal_nonmotor_transportationcolor() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(52, this->_internal_nonmotor_transportationcolor(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:perception.Target)
  return target;
}

size_t Target::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:perception.Target)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string license_plate = 11;
  if (!this->_internal_license_plate().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_license_plate());
  }

  // .perception.Position pos = 3;
  if (this->_internal_has_pos()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *_impl_.pos_);
  }

  // .perception.Size size = 4;
  if (this->_internal_has_size()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *_impl_.size_);
  }

  // .perception.LaneInfo lane_info = 10;
  if (this->_internal_has_lane_info()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *_impl_.lane_info_);
  }

  // int64 id = 1;
  if (this->_internal_id() != 0) {
    total_size += ::_pbi::WireFormatLite::Int64SizePlusOne(this->_internal_id());
  }

  // int32 source = 2;
  if (this->_internal_source() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_source());
  }

  // float confidence = 5;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_confidence = this->_internal_confidence();
  uint32_t raw_confidence;
  memcpy(&raw_confidence, &tmp_confidence, sizeof(tmp_confidence));
  if (raw_confidence != 0) {
    total_size += 1 + 4;
  }

  // int32 type = 6;
  if (this->_internal_type() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_type());
  }

  // int32 speed = 7;
  if (this->_internal_speed() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_speed());
  }

  // int32 heading = 8;
  if (this->_internal_heading() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_heading());
  }

  // int32 lane_id = 9;
  if (this->_internal_lane_id() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_lane_id());
  }

  // int32 car_type = 12;
  if (this->_internal_car_type() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_car_type());
  }

  // int32 color = 13;
  if (this->_internal_color() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_color());
  }

  // int32 status = 14;
  if (this->_internal_status() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_status());
  }

  // float distance = 15;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_distance = this->_internal_distance();
  uint32_t raw_distance;
  memcpy(&raw_distance, &tmp_distance, sizeof(tmp_distance));
  if (raw_distance != 0) {
    total_size += 1 + 4;
  }

  // int64 track_time = 17;
  if (this->_internal_track_time() != 0) {
    total_size += 2 +
      ::_pbi::WireFormatLite::Int64Size(
        this->_internal_track_time());
  }

  // float rcs = 16;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_rcs = this->_internal_rcs();
  uint32_t raw_rcs;
  memcpy(&raw_rcs, &tmp_rcs, sizeof(tmp_rcs));
  if (raw_rcs != 0) {
    total_size += 2 + 4;
  }

  // int32 vehicle_pose = 18;
  if (this->_internal_vehicle_pose() != 0) {
    total_size += 2 +
      ::_pbi::WireFormatLite::Int32Size(
        this->_internal_vehicle_pose());
  }

  // int32 vehicle_type = 19;
  if (this->_internal_vehicle_type() != 0) {
    total_size += 2 +
      ::_pbi::WireFormatLite::Int32Size(
        this->_internal_vehicle_type());
  }

  // int32 vehicle_marker = 20;
  if (this->_internal_vehicle_marker() != 0) {
    total_size += 2 +
      ::_pbi::WireFormatLite::Int32Size(
        this->_internal_vehicle_marker());
  }

  // int32 vehicle_other = 21;
  if (this->_internal_vehicle_other() != 0) {
    total_size += 2 +
      ::_pbi::WireFormatLite::Int32Size(
        this->_internal_vehicle_other());
  }

  // int32 vehicle_roof = 22;
  if (this->_internal_vehicle_roof() != 0) {
    total_size += 2 +
      ::_pbi::WireFormatLite::Int32Size(
        this->_internal_vehicle_roof());
  }

  // int32 vehicle_carColor = 23;
  if (this->_internal_vehicle_carcolor() != 0) {
    total_size += 2 +
      ::_pbi::WireFormatLite::Int32Size(
        this->_internal_vehicle_carcolor());
  }

  // int32 vehicle_make = 24;
  if (this->_internal_vehicle_make() != 0) {
    total_size += 2 +
      ::_pbi::WireFormatLite::Int32Size(
        this->_internal_vehicle_make());
  }

  // int32 vehicle_model = 25;
  if (this->_internal_vehicle_model() != 0) {
    total_size += 2 +
      ::_pbi::WireFormatLite::Int32Size(
        this->_internal_vehicle_model());
  }

  // int32 vehicle_year = 26;
  if (this->_internal_vehicle_year() != 0) {
    total_size += 2 +
      ::_pbi::WireFormatLite::Int32Size(
        this->_internal_vehicle_year());
  }

  // int32 vehicle_plate_color = 27;
  if (this->_internal_vehicle_plate_color() != 0) {
    total_size += 2 +
      ::_pbi::WireFormatLite::Int32Size(
        this->_internal_vehicle_plate_color());
  }

  // int32 vehicle_plate_type = 28;
  if (this->_internal_vehicle_plate_type() != 0) {
    total_size += 2 +
      ::_pbi::WireFormatLite::Int32Size(
        this->_internal_vehicle_plate_type());
  }

  // int32 face_age = 29;
  if (this->_internal_face_age() != 0) {
    total_size += 2 +
      ::_pbi::WireFormatLite::Int32Size(
        this->_internal_face_age());
  }

  // int32 face_gender = 30;
  if (this->_internal_face_gender() != 0) {
    total_size += 2 +
      ::_pbi::WireFormatLite::Int32Size(
        this->_internal_face_gender());
  }

  // int32 face_mask = 31;
  if (this->_internal_face_mask() != 0) {
    total_size += 2 +
      ::_pbi::WireFormatLite::Int32Size(
        this->_internal_face_mask());
  }

  // int32 face_hat = 32;
  if (this->_internal_face_hat() != 0) {
    total_size += 2 +
      ::_pbi::WireFormatLite::Int32Size(
        this->_internal_face_hat());
  }

  // int32 face_glass = 33;
  if (this->_internal_face_glass() != 0) {
    total_size += 2 +
      ::_pbi::WireFormatLite::Int32Size(
        this->_internal_face_glass());
  }

  // int32 pedestrian_shoecolor = 34;
  if (this->_internal_pedestrian_shoecolor() != 0) {
    total_size += 2 +
      ::_pbi::WireFormatLite::Int32Size(
        this->_internal_pedestrian_shoecolor());
  }

  // int32 pedestrian_shoestyle = 35;
  if (this->_internal_pedestrian_shoestyle() != 0) {
    total_size += 2 +
      ::_pbi::WireFormatLite::Int32Size(
        this->_internal_pedestrian_shoestyle());
  }

  // int32 pedestrian_sleeve = 36;
  if (this->_internal_pedestrian_sleeve() != 0) {
    total_size += 2 +
      ::_pbi::WireFormatLite::Int32Size(
        this->_internal_pedestrian_sleeve());
  }

  // int32 pedestrian_hair = 37;
  if (this->_internal_pedestrian_hair() != 0) {
    total_size += 2 +
      ::_pbi::WireFormatLite::Int32Size(
        this->_internal_pedestrian_hair());
  }

  // int32 pedestrian_nation = 38;
  if (this->_internal_pedestrian_nation() != 0) {
    total_size += 2 +
      ::_pbi::WireFormatLite::Int32Size(
        this->_internal_pedestrian_nation());
  }

  // int32 pedestrian_gender = 39;
  if (this->_internal_pedestrian_gender() != 0) {
    total_size += 2 +
      ::_pbi::WireFormatLite::Int32Size(
        this->_internal_pedestrian_gender());
  }

  // int32 pedestrian_lowerstyle = 40;
  if (this->_internal_pedestrian_lowerstyle() != 0) {
    total_size += 2 +
      ::_pbi::WireFormatLite::Int32Size(
        this->_internal_pedestrian_lowerstyle());
  }

  // int32 pedestrian_upperstyle = 41;
  if (this->_internal_pedestrian_upperstyle() != 0) {
    total_size += 2 +
      ::_pbi::WireFormatLite::Int32Size(
        this->_internal_pedestrian_upperstyle());
  }

  // int32 pedestrian_age = 42;
  if (this->_internal_pedestrian_age() != 0) {
    total_size += 2 +
      ::_pbi::WireFormatLite::Int32Size(
        this->_internal_pedestrian_age());
  }

  // int32 pedestrian_lower = 43;
  if (this->_internal_pedestrian_lower() != 0) {
    total_size += 2 +
      ::_pbi::WireFormatLite::Int32Size(
        this->_internal_pedestrian_lower());
  }

  // int32 pedestrian_upper = 44;
  if (this->_internal_pedestrian_upper() != 0) {
    total_size += 2 +
      ::_pbi::WireFormatLite::Int32Size(
        this->_internal_pedestrian_upper());
  }

  // int32 pedestrian_backpack = 45;
  if (this->_internal_pedestrian_backpack() != 0) {
    total_size += 2 +
      ::_pbi::WireFormatLite::Int32Size(
        this->_internal_pedestrian_backpack());
  }

  // int32 nonmotor_attitude = 46;
  if (this->_internal_nonmotor_attitude() != 0) {
    total_size += 2 +
      ::_pbi::WireFormatLite::Int32Size(
        this->_internal_nonmotor_attitude());
  }

  // int32 nonmotor_gender = 47;
  if (this->_internal_nonmotor_gender() != 0) {
    total_size += 2 +
      ::_pbi::WireFormatLite::Int32Size(
        this->_internal_nonmotor_gender());
  }

  // int32 nonmotor_nation = 48;
  if (this->_internal_nonmotor_nation() != 0) {
    total_size += 2 +
      ::_pbi::WireFormatLite::Int32Size(
        this->_internal_nonmotor_nation());
  }

  // int32 nonmotor_transportation = 49;
  if (this->_internal_nonmotor_transportation() != 0) {
    total_size += 2 +
      ::_pbi::WireFormatLite::Int32Size(
        this->_internal_nonmotor_transportation());
  }

  // int32 nonmotor_upperColor = 50;
  if (this->_internal_nonmotor_uppercolor() != 0) {
    total_size += 2 +
      ::_pbi::WireFormatLite::Int32Size(
        this->_internal_nonmotor_uppercolor());
  }

  // int32 nonmotor_upper = 51;
  if (this->_internal_nonmotor_upper() != 0) {
    total_size += 2 +
      ::_pbi::WireFormatLite::Int32Size(
        this->_internal_nonmotor_upper());
  }

  // int32 nonmotor_transportationColor = 52;
  if (this->_internal_nonmotor_transportationcolor() != 0) {
    total_size += 2 +
      ::_pbi::WireFormatLite::Int32Size(
        this->_internal_nonmotor_transportationcolor());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData Target::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    Target::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*Target::GetClassData() const { return &_class_data_; }


void Target::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<Target*>(&to_msg);
  auto& from = static_cast<const Target&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:perception.Target)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_license_plate().empty()) {
    _this->_internal_set_license_plate(from._internal_license_plate());
  }
  if (from._internal_has_pos()) {
    _this->_internal_mutable_pos()->::perception::Position::MergeFrom(
        from._internal_pos());
  }
  if (from._internal_has_size()) {
    _this->_internal_mutable_size()->::perception::Size::MergeFrom(
        from._internal_size());
  }
  if (from._internal_has_lane_info()) {
    _this->_internal_mutable_lane_info()->::perception::LaneInfo::MergeFrom(
        from._internal_lane_info());
  }
  if (from._internal_id() != 0) {
    _this->_internal_set_id(from._internal_id());
  }
  if (from._internal_source() != 0) {
    _this->_internal_set_source(from._internal_source());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_confidence = from._internal_confidence();
  uint32_t raw_confidence;
  memcpy(&raw_confidence, &tmp_confidence, sizeof(tmp_confidence));
  if (raw_confidence != 0) {
    _this->_internal_set_confidence(from._internal_confidence());
  }
  if (from._internal_type() != 0) {
    _this->_internal_set_type(from._internal_type());
  }
  if (from._internal_speed() != 0) {
    _this->_internal_set_speed(from._internal_speed());
  }
  if (from._internal_heading() != 0) {
    _this->_internal_set_heading(from._internal_heading());
  }
  if (from._internal_lane_id() != 0) {
    _this->_internal_set_lane_id(from._internal_lane_id());
  }
  if (from._internal_car_type() != 0) {
    _this->_internal_set_car_type(from._internal_car_type());
  }
  if (from._internal_color() != 0) {
    _this->_internal_set_color(from._internal_color());
  }
  if (from._internal_status() != 0) {
    _this->_internal_set_status(from._internal_status());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_distance = from._internal_distance();
  uint32_t raw_distance;
  memcpy(&raw_distance, &tmp_distance, sizeof(tmp_distance));
  if (raw_distance != 0) {
    _this->_internal_set_distance(from._internal_distance());
  }
  if (from._internal_track_time() != 0) {
    _this->_internal_set_track_time(from._internal_track_time());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_rcs = from._internal_rcs();
  uint32_t raw_rcs;
  memcpy(&raw_rcs, &tmp_rcs, sizeof(tmp_rcs));
  if (raw_rcs != 0) {
    _this->_internal_set_rcs(from._internal_rcs());
  }
  if (from._internal_vehicle_pose() != 0) {
    _this->_internal_set_vehicle_pose(from._internal_vehicle_pose());
  }
  if (from._internal_vehicle_type() != 0) {
    _this->_internal_set_vehicle_type(from._internal_vehicle_type());
  }
  if (from._internal_vehicle_marker() != 0) {
    _this->_internal_set_vehicle_marker(from._internal_vehicle_marker());
  }
  if (from._internal_vehicle_other() != 0) {
    _this->_internal_set_vehicle_other(from._internal_vehicle_other());
  }
  if (from._internal_vehicle_roof() != 0) {
    _this->_internal_set_vehicle_roof(from._internal_vehicle_roof());
  }
  if (from._internal_vehicle_carcolor() != 0) {
    _this->_internal_set_vehicle_carcolor(from._internal_vehicle_carcolor());
  }
  if (from._internal_vehicle_make() != 0) {
    _this->_internal_set_vehicle_make(from._internal_vehicle_make());
  }
  if (from._internal_vehicle_model() != 0) {
    _this->_internal_set_vehicle_model(from._internal_vehicle_model());
  }
  if (from._internal_vehicle_year() != 0) {
    _this->_internal_set_vehicle_year(from._internal_vehicle_year());
  }
  if (from._internal_vehicle_plate_color() != 0) {
    _this->_internal_set_vehicle_plate_color(from._internal_vehicle_plate_color());
  }
  if (from._internal_vehicle_plate_type() != 0) {
    _this->_internal_set_vehicle_plate_type(from._internal_vehicle_plate_type());
  }
  if (from._internal_face_age() != 0) {
    _this->_internal_set_face_age(from._internal_face_age());
  }
  if (from._internal_face_gender() != 0) {
    _this->_internal_set_face_gender(from._internal_face_gender());
  }
  if (from._internal_face_mask() != 0) {
    _this->_internal_set_face_mask(from._internal_face_mask());
  }
  if (from._internal_face_hat() != 0) {
    _this->_internal_set_face_hat(from._internal_face_hat());
  }
  if (from._internal_face_glass() != 0) {
    _this->_internal_set_face_glass(from._internal_face_glass());
  }
  if (from._internal_pedestrian_shoecolor() != 0) {
    _this->_internal_set_pedestrian_shoecolor(from._internal_pedestrian_shoecolor());
  }
  if (from._internal_pedestrian_shoestyle() != 0) {
    _this->_internal_set_pedestrian_shoestyle(from._internal_pedestrian_shoestyle());
  }
  if (from._internal_pedestrian_sleeve() != 0) {
    _this->_internal_set_pedestrian_sleeve(from._internal_pedestrian_sleeve());
  }
  if (from._internal_pedestrian_hair() != 0) {
    _this->_internal_set_pedestrian_hair(from._internal_pedestrian_hair());
  }
  if (from._internal_pedestrian_nation() != 0) {
    _this->_internal_set_pedestrian_nation(from._internal_pedestrian_nation());
  }
  if (from._internal_pedestrian_gender() != 0) {
    _this->_internal_set_pedestrian_gender(from._internal_pedestrian_gender());
  }
  if (from._internal_pedestrian_lowerstyle() != 0) {
    _this->_internal_set_pedestrian_lowerstyle(from._internal_pedestrian_lowerstyle());
  }
  if (from._internal_pedestrian_upperstyle() != 0) {
    _this->_internal_set_pedestrian_upperstyle(from._internal_pedestrian_upperstyle());
  }
  if (from._internal_pedestrian_age() != 0) {
    _this->_internal_set_pedestrian_age(from._internal_pedestrian_age());
  }
  if (from._internal_pedestrian_lower() != 0) {
    _this->_internal_set_pedestrian_lower(from._internal_pedestrian_lower());
  }
  if (from._internal_pedestrian_upper() != 0) {
    _this->_internal_set_pedestrian_upper(from._internal_pedestrian_upper());
  }
  if (from._internal_pedestrian_backpack() != 0) {
    _this->_internal_set_pedestrian_backpack(from._internal_pedestrian_backpack());
  }
  if (from._internal_nonmotor_attitude() != 0) {
    _this->_internal_set_nonmotor_attitude(from._internal_nonmotor_attitude());
  }
  if (from._internal_nonmotor_gender() != 0) {
    _this->_internal_set_nonmotor_gender(from._internal_nonmotor_gender());
  }
  if (from._internal_nonmotor_nation() != 0) {
    _this->_internal_set_nonmotor_nation(from._internal_nonmotor_nation());
  }
  if (from._internal_nonmotor_transportation() != 0) {
    _this->_internal_set_nonmotor_transportation(from._internal_nonmotor_transportation());
  }
  if (from._internal_nonmotor_uppercolor() != 0) {
    _this->_internal_set_nonmotor_uppercolor(from._internal_nonmotor_uppercolor());
  }
  if (from._internal_nonmotor_upper() != 0) {
    _this->_internal_set_nonmotor_upper(from._internal_nonmotor_upper());
  }
  if (from._internal_nonmotor_transportationcolor() != 0) {
    _this->_internal_set_nonmotor_transportationcolor(from._internal_nonmotor_transportationcolor());
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void Target::CopyFrom(const Target& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:perception.Target)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Target::IsInitialized() const {
  return true;
}

void Target::InternalSwap(Target* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.license_plate_, lhs_arena,
      &other->_impl_.license_plate_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(Target, _impl_.nonmotor_transportationcolor_)
      + sizeof(Target::_impl_.nonmotor_transportationcolor_)
      - PROTOBUF_FIELD_OFFSET(Target, _impl_.pos_)>(
          reinterpret_cast<char*>(&_impl_.pos_),
          reinterpret_cast<char*>(&other->_impl_.pos_));
}

::PROTOBUF_NAMESPACE_ID::Metadata Target::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_perception_2eproto_getter, &descriptor_table_perception_2eproto_once,
      file_level_metadata_perception_2eproto[3]);
}

// ===================================================================

class Targets::_Internal {
 public:
};

Targets::Targets(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:perception.Targets)
}
Targets::Targets(const Targets& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  Targets* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.array_){from._impl_.array_}
    , decltype(_impl_.device_sn_){}
    , decltype(_impl_.device_ip_){}
    , decltype(_impl_.fusion_state_){}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  _impl_.device_sn_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.device_sn_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_device_sn().empty()) {
    _this->_impl_.device_sn_.Set(from._internal_device_sn(), 
      _this->GetArenaForAllocation());
  }
  _impl_.device_ip_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.device_ip_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_device_ip().empty()) {
    _this->_impl_.device_ip_.Set(from._internal_device_ip(), 
      _this->GetArenaForAllocation());
  }
  _this->_impl_.fusion_state_ = from._impl_.fusion_state_;
  // @@protoc_insertion_point(copy_constructor:perception.Targets)
}

inline void Targets::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.array_){arena}
    , decltype(_impl_.device_sn_){}
    , decltype(_impl_.device_ip_){}
    , decltype(_impl_.fusion_state_){0}
    , /*decltype(_impl_._cached_size_)*/{}
  };
  _impl_.device_sn_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.device_sn_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  _impl_.device_ip_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.device_ip_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

Targets::~Targets() {
  // @@protoc_insertion_point(destructor:perception.Targets)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void Targets::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  _impl_.array_.~RepeatedPtrField();
  _impl_.device_sn_.Destroy();
  _impl_.device_ip_.Destroy();
}

void Targets::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void Targets::Clear() {
// @@protoc_insertion_point(message_clear_start:perception.Targets)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.array_.Clear();
  _impl_.device_sn_.ClearToEmpty();
  _impl_.device_ip_.ClearToEmpty();
  _impl_.fusion_state_ = 0;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* Targets::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string device_sn = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_device_sn();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          CHK_(::_pbi::VerifyUTF8(str, "perception.Targets.device_sn"));
        } else
          goto handle_unusual;
        continue;
      // string device_ip = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_device_ip();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          CHK_(::_pbi::VerifyUTF8(str, "perception.Targets.device_ip"));
        } else
          goto handle_unusual;
        continue;
      // int32 fusion_state = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          _impl_.fusion_state_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated .perception.Target array = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 34)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_array(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<34>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* Targets::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:perception.Targets)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string device_sn = 1;
  if (!this->_internal_device_sn().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_device_sn().data(), static_cast<int>(this->_internal_device_sn().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "perception.Targets.device_sn");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_device_sn(), target);
  }

  // string device_ip = 2;
  if (!this->_internal_device_ip().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_device_ip().data(), static_cast<int>(this->_internal_device_ip().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "perception.Targets.device_ip");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_device_ip(), target);
  }

  // int32 fusion_state = 3;
  if (this->_internal_fusion_state() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(3, this->_internal_fusion_state(), target);
  }

  // repeated .perception.Target array = 4;
  for (unsigned i = 0,
      n = static_cast<unsigned>(this->_internal_array_size()); i < n; i++) {
    const auto& repfield = this->_internal_array(i);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
        InternalWriteMessage(4, repfield, repfield.GetCachedSize(), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:perception.Targets)
  return target;
}

size_t Targets::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:perception.Targets)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .perception.Target array = 4;
  total_size += 1UL * this->_internal_array_size();
  for (const auto& msg : this->_impl_.array_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // string device_sn = 1;
  if (!this->_internal_device_sn().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_device_sn());
  }

  // string device_ip = 2;
  if (!this->_internal_device_ip().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_device_ip());
  }

  // int32 fusion_state = 3;
  if (this->_internal_fusion_state() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_fusion_state());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData Targets::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    Targets::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*Targets::GetClassData() const { return &_class_data_; }


void Targets::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<Targets*>(&to_msg);
  auto& from = static_cast<const Targets&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:perception.Targets)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  _this->_impl_.array_.MergeFrom(from._impl_.array_);
  if (!from._internal_device_sn().empty()) {
    _this->_internal_set_device_sn(from._internal_device_sn());
  }
  if (!from._internal_device_ip().empty()) {
    _this->_internal_set_device_ip(from._internal_device_ip());
  }
  if (from._internal_fusion_state() != 0) {
    _this->_internal_set_fusion_state(from._internal_fusion_state());
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void Targets::CopyFrom(const Targets& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:perception.Targets)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Targets::IsInitialized() const {
  return true;
}

void Targets::InternalSwap(Targets* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  _impl_.array_.InternalSwap(&other->_impl_.array_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.device_sn_, lhs_arena,
      &other->_impl_.device_sn_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.device_ip_, lhs_arena,
      &other->_impl_.device_ip_, rhs_arena
  );
  swap(_impl_.fusion_state_, other->_impl_.fusion_state_);
}

::PROTOBUF_NAMESPACE_ID::Metadata Targets::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_perception_2eproto_getter, &descriptor_table_perception_2eproto_once,
      file_level_metadata_perception_2eproto[4]);
}

// ===================================================================

class AreaInfo::_Internal {
 public:
};

AreaInfo::AreaInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:perception.AreaInfo)
}
AreaInfo::AreaInfo(const AreaInfo& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  AreaInfo* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.dot_){from._impl_.dot_}
    , decltype(_impl_.id_){}
    , decltype(_impl_.width_){}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&_impl_.id_, &from._impl_.id_,
    static_cast<size_t>(reinterpret_cast<char*>(&_impl_.width_) -
    reinterpret_cast<char*>(&_impl_.id_)) + sizeof(_impl_.width_));
  // @@protoc_insertion_point(copy_constructor:perception.AreaInfo)
}

inline void AreaInfo::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.dot_){arena}
    , decltype(_impl_.id_){0}
    , decltype(_impl_.width_){0}
    , /*decltype(_impl_._cached_size_)*/{}
  };
}

AreaInfo::~AreaInfo() {
  // @@protoc_insertion_point(destructor:perception.AreaInfo)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void AreaInfo::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  _impl_.dot_.~RepeatedPtrField();
}

void AreaInfo::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void AreaInfo::Clear() {
// @@protoc_insertion_point(message_clear_start:perception.AreaInfo)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.dot_.Clear();
  ::memset(&_impl_.id_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&_impl_.width_) -
      reinterpret_cast<char*>(&_impl_.id_)) + sizeof(_impl_.width_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* AreaInfo::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // int32 id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          _impl_.id_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // float width = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 21)) {
          _impl_.width_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // repeated .perception.Position dot = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_dot(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<26>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* AreaInfo::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:perception.AreaInfo)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 id = 1;
  if (this->_internal_id() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(1, this->_internal_id(), target);
  }

  // float width = 2;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_width = this->_internal_width();
  uint32_t raw_width;
  memcpy(&raw_width, &tmp_width, sizeof(tmp_width));
  if (raw_width != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(2, this->_internal_width(), target);
  }

  // repeated .perception.Position dot = 3;
  for (unsigned i = 0,
      n = static_cast<unsigned>(this->_internal_dot_size()); i < n; i++) {
    const auto& repfield = this->_internal_dot(i);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
        InternalWriteMessage(3, repfield, repfield.GetCachedSize(), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:perception.AreaInfo)
  return target;
}

size_t AreaInfo::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:perception.AreaInfo)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .perception.Position dot = 3;
  total_size += 1UL * this->_internal_dot_size();
  for (const auto& msg : this->_impl_.dot_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // int32 id = 1;
  if (this->_internal_id() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_id());
  }

  // float width = 2;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_width = this->_internal_width();
  uint32_t raw_width;
  memcpy(&raw_width, &tmp_width, sizeof(tmp_width));
  if (raw_width != 0) {
    total_size += 1 + 4;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData AreaInfo::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    AreaInfo::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*AreaInfo::GetClassData() const { return &_class_data_; }


void AreaInfo::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<AreaInfo*>(&to_msg);
  auto& from = static_cast<const AreaInfo&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:perception.AreaInfo)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  _this->_impl_.dot_.MergeFrom(from._impl_.dot_);
  if (from._internal_id() != 0) {
    _this->_internal_set_id(from._internal_id());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_width = from._internal_width();
  uint32_t raw_width;
  memcpy(&raw_width, &tmp_width, sizeof(tmp_width));
  if (raw_width != 0) {
    _this->_internal_set_width(from._internal_width());
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void AreaInfo::CopyFrom(const AreaInfo& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:perception.AreaInfo)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool AreaInfo::IsInitialized() const {
  return true;
}

void AreaInfo::InternalSwap(AreaInfo* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  _impl_.dot_.InternalSwap(&other->_impl_.dot_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(AreaInfo, _impl_.width_)
      + sizeof(AreaInfo::_impl_.width_)
      - PROTOBUF_FIELD_OFFSET(AreaInfo, _impl_.id_)>(
          reinterpret_cast<char*>(&_impl_.id_),
          reinterpret_cast<char*>(&other->_impl_.id_));
}

::PROTOBUF_NAMESPACE_ID::Metadata AreaInfo::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_perception_2eproto_getter, &descriptor_table_perception_2eproto_once,
      file_level_metadata_perception_2eproto[5]);
}

// ===================================================================

class LaneCount::_Internal {
 public:
};

LaneCount::LaneCount(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:perception.LaneCount)
}
LaneCount::LaneCount(const LaneCount& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  LaneCount* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.laneno_){}
    , decltype(_impl_.volume_){}
    , decltype(_impl_.pcu_){}
    , decltype(_impl_.avspeed_){}
    , decltype(_impl_.occupancy_){}
    , decltype(_impl_.headway_){}
    , decltype(_impl_.gap_){}
    , decltype(_impl_.avdelay_){}
    , decltype(_impl_.avstop_){}
    , decltype(_impl_.speed85_){}
    , decltype(_impl_.queuelength_){}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&_impl_.laneno_, &from._impl_.laneno_,
    static_cast<size_t>(reinterpret_cast<char*>(&_impl_.queuelength_) -
    reinterpret_cast<char*>(&_impl_.laneno_)) + sizeof(_impl_.queuelength_));
  // @@protoc_insertion_point(copy_constructor:perception.LaneCount)
}

inline void LaneCount::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.laneno_){0}
    , decltype(_impl_.volume_){0}
    , decltype(_impl_.pcu_){0}
    , decltype(_impl_.avspeed_){0}
    , decltype(_impl_.occupancy_){0}
    , decltype(_impl_.headway_){0}
    , decltype(_impl_.gap_){0}
    , decltype(_impl_.avdelay_){0}
    , decltype(_impl_.avstop_){0}
    , decltype(_impl_.speed85_){0}
    , decltype(_impl_.queuelength_){0}
    , /*decltype(_impl_._cached_size_)*/{}
  };
}

LaneCount::~LaneCount() {
  // @@protoc_insertion_point(destructor:perception.LaneCount)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void LaneCount::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void LaneCount::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void LaneCount::Clear() {
// @@protoc_insertion_point(message_clear_start:perception.LaneCount)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&_impl_.laneno_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&_impl_.queuelength_) -
      reinterpret_cast<char*>(&_impl_.laneno_)) + sizeof(_impl_.queuelength_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* LaneCount::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // int32 laneNo = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          _impl_.laneno_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 volume = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          _impl_.volume_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 pcu = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          _impl_.pcu_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // float avSpeed = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 37)) {
          _impl_.avspeed_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float occupancy = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 45)) {
          _impl_.occupancy_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float headWay = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 53)) {
          _impl_.headway_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float gap = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 61)) {
          _impl_.gap_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float avDelay = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 69)) {
          _impl_.avdelay_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // int32 avStop = 9;
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 72)) {
          _impl_.avstop_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // float speed85 = 10;
      case 10:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 85)) {
          _impl_.speed85_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float queueLength = 11;
      case 11:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 93)) {
          _impl_.queuelength_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* LaneCount::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:perception.LaneCount)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 laneNo = 1;
  if (this->_internal_laneno() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(1, this->_internal_laneno(), target);
  }

  // int32 volume = 2;
  if (this->_internal_volume() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(2, this->_internal_volume(), target);
  }

  // int32 pcu = 3;
  if (this->_internal_pcu() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(3, this->_internal_pcu(), target);
  }

  // float avSpeed = 4;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_avspeed = this->_internal_avspeed();
  uint32_t raw_avspeed;
  memcpy(&raw_avspeed, &tmp_avspeed, sizeof(tmp_avspeed));
  if (raw_avspeed != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(4, this->_internal_avspeed(), target);
  }

  // float occupancy = 5;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_occupancy = this->_internal_occupancy();
  uint32_t raw_occupancy;
  memcpy(&raw_occupancy, &tmp_occupancy, sizeof(tmp_occupancy));
  if (raw_occupancy != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(5, this->_internal_occupancy(), target);
  }

  // float headWay = 6;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_headway = this->_internal_headway();
  uint32_t raw_headway;
  memcpy(&raw_headway, &tmp_headway, sizeof(tmp_headway));
  if (raw_headway != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(6, this->_internal_headway(), target);
  }

  // float gap = 7;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_gap = this->_internal_gap();
  uint32_t raw_gap;
  memcpy(&raw_gap, &tmp_gap, sizeof(tmp_gap));
  if (raw_gap != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(7, this->_internal_gap(), target);
  }

  // float avDelay = 8;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_avdelay = this->_internal_avdelay();
  uint32_t raw_avdelay;
  memcpy(&raw_avdelay, &tmp_avdelay, sizeof(tmp_avdelay));
  if (raw_avdelay != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(8, this->_internal_avdelay(), target);
  }

  // int32 avStop = 9;
  if (this->_internal_avstop() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(9, this->_internal_avstop(), target);
  }

  // float speed85 = 10;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_speed85 = this->_internal_speed85();
  uint32_t raw_speed85;
  memcpy(&raw_speed85, &tmp_speed85, sizeof(tmp_speed85));
  if (raw_speed85 != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(10, this->_internal_speed85(), target);
  }

  // float queueLength = 11;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_queuelength = this->_internal_queuelength();
  uint32_t raw_queuelength;
  memcpy(&raw_queuelength, &tmp_queuelength, sizeof(tmp_queuelength));
  if (raw_queuelength != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(11, this->_internal_queuelength(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:perception.LaneCount)
  return target;
}

size_t LaneCount::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:perception.LaneCount)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // int32 laneNo = 1;
  if (this->_internal_laneno() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_laneno());
  }

  // int32 volume = 2;
  if (this->_internal_volume() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_volume());
  }

  // int32 pcu = 3;
  if (this->_internal_pcu() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_pcu());
  }

  // float avSpeed = 4;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_avspeed = this->_internal_avspeed();
  uint32_t raw_avspeed;
  memcpy(&raw_avspeed, &tmp_avspeed, sizeof(tmp_avspeed));
  if (raw_avspeed != 0) {
    total_size += 1 + 4;
  }

  // float occupancy = 5;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_occupancy = this->_internal_occupancy();
  uint32_t raw_occupancy;
  memcpy(&raw_occupancy, &tmp_occupancy, sizeof(tmp_occupancy));
  if (raw_occupancy != 0) {
    total_size += 1 + 4;
  }

  // float headWay = 6;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_headway = this->_internal_headway();
  uint32_t raw_headway;
  memcpy(&raw_headway, &tmp_headway, sizeof(tmp_headway));
  if (raw_headway != 0) {
    total_size += 1 + 4;
  }

  // float gap = 7;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_gap = this->_internal_gap();
  uint32_t raw_gap;
  memcpy(&raw_gap, &tmp_gap, sizeof(tmp_gap));
  if (raw_gap != 0) {
    total_size += 1 + 4;
  }

  // float avDelay = 8;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_avdelay = this->_internal_avdelay();
  uint32_t raw_avdelay;
  memcpy(&raw_avdelay, &tmp_avdelay, sizeof(tmp_avdelay));
  if (raw_avdelay != 0) {
    total_size += 1 + 4;
  }

  // int32 avStop = 9;
  if (this->_internal_avstop() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_avstop());
  }

  // float speed85 = 10;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_speed85 = this->_internal_speed85();
  uint32_t raw_speed85;
  memcpy(&raw_speed85, &tmp_speed85, sizeof(tmp_speed85));
  if (raw_speed85 != 0) {
    total_size += 1 + 4;
  }

  // float queueLength = 11;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_queuelength = this->_internal_queuelength();
  uint32_t raw_queuelength;
  memcpy(&raw_queuelength, &tmp_queuelength, sizeof(tmp_queuelength));
  if (raw_queuelength != 0) {
    total_size += 1 + 4;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData LaneCount::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    LaneCount::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*LaneCount::GetClassData() const { return &_class_data_; }


void LaneCount::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<LaneCount*>(&to_msg);
  auto& from = static_cast<const LaneCount&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:perception.LaneCount)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_laneno() != 0) {
    _this->_internal_set_laneno(from._internal_laneno());
  }
  if (from._internal_volume() != 0) {
    _this->_internal_set_volume(from._internal_volume());
  }
  if (from._internal_pcu() != 0) {
    _this->_internal_set_pcu(from._internal_pcu());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_avspeed = from._internal_avspeed();
  uint32_t raw_avspeed;
  memcpy(&raw_avspeed, &tmp_avspeed, sizeof(tmp_avspeed));
  if (raw_avspeed != 0) {
    _this->_internal_set_avspeed(from._internal_avspeed());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_occupancy = from._internal_occupancy();
  uint32_t raw_occupancy;
  memcpy(&raw_occupancy, &tmp_occupancy, sizeof(tmp_occupancy));
  if (raw_occupancy != 0) {
    _this->_internal_set_occupancy(from._internal_occupancy());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_headway = from._internal_headway();
  uint32_t raw_headway;
  memcpy(&raw_headway, &tmp_headway, sizeof(tmp_headway));
  if (raw_headway != 0) {
    _this->_internal_set_headway(from._internal_headway());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_gap = from._internal_gap();
  uint32_t raw_gap;
  memcpy(&raw_gap, &tmp_gap, sizeof(tmp_gap));
  if (raw_gap != 0) {
    _this->_internal_set_gap(from._internal_gap());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_avdelay = from._internal_avdelay();
  uint32_t raw_avdelay;
  memcpy(&raw_avdelay, &tmp_avdelay, sizeof(tmp_avdelay));
  if (raw_avdelay != 0) {
    _this->_internal_set_avdelay(from._internal_avdelay());
  }
  if (from._internal_avstop() != 0) {
    _this->_internal_set_avstop(from._internal_avstop());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_speed85 = from._internal_speed85();
  uint32_t raw_speed85;
  memcpy(&raw_speed85, &tmp_speed85, sizeof(tmp_speed85));
  if (raw_speed85 != 0) {
    _this->_internal_set_speed85(from._internal_speed85());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_queuelength = from._internal_queuelength();
  uint32_t raw_queuelength;
  memcpy(&raw_queuelength, &tmp_queuelength, sizeof(tmp_queuelength));
  if (raw_queuelength != 0) {
    _this->_internal_set_queuelength(from._internal_queuelength());
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void LaneCount::CopyFrom(const LaneCount& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:perception.LaneCount)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool LaneCount::IsInitialized() const {
  return true;
}

void LaneCount::InternalSwap(LaneCount* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(LaneCount, _impl_.queuelength_)
      + sizeof(LaneCount::_impl_.queuelength_)
      - PROTOBUF_FIELD_OFFSET(LaneCount, _impl_.laneno_)>(
          reinterpret_cast<char*>(&_impl_.laneno_),
          reinterpret_cast<char*>(&other->_impl_.laneno_));
}

::PROTOBUF_NAMESPACE_ID::Metadata LaneCount::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_perception_2eproto_getter, &descriptor_table_perception_2eproto_once,
      file_level_metadata_perception_2eproto[6]);
}

// ===================================================================

class RoadCount::_Internal {
 public:
  static const ::perception::Position& roadincoord(const RoadCount* msg);
};

const ::perception::Position&
RoadCount::_Internal::roadincoord(const RoadCount* msg) {
  return *msg->_impl_.roadincoord_;
}
RoadCount::RoadCount(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:perception.RoadCount)
}
RoadCount::RoadCount(const RoadCount& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  RoadCount* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.lanecount_){from._impl_.lanecount_}
    , decltype(_impl_.deviceid_){}
    , decltype(_impl_.roadname_){}
    , decltype(_impl_.roadincoord_){nullptr}
    , decltype(_impl_.heading_){}
    , decltype(_impl_.volume_){}
    , decltype(_impl_.pcu_){}
    , decltype(_impl_.avspeed_){}
    , decltype(_impl_.occupancy_){}
    , decltype(_impl_.headway_){}
    , decltype(_impl_.gap_){}
    , decltype(_impl_.avdelay_){}
    , decltype(_impl_.avstop_){}
    , decltype(_impl_.speed85_){}
    , decltype(_impl_.queuelength_){}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  _impl_.deviceid_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.deviceid_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_deviceid().empty()) {
    _this->_impl_.deviceid_.Set(from._internal_deviceid(), 
      _this->GetArenaForAllocation());
  }
  _impl_.roadname_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.roadname_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_roadname().empty()) {
    _this->_impl_.roadname_.Set(from._internal_roadname(), 
      _this->GetArenaForAllocation());
  }
  if (from._internal_has_roadincoord()) {
    _this->_impl_.roadincoord_ = new ::perception::Position(*from._impl_.roadincoord_);
  }
  ::memcpy(&_impl_.heading_, &from._impl_.heading_,
    static_cast<size_t>(reinterpret_cast<char*>(&_impl_.queuelength_) -
    reinterpret_cast<char*>(&_impl_.heading_)) + sizeof(_impl_.queuelength_));
  // @@protoc_insertion_point(copy_constructor:perception.RoadCount)
}

inline void RoadCount::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.lanecount_){arena}
    , decltype(_impl_.deviceid_){}
    , decltype(_impl_.roadname_){}
    , decltype(_impl_.roadincoord_){nullptr}
    , decltype(_impl_.heading_){0}
    , decltype(_impl_.volume_){0}
    , decltype(_impl_.pcu_){0}
    , decltype(_impl_.avspeed_){0}
    , decltype(_impl_.occupancy_){0}
    , decltype(_impl_.headway_){0}
    , decltype(_impl_.gap_){0}
    , decltype(_impl_.avdelay_){0}
    , decltype(_impl_.avstop_){0}
    , decltype(_impl_.speed85_){0}
    , decltype(_impl_.queuelength_){0}
    , /*decltype(_impl_._cached_size_)*/{}
  };
  _impl_.deviceid_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.deviceid_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  _impl_.roadname_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.roadname_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

RoadCount::~RoadCount() {
  // @@protoc_insertion_point(destructor:perception.RoadCount)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void RoadCount::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  _impl_.lanecount_.~RepeatedPtrField();
  _impl_.deviceid_.Destroy();
  _impl_.roadname_.Destroy();
  if (this != internal_default_instance()) delete _impl_.roadincoord_;
}

void RoadCount::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void RoadCount::Clear() {
// @@protoc_insertion_point(message_clear_start:perception.RoadCount)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.lanecount_.Clear();
  _impl_.deviceid_.ClearToEmpty();
  _impl_.roadname_.ClearToEmpty();
  if (GetArenaForAllocation() == nullptr && _impl_.roadincoord_ != nullptr) {
    delete _impl_.roadincoord_;
  }
  _impl_.roadincoord_ = nullptr;
  ::memset(&_impl_.heading_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&_impl_.queuelength_) -
      reinterpret_cast<char*>(&_impl_.heading_)) + sizeof(_impl_.queuelength_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* RoadCount::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string deviceId = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_deviceid();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          CHK_(::_pbi::VerifyUTF8(str, "perception.RoadCount.deviceId"));
        } else
          goto handle_unusual;
        continue;
      // float heading = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 21)) {
          _impl_.heading_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // string roadName = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          auto str = _internal_mutable_roadname();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          CHK_(::_pbi::VerifyUTF8(str, "perception.RoadCount.roadName"));
        } else
          goto handle_unusual;
        continue;
      // .perception.Position roadInCoord = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 34)) {
          ptr = ctx->ParseMessage(_internal_mutable_roadincoord(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 volume = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 40)) {
          _impl_.volume_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 pcu = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 48)) {
          _impl_.pcu_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // float avSpeed = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 61)) {
          _impl_.avspeed_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float occupancy = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 69)) {
          _impl_.occupancy_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float headWay = 9;
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 77)) {
          _impl_.headway_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float gap = 10;
      case 10:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 85)) {
          _impl_.gap_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float avDelay = 11;
      case 11:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 93)) {
          _impl_.avdelay_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // int32 avStop = 12;
      case 12:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 96)) {
          _impl_.avstop_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // float speed85 = 13;
      case 13:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 109)) {
          _impl_.speed85_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float queueLength = 14;
      case 14:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 117)) {
          _impl_.queuelength_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // repeated .perception.LaneCount laneCount = 15;
      case 15:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 122)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_lanecount(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<122>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* RoadCount::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:perception.RoadCount)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string deviceId = 1;
  if (!this->_internal_deviceid().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_deviceid().data(), static_cast<int>(this->_internal_deviceid().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "perception.RoadCount.deviceId");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_deviceid(), target);
  }

  // float heading = 2;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_heading = this->_internal_heading();
  uint32_t raw_heading;
  memcpy(&raw_heading, &tmp_heading, sizeof(tmp_heading));
  if (raw_heading != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(2, this->_internal_heading(), target);
  }

  // string roadName = 3;
  if (!this->_internal_roadname().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_roadname().data(), static_cast<int>(this->_internal_roadname().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "perception.RoadCount.roadName");
    target = stream->WriteStringMaybeAliased(
        3, this->_internal_roadname(), target);
  }

  // .perception.Position roadInCoord = 4;
  if (this->_internal_has_roadincoord()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(4, _Internal::roadincoord(this),
        _Internal::roadincoord(this).GetCachedSize(), target, stream);
  }

  // int32 volume = 5;
  if (this->_internal_volume() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(5, this->_internal_volume(), target);
  }

  // int32 pcu = 6;
  if (this->_internal_pcu() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(6, this->_internal_pcu(), target);
  }

  // float avSpeed = 7;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_avspeed = this->_internal_avspeed();
  uint32_t raw_avspeed;
  memcpy(&raw_avspeed, &tmp_avspeed, sizeof(tmp_avspeed));
  if (raw_avspeed != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(7, this->_internal_avspeed(), target);
  }

  // float occupancy = 8;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_occupancy = this->_internal_occupancy();
  uint32_t raw_occupancy;
  memcpy(&raw_occupancy, &tmp_occupancy, sizeof(tmp_occupancy));
  if (raw_occupancy != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(8, this->_internal_occupancy(), target);
  }

  // float headWay = 9;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_headway = this->_internal_headway();
  uint32_t raw_headway;
  memcpy(&raw_headway, &tmp_headway, sizeof(tmp_headway));
  if (raw_headway != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(9, this->_internal_headway(), target);
  }

  // float gap = 10;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_gap = this->_internal_gap();
  uint32_t raw_gap;
  memcpy(&raw_gap, &tmp_gap, sizeof(tmp_gap));
  if (raw_gap != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(10, this->_internal_gap(), target);
  }

  // float avDelay = 11;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_avdelay = this->_internal_avdelay();
  uint32_t raw_avdelay;
  memcpy(&raw_avdelay, &tmp_avdelay, sizeof(tmp_avdelay));
  if (raw_avdelay != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(11, this->_internal_avdelay(), target);
  }

  // int32 avStop = 12;
  if (this->_internal_avstop() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(12, this->_internal_avstop(), target);
  }

  // float speed85 = 13;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_speed85 = this->_internal_speed85();
  uint32_t raw_speed85;
  memcpy(&raw_speed85, &tmp_speed85, sizeof(tmp_speed85));
  if (raw_speed85 != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(13, this->_internal_speed85(), target);
  }

  // float queueLength = 14;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_queuelength = this->_internal_queuelength();
  uint32_t raw_queuelength;
  memcpy(&raw_queuelength, &tmp_queuelength, sizeof(tmp_queuelength));
  if (raw_queuelength != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(14, this->_internal_queuelength(), target);
  }

  // repeated .perception.LaneCount laneCount = 15;
  for (unsigned i = 0,
      n = static_cast<unsigned>(this->_internal_lanecount_size()); i < n; i++) {
    const auto& repfield = this->_internal_lanecount(i);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
        InternalWriteMessage(15, repfield, repfield.GetCachedSize(), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:perception.RoadCount)
  return target;
}

size_t RoadCount::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:perception.RoadCount)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .perception.LaneCount laneCount = 15;
  total_size += 1UL * this->_internal_lanecount_size();
  for (const auto& msg : this->_impl_.lanecount_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // string deviceId = 1;
  if (!this->_internal_deviceid().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_deviceid());
  }

  // string roadName = 3;
  if (!this->_internal_roadname().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_roadname());
  }

  // .perception.Position roadInCoord = 4;
  if (this->_internal_has_roadincoord()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *_impl_.roadincoord_);
  }

  // float heading = 2;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_heading = this->_internal_heading();
  uint32_t raw_heading;
  memcpy(&raw_heading, &tmp_heading, sizeof(tmp_heading));
  if (raw_heading != 0) {
    total_size += 1 + 4;
  }

  // int32 volume = 5;
  if (this->_internal_volume() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_volume());
  }

  // int32 pcu = 6;
  if (this->_internal_pcu() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_pcu());
  }

  // float avSpeed = 7;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_avspeed = this->_internal_avspeed();
  uint32_t raw_avspeed;
  memcpy(&raw_avspeed, &tmp_avspeed, sizeof(tmp_avspeed));
  if (raw_avspeed != 0) {
    total_size += 1 + 4;
  }

  // float occupancy = 8;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_occupancy = this->_internal_occupancy();
  uint32_t raw_occupancy;
  memcpy(&raw_occupancy, &tmp_occupancy, sizeof(tmp_occupancy));
  if (raw_occupancy != 0) {
    total_size += 1 + 4;
  }

  // float headWay = 9;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_headway = this->_internal_headway();
  uint32_t raw_headway;
  memcpy(&raw_headway, &tmp_headway, sizeof(tmp_headway));
  if (raw_headway != 0) {
    total_size += 1 + 4;
  }

  // float gap = 10;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_gap = this->_internal_gap();
  uint32_t raw_gap;
  memcpy(&raw_gap, &tmp_gap, sizeof(tmp_gap));
  if (raw_gap != 0) {
    total_size += 1 + 4;
  }

  // float avDelay = 11;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_avdelay = this->_internal_avdelay();
  uint32_t raw_avdelay;
  memcpy(&raw_avdelay, &tmp_avdelay, sizeof(tmp_avdelay));
  if (raw_avdelay != 0) {
    total_size += 1 + 4;
  }

  // int32 avStop = 12;
  if (this->_internal_avstop() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_avstop());
  }

  // float speed85 = 13;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_speed85 = this->_internal_speed85();
  uint32_t raw_speed85;
  memcpy(&raw_speed85, &tmp_speed85, sizeof(tmp_speed85));
  if (raw_speed85 != 0) {
    total_size += 1 + 4;
  }

  // float queueLength = 14;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_queuelength = this->_internal_queuelength();
  uint32_t raw_queuelength;
  memcpy(&raw_queuelength, &tmp_queuelength, sizeof(tmp_queuelength));
  if (raw_queuelength != 0) {
    total_size += 1 + 4;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData RoadCount::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    RoadCount::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*RoadCount::GetClassData() const { return &_class_data_; }


void RoadCount::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<RoadCount*>(&to_msg);
  auto& from = static_cast<const RoadCount&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:perception.RoadCount)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  _this->_impl_.lanecount_.MergeFrom(from._impl_.lanecount_);
  if (!from._internal_deviceid().empty()) {
    _this->_internal_set_deviceid(from._internal_deviceid());
  }
  if (!from._internal_roadname().empty()) {
    _this->_internal_set_roadname(from._internal_roadname());
  }
  if (from._internal_has_roadincoord()) {
    _this->_internal_mutable_roadincoord()->::perception::Position::MergeFrom(
        from._internal_roadincoord());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_heading = from._internal_heading();
  uint32_t raw_heading;
  memcpy(&raw_heading, &tmp_heading, sizeof(tmp_heading));
  if (raw_heading != 0) {
    _this->_internal_set_heading(from._internal_heading());
  }
  if (from._internal_volume() != 0) {
    _this->_internal_set_volume(from._internal_volume());
  }
  if (from._internal_pcu() != 0) {
    _this->_internal_set_pcu(from._internal_pcu());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_avspeed = from._internal_avspeed();
  uint32_t raw_avspeed;
  memcpy(&raw_avspeed, &tmp_avspeed, sizeof(tmp_avspeed));
  if (raw_avspeed != 0) {
    _this->_internal_set_avspeed(from._internal_avspeed());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_occupancy = from._internal_occupancy();
  uint32_t raw_occupancy;
  memcpy(&raw_occupancy, &tmp_occupancy, sizeof(tmp_occupancy));
  if (raw_occupancy != 0) {
    _this->_internal_set_occupancy(from._internal_occupancy());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_headway = from._internal_headway();
  uint32_t raw_headway;
  memcpy(&raw_headway, &tmp_headway, sizeof(tmp_headway));
  if (raw_headway != 0) {
    _this->_internal_set_headway(from._internal_headway());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_gap = from._internal_gap();
  uint32_t raw_gap;
  memcpy(&raw_gap, &tmp_gap, sizeof(tmp_gap));
  if (raw_gap != 0) {
    _this->_internal_set_gap(from._internal_gap());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_avdelay = from._internal_avdelay();
  uint32_t raw_avdelay;
  memcpy(&raw_avdelay, &tmp_avdelay, sizeof(tmp_avdelay));
  if (raw_avdelay != 0) {
    _this->_internal_set_avdelay(from._internal_avdelay());
  }
  if (from._internal_avstop() != 0) {
    _this->_internal_set_avstop(from._internal_avstop());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_speed85 = from._internal_speed85();
  uint32_t raw_speed85;
  memcpy(&raw_speed85, &tmp_speed85, sizeof(tmp_speed85));
  if (raw_speed85 != 0) {
    _this->_internal_set_speed85(from._internal_speed85());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_queuelength = from._internal_queuelength();
  uint32_t raw_queuelength;
  memcpy(&raw_queuelength, &tmp_queuelength, sizeof(tmp_queuelength));
  if (raw_queuelength != 0) {
    _this->_internal_set_queuelength(from._internal_queuelength());
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void RoadCount::CopyFrom(const RoadCount& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:perception.RoadCount)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RoadCount::IsInitialized() const {
  return true;
}

void RoadCount::InternalSwap(RoadCount* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  _impl_.lanecount_.InternalSwap(&other->_impl_.lanecount_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.deviceid_, lhs_arena,
      &other->_impl_.deviceid_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.roadname_, lhs_arena,
      &other->_impl_.roadname_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(RoadCount, _impl_.queuelength_)
      + sizeof(RoadCount::_impl_.queuelength_)
      - PROTOBUF_FIELD_OFFSET(RoadCount, _impl_.roadincoord_)>(
          reinterpret_cast<char*>(&_impl_.roadincoord_),
          reinterpret_cast<char*>(&other->_impl_.roadincoord_));
}

::PROTOBUF_NAMESPACE_ID::Metadata RoadCount::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_perception_2eproto_getter, &descriptor_table_perception_2eproto_once,
      file_level_metadata_perception_2eproto[7]);
}

// ===================================================================

class IntersectionCount::_Internal {
 public:
};

IntersectionCount::IntersectionCount(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:perception.IntersectionCount)
}
IntersectionCount::IntersectionCount(const IntersectionCount& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  IntersectionCount* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.roadcount_){from._impl_.roadcount_}
    , decltype(_impl_.volume_){}
    , decltype(_impl_.pcu_){}
    , decltype(_impl_.avspeed_){}
    , decltype(_impl_.occupancy_){}
    , decltype(_impl_.headway_){}
    , decltype(_impl_.gap_){}
    , decltype(_impl_.avdelay_){}
    , decltype(_impl_.avstop_){}
    , decltype(_impl_.speed85_){}
    , decltype(_impl_.queuelength_){}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&_impl_.volume_, &from._impl_.volume_,
    static_cast<size_t>(reinterpret_cast<char*>(&_impl_.queuelength_) -
    reinterpret_cast<char*>(&_impl_.volume_)) + sizeof(_impl_.queuelength_));
  // @@protoc_insertion_point(copy_constructor:perception.IntersectionCount)
}

inline void IntersectionCount::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.roadcount_){arena}
    , decltype(_impl_.volume_){0}
    , decltype(_impl_.pcu_){0}
    , decltype(_impl_.avspeed_){0}
    , decltype(_impl_.occupancy_){0}
    , decltype(_impl_.headway_){0}
    , decltype(_impl_.gap_){0}
    , decltype(_impl_.avdelay_){0}
    , decltype(_impl_.avstop_){0}
    , decltype(_impl_.speed85_){0}
    , decltype(_impl_.queuelength_){0}
    , /*decltype(_impl_._cached_size_)*/{}
  };
}

IntersectionCount::~IntersectionCount() {
  // @@protoc_insertion_point(destructor:perception.IntersectionCount)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void IntersectionCount::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  _impl_.roadcount_.~RepeatedPtrField();
}

void IntersectionCount::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void IntersectionCount::Clear() {
// @@protoc_insertion_point(message_clear_start:perception.IntersectionCount)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.roadcount_.Clear();
  ::memset(&_impl_.volume_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&_impl_.queuelength_) -
      reinterpret_cast<char*>(&_impl_.volume_)) + sizeof(_impl_.queuelength_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* IntersectionCount::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // int32 volume = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          _impl_.volume_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 pcu = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          _impl_.pcu_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // float avSpeed = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 29)) {
          _impl_.avspeed_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float occupancy = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 37)) {
          _impl_.occupancy_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float headWay = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 45)) {
          _impl_.headway_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float gap = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 53)) {
          _impl_.gap_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float avDelay = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 61)) {
          _impl_.avdelay_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // int32 avStop = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 64)) {
          _impl_.avstop_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // float speed85 = 9;
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 77)) {
          _impl_.speed85_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float queueLength = 10;
      case 10:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 85)) {
          _impl_.queuelength_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // repeated .perception.RoadCount roadCount = 11;
      case 11:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 90)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_roadcount(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<90>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* IntersectionCount::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:perception.IntersectionCount)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 volume = 1;
  if (this->_internal_volume() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(1, this->_internal_volume(), target);
  }

  // int32 pcu = 2;
  if (this->_internal_pcu() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(2, this->_internal_pcu(), target);
  }

  // float avSpeed = 3;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_avspeed = this->_internal_avspeed();
  uint32_t raw_avspeed;
  memcpy(&raw_avspeed, &tmp_avspeed, sizeof(tmp_avspeed));
  if (raw_avspeed != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(3, this->_internal_avspeed(), target);
  }

  // float occupancy = 4;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_occupancy = this->_internal_occupancy();
  uint32_t raw_occupancy;
  memcpy(&raw_occupancy, &tmp_occupancy, sizeof(tmp_occupancy));
  if (raw_occupancy != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(4, this->_internal_occupancy(), target);
  }

  // float headWay = 5;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_headway = this->_internal_headway();
  uint32_t raw_headway;
  memcpy(&raw_headway, &tmp_headway, sizeof(tmp_headway));
  if (raw_headway != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(5, this->_internal_headway(), target);
  }

  // float gap = 6;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_gap = this->_internal_gap();
  uint32_t raw_gap;
  memcpy(&raw_gap, &tmp_gap, sizeof(tmp_gap));
  if (raw_gap != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(6, this->_internal_gap(), target);
  }

  // float avDelay = 7;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_avdelay = this->_internal_avdelay();
  uint32_t raw_avdelay;
  memcpy(&raw_avdelay, &tmp_avdelay, sizeof(tmp_avdelay));
  if (raw_avdelay != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(7, this->_internal_avdelay(), target);
  }

  // int32 avStop = 8;
  if (this->_internal_avstop() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(8, this->_internal_avstop(), target);
  }

  // float speed85 = 9;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_speed85 = this->_internal_speed85();
  uint32_t raw_speed85;
  memcpy(&raw_speed85, &tmp_speed85, sizeof(tmp_speed85));
  if (raw_speed85 != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(9, this->_internal_speed85(), target);
  }

  // float queueLength = 10;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_queuelength = this->_internal_queuelength();
  uint32_t raw_queuelength;
  memcpy(&raw_queuelength, &tmp_queuelength, sizeof(tmp_queuelength));
  if (raw_queuelength != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(10, this->_internal_queuelength(), target);
  }

  // repeated .perception.RoadCount roadCount = 11;
  for (unsigned i = 0,
      n = static_cast<unsigned>(this->_internal_roadcount_size()); i < n; i++) {
    const auto& repfield = this->_internal_roadcount(i);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
        InternalWriteMessage(11, repfield, repfield.GetCachedSize(), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:perception.IntersectionCount)
  return target;
}

size_t IntersectionCount::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:perception.IntersectionCount)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .perception.RoadCount roadCount = 11;
  total_size += 1UL * this->_internal_roadcount_size();
  for (const auto& msg : this->_impl_.roadcount_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // int32 volume = 1;
  if (this->_internal_volume() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_volume());
  }

  // int32 pcu = 2;
  if (this->_internal_pcu() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_pcu());
  }

  // float avSpeed = 3;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_avspeed = this->_internal_avspeed();
  uint32_t raw_avspeed;
  memcpy(&raw_avspeed, &tmp_avspeed, sizeof(tmp_avspeed));
  if (raw_avspeed != 0) {
    total_size += 1 + 4;
  }

  // float occupancy = 4;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_occupancy = this->_internal_occupancy();
  uint32_t raw_occupancy;
  memcpy(&raw_occupancy, &tmp_occupancy, sizeof(tmp_occupancy));
  if (raw_occupancy != 0) {
    total_size += 1 + 4;
  }

  // float headWay = 5;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_headway = this->_internal_headway();
  uint32_t raw_headway;
  memcpy(&raw_headway, &tmp_headway, sizeof(tmp_headway));
  if (raw_headway != 0) {
    total_size += 1 + 4;
  }

  // float gap = 6;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_gap = this->_internal_gap();
  uint32_t raw_gap;
  memcpy(&raw_gap, &tmp_gap, sizeof(tmp_gap));
  if (raw_gap != 0) {
    total_size += 1 + 4;
  }

  // float avDelay = 7;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_avdelay = this->_internal_avdelay();
  uint32_t raw_avdelay;
  memcpy(&raw_avdelay, &tmp_avdelay, sizeof(tmp_avdelay));
  if (raw_avdelay != 0) {
    total_size += 1 + 4;
  }

  // int32 avStop = 8;
  if (this->_internal_avstop() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_avstop());
  }

  // float speed85 = 9;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_speed85 = this->_internal_speed85();
  uint32_t raw_speed85;
  memcpy(&raw_speed85, &tmp_speed85, sizeof(tmp_speed85));
  if (raw_speed85 != 0) {
    total_size += 1 + 4;
  }

  // float queueLength = 10;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_queuelength = this->_internal_queuelength();
  uint32_t raw_queuelength;
  memcpy(&raw_queuelength, &tmp_queuelength, sizeof(tmp_queuelength));
  if (raw_queuelength != 0) {
    total_size += 1 + 4;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData IntersectionCount::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    IntersectionCount::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*IntersectionCount::GetClassData() const { return &_class_data_; }


void IntersectionCount::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<IntersectionCount*>(&to_msg);
  auto& from = static_cast<const IntersectionCount&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:perception.IntersectionCount)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  _this->_impl_.roadcount_.MergeFrom(from._impl_.roadcount_);
  if (from._internal_volume() != 0) {
    _this->_internal_set_volume(from._internal_volume());
  }
  if (from._internal_pcu() != 0) {
    _this->_internal_set_pcu(from._internal_pcu());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_avspeed = from._internal_avspeed();
  uint32_t raw_avspeed;
  memcpy(&raw_avspeed, &tmp_avspeed, sizeof(tmp_avspeed));
  if (raw_avspeed != 0) {
    _this->_internal_set_avspeed(from._internal_avspeed());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_occupancy = from._internal_occupancy();
  uint32_t raw_occupancy;
  memcpy(&raw_occupancy, &tmp_occupancy, sizeof(tmp_occupancy));
  if (raw_occupancy != 0) {
    _this->_internal_set_occupancy(from._internal_occupancy());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_headway = from._internal_headway();
  uint32_t raw_headway;
  memcpy(&raw_headway, &tmp_headway, sizeof(tmp_headway));
  if (raw_headway != 0) {
    _this->_internal_set_headway(from._internal_headway());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_gap = from._internal_gap();
  uint32_t raw_gap;
  memcpy(&raw_gap, &tmp_gap, sizeof(tmp_gap));
  if (raw_gap != 0) {
    _this->_internal_set_gap(from._internal_gap());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_avdelay = from._internal_avdelay();
  uint32_t raw_avdelay;
  memcpy(&raw_avdelay, &tmp_avdelay, sizeof(tmp_avdelay));
  if (raw_avdelay != 0) {
    _this->_internal_set_avdelay(from._internal_avdelay());
  }
  if (from._internal_avstop() != 0) {
    _this->_internal_set_avstop(from._internal_avstop());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_speed85 = from._internal_speed85();
  uint32_t raw_speed85;
  memcpy(&raw_speed85, &tmp_speed85, sizeof(tmp_speed85));
  if (raw_speed85 != 0) {
    _this->_internal_set_speed85(from._internal_speed85());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_queuelength = from._internal_queuelength();
  uint32_t raw_queuelength;
  memcpy(&raw_queuelength, &tmp_queuelength, sizeof(tmp_queuelength));
  if (raw_queuelength != 0) {
    _this->_internal_set_queuelength(from._internal_queuelength());
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void IntersectionCount::CopyFrom(const IntersectionCount& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:perception.IntersectionCount)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool IntersectionCount::IsInitialized() const {
  return true;
}

void IntersectionCount::InternalSwap(IntersectionCount* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  _impl_.roadcount_.InternalSwap(&other->_impl_.roadcount_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(IntersectionCount, _impl_.queuelength_)
      + sizeof(IntersectionCount::_impl_.queuelength_)
      - PROTOBUF_FIELD_OFFSET(IntersectionCount, _impl_.volume_)>(
          reinterpret_cast<char*>(&_impl_.volume_),
          reinterpret_cast<char*>(&other->_impl_.volume_));
}

::PROTOBUF_NAMESPACE_ID::Metadata IntersectionCount::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_perception_2eproto_getter, &descriptor_table_perception_2eproto_once,
      file_level_metadata_perception_2eproto[8]);
}

// ===================================================================

class Event::_Internal {
 public:
};

Event::Event(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:perception.Event)
}
Event::Event(const Event& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  Event* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.pos_){from._impl_.pos_}
    , decltype(_impl_.refpath_){from._impl_.refpath_}
    , decltype(_impl_.device_sn_){}
    , decltype(_impl_.license_plate_){}
    , decltype(_impl_.event_type_){}
    , decltype(_impl_.level_){}
    , decltype(_impl_.lane_id_){}
    , decltype(_impl_.fusion_state_){}
    , decltype(_impl_.track_time_){}
    , decltype(_impl_.ttl_){}
    , decltype(_impl_.source_){}
    , decltype(_impl_.DataType_){}
    , /*decltype(_impl_._cached_size_)*/{}
    , /*decltype(_impl_._oneof_case_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  _impl_.device_sn_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.device_sn_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_device_sn().empty()) {
    _this->_impl_.device_sn_.Set(from._internal_device_sn(), 
      _this->GetArenaForAllocation());
  }
  _impl_.license_plate_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.license_plate_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_license_plate().empty()) {
    _this->_impl_.license_plate_.Set(from._internal_license_plate(), 
      _this->GetArenaForAllocation());
  }
  ::memcpy(&_impl_.event_type_, &from._impl_.event_type_,
    static_cast<size_t>(reinterpret_cast<char*>(&_impl_.source_) -
    reinterpret_cast<char*>(&_impl_.event_type_)) + sizeof(_impl_.source_));
  clear_has_DataType();
  switch (from.DataType_case()) {
    case kStringData: {
      _this->_internal_set_string_data(from._internal_string_data());
      break;
    }
    case kIntData: {
      _this->_internal_set_int_data(from._internal_int_data());
      break;
    }
    case DATATYPE_NOT_SET: {
      break;
    }
  }
  // @@protoc_insertion_point(copy_constructor:perception.Event)
}

inline void Event::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.pos_){arena}
    , decltype(_impl_.refpath_){arena}
    , decltype(_impl_.device_sn_){}
    , decltype(_impl_.license_plate_){}
    , decltype(_impl_.event_type_){0}
    , decltype(_impl_.level_){0}
    , decltype(_impl_.lane_id_){0}
    , decltype(_impl_.fusion_state_){0}
    , decltype(_impl_.track_time_){int64_t{0}}
    , decltype(_impl_.ttl_){int64_t{0}}
    , decltype(_impl_.source_){0}
    , decltype(_impl_.DataType_){}
    , /*decltype(_impl_._cached_size_)*/{}
    , /*decltype(_impl_._oneof_case_)*/{}
  };
  _impl_.device_sn_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.device_sn_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  _impl_.license_plate_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.license_plate_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  clear_has_DataType();
}

Event::~Event() {
  // @@protoc_insertion_point(destructor:perception.Event)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void Event::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  _impl_.pos_.~RepeatedPtrField();
  _impl_.refpath_.~RepeatedPtrField();
  _impl_.device_sn_.Destroy();
  _impl_.license_plate_.Destroy();
  if (has_DataType()) {
    clear_DataType();
  }
}

void Event::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void Event::clear_DataType() {
// @@protoc_insertion_point(one_of_clear_start:perception.Event)
  switch (DataType_case()) {
    case kStringData: {
      _impl_.DataType_.string_data_.Destroy();
      break;
    }
    case kIntData: {
      // No need to clear
      break;
    }
    case DATATYPE_NOT_SET: {
      break;
    }
  }
  _impl_._oneof_case_[0] = DATATYPE_NOT_SET;
}


void Event::Clear() {
// @@protoc_insertion_point(message_clear_start:perception.Event)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.pos_.Clear();
  _impl_.refpath_.Clear();
  _impl_.device_sn_.ClearToEmpty();
  _impl_.license_plate_.ClearToEmpty();
  ::memset(&_impl_.event_type_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&_impl_.source_) -
      reinterpret_cast<char*>(&_impl_.event_type_)) + sizeof(_impl_.source_));
  clear_DataType();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* Event::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // int32 event_type = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          _impl_.event_type_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 level = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          _impl_.level_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string device_sn = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          auto str = _internal_mutable_device_sn();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          CHK_(::_pbi::VerifyUTF8(str, "perception.Event.device_sn"));
        } else
          goto handle_unusual;
        continue;
      // int32 lane_id = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 32)) {
          _impl_.lane_id_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 fusion_state = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 40)) {
          _impl_.fusion_state_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 source = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 48)) {
          _impl_.source_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated .perception.Position pos = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 58)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_pos(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<58>(ptr));
        } else
          goto handle_unusual;
        continue;
      // string license_plate = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 66)) {
          auto str = _internal_mutable_license_plate();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          CHK_(::_pbi::VerifyUTF8(str, "perception.Event.license_plate"));
        } else
          goto handle_unusual;
        continue;
      // int64 track_time = 9;
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 72)) {
          _impl_.track_time_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int64 ttl = 10;
      case 10:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 80)) {
          _impl_.ttl_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated .perception.AreaInfo refpath = 11;
      case 11:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 90)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_refpath(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<90>(ptr));
        } else
          goto handle_unusual;
        continue;
      // string string_data = 12;
      case 12:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 98)) {
          auto str = _internal_mutable_string_data();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          CHK_(::_pbi::VerifyUTF8(str, "perception.Event.string_data"));
        } else
          goto handle_unusual;
        continue;
      // int32 int_data = 13;
      case 13:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 104)) {
          _internal_set_int_data(::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* Event::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:perception.Event)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 event_type = 1;
  if (this->_internal_event_type() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(1, this->_internal_event_type(), target);
  }

  // int32 level = 2;
  if (this->_internal_level() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(2, this->_internal_level(), target);
  }

  // string device_sn = 3;
  if (!this->_internal_device_sn().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_device_sn().data(), static_cast<int>(this->_internal_device_sn().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "perception.Event.device_sn");
    target = stream->WriteStringMaybeAliased(
        3, this->_internal_device_sn(), target);
  }

  // int32 lane_id = 4;
  if (this->_internal_lane_id() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(4, this->_internal_lane_id(), target);
  }

  // int32 fusion_state = 5;
  if (this->_internal_fusion_state() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(5, this->_internal_fusion_state(), target);
  }

  // int32 source = 6;
  if (this->_internal_source() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(6, this->_internal_source(), target);
  }

  // repeated .perception.Position pos = 7;
  for (unsigned i = 0,
      n = static_cast<unsigned>(this->_internal_pos_size()); i < n; i++) {
    const auto& repfield = this->_internal_pos(i);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
        InternalWriteMessage(7, repfield, repfield.GetCachedSize(), target, stream);
  }

  // string license_plate = 8;
  if (!this->_internal_license_plate().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_license_plate().data(), static_cast<int>(this->_internal_license_plate().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "perception.Event.license_plate");
    target = stream->WriteStringMaybeAliased(
        8, this->_internal_license_plate(), target);
  }

  // int64 track_time = 9;
  if (this->_internal_track_time() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt64ToArray(9, this->_internal_track_time(), target);
  }

  // int64 ttl = 10;
  if (this->_internal_ttl() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt64ToArray(10, this->_internal_ttl(), target);
  }

  // repeated .perception.AreaInfo refpath = 11;
  for (unsigned i = 0,
      n = static_cast<unsigned>(this->_internal_refpath_size()); i < n; i++) {
    const auto& repfield = this->_internal_refpath(i);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
        InternalWriteMessage(11, repfield, repfield.GetCachedSize(), target, stream);
  }

  // string string_data = 12;
  if (_internal_has_string_data()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_string_data().data(), static_cast<int>(this->_internal_string_data().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "perception.Event.string_data");
    target = stream->WriteStringMaybeAliased(
        12, this->_internal_string_data(), target);
  }

  // int32 int_data = 13;
  if (_internal_has_int_data()) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(13, this->_internal_int_data(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:perception.Event)
  return target;
}

size_t Event::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:perception.Event)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .perception.Position pos = 7;
  total_size += 1UL * this->_internal_pos_size();
  for (const auto& msg : this->_impl_.pos_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // repeated .perception.AreaInfo refpath = 11;
  total_size += 1UL * this->_internal_refpath_size();
  for (const auto& msg : this->_impl_.refpath_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // string device_sn = 3;
  if (!this->_internal_device_sn().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_device_sn());
  }

  // string license_plate = 8;
  if (!this->_internal_license_plate().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_license_plate());
  }

  // int32 event_type = 1;
  if (this->_internal_event_type() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_event_type());
  }

  // int32 level = 2;
  if (this->_internal_level() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_level());
  }

  // int32 lane_id = 4;
  if (this->_internal_lane_id() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_lane_id());
  }

  // int32 fusion_state = 5;
  if (this->_internal_fusion_state() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_fusion_state());
  }

  // int64 track_time = 9;
  if (this->_internal_track_time() != 0) {
    total_size += ::_pbi::WireFormatLite::Int64SizePlusOne(this->_internal_track_time());
  }

  // int64 ttl = 10;
  if (this->_internal_ttl() != 0) {
    total_size += ::_pbi::WireFormatLite::Int64SizePlusOne(this->_internal_ttl());
  }

  // int32 source = 6;
  if (this->_internal_source() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_source());
  }

  switch (DataType_case()) {
    // string string_data = 12;
    case kStringData: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
          this->_internal_string_data());
      break;
    }
    // int32 int_data = 13;
    case kIntData: {
      total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_int_data());
      break;
    }
    case DATATYPE_NOT_SET: {
      break;
    }
  }
  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData Event::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    Event::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*Event::GetClassData() const { return &_class_data_; }


void Event::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<Event*>(&to_msg);
  auto& from = static_cast<const Event&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:perception.Event)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  _this->_impl_.pos_.MergeFrom(from._impl_.pos_);
  _this->_impl_.refpath_.MergeFrom(from._impl_.refpath_);
  if (!from._internal_device_sn().empty()) {
    _this->_internal_set_device_sn(from._internal_device_sn());
  }
  if (!from._internal_license_plate().empty()) {
    _this->_internal_set_license_plate(from._internal_license_plate());
  }
  if (from._internal_event_type() != 0) {
    _this->_internal_set_event_type(from._internal_event_type());
  }
  if (from._internal_level() != 0) {
    _this->_internal_set_level(from._internal_level());
  }
  if (from._internal_lane_id() != 0) {
    _this->_internal_set_lane_id(from._internal_lane_id());
  }
  if (from._internal_fusion_state() != 0) {
    _this->_internal_set_fusion_state(from._internal_fusion_state());
  }
  if (from._internal_track_time() != 0) {
    _this->_internal_set_track_time(from._internal_track_time());
  }
  if (from._internal_ttl() != 0) {
    _this->_internal_set_ttl(from._internal_ttl());
  }
  if (from._internal_source() != 0) {
    _this->_internal_set_source(from._internal_source());
  }
  switch (from.DataType_case()) {
    case kStringData: {
      _this->_internal_set_string_data(from._internal_string_data());
      break;
    }
    case kIntData: {
      _this->_internal_set_int_data(from._internal_int_data());
      break;
    }
    case DATATYPE_NOT_SET: {
      break;
    }
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void Event::CopyFrom(const Event& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:perception.Event)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Event::IsInitialized() const {
  return true;
}

void Event::InternalSwap(Event* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  _impl_.pos_.InternalSwap(&other->_impl_.pos_);
  _impl_.refpath_.InternalSwap(&other->_impl_.refpath_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.device_sn_, lhs_arena,
      &other->_impl_.device_sn_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.license_plate_, lhs_arena,
      &other->_impl_.license_plate_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(Event, _impl_.source_)
      + sizeof(Event::_impl_.source_)
      - PROTOBUF_FIELD_OFFSET(Event, _impl_.event_type_)>(
          reinterpret_cast<char*>(&_impl_.event_type_),
          reinterpret_cast<char*>(&other->_impl_.event_type_));
  swap(_impl_.DataType_, other->_impl_.DataType_);
  swap(_impl_._oneof_case_[0], other->_impl_._oneof_case_[0]);
}

::PROTOBUF_NAMESPACE_ID::Metadata Event::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_perception_2eproto_getter, &descriptor_table_perception_2eproto_once,
      file_level_metadata_perception_2eproto[9]);
}

// ===================================================================

class Events::_Internal {
 public:
};

Events::Events(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:perception.Events)
}
Events::Events(const Events& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  Events* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.event_){from._impl_.event_}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:perception.Events)
}

inline void Events::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.event_){arena}
    , /*decltype(_impl_._cached_size_)*/{}
  };
}

Events::~Events() {
  // @@protoc_insertion_point(destructor:perception.Events)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void Events::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  _impl_.event_.~RepeatedPtrField();
}

void Events::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void Events::Clear() {
// @@protoc_insertion_point(message_clear_start:perception.Events)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.event_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* Events::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // repeated .perception.Event event = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_event(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<10>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* Events::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:perception.Events)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .perception.Event event = 1;
  for (unsigned i = 0,
      n = static_cast<unsigned>(this->_internal_event_size()); i < n; i++) {
    const auto& repfield = this->_internal_event(i);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
        InternalWriteMessage(1, repfield, repfield.GetCachedSize(), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:perception.Events)
  return target;
}

size_t Events::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:perception.Events)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .perception.Event event = 1;
  total_size += 1UL * this->_internal_event_size();
  for (const auto& msg : this->_impl_.event_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData Events::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    Events::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*Events::GetClassData() const { return &_class_data_; }


void Events::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<Events*>(&to_msg);
  auto& from = static_cast<const Events&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:perception.Events)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  _this->_impl_.event_.MergeFrom(from._impl_.event_);
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void Events::CopyFrom(const Events& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:perception.Events)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Events::IsInitialized() const {
  return true;
}

void Events::InternalSwap(Events* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  _impl_.event_.InternalSwap(&other->_impl_.event_);
}

::PROTOBUF_NAMESPACE_ID::Metadata Events::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_perception_2eproto_getter, &descriptor_table_perception_2eproto_once,
      file_level_metadata_perception_2eproto[10]);
}

// ===================================================================

class HeartBeat::_Internal {
 public:
};

HeartBeat::HeartBeat(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:perception.HeartBeat)
}
HeartBeat::HeartBeat(const HeartBeat& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  HeartBeat* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.err_code_){from._impl_.err_code_}
    , /*decltype(_impl_._err_code_cached_byte_size_)*/{0}
    , decltype(_impl_.device_sn_){}
    , decltype(_impl_.time_){}
    , decltype(_impl_.seq_count_){}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  _impl_.device_sn_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.device_sn_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_device_sn().empty()) {
    _this->_impl_.device_sn_.Set(from._internal_device_sn(), 
      _this->GetArenaForAllocation());
  }
  ::memcpy(&_impl_.time_, &from._impl_.time_,
    static_cast<size_t>(reinterpret_cast<char*>(&_impl_.seq_count_) -
    reinterpret_cast<char*>(&_impl_.time_)) + sizeof(_impl_.seq_count_));
  // @@protoc_insertion_point(copy_constructor:perception.HeartBeat)
}

inline void HeartBeat::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.err_code_){arena}
    , /*decltype(_impl_._err_code_cached_byte_size_)*/{0}
    , decltype(_impl_.device_sn_){}
    , decltype(_impl_.time_){int64_t{0}}
    , decltype(_impl_.seq_count_){0}
    , /*decltype(_impl_._cached_size_)*/{}
  };
  _impl_.device_sn_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.device_sn_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

HeartBeat::~HeartBeat() {
  // @@protoc_insertion_point(destructor:perception.HeartBeat)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void HeartBeat::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  _impl_.err_code_.~RepeatedField();
  _impl_.device_sn_.Destroy();
}

void HeartBeat::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void HeartBeat::Clear() {
// @@protoc_insertion_point(message_clear_start:perception.HeartBeat)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.err_code_.Clear();
  _impl_.device_sn_.ClearToEmpty();
  ::memset(&_impl_.time_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&_impl_.seq_count_) -
      reinterpret_cast<char*>(&_impl_.time_)) + sizeof(_impl_.seq_count_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* HeartBeat::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // int32 seq_count = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          _impl_.seq_count_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int64 time = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          _impl_.time_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string device_sn = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          auto str = _internal_mutable_device_sn();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          CHK_(::_pbi::VerifyUTF8(str, "perception.HeartBeat.device_sn"));
        } else
          goto handle_unusual;
        continue;
      // repeated int32 err_code = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 34)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::PackedInt32Parser(_internal_mutable_err_code(), ptr, ctx);
          CHK_(ptr);
        } else if (static_cast<uint8_t>(tag) == 32) {
          _internal_add_err_code(::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* HeartBeat::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:perception.HeartBeat)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 seq_count = 1;
  if (this->_internal_seq_count() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(1, this->_internal_seq_count(), target);
  }

  // int64 time = 2;
  if (this->_internal_time() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt64ToArray(2, this->_internal_time(), target);
  }

  // string device_sn = 3;
  if (!this->_internal_device_sn().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_device_sn().data(), static_cast<int>(this->_internal_device_sn().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "perception.HeartBeat.device_sn");
    target = stream->WriteStringMaybeAliased(
        3, this->_internal_device_sn(), target);
  }

  // repeated int32 err_code = 4;
  {
    int byte_size = _impl_._err_code_cached_byte_size_.load(std::memory_order_relaxed);
    if (byte_size > 0) {
      target = stream->WriteInt32Packed(
          4, _internal_err_code(), byte_size, target);
    }
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:perception.HeartBeat)
  return target;
}

size_t HeartBeat::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:perception.HeartBeat)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated int32 err_code = 4;
  {
    size_t data_size = ::_pbi::WireFormatLite::
      Int32Size(this->_impl_.err_code_);
    if (data_size > 0) {
      total_size += 1 +
        ::_pbi::WireFormatLite::Int32Size(static_cast<int32_t>(data_size));
    }
    int cached_size = ::_pbi::ToCachedSize(data_size);
    _impl_._err_code_cached_byte_size_.store(cached_size,
                                    std::memory_order_relaxed);
    total_size += data_size;
  }

  // string device_sn = 3;
  if (!this->_internal_device_sn().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_device_sn());
  }

  // int64 time = 2;
  if (this->_internal_time() != 0) {
    total_size += ::_pbi::WireFormatLite::Int64SizePlusOne(this->_internal_time());
  }

  // int32 seq_count = 1;
  if (this->_internal_seq_count() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_seq_count());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData HeartBeat::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    HeartBeat::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*HeartBeat::GetClassData() const { return &_class_data_; }


void HeartBeat::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<HeartBeat*>(&to_msg);
  auto& from = static_cast<const HeartBeat&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:perception.HeartBeat)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  _this->_impl_.err_code_.MergeFrom(from._impl_.err_code_);
  if (!from._internal_device_sn().empty()) {
    _this->_internal_set_device_sn(from._internal_device_sn());
  }
  if (from._internal_time() != 0) {
    _this->_internal_set_time(from._internal_time());
  }
  if (from._internal_seq_count() != 0) {
    _this->_internal_set_seq_count(from._internal_seq_count());
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void HeartBeat::CopyFrom(const HeartBeat& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:perception.HeartBeat)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool HeartBeat::IsInitialized() const {
  return true;
}

void HeartBeat::InternalSwap(HeartBeat* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  _impl_.err_code_.InternalSwap(&other->_impl_.err_code_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.device_sn_, lhs_arena,
      &other->_impl_.device_sn_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(HeartBeat, _impl_.seq_count_)
      + sizeof(HeartBeat::_impl_.seq_count_)
      - PROTOBUF_FIELD_OFFSET(HeartBeat, _impl_.time_)>(
          reinterpret_cast<char*>(&_impl_.time_),
          reinterpret_cast<char*>(&other->_impl_.time_));
}

::PROTOBUF_NAMESPACE_ID::Metadata HeartBeat::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_perception_2eproto_getter, &descriptor_table_perception_2eproto_once,
      file_level_metadata_perception_2eproto[11]);
}

// ===================================================================

class DeviceInfo::_Internal {
 public:
  static const ::perception::Position& location(const DeviceInfo* msg);
};

const ::perception::Position&
DeviceInfo::_Internal::location(const DeviceInfo* msg) {
  return *msg->_impl_.location_;
}
DeviceInfo::DeviceInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:perception.DeviceInfo)
}
DeviceInfo::DeviceInfo(const DeviceInfo& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  DeviceInfo* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.perceptual_area_){from._impl_.perceptual_area_}
    , decltype(_impl_.device_sn_){}
    , decltype(_impl_.description_){}
    , decltype(_impl_.location_){nullptr}
    , decltype(_impl_.orientation_){}
    , decltype(_impl_.type_){}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  _impl_.device_sn_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.device_sn_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_device_sn().empty()) {
    _this->_impl_.device_sn_.Set(from._internal_device_sn(), 
      _this->GetArenaForAllocation());
  }
  _impl_.description_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.description_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_description().empty()) {
    _this->_impl_.description_.Set(from._internal_description(), 
      _this->GetArenaForAllocation());
  }
  if (from._internal_has_location()) {
    _this->_impl_.location_ = new ::perception::Position(*from._impl_.location_);
  }
  ::memcpy(&_impl_.orientation_, &from._impl_.orientation_,
    static_cast<size_t>(reinterpret_cast<char*>(&_impl_.type_) -
    reinterpret_cast<char*>(&_impl_.orientation_)) + sizeof(_impl_.type_));
  // @@protoc_insertion_point(copy_constructor:perception.DeviceInfo)
}

inline void DeviceInfo::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.perceptual_area_){arena}
    , decltype(_impl_.device_sn_){}
    , decltype(_impl_.description_){}
    , decltype(_impl_.location_){nullptr}
    , decltype(_impl_.orientation_){0}
    , decltype(_impl_.type_){0}
    , /*decltype(_impl_._cached_size_)*/{}
  };
  _impl_.device_sn_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.device_sn_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  _impl_.description_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.description_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

DeviceInfo::~DeviceInfo() {
  // @@protoc_insertion_point(destructor:perception.DeviceInfo)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void DeviceInfo::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  _impl_.perceptual_area_.~RepeatedPtrField();
  _impl_.device_sn_.Destroy();
  _impl_.description_.Destroy();
  if (this != internal_default_instance()) delete _impl_.location_;
}

void DeviceInfo::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void DeviceInfo::Clear() {
// @@protoc_insertion_point(message_clear_start:perception.DeviceInfo)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.perceptual_area_.Clear();
  _impl_.device_sn_.ClearToEmpty();
  _impl_.description_.ClearToEmpty();
  if (GetArenaForAllocation() == nullptr && _impl_.location_ != nullptr) {
    delete _impl_.location_;
  }
  _impl_.location_ = nullptr;
  ::memset(&_impl_.orientation_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&_impl_.type_) -
      reinterpret_cast<char*>(&_impl_.orientation_)) + sizeof(_impl_.type_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* DeviceInfo::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string device_sn = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_device_sn();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          CHK_(::_pbi::VerifyUTF8(str, "perception.DeviceInfo.device_sn"));
        } else
          goto handle_unusual;
        continue;
      // .perception.Position location = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_location(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // float orientation = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 29)) {
          _impl_.orientation_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // int32 type = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 32)) {
          _impl_.type_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated .perception.AreaInfo perceptual_area = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 42)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_perceptual_area(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<42>(ptr));
        } else
          goto handle_unusual;
        continue;
      // string description = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 50)) {
          auto str = _internal_mutable_description();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          CHK_(::_pbi::VerifyUTF8(str, "perception.DeviceInfo.description"));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* DeviceInfo::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:perception.DeviceInfo)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string device_sn = 1;
  if (!this->_internal_device_sn().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_device_sn().data(), static_cast<int>(this->_internal_device_sn().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "perception.DeviceInfo.device_sn");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_device_sn(), target);
  }

  // .perception.Position location = 2;
  if (this->_internal_has_location()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(2, _Internal::location(this),
        _Internal::location(this).GetCachedSize(), target, stream);
  }

  // float orientation = 3;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_orientation = this->_internal_orientation();
  uint32_t raw_orientation;
  memcpy(&raw_orientation, &tmp_orientation, sizeof(tmp_orientation));
  if (raw_orientation != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(3, this->_internal_orientation(), target);
  }

  // int32 type = 4;
  if (this->_internal_type() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(4, this->_internal_type(), target);
  }

  // repeated .perception.AreaInfo perceptual_area = 5;
  for (unsigned i = 0,
      n = static_cast<unsigned>(this->_internal_perceptual_area_size()); i < n; i++) {
    const auto& repfield = this->_internal_perceptual_area(i);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
        InternalWriteMessage(5, repfield, repfield.GetCachedSize(), target, stream);
  }

  // string description = 6;
  if (!this->_internal_description().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_description().data(), static_cast<int>(this->_internal_description().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "perception.DeviceInfo.description");
    target = stream->WriteStringMaybeAliased(
        6, this->_internal_description(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:perception.DeviceInfo)
  return target;
}

size_t DeviceInfo::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:perception.DeviceInfo)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .perception.AreaInfo perceptual_area = 5;
  total_size += 1UL * this->_internal_perceptual_area_size();
  for (const auto& msg : this->_impl_.perceptual_area_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // string device_sn = 1;
  if (!this->_internal_device_sn().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_device_sn());
  }

  // string description = 6;
  if (!this->_internal_description().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_description());
  }

  // .perception.Position location = 2;
  if (this->_internal_has_location()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *_impl_.location_);
  }

  // float orientation = 3;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_orientation = this->_internal_orientation();
  uint32_t raw_orientation;
  memcpy(&raw_orientation, &tmp_orientation, sizeof(tmp_orientation));
  if (raw_orientation != 0) {
    total_size += 1 + 4;
  }

  // int32 type = 4;
  if (this->_internal_type() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_type());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData DeviceInfo::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    DeviceInfo::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*DeviceInfo::GetClassData() const { return &_class_data_; }


void DeviceInfo::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<DeviceInfo*>(&to_msg);
  auto& from = static_cast<const DeviceInfo&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:perception.DeviceInfo)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  _this->_impl_.perceptual_area_.MergeFrom(from._impl_.perceptual_area_);
  if (!from._internal_device_sn().empty()) {
    _this->_internal_set_device_sn(from._internal_device_sn());
  }
  if (!from._internal_description().empty()) {
    _this->_internal_set_description(from._internal_description());
  }
  if (from._internal_has_location()) {
    _this->_internal_mutable_location()->::perception::Position::MergeFrom(
        from._internal_location());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_orientation = from._internal_orientation();
  uint32_t raw_orientation;
  memcpy(&raw_orientation, &tmp_orientation, sizeof(tmp_orientation));
  if (raw_orientation != 0) {
    _this->_internal_set_orientation(from._internal_orientation());
  }
  if (from._internal_type() != 0) {
    _this->_internal_set_type(from._internal_type());
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void DeviceInfo::CopyFrom(const DeviceInfo& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:perception.DeviceInfo)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool DeviceInfo::IsInitialized() const {
  return true;
}

void DeviceInfo::InternalSwap(DeviceInfo* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  _impl_.perceptual_area_.InternalSwap(&other->_impl_.perceptual_area_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.device_sn_, lhs_arena,
      &other->_impl_.device_sn_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.description_, lhs_arena,
      &other->_impl_.description_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(DeviceInfo, _impl_.type_)
      + sizeof(DeviceInfo::_impl_.type_)
      - PROTOBUF_FIELD_OFFSET(DeviceInfo, _impl_.location_)>(
          reinterpret_cast<char*>(&_impl_.location_),
          reinterpret_cast<char*>(&other->_impl_.location_));
}

::PROTOBUF_NAMESPACE_ID::Metadata DeviceInfo::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_perception_2eproto_getter, &descriptor_table_perception_2eproto_once,
      file_level_metadata_perception_2eproto[12]);
}

// ===================================================================

class RoadCounts::_Internal {
 public:
  static const ::perception::IntersectionCount& intersectioncount(const RoadCounts* msg);
};

const ::perception::IntersectionCount&
RoadCounts::_Internal::intersectioncount(const RoadCounts* msg) {
  return *msg->_impl_.intersectioncount_;
}
RoadCounts::RoadCounts(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:perception.RoadCounts)
}
RoadCounts::RoadCounts(const RoadCounts& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  RoadCounts* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.rsuid_){}
    , decltype(_impl_.seqnum_){}
    , decltype(_impl_.rsuesn_){}
    , decltype(_impl_.protocolversion_){}
    , decltype(_impl_.intersectioncount_){nullptr}
    , decltype(_impl_.cycle_){}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  _impl_.rsuid_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.rsuid_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_rsuid().empty()) {
    _this->_impl_.rsuid_.Set(from._internal_rsuid(), 
      _this->GetArenaForAllocation());
  }
  _impl_.seqnum_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.seqnum_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_seqnum().empty()) {
    _this->_impl_.seqnum_.Set(from._internal_seqnum(), 
      _this->GetArenaForAllocation());
  }
  _impl_.rsuesn_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.rsuesn_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_rsuesn().empty()) {
    _this->_impl_.rsuesn_.Set(from._internal_rsuesn(), 
      _this->GetArenaForAllocation());
  }
  _impl_.protocolversion_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.protocolversion_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_protocolversion().empty()) {
    _this->_impl_.protocolversion_.Set(from._internal_protocolversion(), 
      _this->GetArenaForAllocation());
  }
  if (from._internal_has_intersectioncount()) {
    _this->_impl_.intersectioncount_ = new ::perception::IntersectionCount(*from._impl_.intersectioncount_);
  }
  _this->_impl_.cycle_ = from._impl_.cycle_;
  // @@protoc_insertion_point(copy_constructor:perception.RoadCounts)
}

inline void RoadCounts::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.rsuid_){}
    , decltype(_impl_.seqnum_){}
    , decltype(_impl_.rsuesn_){}
    , decltype(_impl_.protocolversion_){}
    , decltype(_impl_.intersectioncount_){nullptr}
    , decltype(_impl_.cycle_){0}
    , /*decltype(_impl_._cached_size_)*/{}
  };
  _impl_.rsuid_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.rsuid_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  _impl_.seqnum_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.seqnum_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  _impl_.rsuesn_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.rsuesn_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  _impl_.protocolversion_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.protocolversion_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

RoadCounts::~RoadCounts() {
  // @@protoc_insertion_point(destructor:perception.RoadCounts)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void RoadCounts::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  _impl_.rsuid_.Destroy();
  _impl_.seqnum_.Destroy();
  _impl_.rsuesn_.Destroy();
  _impl_.protocolversion_.Destroy();
  if (this != internal_default_instance()) delete _impl_.intersectioncount_;
}

void RoadCounts::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void RoadCounts::Clear() {
// @@protoc_insertion_point(message_clear_start:perception.RoadCounts)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.rsuid_.ClearToEmpty();
  _impl_.seqnum_.ClearToEmpty();
  _impl_.rsuesn_.ClearToEmpty();
  _impl_.protocolversion_.ClearToEmpty();
  if (GetArenaForAllocation() == nullptr && _impl_.intersectioncount_ != nullptr) {
    delete _impl_.intersectioncount_;
  }
  _impl_.intersectioncount_ = nullptr;
  _impl_.cycle_ = 0;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* RoadCounts::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string rsuId = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_rsuid();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          CHK_(::_pbi::VerifyUTF8(str, "perception.RoadCounts.rsuId"));
        } else
          goto handle_unusual;
        continue;
      // string seqNum = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_seqnum();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          CHK_(::_pbi::VerifyUTF8(str, "perception.RoadCounts.seqNum"));
        } else
          goto handle_unusual;
        continue;
      // string rsuEsn = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          auto str = _internal_mutable_rsuesn();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          CHK_(::_pbi::VerifyUTF8(str, "perception.RoadCounts.rsuEsn"));
        } else
          goto handle_unusual;
        continue;
      // string protocolVersion = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 34)) {
          auto str = _internal_mutable_protocolversion();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          CHK_(::_pbi::VerifyUTF8(str, "perception.RoadCounts.protocolVersion"));
        } else
          goto handle_unusual;
        continue;
      // int32 cycle = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 40)) {
          _impl_.cycle_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .perception.IntersectionCount intersectionCount = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 50)) {
          ptr = ctx->ParseMessage(_internal_mutable_intersectioncount(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* RoadCounts::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:perception.RoadCounts)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string rsuId = 1;
  if (!this->_internal_rsuid().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_rsuid().data(), static_cast<int>(this->_internal_rsuid().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "perception.RoadCounts.rsuId");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_rsuid(), target);
  }

  // string seqNum = 2;
  if (!this->_internal_seqnum().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_seqnum().data(), static_cast<int>(this->_internal_seqnum().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "perception.RoadCounts.seqNum");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_seqnum(), target);
  }

  // string rsuEsn = 3;
  if (!this->_internal_rsuesn().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_rsuesn().data(), static_cast<int>(this->_internal_rsuesn().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "perception.RoadCounts.rsuEsn");
    target = stream->WriteStringMaybeAliased(
        3, this->_internal_rsuesn(), target);
  }

  // string protocolVersion = 4;
  if (!this->_internal_protocolversion().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_protocolversion().data(), static_cast<int>(this->_internal_protocolversion().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "perception.RoadCounts.protocolVersion");
    target = stream->WriteStringMaybeAliased(
        4, this->_internal_protocolversion(), target);
  }

  // int32 cycle = 5;
  if (this->_internal_cycle() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(5, this->_internal_cycle(), target);
  }

  // .perception.IntersectionCount intersectionCount = 6;
  if (this->_internal_has_intersectioncount()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(6, _Internal::intersectioncount(this),
        _Internal::intersectioncount(this).GetCachedSize(), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:perception.RoadCounts)
  return target;
}

size_t RoadCounts::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:perception.RoadCounts)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string rsuId = 1;
  if (!this->_internal_rsuid().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_rsuid());
  }

  // string seqNum = 2;
  if (!this->_internal_seqnum().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_seqnum());
  }

  // string rsuEsn = 3;
  if (!this->_internal_rsuesn().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_rsuesn());
  }

  // string protocolVersion = 4;
  if (!this->_internal_protocolversion().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_protocolversion());
  }

  // .perception.IntersectionCount intersectionCount = 6;
  if (this->_internal_has_intersectioncount()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *_impl_.intersectioncount_);
  }

  // int32 cycle = 5;
  if (this->_internal_cycle() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_cycle());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData RoadCounts::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    RoadCounts::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*RoadCounts::GetClassData() const { return &_class_data_; }


void RoadCounts::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<RoadCounts*>(&to_msg);
  auto& from = static_cast<const RoadCounts&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:perception.RoadCounts)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_rsuid().empty()) {
    _this->_internal_set_rsuid(from._internal_rsuid());
  }
  if (!from._internal_seqnum().empty()) {
    _this->_internal_set_seqnum(from._internal_seqnum());
  }
  if (!from._internal_rsuesn().empty()) {
    _this->_internal_set_rsuesn(from._internal_rsuesn());
  }
  if (!from._internal_protocolversion().empty()) {
    _this->_internal_set_protocolversion(from._internal_protocolversion());
  }
  if (from._internal_has_intersectioncount()) {
    _this->_internal_mutable_intersectioncount()->::perception::IntersectionCount::MergeFrom(
        from._internal_intersectioncount());
  }
  if (from._internal_cycle() != 0) {
    _this->_internal_set_cycle(from._internal_cycle());
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void RoadCounts::CopyFrom(const RoadCounts& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:perception.RoadCounts)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RoadCounts::IsInitialized() const {
  return true;
}

void RoadCounts::InternalSwap(RoadCounts* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.rsuid_, lhs_arena,
      &other->_impl_.rsuid_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.seqnum_, lhs_arena,
      &other->_impl_.seqnum_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.rsuesn_, lhs_arena,
      &other->_impl_.rsuesn_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.protocolversion_, lhs_arena,
      &other->_impl_.protocolversion_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(RoadCounts, _impl_.cycle_)
      + sizeof(RoadCounts::_impl_.cycle_)
      - PROTOBUF_FIELD_OFFSET(RoadCounts, _impl_.intersectioncount_)>(
          reinterpret_cast<char*>(&_impl_.intersectioncount_),
          reinterpret_cast<char*>(&other->_impl_.intersectioncount_));
}

::PROTOBUF_NAMESPACE_ID::Metadata RoadCounts::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_perception_2eproto_getter, &descriptor_table_perception_2eproto_once,
      file_level_metadata_perception_2eproto[13]);
}

// ===================================================================

class LaneCountInfo::_Internal {
 public:
};

LaneCountInfo::LaneCountInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:perception.LaneCountInfo)
}
LaneCountInfo::LaneCountInfo(const LaneCountInfo& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  LaneCountInfo* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.laneno_){}
    , decltype(_impl_.volume1_){}
    , decltype(_impl_.volume2_){}
    , decltype(_impl_.volume3_){}
    , decltype(_impl_.volume4_){}
    , decltype(_impl_.volume5_){}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&_impl_.laneno_, &from._impl_.laneno_,
    static_cast<size_t>(reinterpret_cast<char*>(&_impl_.volume5_) -
    reinterpret_cast<char*>(&_impl_.laneno_)) + sizeof(_impl_.volume5_));
  // @@protoc_insertion_point(copy_constructor:perception.LaneCountInfo)
}

inline void LaneCountInfo::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.laneno_){0}
    , decltype(_impl_.volume1_){0}
    , decltype(_impl_.volume2_){0}
    , decltype(_impl_.volume3_){0}
    , decltype(_impl_.volume4_){0}
    , decltype(_impl_.volume5_){0}
    , /*decltype(_impl_._cached_size_)*/{}
  };
}

LaneCountInfo::~LaneCountInfo() {
  // @@protoc_insertion_point(destructor:perception.LaneCountInfo)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void LaneCountInfo::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void LaneCountInfo::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void LaneCountInfo::Clear() {
// @@protoc_insertion_point(message_clear_start:perception.LaneCountInfo)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&_impl_.laneno_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&_impl_.volume5_) -
      reinterpret_cast<char*>(&_impl_.laneno_)) + sizeof(_impl_.volume5_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* LaneCountInfo::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // int32 laneNo = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          _impl_.laneno_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 volume1 = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          _impl_.volume1_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 volume2 = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          _impl_.volume2_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 volume3 = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 32)) {
          _impl_.volume3_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 volume4 = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 40)) {
          _impl_.volume4_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 volume5 = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 48)) {
          _impl_.volume5_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* LaneCountInfo::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:perception.LaneCountInfo)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 laneNo = 1;
  if (this->_internal_laneno() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(1, this->_internal_laneno(), target);
  }

  // int32 volume1 = 2;
  if (this->_internal_volume1() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(2, this->_internal_volume1(), target);
  }

  // int32 volume2 = 3;
  if (this->_internal_volume2() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(3, this->_internal_volume2(), target);
  }

  // int32 volume3 = 4;
  if (this->_internal_volume3() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(4, this->_internal_volume3(), target);
  }

  // int32 volume4 = 5;
  if (this->_internal_volume4() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(5, this->_internal_volume4(), target);
  }

  // int32 volume5 = 6;
  if (this->_internal_volume5() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(6, this->_internal_volume5(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:perception.LaneCountInfo)
  return target;
}

size_t LaneCountInfo::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:perception.LaneCountInfo)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // int32 laneNo = 1;
  if (this->_internal_laneno() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_laneno());
  }

  // int32 volume1 = 2;
  if (this->_internal_volume1() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_volume1());
  }

  // int32 volume2 = 3;
  if (this->_internal_volume2() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_volume2());
  }

  // int32 volume3 = 4;
  if (this->_internal_volume3() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_volume3());
  }

  // int32 volume4 = 5;
  if (this->_internal_volume4() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_volume4());
  }

  // int32 volume5 = 6;
  if (this->_internal_volume5() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_volume5());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData LaneCountInfo::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    LaneCountInfo::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*LaneCountInfo::GetClassData() const { return &_class_data_; }


void LaneCountInfo::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<LaneCountInfo*>(&to_msg);
  auto& from = static_cast<const LaneCountInfo&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:perception.LaneCountInfo)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_laneno() != 0) {
    _this->_internal_set_laneno(from._internal_laneno());
  }
  if (from._internal_volume1() != 0) {
    _this->_internal_set_volume1(from._internal_volume1());
  }
  if (from._internal_volume2() != 0) {
    _this->_internal_set_volume2(from._internal_volume2());
  }
  if (from._internal_volume3() != 0) {
    _this->_internal_set_volume3(from._internal_volume3());
  }
  if (from._internal_volume4() != 0) {
    _this->_internal_set_volume4(from._internal_volume4());
  }
  if (from._internal_volume5() != 0) {
    _this->_internal_set_volume5(from._internal_volume5());
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void LaneCountInfo::CopyFrom(const LaneCountInfo& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:perception.LaneCountInfo)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool LaneCountInfo::IsInitialized() const {
  return true;
}

void LaneCountInfo::InternalSwap(LaneCountInfo* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(LaneCountInfo, _impl_.volume5_)
      + sizeof(LaneCountInfo::_impl_.volume5_)
      - PROTOBUF_FIELD_OFFSET(LaneCountInfo, _impl_.laneno_)>(
          reinterpret_cast<char*>(&_impl_.laneno_),
          reinterpret_cast<char*>(&other->_impl_.laneno_));
}

::PROTOBUF_NAMESPACE_ID::Metadata LaneCountInfo::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_perception_2eproto_getter, &descriptor_table_perception_2eproto_once,
      file_level_metadata_perception_2eproto[14]);
}

// ===================================================================

class RoadCountInfo::_Internal {
 public:
  static const ::perception::Position& roadincoord(const RoadCountInfo* msg);
};

const ::perception::Position&
RoadCountInfo::_Internal::roadincoord(const RoadCountInfo* msg) {
  return *msg->_impl_.roadincoord_;
}
RoadCountInfo::RoadCountInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:perception.RoadCountInfo)
}
RoadCountInfo::RoadCountInfo(const RoadCountInfo& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  RoadCountInfo* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.lanecount_){from._impl_.lanecount_}
    , decltype(_impl_.deviceid_){}
    , decltype(_impl_.roadname_){}
    , decltype(_impl_.roadincoord_){nullptr}
    , decltype(_impl_.heading_){}
    , decltype(_impl_.volume1_){}
    , decltype(_impl_.volume2_){}
    , decltype(_impl_.volume3_){}
    , decltype(_impl_.volume4_){}
    , decltype(_impl_.volume5_){}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  _impl_.deviceid_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.deviceid_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_deviceid().empty()) {
    _this->_impl_.deviceid_.Set(from._internal_deviceid(), 
      _this->GetArenaForAllocation());
  }
  _impl_.roadname_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.roadname_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_roadname().empty()) {
    _this->_impl_.roadname_.Set(from._internal_roadname(), 
      _this->GetArenaForAllocation());
  }
  if (from._internal_has_roadincoord()) {
    _this->_impl_.roadincoord_ = new ::perception::Position(*from._impl_.roadincoord_);
  }
  ::memcpy(&_impl_.heading_, &from._impl_.heading_,
    static_cast<size_t>(reinterpret_cast<char*>(&_impl_.volume5_) -
    reinterpret_cast<char*>(&_impl_.heading_)) + sizeof(_impl_.volume5_));
  // @@protoc_insertion_point(copy_constructor:perception.RoadCountInfo)
}

inline void RoadCountInfo::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.lanecount_){arena}
    , decltype(_impl_.deviceid_){}
    , decltype(_impl_.roadname_){}
    , decltype(_impl_.roadincoord_){nullptr}
    , decltype(_impl_.heading_){0}
    , decltype(_impl_.volume1_){0}
    , decltype(_impl_.volume2_){0}
    , decltype(_impl_.volume3_){0}
    , decltype(_impl_.volume4_){0}
    , decltype(_impl_.volume5_){0}
    , /*decltype(_impl_._cached_size_)*/{}
  };
  _impl_.deviceid_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.deviceid_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  _impl_.roadname_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.roadname_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

RoadCountInfo::~RoadCountInfo() {
  // @@protoc_insertion_point(destructor:perception.RoadCountInfo)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void RoadCountInfo::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  _impl_.lanecount_.~RepeatedPtrField();
  _impl_.deviceid_.Destroy();
  _impl_.roadname_.Destroy();
  if (this != internal_default_instance()) delete _impl_.roadincoord_;
}

void RoadCountInfo::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void RoadCountInfo::Clear() {
// @@protoc_insertion_point(message_clear_start:perception.RoadCountInfo)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.lanecount_.Clear();
  _impl_.deviceid_.ClearToEmpty();
  _impl_.roadname_.ClearToEmpty();
  if (GetArenaForAllocation() == nullptr && _impl_.roadincoord_ != nullptr) {
    delete _impl_.roadincoord_;
  }
  _impl_.roadincoord_ = nullptr;
  ::memset(&_impl_.heading_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&_impl_.volume5_) -
      reinterpret_cast<char*>(&_impl_.heading_)) + sizeof(_impl_.volume5_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* RoadCountInfo::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string deviceId = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_deviceid();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          CHK_(::_pbi::VerifyUTF8(str, "perception.RoadCountInfo.deviceId"));
        } else
          goto handle_unusual;
        continue;
      // float heading = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 21)) {
          _impl_.heading_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // string roadName = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          auto str = _internal_mutable_roadname();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          CHK_(::_pbi::VerifyUTF8(str, "perception.RoadCountInfo.roadName"));
        } else
          goto handle_unusual;
        continue;
      // .perception.Position roadInCoord = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 34)) {
          ptr = ctx->ParseMessage(_internal_mutable_roadincoord(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 volume1 = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 40)) {
          _impl_.volume1_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 volume2 = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 48)) {
          _impl_.volume2_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 volume3 = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 56)) {
          _impl_.volume3_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 volume4 = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 64)) {
          _impl_.volume4_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 volume5 = 9;
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 72)) {
          _impl_.volume5_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated .perception.LaneCountInfo laneCount = 10;
      case 10:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 82)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_lanecount(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<82>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* RoadCountInfo::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:perception.RoadCountInfo)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string deviceId = 1;
  if (!this->_internal_deviceid().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_deviceid().data(), static_cast<int>(this->_internal_deviceid().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "perception.RoadCountInfo.deviceId");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_deviceid(), target);
  }

  // float heading = 2;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_heading = this->_internal_heading();
  uint32_t raw_heading;
  memcpy(&raw_heading, &tmp_heading, sizeof(tmp_heading));
  if (raw_heading != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(2, this->_internal_heading(), target);
  }

  // string roadName = 3;
  if (!this->_internal_roadname().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_roadname().data(), static_cast<int>(this->_internal_roadname().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "perception.RoadCountInfo.roadName");
    target = stream->WriteStringMaybeAliased(
        3, this->_internal_roadname(), target);
  }

  // .perception.Position roadInCoord = 4;
  if (this->_internal_has_roadincoord()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(4, _Internal::roadincoord(this),
        _Internal::roadincoord(this).GetCachedSize(), target, stream);
  }

  // int32 volume1 = 5;
  if (this->_internal_volume1() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(5, this->_internal_volume1(), target);
  }

  // int32 volume2 = 6;
  if (this->_internal_volume2() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(6, this->_internal_volume2(), target);
  }

  // int32 volume3 = 7;
  if (this->_internal_volume3() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(7, this->_internal_volume3(), target);
  }

  // int32 volume4 = 8;
  if (this->_internal_volume4() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(8, this->_internal_volume4(), target);
  }

  // int32 volume5 = 9;
  if (this->_internal_volume5() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(9, this->_internal_volume5(), target);
  }

  // repeated .perception.LaneCountInfo laneCount = 10;
  for (unsigned i = 0,
      n = static_cast<unsigned>(this->_internal_lanecount_size()); i < n; i++) {
    const auto& repfield = this->_internal_lanecount(i);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
        InternalWriteMessage(10, repfield, repfield.GetCachedSize(), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:perception.RoadCountInfo)
  return target;
}

size_t RoadCountInfo::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:perception.RoadCountInfo)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .perception.LaneCountInfo laneCount = 10;
  total_size += 1UL * this->_internal_lanecount_size();
  for (const auto& msg : this->_impl_.lanecount_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // string deviceId = 1;
  if (!this->_internal_deviceid().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_deviceid());
  }

  // string roadName = 3;
  if (!this->_internal_roadname().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_roadname());
  }

  // .perception.Position roadInCoord = 4;
  if (this->_internal_has_roadincoord()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *_impl_.roadincoord_);
  }

  // float heading = 2;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_heading = this->_internal_heading();
  uint32_t raw_heading;
  memcpy(&raw_heading, &tmp_heading, sizeof(tmp_heading));
  if (raw_heading != 0) {
    total_size += 1 + 4;
  }

  // int32 volume1 = 5;
  if (this->_internal_volume1() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_volume1());
  }

  // int32 volume2 = 6;
  if (this->_internal_volume2() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_volume2());
  }

  // int32 volume3 = 7;
  if (this->_internal_volume3() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_volume3());
  }

  // int32 volume4 = 8;
  if (this->_internal_volume4() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_volume4());
  }

  // int32 volume5 = 9;
  if (this->_internal_volume5() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_volume5());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData RoadCountInfo::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    RoadCountInfo::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*RoadCountInfo::GetClassData() const { return &_class_data_; }


void RoadCountInfo::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<RoadCountInfo*>(&to_msg);
  auto& from = static_cast<const RoadCountInfo&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:perception.RoadCountInfo)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  _this->_impl_.lanecount_.MergeFrom(from._impl_.lanecount_);
  if (!from._internal_deviceid().empty()) {
    _this->_internal_set_deviceid(from._internal_deviceid());
  }
  if (!from._internal_roadname().empty()) {
    _this->_internal_set_roadname(from._internal_roadname());
  }
  if (from._internal_has_roadincoord()) {
    _this->_internal_mutable_roadincoord()->::perception::Position::MergeFrom(
        from._internal_roadincoord());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_heading = from._internal_heading();
  uint32_t raw_heading;
  memcpy(&raw_heading, &tmp_heading, sizeof(tmp_heading));
  if (raw_heading != 0) {
    _this->_internal_set_heading(from._internal_heading());
  }
  if (from._internal_volume1() != 0) {
    _this->_internal_set_volume1(from._internal_volume1());
  }
  if (from._internal_volume2() != 0) {
    _this->_internal_set_volume2(from._internal_volume2());
  }
  if (from._internal_volume3() != 0) {
    _this->_internal_set_volume3(from._internal_volume3());
  }
  if (from._internal_volume4() != 0) {
    _this->_internal_set_volume4(from._internal_volume4());
  }
  if (from._internal_volume5() != 0) {
    _this->_internal_set_volume5(from._internal_volume5());
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void RoadCountInfo::CopyFrom(const RoadCountInfo& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:perception.RoadCountInfo)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RoadCountInfo::IsInitialized() const {
  return true;
}

void RoadCountInfo::InternalSwap(RoadCountInfo* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  _impl_.lanecount_.InternalSwap(&other->_impl_.lanecount_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.deviceid_, lhs_arena,
      &other->_impl_.deviceid_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.roadname_, lhs_arena,
      &other->_impl_.roadname_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(RoadCountInfo, _impl_.volume5_)
      + sizeof(RoadCountInfo::_impl_.volume5_)
      - PROTOBUF_FIELD_OFFSET(RoadCountInfo, _impl_.roadincoord_)>(
          reinterpret_cast<char*>(&_impl_.roadincoord_),
          reinterpret_cast<char*>(&other->_impl_.roadincoord_));
}

::PROTOBUF_NAMESPACE_ID::Metadata RoadCountInfo::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_perception_2eproto_getter, &descriptor_table_perception_2eproto_once,
      file_level_metadata_perception_2eproto[15]);
}

// ===================================================================

class IntersectionInfo::_Internal {
 public:
};

IntersectionInfo::IntersectionInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:perception.IntersectionInfo)
}
IntersectionInfo::IntersectionInfo(const IntersectionInfo& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  IntersectionInfo* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.roadcount_){from._impl_.roadcount_}
    , decltype(_impl_.volume1_){}
    , decltype(_impl_.volume2_){}
    , decltype(_impl_.volume3_){}
    , decltype(_impl_.volume4_){}
    , decltype(_impl_.volume5_){}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&_impl_.volume1_, &from._impl_.volume1_,
    static_cast<size_t>(reinterpret_cast<char*>(&_impl_.volume5_) -
    reinterpret_cast<char*>(&_impl_.volume1_)) + sizeof(_impl_.volume5_));
  // @@protoc_insertion_point(copy_constructor:perception.IntersectionInfo)
}

inline void IntersectionInfo::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.roadcount_){arena}
    , decltype(_impl_.volume1_){0}
    , decltype(_impl_.volume2_){0}
    , decltype(_impl_.volume3_){0}
    , decltype(_impl_.volume4_){0}
    , decltype(_impl_.volume5_){0}
    , /*decltype(_impl_._cached_size_)*/{}
  };
}

IntersectionInfo::~IntersectionInfo() {
  // @@protoc_insertion_point(destructor:perception.IntersectionInfo)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void IntersectionInfo::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  _impl_.roadcount_.~RepeatedPtrField();
}

void IntersectionInfo::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void IntersectionInfo::Clear() {
// @@protoc_insertion_point(message_clear_start:perception.IntersectionInfo)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.roadcount_.Clear();
  ::memset(&_impl_.volume1_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&_impl_.volume5_) -
      reinterpret_cast<char*>(&_impl_.volume1_)) + sizeof(_impl_.volume5_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* IntersectionInfo::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // int32 volume1 = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          _impl_.volume1_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 volume2 = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          _impl_.volume2_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 volume3 = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          _impl_.volume3_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 volume4 = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 32)) {
          _impl_.volume4_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 volume5 = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 40)) {
          _impl_.volume5_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated .perception.RoadCountInfo roadCount = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 50)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_roadcount(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<50>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* IntersectionInfo::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:perception.IntersectionInfo)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 volume1 = 1;
  if (this->_internal_volume1() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(1, this->_internal_volume1(), target);
  }

  // int32 volume2 = 2;
  if (this->_internal_volume2() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(2, this->_internal_volume2(), target);
  }

  // int32 volume3 = 3;
  if (this->_internal_volume3() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(3, this->_internal_volume3(), target);
  }

  // int32 volume4 = 4;
  if (this->_internal_volume4() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(4, this->_internal_volume4(), target);
  }

  // int32 volume5 = 5;
  if (this->_internal_volume5() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(5, this->_internal_volume5(), target);
  }

  // repeated .perception.RoadCountInfo roadCount = 6;
  for (unsigned i = 0,
      n = static_cast<unsigned>(this->_internal_roadcount_size()); i < n; i++) {
    const auto& repfield = this->_internal_roadcount(i);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
        InternalWriteMessage(6, repfield, repfield.GetCachedSize(), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:perception.IntersectionInfo)
  return target;
}

size_t IntersectionInfo::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:perception.IntersectionInfo)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .perception.RoadCountInfo roadCount = 6;
  total_size += 1UL * this->_internal_roadcount_size();
  for (const auto& msg : this->_impl_.roadcount_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // int32 volume1 = 1;
  if (this->_internal_volume1() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_volume1());
  }

  // int32 volume2 = 2;
  if (this->_internal_volume2() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_volume2());
  }

  // int32 volume3 = 3;
  if (this->_internal_volume3() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_volume3());
  }

  // int32 volume4 = 4;
  if (this->_internal_volume4() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_volume4());
  }

  // int32 volume5 = 5;
  if (this->_internal_volume5() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_volume5());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData IntersectionInfo::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    IntersectionInfo::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*IntersectionInfo::GetClassData() const { return &_class_data_; }


void IntersectionInfo::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<IntersectionInfo*>(&to_msg);
  auto& from = static_cast<const IntersectionInfo&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:perception.IntersectionInfo)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  _this->_impl_.roadcount_.MergeFrom(from._impl_.roadcount_);
  if (from._internal_volume1() != 0) {
    _this->_internal_set_volume1(from._internal_volume1());
  }
  if (from._internal_volume2() != 0) {
    _this->_internal_set_volume2(from._internal_volume2());
  }
  if (from._internal_volume3() != 0) {
    _this->_internal_set_volume3(from._internal_volume3());
  }
  if (from._internal_volume4() != 0) {
    _this->_internal_set_volume4(from._internal_volume4());
  }
  if (from._internal_volume5() != 0) {
    _this->_internal_set_volume5(from._internal_volume5());
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void IntersectionInfo::CopyFrom(const IntersectionInfo& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:perception.IntersectionInfo)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool IntersectionInfo::IsInitialized() const {
  return true;
}

void IntersectionInfo::InternalSwap(IntersectionInfo* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  _impl_.roadcount_.InternalSwap(&other->_impl_.roadcount_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(IntersectionInfo, _impl_.volume5_)
      + sizeof(IntersectionInfo::_impl_.volume5_)
      - PROTOBUF_FIELD_OFFSET(IntersectionInfo, _impl_.volume1_)>(
          reinterpret_cast<char*>(&_impl_.volume1_),
          reinterpret_cast<char*>(&other->_impl_.volume1_));
}

::PROTOBUF_NAMESPACE_ID::Metadata IntersectionInfo::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_perception_2eproto_getter, &descriptor_table_perception_2eproto_once,
      file_level_metadata_perception_2eproto[16]);
}

// ===================================================================

class RoadInfo::_Internal {
 public:
  static const ::perception::IntersectionInfo& intersectioninfo(const RoadInfo* msg);
};

const ::perception::IntersectionInfo&
RoadInfo::_Internal::intersectioninfo(const RoadInfo* msg) {
  return *msg->_impl_.intersectioninfo_;
}
RoadInfo::RoadInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:perception.RoadInfo)
}
RoadInfo::RoadInfo(const RoadInfo& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  RoadInfo* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.rsuid_){}
    , decltype(_impl_.seqnum_){}
    , decltype(_impl_.rsuesn_){}
    , decltype(_impl_.protocolversion_){}
    , decltype(_impl_.intersectioninfo_){nullptr}
    , decltype(_impl_.cycle_){}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  _impl_.rsuid_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.rsuid_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_rsuid().empty()) {
    _this->_impl_.rsuid_.Set(from._internal_rsuid(), 
      _this->GetArenaForAllocation());
  }
  _impl_.seqnum_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.seqnum_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_seqnum().empty()) {
    _this->_impl_.seqnum_.Set(from._internal_seqnum(), 
      _this->GetArenaForAllocation());
  }
  _impl_.rsuesn_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.rsuesn_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_rsuesn().empty()) {
    _this->_impl_.rsuesn_.Set(from._internal_rsuesn(), 
      _this->GetArenaForAllocation());
  }
  _impl_.protocolversion_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.protocolversion_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_protocolversion().empty()) {
    _this->_impl_.protocolversion_.Set(from._internal_protocolversion(), 
      _this->GetArenaForAllocation());
  }
  if (from._internal_has_intersectioninfo()) {
    _this->_impl_.intersectioninfo_ = new ::perception::IntersectionInfo(*from._impl_.intersectioninfo_);
  }
  _this->_impl_.cycle_ = from._impl_.cycle_;
  // @@protoc_insertion_point(copy_constructor:perception.RoadInfo)
}

inline void RoadInfo::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.rsuid_){}
    , decltype(_impl_.seqnum_){}
    , decltype(_impl_.rsuesn_){}
    , decltype(_impl_.protocolversion_){}
    , decltype(_impl_.intersectioninfo_){nullptr}
    , decltype(_impl_.cycle_){0}
    , /*decltype(_impl_._cached_size_)*/{}
  };
  _impl_.rsuid_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.rsuid_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  _impl_.seqnum_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.seqnum_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  _impl_.rsuesn_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.rsuesn_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  _impl_.protocolversion_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.protocolversion_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

RoadInfo::~RoadInfo() {
  // @@protoc_insertion_point(destructor:perception.RoadInfo)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void RoadInfo::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  _impl_.rsuid_.Destroy();
  _impl_.seqnum_.Destroy();
  _impl_.rsuesn_.Destroy();
  _impl_.protocolversion_.Destroy();
  if (this != internal_default_instance()) delete _impl_.intersectioninfo_;
}

void RoadInfo::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void RoadInfo::Clear() {
// @@protoc_insertion_point(message_clear_start:perception.RoadInfo)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.rsuid_.ClearToEmpty();
  _impl_.seqnum_.ClearToEmpty();
  _impl_.rsuesn_.ClearToEmpty();
  _impl_.protocolversion_.ClearToEmpty();
  if (GetArenaForAllocation() == nullptr && _impl_.intersectioninfo_ != nullptr) {
    delete _impl_.intersectioninfo_;
  }
  _impl_.intersectioninfo_ = nullptr;
  _impl_.cycle_ = 0;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* RoadInfo::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string rsuId = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_rsuid();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          CHK_(::_pbi::VerifyUTF8(str, "perception.RoadInfo.rsuId"));
        } else
          goto handle_unusual;
        continue;
      // string seqNum = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_seqnum();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          CHK_(::_pbi::VerifyUTF8(str, "perception.RoadInfo.seqNum"));
        } else
          goto handle_unusual;
        continue;
      // string rsuEsn = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          auto str = _internal_mutable_rsuesn();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          CHK_(::_pbi::VerifyUTF8(str, "perception.RoadInfo.rsuEsn"));
        } else
          goto handle_unusual;
        continue;
      // string protocolVersion = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 34)) {
          auto str = _internal_mutable_protocolversion();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          CHK_(::_pbi::VerifyUTF8(str, "perception.RoadInfo.protocolVersion"));
        } else
          goto handle_unusual;
        continue;
      // int32 cycle = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 40)) {
          _impl_.cycle_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .perception.IntersectionInfo intersectionInfo = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 50)) {
          ptr = ctx->ParseMessage(_internal_mutable_intersectioninfo(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* RoadInfo::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:perception.RoadInfo)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string rsuId = 1;
  if (!this->_internal_rsuid().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_rsuid().data(), static_cast<int>(this->_internal_rsuid().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "perception.RoadInfo.rsuId");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_rsuid(), target);
  }

  // string seqNum = 2;
  if (!this->_internal_seqnum().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_seqnum().data(), static_cast<int>(this->_internal_seqnum().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "perception.RoadInfo.seqNum");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_seqnum(), target);
  }

  // string rsuEsn = 3;
  if (!this->_internal_rsuesn().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_rsuesn().data(), static_cast<int>(this->_internal_rsuesn().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "perception.RoadInfo.rsuEsn");
    target = stream->WriteStringMaybeAliased(
        3, this->_internal_rsuesn(), target);
  }

  // string protocolVersion = 4;
  if (!this->_internal_protocolversion().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_protocolversion().data(), static_cast<int>(this->_internal_protocolversion().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "perception.RoadInfo.protocolVersion");
    target = stream->WriteStringMaybeAliased(
        4, this->_internal_protocolversion(), target);
  }

  // int32 cycle = 5;
  if (this->_internal_cycle() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(5, this->_internal_cycle(), target);
  }

  // .perception.IntersectionInfo intersectionInfo = 6;
  if (this->_internal_has_intersectioninfo()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(6, _Internal::intersectioninfo(this),
        _Internal::intersectioninfo(this).GetCachedSize(), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:perception.RoadInfo)
  return target;
}

size_t RoadInfo::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:perception.RoadInfo)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string rsuId = 1;
  if (!this->_internal_rsuid().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_rsuid());
  }

  // string seqNum = 2;
  if (!this->_internal_seqnum().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_seqnum());
  }

  // string rsuEsn = 3;
  if (!this->_internal_rsuesn().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_rsuesn());
  }

  // string protocolVersion = 4;
  if (!this->_internal_protocolversion().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_protocolversion());
  }

  // .perception.IntersectionInfo intersectionInfo = 6;
  if (this->_internal_has_intersectioninfo()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *_impl_.intersectioninfo_);
  }

  // int32 cycle = 5;
  if (this->_internal_cycle() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_cycle());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData RoadInfo::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    RoadInfo::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*RoadInfo::GetClassData() const { return &_class_data_; }


void RoadInfo::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<RoadInfo*>(&to_msg);
  auto& from = static_cast<const RoadInfo&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:perception.RoadInfo)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_rsuid().empty()) {
    _this->_internal_set_rsuid(from._internal_rsuid());
  }
  if (!from._internal_seqnum().empty()) {
    _this->_internal_set_seqnum(from._internal_seqnum());
  }
  if (!from._internal_rsuesn().empty()) {
    _this->_internal_set_rsuesn(from._internal_rsuesn());
  }
  if (!from._internal_protocolversion().empty()) {
    _this->_internal_set_protocolversion(from._internal_protocolversion());
  }
  if (from._internal_has_intersectioninfo()) {
    _this->_internal_mutable_intersectioninfo()->::perception::IntersectionInfo::MergeFrom(
        from._internal_intersectioninfo());
  }
  if (from._internal_cycle() != 0) {
    _this->_internal_set_cycle(from._internal_cycle());
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void RoadInfo::CopyFrom(const RoadInfo& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:perception.RoadInfo)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RoadInfo::IsInitialized() const {
  return true;
}

void RoadInfo::InternalSwap(RoadInfo* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.rsuid_, lhs_arena,
      &other->_impl_.rsuid_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.seqnum_, lhs_arena,
      &other->_impl_.seqnum_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.rsuesn_, lhs_arena,
      &other->_impl_.rsuesn_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.protocolversion_, lhs_arena,
      &other->_impl_.protocolversion_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(RoadInfo, _impl_.cycle_)
      + sizeof(RoadInfo::_impl_.cycle_)
      - PROTOBUF_FIELD_OFFSET(RoadInfo, _impl_.intersectioninfo_)>(
          reinterpret_cast<char*>(&_impl_.intersectioninfo_),
          reinterpret_cast<char*>(&other->_impl_.intersectioninfo_));
}

::PROTOBUF_NAMESPACE_ID::Metadata RoadInfo::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_perception_2eproto_getter, &descriptor_table_perception_2eproto_once,
      file_level_metadata_perception_2eproto[17]);
}

// ===================================================================

class PerceptionMsg::_Internal {
 public:
  static const ::perception::Targets& target_msg(const PerceptionMsg* msg);
  static const ::perception::Events& event_msg(const PerceptionMsg* msg);
  static const ::perception::HeartBeat& heart_beat_msg(const PerceptionMsg* msg);
  static const ::perception::DeviceInfo& dev_info(const PerceptionMsg* msg);
  static const ::perception::RoadCounts& road_count(const PerceptionMsg* msg);
  static const ::perception::RoadInfo& road_info(const PerceptionMsg* msg);
};

const ::perception::Targets&
PerceptionMsg::_Internal::target_msg(const PerceptionMsg* msg) {
  return *msg->_impl_.MsgType_.target_msg_;
}
const ::perception::Events&
PerceptionMsg::_Internal::event_msg(const PerceptionMsg* msg) {
  return *msg->_impl_.MsgType_.event_msg_;
}
const ::perception::HeartBeat&
PerceptionMsg::_Internal::heart_beat_msg(const PerceptionMsg* msg) {
  return *msg->_impl_.MsgType_.heart_beat_msg_;
}
const ::perception::DeviceInfo&
PerceptionMsg::_Internal::dev_info(const PerceptionMsg* msg) {
  return *msg->_impl_.MsgType_.dev_info_;
}
const ::perception::RoadCounts&
PerceptionMsg::_Internal::road_count(const PerceptionMsg* msg) {
  return *msg->_impl_.MsgType_.road_count_;
}
const ::perception::RoadInfo&
PerceptionMsg::_Internal::road_info(const PerceptionMsg* msg) {
  return *msg->_impl_.MsgType_.road_info_;
}
void PerceptionMsg::set_allocated_target_msg(::perception::Targets* target_msg) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  clear_MsgType();
  if (target_msg) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(target_msg);
    if (message_arena != submessage_arena) {
      target_msg = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, target_msg, submessage_arena);
    }
    set_has_target_msg();
    _impl_.MsgType_.target_msg_ = target_msg;
  }
  // @@protoc_insertion_point(field_set_allocated:perception.PerceptionMsg.target_msg)
}
void PerceptionMsg::set_allocated_event_msg(::perception::Events* event_msg) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  clear_MsgType();
  if (event_msg) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(event_msg);
    if (message_arena != submessage_arena) {
      event_msg = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, event_msg, submessage_arena);
    }
    set_has_event_msg();
    _impl_.MsgType_.event_msg_ = event_msg;
  }
  // @@protoc_insertion_point(field_set_allocated:perception.PerceptionMsg.event_msg)
}
void PerceptionMsg::set_allocated_heart_beat_msg(::perception::HeartBeat* heart_beat_msg) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  clear_MsgType();
  if (heart_beat_msg) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(heart_beat_msg);
    if (message_arena != submessage_arena) {
      heart_beat_msg = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, heart_beat_msg, submessage_arena);
    }
    set_has_heart_beat_msg();
    _impl_.MsgType_.heart_beat_msg_ = heart_beat_msg;
  }
  // @@protoc_insertion_point(field_set_allocated:perception.PerceptionMsg.heart_beat_msg)
}
void PerceptionMsg::set_allocated_dev_info(::perception::DeviceInfo* dev_info) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  clear_MsgType();
  if (dev_info) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(dev_info);
    if (message_arena != submessage_arena) {
      dev_info = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, dev_info, submessage_arena);
    }
    set_has_dev_info();
    _impl_.MsgType_.dev_info_ = dev_info;
  }
  // @@protoc_insertion_point(field_set_allocated:perception.PerceptionMsg.dev_info)
}
void PerceptionMsg::set_allocated_road_count(::perception::RoadCounts* road_count) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  clear_MsgType();
  if (road_count) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(road_count);
    if (message_arena != submessage_arena) {
      road_count = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, road_count, submessage_arena);
    }
    set_has_road_count();
    _impl_.MsgType_.road_count_ = road_count;
  }
  // @@protoc_insertion_point(field_set_allocated:perception.PerceptionMsg.road_count)
}
void PerceptionMsg::set_allocated_road_info(::perception::RoadInfo* road_info) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  clear_MsgType();
  if (road_info) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(road_info);
    if (message_arena != submessage_arena) {
      road_info = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, road_info, submessage_arena);
    }
    set_has_road_info();
    _impl_.MsgType_.road_info_ = road_info;
  }
  // @@protoc_insertion_point(field_set_allocated:perception.PerceptionMsg.road_info)
}
PerceptionMsg::PerceptionMsg(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:perception.PerceptionMsg)
}
PerceptionMsg::PerceptionMsg(const PerceptionMsg& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  PerceptionMsg* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.time_){}
    , decltype(_impl_.MsgType_){}
    , /*decltype(_impl_._cached_size_)*/{}
    , /*decltype(_impl_._oneof_case_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  _this->_impl_.time_ = from._impl_.time_;
  clear_has_MsgType();
  switch (from.MsgType_case()) {
    case kTargetMsg: {
      _this->_internal_mutable_target_msg()->::perception::Targets::MergeFrom(
          from._internal_target_msg());
      break;
    }
    case kEventMsg: {
      _this->_internal_mutable_event_msg()->::perception::Events::MergeFrom(
          from._internal_event_msg());
      break;
    }
    case kHeartBeatMsg: {
      _this->_internal_mutable_heart_beat_msg()->::perception::HeartBeat::MergeFrom(
          from._internal_heart_beat_msg());
      break;
    }
    case kDevInfo: {
      _this->_internal_mutable_dev_info()->::perception::DeviceInfo::MergeFrom(
          from._internal_dev_info());
      break;
    }
    case kRoadCount: {
      _this->_internal_mutable_road_count()->::perception::RoadCounts::MergeFrom(
          from._internal_road_count());
      break;
    }
    case kRoadInfo: {
      _this->_internal_mutable_road_info()->::perception::RoadInfo::MergeFrom(
          from._internal_road_info());
      break;
    }
    case MSGTYPE_NOT_SET: {
      break;
    }
  }
  // @@protoc_insertion_point(copy_constructor:perception.PerceptionMsg)
}

inline void PerceptionMsg::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.time_){int64_t{0}}
    , decltype(_impl_.MsgType_){}
    , /*decltype(_impl_._cached_size_)*/{}
    , /*decltype(_impl_._oneof_case_)*/{}
  };
  clear_has_MsgType();
}

PerceptionMsg::~PerceptionMsg() {
  // @@protoc_insertion_point(destructor:perception.PerceptionMsg)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void PerceptionMsg::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (has_MsgType()) {
    clear_MsgType();
  }
}

void PerceptionMsg::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void PerceptionMsg::clear_MsgType() {
// @@protoc_insertion_point(one_of_clear_start:perception.PerceptionMsg)
  switch (MsgType_case()) {
    case kTargetMsg: {
      if (GetArenaForAllocation() == nullptr) {
        delete _impl_.MsgType_.target_msg_;
      }
      break;
    }
    case kEventMsg: {
      if (GetArenaForAllocation() == nullptr) {
        delete _impl_.MsgType_.event_msg_;
      }
      break;
    }
    case kHeartBeatMsg: {
      if (GetArenaForAllocation() == nullptr) {
        delete _impl_.MsgType_.heart_beat_msg_;
      }
      break;
    }
    case kDevInfo: {
      if (GetArenaForAllocation() == nullptr) {
        delete _impl_.MsgType_.dev_info_;
      }
      break;
    }
    case kRoadCount: {
      if (GetArenaForAllocation() == nullptr) {
        delete _impl_.MsgType_.road_count_;
      }
      break;
    }
    case kRoadInfo: {
      if (GetArenaForAllocation() == nullptr) {
        delete _impl_.MsgType_.road_info_;
      }
      break;
    }
    case MSGTYPE_NOT_SET: {
      break;
    }
  }
  _impl_._oneof_case_[0] = MSGTYPE_NOT_SET;
}


void PerceptionMsg::Clear() {
// @@protoc_insertion_point(message_clear_start:perception.PerceptionMsg)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.time_ = int64_t{0};
  clear_MsgType();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* PerceptionMsg::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // int64 time = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          _impl_.time_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .perception.Targets target_msg = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_target_msg(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .perception.Events event_msg = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          ptr = ctx->ParseMessage(_internal_mutable_event_msg(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .perception.HeartBeat heart_beat_msg = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 34)) {
          ptr = ctx->ParseMessage(_internal_mutable_heart_beat_msg(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .perception.DeviceInfo dev_info = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 42)) {
          ptr = ctx->ParseMessage(_internal_mutable_dev_info(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .perception.RoadCounts road_count = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 50)) {
          ptr = ctx->ParseMessage(_internal_mutable_road_count(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .perception.RoadInfo road_info = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 58)) {
          ptr = ctx->ParseMessage(_internal_mutable_road_info(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* PerceptionMsg::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:perception.PerceptionMsg)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // int64 time = 1;
  if (this->_internal_time() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt64ToArray(1, this->_internal_time(), target);
  }

  // .perception.Targets target_msg = 2;
  if (_internal_has_target_msg()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(2, _Internal::target_msg(this),
        _Internal::target_msg(this).GetCachedSize(), target, stream);
  }

  // .perception.Events event_msg = 3;
  if (_internal_has_event_msg()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(3, _Internal::event_msg(this),
        _Internal::event_msg(this).GetCachedSize(), target, stream);
  }

  // .perception.HeartBeat heart_beat_msg = 4;
  if (_internal_has_heart_beat_msg()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(4, _Internal::heart_beat_msg(this),
        _Internal::heart_beat_msg(this).GetCachedSize(), target, stream);
  }

  // .perception.DeviceInfo dev_info = 5;
  if (_internal_has_dev_info()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(5, _Internal::dev_info(this),
        _Internal::dev_info(this).GetCachedSize(), target, stream);
  }

  // .perception.RoadCounts road_count = 6;
  if (_internal_has_road_count()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(6, _Internal::road_count(this),
        _Internal::road_count(this).GetCachedSize(), target, stream);
  }

  // .perception.RoadInfo road_info = 7;
  if (_internal_has_road_info()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(7, _Internal::road_info(this),
        _Internal::road_info(this).GetCachedSize(), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:perception.PerceptionMsg)
  return target;
}

size_t PerceptionMsg::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:perception.PerceptionMsg)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // int64 time = 1;
  if (this->_internal_time() != 0) {
    total_size += ::_pbi::WireFormatLite::Int64SizePlusOne(this->_internal_time());
  }

  switch (MsgType_case()) {
    // .perception.Targets target_msg = 2;
    case kTargetMsg: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *_impl_.MsgType_.target_msg_);
      break;
    }
    // .perception.Events event_msg = 3;
    case kEventMsg: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *_impl_.MsgType_.event_msg_);
      break;
    }
    // .perception.HeartBeat heart_beat_msg = 4;
    case kHeartBeatMsg: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *_impl_.MsgType_.heart_beat_msg_);
      break;
    }
    // .perception.DeviceInfo dev_info = 5;
    case kDevInfo: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *_impl_.MsgType_.dev_info_);
      break;
    }
    // .perception.RoadCounts road_count = 6;
    case kRoadCount: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *_impl_.MsgType_.road_count_);
      break;
    }
    // .perception.RoadInfo road_info = 7;
    case kRoadInfo: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *_impl_.MsgType_.road_info_);
      break;
    }
    case MSGTYPE_NOT_SET: {
      break;
    }
  }
  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData PerceptionMsg::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    PerceptionMsg::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*PerceptionMsg::GetClassData() const { return &_class_data_; }


void PerceptionMsg::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<PerceptionMsg*>(&to_msg);
  auto& from = static_cast<const PerceptionMsg&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:perception.PerceptionMsg)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_time() != 0) {
    _this->_internal_set_time(from._internal_time());
  }
  switch (from.MsgType_case()) {
    case kTargetMsg: {
      _this->_internal_mutable_target_msg()->::perception::Targets::MergeFrom(
          from._internal_target_msg());
      break;
    }
    case kEventMsg: {
      _this->_internal_mutable_event_msg()->::perception::Events::MergeFrom(
          from._internal_event_msg());
      break;
    }
    case kHeartBeatMsg: {
      _this->_internal_mutable_heart_beat_msg()->::perception::HeartBeat::MergeFrom(
          from._internal_heart_beat_msg());
      break;
    }
    case kDevInfo: {
      _this->_internal_mutable_dev_info()->::perception::DeviceInfo::MergeFrom(
          from._internal_dev_info());
      break;
    }
    case kRoadCount: {
      _this->_internal_mutable_road_count()->::perception::RoadCounts::MergeFrom(
          from._internal_road_count());
      break;
    }
    case kRoadInfo: {
      _this->_internal_mutable_road_info()->::perception::RoadInfo::MergeFrom(
          from._internal_road_info());
      break;
    }
    case MSGTYPE_NOT_SET: {
      break;
    }
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void PerceptionMsg::CopyFrom(const PerceptionMsg& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:perception.PerceptionMsg)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool PerceptionMsg::IsInitialized() const {
  return true;
}

void PerceptionMsg::InternalSwap(PerceptionMsg* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(_impl_.time_, other->_impl_.time_);
  swap(_impl_.MsgType_, other->_impl_.MsgType_);
  swap(_impl_._oneof_case_[0], other->_impl_._oneof_case_[0]);
}

::PROTOBUF_NAMESPACE_ID::Metadata PerceptionMsg::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_perception_2eproto_getter, &descriptor_table_perception_2eproto_once,
      file_level_metadata_perception_2eproto[18]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace perception
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::perception::Position*
Arena::CreateMaybeMessage< ::perception::Position >(Arena* arena) {
  return Arena::CreateMessageInternal< ::perception::Position >(arena);
}
template<> PROTOBUF_NOINLINE ::perception::Size*
Arena::CreateMaybeMessage< ::perception::Size >(Arena* arena) {
  return Arena::CreateMessageInternal< ::perception::Size >(arena);
}
template<> PROTOBUF_NOINLINE ::perception::LaneInfo*
Arena::CreateMaybeMessage< ::perception::LaneInfo >(Arena* arena) {
  return Arena::CreateMessageInternal< ::perception::LaneInfo >(arena);
}
template<> PROTOBUF_NOINLINE ::perception::Target*
Arena::CreateMaybeMessage< ::perception::Target >(Arena* arena) {
  return Arena::CreateMessageInternal< ::perception::Target >(arena);
}
template<> PROTOBUF_NOINLINE ::perception::Targets*
Arena::CreateMaybeMessage< ::perception::Targets >(Arena* arena) {
  return Arena::CreateMessageInternal< ::perception::Targets >(arena);
}
template<> PROTOBUF_NOINLINE ::perception::AreaInfo*
Arena::CreateMaybeMessage< ::perception::AreaInfo >(Arena* arena) {
  return Arena::CreateMessageInternal< ::perception::AreaInfo >(arena);
}
template<> PROTOBUF_NOINLINE ::perception::LaneCount*
Arena::CreateMaybeMessage< ::perception::LaneCount >(Arena* arena) {
  return Arena::CreateMessageInternal< ::perception::LaneCount >(arena);
}
template<> PROTOBUF_NOINLINE ::perception::RoadCount*
Arena::CreateMaybeMessage< ::perception::RoadCount >(Arena* arena) {
  return Arena::CreateMessageInternal< ::perception::RoadCount >(arena);
}
template<> PROTOBUF_NOINLINE ::perception::IntersectionCount*
Arena::CreateMaybeMessage< ::perception::IntersectionCount >(Arena* arena) {
  return Arena::CreateMessageInternal< ::perception::IntersectionCount >(arena);
}
template<> PROTOBUF_NOINLINE ::perception::Event*
Arena::CreateMaybeMessage< ::perception::Event >(Arena* arena) {
  return Arena::CreateMessageInternal< ::perception::Event >(arena);
}
template<> PROTOBUF_NOINLINE ::perception::Events*
Arena::CreateMaybeMessage< ::perception::Events >(Arena* arena) {
  return Arena::CreateMessageInternal< ::perception::Events >(arena);
}
template<> PROTOBUF_NOINLINE ::perception::HeartBeat*
Arena::CreateMaybeMessage< ::perception::HeartBeat >(Arena* arena) {
  return Arena::CreateMessageInternal< ::perception::HeartBeat >(arena);
}
template<> PROTOBUF_NOINLINE ::perception::DeviceInfo*
Arena::CreateMaybeMessage< ::perception::DeviceInfo >(Arena* arena) {
  return Arena::CreateMessageInternal< ::perception::DeviceInfo >(arena);
}
template<> PROTOBUF_NOINLINE ::perception::RoadCounts*
Arena::CreateMaybeMessage< ::perception::RoadCounts >(Arena* arena) {
  return Arena::CreateMessageInternal< ::perception::RoadCounts >(arena);
}
template<> PROTOBUF_NOINLINE ::perception::LaneCountInfo*
Arena::CreateMaybeMessage< ::perception::LaneCountInfo >(Arena* arena) {
  return Arena::CreateMessageInternal< ::perception::LaneCountInfo >(arena);
}
template<> PROTOBUF_NOINLINE ::perception::RoadCountInfo*
Arena::CreateMaybeMessage< ::perception::RoadCountInfo >(Arena* arena) {
  return Arena::CreateMessageInternal< ::perception::RoadCountInfo >(arena);
}
template<> PROTOBUF_NOINLINE ::perception::IntersectionInfo*
Arena::CreateMaybeMessage< ::perception::IntersectionInfo >(Arena* arena) {
  return Arena::CreateMessageInternal< ::perception::IntersectionInfo >(arena);
}
template<> PROTOBUF_NOINLINE ::perception::RoadInfo*
Arena::CreateMaybeMessage< ::perception::RoadInfo >(Arena* arena) {
  return Arena::CreateMessageInternal< ::perception::RoadInfo >(arena);
}
template<> PROTOBUF_NOINLINE ::perception::PerceptionMsg*
Arena::CreateMaybeMessage< ::perception::PerceptionMsg >(Arena* arena) {
  return Arena::CreateMessageInternal< ::perception::PerceptionMsg >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>
