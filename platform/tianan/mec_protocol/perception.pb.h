// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: perception.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_perception_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_perception_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3021000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3021012 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_perception_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_perception_2eproto {
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_perception_2eproto;
namespace perception {
class AreaInfo;
struct AreaInfoDefaultTypeInternal;
extern AreaInfoDefaultTypeInternal _AreaInfo_default_instance_;
class DeviceInfo;
struct DeviceInfoDefaultTypeInternal;
extern DeviceInfoDefaultTypeInternal _DeviceInfo_default_instance_;
class Event;
struct EventDefaultTypeInternal;
extern EventDefaultTypeInternal _Event_default_instance_;
class Events;
struct EventsDefaultTypeInternal;
extern EventsDefaultTypeInternal _Events_default_instance_;
class HeartBeat;
struct HeartBeatDefaultTypeInternal;
extern HeartBeatDefaultTypeInternal _HeartBeat_default_instance_;
class IntersectionCount;
struct IntersectionCountDefaultTypeInternal;
extern IntersectionCountDefaultTypeInternal _IntersectionCount_default_instance_;
class IntersectionInfo;
struct IntersectionInfoDefaultTypeInternal;
extern IntersectionInfoDefaultTypeInternal _IntersectionInfo_default_instance_;
class LaneCount;
struct LaneCountDefaultTypeInternal;
extern LaneCountDefaultTypeInternal _LaneCount_default_instance_;
class LaneCountInfo;
struct LaneCountInfoDefaultTypeInternal;
extern LaneCountInfoDefaultTypeInternal _LaneCountInfo_default_instance_;
class LaneInfo;
struct LaneInfoDefaultTypeInternal;
extern LaneInfoDefaultTypeInternal _LaneInfo_default_instance_;
class PerceptionMsg;
struct PerceptionMsgDefaultTypeInternal;
extern PerceptionMsgDefaultTypeInternal _PerceptionMsg_default_instance_;
class Position;
struct PositionDefaultTypeInternal;
extern PositionDefaultTypeInternal _Position_default_instance_;
class RoadCount;
struct RoadCountDefaultTypeInternal;
extern RoadCountDefaultTypeInternal _RoadCount_default_instance_;
class RoadCountInfo;
struct RoadCountInfoDefaultTypeInternal;
extern RoadCountInfoDefaultTypeInternal _RoadCountInfo_default_instance_;
class RoadCounts;
struct RoadCountsDefaultTypeInternal;
extern RoadCountsDefaultTypeInternal _RoadCounts_default_instance_;
class RoadInfo;
struct RoadInfoDefaultTypeInternal;
extern RoadInfoDefaultTypeInternal _RoadInfo_default_instance_;
class Size;
struct SizeDefaultTypeInternal;
extern SizeDefaultTypeInternal _Size_default_instance_;
class Target;
struct TargetDefaultTypeInternal;
extern TargetDefaultTypeInternal _Target_default_instance_;
class Targets;
struct TargetsDefaultTypeInternal;
extern TargetsDefaultTypeInternal _Targets_default_instance_;
}  // namespace perception
PROTOBUF_NAMESPACE_OPEN
template<> ::perception::AreaInfo* Arena::CreateMaybeMessage<::perception::AreaInfo>(Arena*);
template<> ::perception::DeviceInfo* Arena::CreateMaybeMessage<::perception::DeviceInfo>(Arena*);
template<> ::perception::Event* Arena::CreateMaybeMessage<::perception::Event>(Arena*);
template<> ::perception::Events* Arena::CreateMaybeMessage<::perception::Events>(Arena*);
template<> ::perception::HeartBeat* Arena::CreateMaybeMessage<::perception::HeartBeat>(Arena*);
template<> ::perception::IntersectionCount* Arena::CreateMaybeMessage<::perception::IntersectionCount>(Arena*);
template<> ::perception::IntersectionInfo* Arena::CreateMaybeMessage<::perception::IntersectionInfo>(Arena*);
template<> ::perception::LaneCount* Arena::CreateMaybeMessage<::perception::LaneCount>(Arena*);
template<> ::perception::LaneCountInfo* Arena::CreateMaybeMessage<::perception::LaneCountInfo>(Arena*);
template<> ::perception::LaneInfo* Arena::CreateMaybeMessage<::perception::LaneInfo>(Arena*);
template<> ::perception::PerceptionMsg* Arena::CreateMaybeMessage<::perception::PerceptionMsg>(Arena*);
template<> ::perception::Position* Arena::CreateMaybeMessage<::perception::Position>(Arena*);
template<> ::perception::RoadCount* Arena::CreateMaybeMessage<::perception::RoadCount>(Arena*);
template<> ::perception::RoadCountInfo* Arena::CreateMaybeMessage<::perception::RoadCountInfo>(Arena*);
template<> ::perception::RoadCounts* Arena::CreateMaybeMessage<::perception::RoadCounts>(Arena*);
template<> ::perception::RoadInfo* Arena::CreateMaybeMessage<::perception::RoadInfo>(Arena*);
template<> ::perception::Size* Arena::CreateMaybeMessage<::perception::Size>(Arena*);
template<> ::perception::Target* Arena::CreateMaybeMessage<::perception::Target>(Arena*);
template<> ::perception::Targets* Arena::CreateMaybeMessage<::perception::Targets>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace perception {

// ===================================================================

class Position final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:perception.Position) */ {
 public:
  inline Position() : Position(nullptr) {}
  ~Position() override;
  explicit PROTOBUF_CONSTEXPR Position(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Position(const Position& from);
  Position(Position&& from) noexcept
    : Position() {
    *this = ::std::move(from);
  }

  inline Position& operator=(const Position& from) {
    CopyFrom(from);
    return *this;
  }
  inline Position& operator=(Position&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Position& default_instance() {
    return *internal_default_instance();
  }
  static inline const Position* internal_default_instance() {
    return reinterpret_cast<const Position*>(
               &_Position_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(Position& a, Position& b) {
    a.Swap(&b);
  }
  inline void Swap(Position* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Position* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Position* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<Position>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const Position& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const Position& from) {
    Position::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Position* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "perception.Position";
  }
  protected:
  explicit Position(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kLatFieldNumber = 1,
    kLonFieldNumber = 2,
    kElevFieldNumber = 3,
  };
  // int64 lat = 1;
  void clear_lat();
  int64_t lat() const;
  void set_lat(int64_t value);
  private:
  int64_t _internal_lat() const;
  void _internal_set_lat(int64_t value);
  public:

  // int64 lon = 2;
  void clear_lon();
  int64_t lon() const;
  void set_lon(int64_t value);
  private:
  int64_t _internal_lon() const;
  void _internal_set_lon(int64_t value);
  public:

  // int32 elev = 3;
  void clear_elev();
  int32_t elev() const;
  void set_elev(int32_t value);
  private:
  int32_t _internal_elev() const;
  void _internal_set_elev(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:perception.Position)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    int64_t lat_;
    int64_t lon_;
    int32_t elev_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_perception_2eproto;
};
// -------------------------------------------------------------------

class Size final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:perception.Size) */ {
 public:
  inline Size() : Size(nullptr) {}
  ~Size() override;
  explicit PROTOBUF_CONSTEXPR Size(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Size(const Size& from);
  Size(Size&& from) noexcept
    : Size() {
    *this = ::std::move(from);
  }

  inline Size& operator=(const Size& from) {
    CopyFrom(from);
    return *this;
  }
  inline Size& operator=(Size&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Size& default_instance() {
    return *internal_default_instance();
  }
  static inline const Size* internal_default_instance() {
    return reinterpret_cast<const Size*>(
               &_Size_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(Size& a, Size& b) {
    a.Swap(&b);
  }
  inline void Swap(Size* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Size* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Size* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<Size>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const Size& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const Size& from) {
    Size::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Size* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "perception.Size";
  }
  protected:
  explicit Size(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kLengthFieldNumber = 1,
    kWidthFieldNumber = 2,
    kHeightFieldNumber = 3,
  };
  // int32 length = 1;
  void clear_length();
  int32_t length() const;
  void set_length(int32_t value);
  private:
  int32_t _internal_length() const;
  void _internal_set_length(int32_t value);
  public:

  // int32 width = 2;
  void clear_width();
  int32_t width() const;
  void set_width(int32_t value);
  private:
  int32_t _internal_width() const;
  void _internal_set_width(int32_t value);
  public:

  // int32 height = 3;
  void clear_height();
  int32_t height() const;
  void set_height(int32_t value);
  private:
  int32_t _internal_height() const;
  void _internal_set_height(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:perception.Size)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    int32_t length_;
    int32_t width_;
    int32_t height_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_perception_2eproto;
};
// -------------------------------------------------------------------

class LaneInfo final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:perception.LaneInfo) */ {
 public:
  inline LaneInfo() : LaneInfo(nullptr) {}
  ~LaneInfo() override;
  explicit PROTOBUF_CONSTEXPR LaneInfo(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  LaneInfo(const LaneInfo& from);
  LaneInfo(LaneInfo&& from) noexcept
    : LaneInfo() {
    *this = ::std::move(from);
  }

  inline LaneInfo& operator=(const LaneInfo& from) {
    CopyFrom(from);
    return *this;
  }
  inline LaneInfo& operator=(LaneInfo&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const LaneInfo& default_instance() {
    return *internal_default_instance();
  }
  static inline const LaneInfo* internal_default_instance() {
    return reinterpret_cast<const LaneInfo*>(
               &_LaneInfo_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(LaneInfo& a, LaneInfo& b) {
    a.Swap(&b);
  }
  inline void Swap(LaneInfo* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(LaneInfo* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  LaneInfo* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<LaneInfo>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const LaneInfo& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const LaneInfo& from) {
    LaneInfo::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(LaneInfo* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "perception.LaneInfo";
  }
  protected:
  explicit LaneInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kAreaTypeFieldNumber = 1,
    kLaneCountFieldNumber = 2,
    kLaneHeadingFieldNumber = 3,
  };
  // int32 area_type = 1;
  void clear_area_type();
  int32_t area_type() const;
  void set_area_type(int32_t value);
  private:
  int32_t _internal_area_type() const;
  void _internal_set_area_type(int32_t value);
  public:

  // int32 lane_count = 2;
  void clear_lane_count();
  int32_t lane_count() const;
  void set_lane_count(int32_t value);
  private:
  int32_t _internal_lane_count() const;
  void _internal_set_lane_count(int32_t value);
  public:

  // float lane_heading = 3;
  void clear_lane_heading();
  float lane_heading() const;
  void set_lane_heading(float value);
  private:
  float _internal_lane_heading() const;
  void _internal_set_lane_heading(float value);
  public:

  // @@protoc_insertion_point(class_scope:perception.LaneInfo)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    int32_t area_type_;
    int32_t lane_count_;
    float lane_heading_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_perception_2eproto;
};
// -------------------------------------------------------------------

class Target final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:perception.Target) */ {
 public:
  inline Target() : Target(nullptr) {}
  ~Target() override;
  explicit PROTOBUF_CONSTEXPR Target(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Target(const Target& from);
  Target(Target&& from) noexcept
    : Target() {
    *this = ::std::move(from);
  }

  inline Target& operator=(const Target& from) {
    CopyFrom(from);
    return *this;
  }
  inline Target& operator=(Target&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Target& default_instance() {
    return *internal_default_instance();
  }
  static inline const Target* internal_default_instance() {
    return reinterpret_cast<const Target*>(
               &_Target_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(Target& a, Target& b) {
    a.Swap(&b);
  }
  inline void Swap(Target* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Target* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Target* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<Target>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const Target& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const Target& from) {
    Target::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Target* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "perception.Target";
  }
  protected:
  explicit Target(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kLicensePlateFieldNumber = 11,
    kPosFieldNumber = 3,
    kSizeFieldNumber = 4,
    kLaneInfoFieldNumber = 10,
    kIdFieldNumber = 1,
    kSourceFieldNumber = 2,
    kConfidenceFieldNumber = 5,
    kTypeFieldNumber = 6,
    kSpeedFieldNumber = 7,
    kHeadingFieldNumber = 8,
    kLaneIdFieldNumber = 9,
    kCarTypeFieldNumber = 12,
    kColorFieldNumber = 13,
    kStatusFieldNumber = 14,
    kDistanceFieldNumber = 15,
    kTrackTimeFieldNumber = 17,
    kRcsFieldNumber = 16,
    kVehiclePoseFieldNumber = 18,
    kVehicleTypeFieldNumber = 19,
    kVehicleMarkerFieldNumber = 20,
    kVehicleOtherFieldNumber = 21,
    kVehicleRoofFieldNumber = 22,
    kVehicleCarColorFieldNumber = 23,
    kVehicleMakeFieldNumber = 24,
    kVehicleModelFieldNumber = 25,
    kVehicleYearFieldNumber = 26,
    kVehiclePlateColorFieldNumber = 27,
    kVehiclePlateTypeFieldNumber = 28,
    kFaceAgeFieldNumber = 29,
    kFaceGenderFieldNumber = 30,
    kFaceMaskFieldNumber = 31,
    kFaceHatFieldNumber = 32,
    kFaceGlassFieldNumber = 33,
    kPedestrianShoecolorFieldNumber = 34,
    kPedestrianShoestyleFieldNumber = 35,
    kPedestrianSleeveFieldNumber = 36,
    kPedestrianHairFieldNumber = 37,
    kPedestrianNationFieldNumber = 38,
    kPedestrianGenderFieldNumber = 39,
    kPedestrianLowerstyleFieldNumber = 40,
    kPedestrianUpperstyleFieldNumber = 41,
    kPedestrianAgeFieldNumber = 42,
    kPedestrianLowerFieldNumber = 43,
    kPedestrianUpperFieldNumber = 44,
    kPedestrianBackpackFieldNumber = 45,
    kNonmotorAttitudeFieldNumber = 46,
    kNonmotorGenderFieldNumber = 47,
    kNonmotorNationFieldNumber = 48,
    kNonmotorTransportationFieldNumber = 49,
    kNonmotorUpperColorFieldNumber = 50,
    kNonmotorUpperFieldNumber = 51,
    kNonmotorTransportationColorFieldNumber = 52,
  };
  // string license_plate = 11;
  void clear_license_plate();
  const std::string& license_plate() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_license_plate(ArgT0&& arg0, ArgT... args);
  std::string* mutable_license_plate();
  PROTOBUF_NODISCARD std::string* release_license_plate();
  void set_allocated_license_plate(std::string* license_plate);
  private:
  const std::string& _internal_license_plate() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_license_plate(const std::string& value);
  std::string* _internal_mutable_license_plate();
  public:

  // .perception.Position pos = 3;
  bool has_pos() const;
  private:
  bool _internal_has_pos() const;
  public:
  void clear_pos();
  const ::perception::Position& pos() const;
  PROTOBUF_NODISCARD ::perception::Position* release_pos();
  ::perception::Position* mutable_pos();
  void set_allocated_pos(::perception::Position* pos);
  private:
  const ::perception::Position& _internal_pos() const;
  ::perception::Position* _internal_mutable_pos();
  public:
  void unsafe_arena_set_allocated_pos(
      ::perception::Position* pos);
  ::perception::Position* unsafe_arena_release_pos();

  // .perception.Size size = 4;
  bool has_size() const;
  private:
  bool _internal_has_size() const;
  public:
  void clear_size();
  const ::perception::Size& size() const;
  PROTOBUF_NODISCARD ::perception::Size* release_size();
  ::perception::Size* mutable_size();
  void set_allocated_size(::perception::Size* size);
  private:
  const ::perception::Size& _internal_size() const;
  ::perception::Size* _internal_mutable_size();
  public:
  void unsafe_arena_set_allocated_size(
      ::perception::Size* size);
  ::perception::Size* unsafe_arena_release_size();

  // .perception.LaneInfo lane_info = 10;
  bool has_lane_info() const;
  private:
  bool _internal_has_lane_info() const;
  public:
  void clear_lane_info();
  const ::perception::LaneInfo& lane_info() const;
  PROTOBUF_NODISCARD ::perception::LaneInfo* release_lane_info();
  ::perception::LaneInfo* mutable_lane_info();
  void set_allocated_lane_info(::perception::LaneInfo* lane_info);
  private:
  const ::perception::LaneInfo& _internal_lane_info() const;
  ::perception::LaneInfo* _internal_mutable_lane_info();
  public:
  void unsafe_arena_set_allocated_lane_info(
      ::perception::LaneInfo* lane_info);
  ::perception::LaneInfo* unsafe_arena_release_lane_info();

  // int64 id = 1;
  void clear_id();
  int64_t id() const;
  void set_id(int64_t value);
  private:
  int64_t _internal_id() const;
  void _internal_set_id(int64_t value);
  public:

  // int32 source = 2;
  void clear_source();
  int32_t source() const;
  void set_source(int32_t value);
  private:
  int32_t _internal_source() const;
  void _internal_set_source(int32_t value);
  public:

  // float confidence = 5;
  void clear_confidence();
  float confidence() const;
  void set_confidence(float value);
  private:
  float _internal_confidence() const;
  void _internal_set_confidence(float value);
  public:

  // int32 type = 6;
  void clear_type();
  int32_t type() const;
  void set_type(int32_t value);
  private:
  int32_t _internal_type() const;
  void _internal_set_type(int32_t value);
  public:

  // int32 speed = 7;
  void clear_speed();
  int32_t speed() const;
  void set_speed(int32_t value);
  private:
  int32_t _internal_speed() const;
  void _internal_set_speed(int32_t value);
  public:

  // int32 heading = 8;
  void clear_heading();
  int32_t heading() const;
  void set_heading(int32_t value);
  private:
  int32_t _internal_heading() const;
  void _internal_set_heading(int32_t value);
  public:

  // int32 lane_id = 9;
  void clear_lane_id();
  int32_t lane_id() const;
  void set_lane_id(int32_t value);
  private:
  int32_t _internal_lane_id() const;
  void _internal_set_lane_id(int32_t value);
  public:

  // int32 car_type = 12;
  void clear_car_type();
  int32_t car_type() const;
  void set_car_type(int32_t value);
  private:
  int32_t _internal_car_type() const;
  void _internal_set_car_type(int32_t value);
  public:

  // int32 color = 13;
  void clear_color();
  int32_t color() const;
  void set_color(int32_t value);
  private:
  int32_t _internal_color() const;
  void _internal_set_color(int32_t value);
  public:

  // int32 status = 14;
  void clear_status();
  int32_t status() const;
  void set_status(int32_t value);
  private:
  int32_t _internal_status() const;
  void _internal_set_status(int32_t value);
  public:

  // float distance = 15;
  void clear_distance();
  float distance() const;
  void set_distance(float value);
  private:
  float _internal_distance() const;
  void _internal_set_distance(float value);
  public:

  // int64 track_time = 17;
  void clear_track_time();
  int64_t track_time() const;
  void set_track_time(int64_t value);
  private:
  int64_t _internal_track_time() const;
  void _internal_set_track_time(int64_t value);
  public:

  // float rcs = 16;
  void clear_rcs();
  float rcs() const;
  void set_rcs(float value);
  private:
  float _internal_rcs() const;
  void _internal_set_rcs(float value);
  public:

  // int32 vehicle_pose = 18;
  void clear_vehicle_pose();
  int32_t vehicle_pose() const;
  void set_vehicle_pose(int32_t value);
  private:
  int32_t _internal_vehicle_pose() const;
  void _internal_set_vehicle_pose(int32_t value);
  public:

  // int32 vehicle_type = 19;
  void clear_vehicle_type();
  int32_t vehicle_type() const;
  void set_vehicle_type(int32_t value);
  private:
  int32_t _internal_vehicle_type() const;
  void _internal_set_vehicle_type(int32_t value);
  public:

  // int32 vehicle_marker = 20;
  void clear_vehicle_marker();
  int32_t vehicle_marker() const;
  void set_vehicle_marker(int32_t value);
  private:
  int32_t _internal_vehicle_marker() const;
  void _internal_set_vehicle_marker(int32_t value);
  public:

  // int32 vehicle_other = 21;
  void clear_vehicle_other();
  int32_t vehicle_other() const;
  void set_vehicle_other(int32_t value);
  private:
  int32_t _internal_vehicle_other() const;
  void _internal_set_vehicle_other(int32_t value);
  public:

  // int32 vehicle_roof = 22;
  void clear_vehicle_roof();
  int32_t vehicle_roof() const;
  void set_vehicle_roof(int32_t value);
  private:
  int32_t _internal_vehicle_roof() const;
  void _internal_set_vehicle_roof(int32_t value);
  public:

  // int32 vehicle_carColor = 23;
  void clear_vehicle_carcolor();
  int32_t vehicle_carcolor() const;
  void set_vehicle_carcolor(int32_t value);
  private:
  int32_t _internal_vehicle_carcolor() const;
  void _internal_set_vehicle_carcolor(int32_t value);
  public:

  // int32 vehicle_make = 24;
  void clear_vehicle_make();
  int32_t vehicle_make() const;
  void set_vehicle_make(int32_t value);
  private:
  int32_t _internal_vehicle_make() const;
  void _internal_set_vehicle_make(int32_t value);
  public:

  // int32 vehicle_model = 25;
  void clear_vehicle_model();
  int32_t vehicle_model() const;
  void set_vehicle_model(int32_t value);
  private:
  int32_t _internal_vehicle_model() const;
  void _internal_set_vehicle_model(int32_t value);
  public:

  // int32 vehicle_year = 26;
  void clear_vehicle_year();
  int32_t vehicle_year() const;
  void set_vehicle_year(int32_t value);
  private:
  int32_t _internal_vehicle_year() const;
  void _internal_set_vehicle_year(int32_t value);
  public:

  // int32 vehicle_plate_color = 27;
  void clear_vehicle_plate_color();
  int32_t vehicle_plate_color() const;
  void set_vehicle_plate_color(int32_t value);
  private:
  int32_t _internal_vehicle_plate_color() const;
  void _internal_set_vehicle_plate_color(int32_t value);
  public:

  // int32 vehicle_plate_type = 28;
  void clear_vehicle_plate_type();
  int32_t vehicle_plate_type() const;
  void set_vehicle_plate_type(int32_t value);
  private:
  int32_t _internal_vehicle_plate_type() const;
  void _internal_set_vehicle_plate_type(int32_t value);
  public:

  // int32 face_age = 29;
  void clear_face_age();
  int32_t face_age() const;
  void set_face_age(int32_t value);
  private:
  int32_t _internal_face_age() const;
  void _internal_set_face_age(int32_t value);
  public:

  // int32 face_gender = 30;
  void clear_face_gender();
  int32_t face_gender() const;
  void set_face_gender(int32_t value);
  private:
  int32_t _internal_face_gender() const;
  void _internal_set_face_gender(int32_t value);
  public:

  // int32 face_mask = 31;
  void clear_face_mask();
  int32_t face_mask() const;
  void set_face_mask(int32_t value);
  private:
  int32_t _internal_face_mask() const;
  void _internal_set_face_mask(int32_t value);
  public:

  // int32 face_hat = 32;
  void clear_face_hat();
  int32_t face_hat() const;
  void set_face_hat(int32_t value);
  private:
  int32_t _internal_face_hat() const;
  void _internal_set_face_hat(int32_t value);
  public:

  // int32 face_glass = 33;
  void clear_face_glass();
  int32_t face_glass() const;
  void set_face_glass(int32_t value);
  private:
  int32_t _internal_face_glass() const;
  void _internal_set_face_glass(int32_t value);
  public:

  // int32 pedestrian_shoecolor = 34;
  void clear_pedestrian_shoecolor();
  int32_t pedestrian_shoecolor() const;
  void set_pedestrian_shoecolor(int32_t value);
  private:
  int32_t _internal_pedestrian_shoecolor() const;
  void _internal_set_pedestrian_shoecolor(int32_t value);
  public:

  // int32 pedestrian_shoestyle = 35;
  void clear_pedestrian_shoestyle();
  int32_t pedestrian_shoestyle() const;
  void set_pedestrian_shoestyle(int32_t value);
  private:
  int32_t _internal_pedestrian_shoestyle() const;
  void _internal_set_pedestrian_shoestyle(int32_t value);
  public:

  // int32 pedestrian_sleeve = 36;
  void clear_pedestrian_sleeve();
  int32_t pedestrian_sleeve() const;
  void set_pedestrian_sleeve(int32_t value);
  private:
  int32_t _internal_pedestrian_sleeve() const;
  void _internal_set_pedestrian_sleeve(int32_t value);
  public:

  // int32 pedestrian_hair = 37;
  void clear_pedestrian_hair();
  int32_t pedestrian_hair() const;
  void set_pedestrian_hair(int32_t value);
  private:
  int32_t _internal_pedestrian_hair() const;
  void _internal_set_pedestrian_hair(int32_t value);
  public:

  // int32 pedestrian_nation = 38;
  void clear_pedestrian_nation();
  int32_t pedestrian_nation() const;
  void set_pedestrian_nation(int32_t value);
  private:
  int32_t _internal_pedestrian_nation() const;
  void _internal_set_pedestrian_nation(int32_t value);
  public:

  // int32 pedestrian_gender = 39;
  void clear_pedestrian_gender();
  int32_t pedestrian_gender() const;
  void set_pedestrian_gender(int32_t value);
  private:
  int32_t _internal_pedestrian_gender() const;
  void _internal_set_pedestrian_gender(int32_t value);
  public:

  // int32 pedestrian_lowerstyle = 40;
  void clear_pedestrian_lowerstyle();
  int32_t pedestrian_lowerstyle() const;
  void set_pedestrian_lowerstyle(int32_t value);
  private:
  int32_t _internal_pedestrian_lowerstyle() const;
  void _internal_set_pedestrian_lowerstyle(int32_t value);
  public:

  // int32 pedestrian_upperstyle = 41;
  void clear_pedestrian_upperstyle();
  int32_t pedestrian_upperstyle() const;
  void set_pedestrian_upperstyle(int32_t value);
  private:
  int32_t _internal_pedestrian_upperstyle() const;
  void _internal_set_pedestrian_upperstyle(int32_t value);
  public:

  // int32 pedestrian_age = 42;
  void clear_pedestrian_age();
  int32_t pedestrian_age() const;
  void set_pedestrian_age(int32_t value);
  private:
  int32_t _internal_pedestrian_age() const;
  void _internal_set_pedestrian_age(int32_t value);
  public:

  // int32 pedestrian_lower = 43;
  void clear_pedestrian_lower();
  int32_t pedestrian_lower() const;
  void set_pedestrian_lower(int32_t value);
  private:
  int32_t _internal_pedestrian_lower() const;
  void _internal_set_pedestrian_lower(int32_t value);
  public:

  // int32 pedestrian_upper = 44;
  void clear_pedestrian_upper();
  int32_t pedestrian_upper() const;
  void set_pedestrian_upper(int32_t value);
  private:
  int32_t _internal_pedestrian_upper() const;
  void _internal_set_pedestrian_upper(int32_t value);
  public:

  // int32 pedestrian_backpack = 45;
  void clear_pedestrian_backpack();
  int32_t pedestrian_backpack() const;
  void set_pedestrian_backpack(int32_t value);
  private:
  int32_t _internal_pedestrian_backpack() const;
  void _internal_set_pedestrian_backpack(int32_t value);
  public:

  // int32 nonmotor_attitude = 46;
  void clear_nonmotor_attitude();
  int32_t nonmotor_attitude() const;
  void set_nonmotor_attitude(int32_t value);
  private:
  int32_t _internal_nonmotor_attitude() const;
  void _internal_set_nonmotor_attitude(int32_t value);
  public:

  // int32 nonmotor_gender = 47;
  void clear_nonmotor_gender();
  int32_t nonmotor_gender() const;
  void set_nonmotor_gender(int32_t value);
  private:
  int32_t _internal_nonmotor_gender() const;
  void _internal_set_nonmotor_gender(int32_t value);
  public:

  // int32 nonmotor_nation = 48;
  void clear_nonmotor_nation();
  int32_t nonmotor_nation() const;
  void set_nonmotor_nation(int32_t value);
  private:
  int32_t _internal_nonmotor_nation() const;
  void _internal_set_nonmotor_nation(int32_t value);
  public:

  // int32 nonmotor_transportation = 49;
  void clear_nonmotor_transportation();
  int32_t nonmotor_transportation() const;
  void set_nonmotor_transportation(int32_t value);
  private:
  int32_t _internal_nonmotor_transportation() const;
  void _internal_set_nonmotor_transportation(int32_t value);
  public:

  // int32 nonmotor_upperColor = 50;
  void clear_nonmotor_uppercolor();
  int32_t nonmotor_uppercolor() const;
  void set_nonmotor_uppercolor(int32_t value);
  private:
  int32_t _internal_nonmotor_uppercolor() const;
  void _internal_set_nonmotor_uppercolor(int32_t value);
  public:

  // int32 nonmotor_upper = 51;
  void clear_nonmotor_upper();
  int32_t nonmotor_upper() const;
  void set_nonmotor_upper(int32_t value);
  private:
  int32_t _internal_nonmotor_upper() const;
  void _internal_set_nonmotor_upper(int32_t value);
  public:

  // int32 nonmotor_transportationColor = 52;
  void clear_nonmotor_transportationcolor();
  int32_t nonmotor_transportationcolor() const;
  void set_nonmotor_transportationcolor(int32_t value);
  private:
  int32_t _internal_nonmotor_transportationcolor() const;
  void _internal_set_nonmotor_transportationcolor(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:perception.Target)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr license_plate_;
    ::perception::Position* pos_;
    ::perception::Size* size_;
    ::perception::LaneInfo* lane_info_;
    int64_t id_;
    int32_t source_;
    float confidence_;
    int32_t type_;
    int32_t speed_;
    int32_t heading_;
    int32_t lane_id_;
    int32_t car_type_;
    int32_t color_;
    int32_t status_;
    float distance_;
    int64_t track_time_;
    float rcs_;
    int32_t vehicle_pose_;
    int32_t vehicle_type_;
    int32_t vehicle_marker_;
    int32_t vehicle_other_;
    int32_t vehicle_roof_;
    int32_t vehicle_carcolor_;
    int32_t vehicle_make_;
    int32_t vehicle_model_;
    int32_t vehicle_year_;
    int32_t vehicle_plate_color_;
    int32_t vehicle_plate_type_;
    int32_t face_age_;
    int32_t face_gender_;
    int32_t face_mask_;
    int32_t face_hat_;
    int32_t face_glass_;
    int32_t pedestrian_shoecolor_;
    int32_t pedestrian_shoestyle_;
    int32_t pedestrian_sleeve_;
    int32_t pedestrian_hair_;
    int32_t pedestrian_nation_;
    int32_t pedestrian_gender_;
    int32_t pedestrian_lowerstyle_;
    int32_t pedestrian_upperstyle_;
    int32_t pedestrian_age_;
    int32_t pedestrian_lower_;
    int32_t pedestrian_upper_;
    int32_t pedestrian_backpack_;
    int32_t nonmotor_attitude_;
    int32_t nonmotor_gender_;
    int32_t nonmotor_nation_;
    int32_t nonmotor_transportation_;
    int32_t nonmotor_uppercolor_;
    int32_t nonmotor_upper_;
    int32_t nonmotor_transportationcolor_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_perception_2eproto;
};
// -------------------------------------------------------------------

class Targets final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:perception.Targets) */ {
 public:
  inline Targets() : Targets(nullptr) {}
  ~Targets() override;
  explicit PROTOBUF_CONSTEXPR Targets(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Targets(const Targets& from);
  Targets(Targets&& from) noexcept
    : Targets() {
    *this = ::std::move(from);
  }

  inline Targets& operator=(const Targets& from) {
    CopyFrom(from);
    return *this;
  }
  inline Targets& operator=(Targets&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Targets& default_instance() {
    return *internal_default_instance();
  }
  static inline const Targets* internal_default_instance() {
    return reinterpret_cast<const Targets*>(
               &_Targets_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(Targets& a, Targets& b) {
    a.Swap(&b);
  }
  inline void Swap(Targets* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Targets* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Targets* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<Targets>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const Targets& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const Targets& from) {
    Targets::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Targets* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "perception.Targets";
  }
  protected:
  explicit Targets(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kArrayFieldNumber = 4,
    kDeviceSnFieldNumber = 1,
    kDeviceIpFieldNumber = 2,
    kFusionStateFieldNumber = 3,
  };
  // repeated .perception.Target array = 4;
  int array_size() const;
  private:
  int _internal_array_size() const;
  public:
  void clear_array();
  ::perception::Target* mutable_array(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::perception::Target >*
      mutable_array();
  private:
  const ::perception::Target& _internal_array(int index) const;
  ::perception::Target* _internal_add_array();
  public:
  const ::perception::Target& array(int index) const;
  ::perception::Target* add_array();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::perception::Target >&
      array() const;

  // string device_sn = 1;
  void clear_device_sn();
  const std::string& device_sn() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_device_sn(ArgT0&& arg0, ArgT... args);
  std::string* mutable_device_sn();
  PROTOBUF_NODISCARD std::string* release_device_sn();
  void set_allocated_device_sn(std::string* device_sn);
  private:
  const std::string& _internal_device_sn() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_device_sn(const std::string& value);
  std::string* _internal_mutable_device_sn();
  public:

  // string device_ip = 2;
  void clear_device_ip();
  const std::string& device_ip() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_device_ip(ArgT0&& arg0, ArgT... args);
  std::string* mutable_device_ip();
  PROTOBUF_NODISCARD std::string* release_device_ip();
  void set_allocated_device_ip(std::string* device_ip);
  private:
  const std::string& _internal_device_ip() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_device_ip(const std::string& value);
  std::string* _internal_mutable_device_ip();
  public:

  // int32 fusion_state = 3;
  void clear_fusion_state();
  int32_t fusion_state() const;
  void set_fusion_state(int32_t value);
  private:
  int32_t _internal_fusion_state() const;
  void _internal_set_fusion_state(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:perception.Targets)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::perception::Target > array_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr device_sn_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr device_ip_;
    int32_t fusion_state_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_perception_2eproto;
};
// -------------------------------------------------------------------

class AreaInfo final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:perception.AreaInfo) */ {
 public:
  inline AreaInfo() : AreaInfo(nullptr) {}
  ~AreaInfo() override;
  explicit PROTOBUF_CONSTEXPR AreaInfo(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  AreaInfo(const AreaInfo& from);
  AreaInfo(AreaInfo&& from) noexcept
    : AreaInfo() {
    *this = ::std::move(from);
  }

  inline AreaInfo& operator=(const AreaInfo& from) {
    CopyFrom(from);
    return *this;
  }
  inline AreaInfo& operator=(AreaInfo&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const AreaInfo& default_instance() {
    return *internal_default_instance();
  }
  static inline const AreaInfo* internal_default_instance() {
    return reinterpret_cast<const AreaInfo*>(
               &_AreaInfo_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  friend void swap(AreaInfo& a, AreaInfo& b) {
    a.Swap(&b);
  }
  inline void Swap(AreaInfo* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(AreaInfo* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  AreaInfo* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<AreaInfo>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const AreaInfo& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const AreaInfo& from) {
    AreaInfo::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(AreaInfo* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "perception.AreaInfo";
  }
  protected:
  explicit AreaInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kDotFieldNumber = 3,
    kIdFieldNumber = 1,
    kWidthFieldNumber = 2,
  };
  // repeated .perception.Position dot = 3;
  int dot_size() const;
  private:
  int _internal_dot_size() const;
  public:
  void clear_dot();
  ::perception::Position* mutable_dot(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::perception::Position >*
      mutable_dot();
  private:
  const ::perception::Position& _internal_dot(int index) const;
  ::perception::Position* _internal_add_dot();
  public:
  const ::perception::Position& dot(int index) const;
  ::perception::Position* add_dot();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::perception::Position >&
      dot() const;

  // int32 id = 1;
  void clear_id();
  int32_t id() const;
  void set_id(int32_t value);
  private:
  int32_t _internal_id() const;
  void _internal_set_id(int32_t value);
  public:

  // float width = 2;
  void clear_width();
  float width() const;
  void set_width(float value);
  private:
  float _internal_width() const;
  void _internal_set_width(float value);
  public:

  // @@protoc_insertion_point(class_scope:perception.AreaInfo)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::perception::Position > dot_;
    int32_t id_;
    float width_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_perception_2eproto;
};
// -------------------------------------------------------------------

class LaneCount final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:perception.LaneCount) */ {
 public:
  inline LaneCount() : LaneCount(nullptr) {}
  ~LaneCount() override;
  explicit PROTOBUF_CONSTEXPR LaneCount(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  LaneCount(const LaneCount& from);
  LaneCount(LaneCount&& from) noexcept
    : LaneCount() {
    *this = ::std::move(from);
  }

  inline LaneCount& operator=(const LaneCount& from) {
    CopyFrom(from);
    return *this;
  }
  inline LaneCount& operator=(LaneCount&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const LaneCount& default_instance() {
    return *internal_default_instance();
  }
  static inline const LaneCount* internal_default_instance() {
    return reinterpret_cast<const LaneCount*>(
               &_LaneCount_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  friend void swap(LaneCount& a, LaneCount& b) {
    a.Swap(&b);
  }
  inline void Swap(LaneCount* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(LaneCount* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  LaneCount* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<LaneCount>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const LaneCount& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const LaneCount& from) {
    LaneCount::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(LaneCount* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "perception.LaneCount";
  }
  protected:
  explicit LaneCount(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kLaneNoFieldNumber = 1,
    kVolumeFieldNumber = 2,
    kPcuFieldNumber = 3,
    kAvSpeedFieldNumber = 4,
    kOccupancyFieldNumber = 5,
    kHeadWayFieldNumber = 6,
    kGapFieldNumber = 7,
    kAvDelayFieldNumber = 8,
    kAvStopFieldNumber = 9,
    kSpeed85FieldNumber = 10,
    kQueueLengthFieldNumber = 11,
  };
  // int32 laneNo = 1;
  void clear_laneno();
  int32_t laneno() const;
  void set_laneno(int32_t value);
  private:
  int32_t _internal_laneno() const;
  void _internal_set_laneno(int32_t value);
  public:

  // int32 volume = 2;
  void clear_volume();
  int32_t volume() const;
  void set_volume(int32_t value);
  private:
  int32_t _internal_volume() const;
  void _internal_set_volume(int32_t value);
  public:

  // int32 pcu = 3;
  void clear_pcu();
  int32_t pcu() const;
  void set_pcu(int32_t value);
  private:
  int32_t _internal_pcu() const;
  void _internal_set_pcu(int32_t value);
  public:

  // float avSpeed = 4;
  void clear_avspeed();
  float avspeed() const;
  void set_avspeed(float value);
  private:
  float _internal_avspeed() const;
  void _internal_set_avspeed(float value);
  public:

  // float occupancy = 5;
  void clear_occupancy();
  float occupancy() const;
  void set_occupancy(float value);
  private:
  float _internal_occupancy() const;
  void _internal_set_occupancy(float value);
  public:

  // float headWay = 6;
  void clear_headway();
  float headway() const;
  void set_headway(float value);
  private:
  float _internal_headway() const;
  void _internal_set_headway(float value);
  public:

  // float gap = 7;
  void clear_gap();
  float gap() const;
  void set_gap(float value);
  private:
  float _internal_gap() const;
  void _internal_set_gap(float value);
  public:

  // float avDelay = 8;
  void clear_avdelay();
  float avdelay() const;
  void set_avdelay(float value);
  private:
  float _internal_avdelay() const;
  void _internal_set_avdelay(float value);
  public:

  // int32 avStop = 9;
  void clear_avstop();
  int32_t avstop() const;
  void set_avstop(int32_t value);
  private:
  int32_t _internal_avstop() const;
  void _internal_set_avstop(int32_t value);
  public:

  // float speed85 = 10;
  void clear_speed85();
  float speed85() const;
  void set_speed85(float value);
  private:
  float _internal_speed85() const;
  void _internal_set_speed85(float value);
  public:

  // float queueLength = 11;
  void clear_queuelength();
  float queuelength() const;
  void set_queuelength(float value);
  private:
  float _internal_queuelength() const;
  void _internal_set_queuelength(float value);
  public:

  // @@protoc_insertion_point(class_scope:perception.LaneCount)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    int32_t laneno_;
    int32_t volume_;
    int32_t pcu_;
    float avspeed_;
    float occupancy_;
    float headway_;
    float gap_;
    float avdelay_;
    int32_t avstop_;
    float speed85_;
    float queuelength_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_perception_2eproto;
};
// -------------------------------------------------------------------

class RoadCount final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:perception.RoadCount) */ {
 public:
  inline RoadCount() : RoadCount(nullptr) {}
  ~RoadCount() override;
  explicit PROTOBUF_CONSTEXPR RoadCount(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  RoadCount(const RoadCount& from);
  RoadCount(RoadCount&& from) noexcept
    : RoadCount() {
    *this = ::std::move(from);
  }

  inline RoadCount& operator=(const RoadCount& from) {
    CopyFrom(from);
    return *this;
  }
  inline RoadCount& operator=(RoadCount&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const RoadCount& default_instance() {
    return *internal_default_instance();
  }
  static inline const RoadCount* internal_default_instance() {
    return reinterpret_cast<const RoadCount*>(
               &_RoadCount_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    7;

  friend void swap(RoadCount& a, RoadCount& b) {
    a.Swap(&b);
  }
  inline void Swap(RoadCount* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RoadCount* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  RoadCount* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<RoadCount>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const RoadCount& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const RoadCount& from) {
    RoadCount::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RoadCount* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "perception.RoadCount";
  }
  protected:
  explicit RoadCount(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kLaneCountFieldNumber = 15,
    kDeviceIdFieldNumber = 1,
    kRoadNameFieldNumber = 3,
    kRoadInCoordFieldNumber = 4,
    kHeadingFieldNumber = 2,
    kVolumeFieldNumber = 5,
    kPcuFieldNumber = 6,
    kAvSpeedFieldNumber = 7,
    kOccupancyFieldNumber = 8,
    kHeadWayFieldNumber = 9,
    kGapFieldNumber = 10,
    kAvDelayFieldNumber = 11,
    kAvStopFieldNumber = 12,
    kSpeed85FieldNumber = 13,
    kQueueLengthFieldNumber = 14,
  };
  // repeated .perception.LaneCount laneCount = 15;
  int lanecount_size() const;
  private:
  int _internal_lanecount_size() const;
  public:
  void clear_lanecount();
  ::perception::LaneCount* mutable_lanecount(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::perception::LaneCount >*
      mutable_lanecount();
  private:
  const ::perception::LaneCount& _internal_lanecount(int index) const;
  ::perception::LaneCount* _internal_add_lanecount();
  public:
  const ::perception::LaneCount& lanecount(int index) const;
  ::perception::LaneCount* add_lanecount();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::perception::LaneCount >&
      lanecount() const;

  // string deviceId = 1;
  void clear_deviceid();
  const std::string& deviceid() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_deviceid(ArgT0&& arg0, ArgT... args);
  std::string* mutable_deviceid();
  PROTOBUF_NODISCARD std::string* release_deviceid();
  void set_allocated_deviceid(std::string* deviceid);
  private:
  const std::string& _internal_deviceid() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_deviceid(const std::string& value);
  std::string* _internal_mutable_deviceid();
  public:

  // string roadName = 3;
  void clear_roadname();
  const std::string& roadname() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_roadname(ArgT0&& arg0, ArgT... args);
  std::string* mutable_roadname();
  PROTOBUF_NODISCARD std::string* release_roadname();
  void set_allocated_roadname(std::string* roadname);
  private:
  const std::string& _internal_roadname() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_roadname(const std::string& value);
  std::string* _internal_mutable_roadname();
  public:

  // .perception.Position roadInCoord = 4;
  bool has_roadincoord() const;
  private:
  bool _internal_has_roadincoord() const;
  public:
  void clear_roadincoord();
  const ::perception::Position& roadincoord() const;
  PROTOBUF_NODISCARD ::perception::Position* release_roadincoord();
  ::perception::Position* mutable_roadincoord();
  void set_allocated_roadincoord(::perception::Position* roadincoord);
  private:
  const ::perception::Position& _internal_roadincoord() const;
  ::perception::Position* _internal_mutable_roadincoord();
  public:
  void unsafe_arena_set_allocated_roadincoord(
      ::perception::Position* roadincoord);
  ::perception::Position* unsafe_arena_release_roadincoord();

  // float heading = 2;
  void clear_heading();
  float heading() const;
  void set_heading(float value);
  private:
  float _internal_heading() const;
  void _internal_set_heading(float value);
  public:

  // int32 volume = 5;
  void clear_volume();
  int32_t volume() const;
  void set_volume(int32_t value);
  private:
  int32_t _internal_volume() const;
  void _internal_set_volume(int32_t value);
  public:

  // int32 pcu = 6;
  void clear_pcu();
  int32_t pcu() const;
  void set_pcu(int32_t value);
  private:
  int32_t _internal_pcu() const;
  void _internal_set_pcu(int32_t value);
  public:

  // float avSpeed = 7;
  void clear_avspeed();
  float avspeed() const;
  void set_avspeed(float value);
  private:
  float _internal_avspeed() const;
  void _internal_set_avspeed(float value);
  public:

  // float occupancy = 8;
  void clear_occupancy();
  float occupancy() const;
  void set_occupancy(float value);
  private:
  float _internal_occupancy() const;
  void _internal_set_occupancy(float value);
  public:

  // float headWay = 9;
  void clear_headway();
  float headway() const;
  void set_headway(float value);
  private:
  float _internal_headway() const;
  void _internal_set_headway(float value);
  public:

  // float gap = 10;
  void clear_gap();
  float gap() const;
  void set_gap(float value);
  private:
  float _internal_gap() const;
  void _internal_set_gap(float value);
  public:

  // float avDelay = 11;
  void clear_avdelay();
  float avdelay() const;
  void set_avdelay(float value);
  private:
  float _internal_avdelay() const;
  void _internal_set_avdelay(float value);
  public:

  // int32 avStop = 12;
  void clear_avstop();
  int32_t avstop() const;
  void set_avstop(int32_t value);
  private:
  int32_t _internal_avstop() const;
  void _internal_set_avstop(int32_t value);
  public:

  // float speed85 = 13;
  void clear_speed85();
  float speed85() const;
  void set_speed85(float value);
  private:
  float _internal_speed85() const;
  void _internal_set_speed85(float value);
  public:

  // float queueLength = 14;
  void clear_queuelength();
  float queuelength() const;
  void set_queuelength(float value);
  private:
  float _internal_queuelength() const;
  void _internal_set_queuelength(float value);
  public:

  // @@protoc_insertion_point(class_scope:perception.RoadCount)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::perception::LaneCount > lanecount_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr deviceid_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr roadname_;
    ::perception::Position* roadincoord_;
    float heading_;
    int32_t volume_;
    int32_t pcu_;
    float avspeed_;
    float occupancy_;
    float headway_;
    float gap_;
    float avdelay_;
    int32_t avstop_;
    float speed85_;
    float queuelength_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_perception_2eproto;
};
// -------------------------------------------------------------------

class IntersectionCount final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:perception.IntersectionCount) */ {
 public:
  inline IntersectionCount() : IntersectionCount(nullptr) {}
  ~IntersectionCount() override;
  explicit PROTOBUF_CONSTEXPR IntersectionCount(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  IntersectionCount(const IntersectionCount& from);
  IntersectionCount(IntersectionCount&& from) noexcept
    : IntersectionCount() {
    *this = ::std::move(from);
  }

  inline IntersectionCount& operator=(const IntersectionCount& from) {
    CopyFrom(from);
    return *this;
  }
  inline IntersectionCount& operator=(IntersectionCount&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const IntersectionCount& default_instance() {
    return *internal_default_instance();
  }
  static inline const IntersectionCount* internal_default_instance() {
    return reinterpret_cast<const IntersectionCount*>(
               &_IntersectionCount_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    8;

  friend void swap(IntersectionCount& a, IntersectionCount& b) {
    a.Swap(&b);
  }
  inline void Swap(IntersectionCount* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(IntersectionCount* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  IntersectionCount* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<IntersectionCount>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const IntersectionCount& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const IntersectionCount& from) {
    IntersectionCount::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(IntersectionCount* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "perception.IntersectionCount";
  }
  protected:
  explicit IntersectionCount(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kRoadCountFieldNumber = 11,
    kVolumeFieldNumber = 1,
    kPcuFieldNumber = 2,
    kAvSpeedFieldNumber = 3,
    kOccupancyFieldNumber = 4,
    kHeadWayFieldNumber = 5,
    kGapFieldNumber = 6,
    kAvDelayFieldNumber = 7,
    kAvStopFieldNumber = 8,
    kSpeed85FieldNumber = 9,
    kQueueLengthFieldNumber = 10,
  };
  // repeated .perception.RoadCount roadCount = 11;
  int roadcount_size() const;
  private:
  int _internal_roadcount_size() const;
  public:
  void clear_roadcount();
  ::perception::RoadCount* mutable_roadcount(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::perception::RoadCount >*
      mutable_roadcount();
  private:
  const ::perception::RoadCount& _internal_roadcount(int index) const;
  ::perception::RoadCount* _internal_add_roadcount();
  public:
  const ::perception::RoadCount& roadcount(int index) const;
  ::perception::RoadCount* add_roadcount();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::perception::RoadCount >&
      roadcount() const;

  // int32 volume = 1;
  void clear_volume();
  int32_t volume() const;
  void set_volume(int32_t value);
  private:
  int32_t _internal_volume() const;
  void _internal_set_volume(int32_t value);
  public:

  // int32 pcu = 2;
  void clear_pcu();
  int32_t pcu() const;
  void set_pcu(int32_t value);
  private:
  int32_t _internal_pcu() const;
  void _internal_set_pcu(int32_t value);
  public:

  // float avSpeed = 3;
  void clear_avspeed();
  float avspeed() const;
  void set_avspeed(float value);
  private:
  float _internal_avspeed() const;
  void _internal_set_avspeed(float value);
  public:

  // float occupancy = 4;
  void clear_occupancy();
  float occupancy() const;
  void set_occupancy(float value);
  private:
  float _internal_occupancy() const;
  void _internal_set_occupancy(float value);
  public:

  // float headWay = 5;
  void clear_headway();
  float headway() const;
  void set_headway(float value);
  private:
  float _internal_headway() const;
  void _internal_set_headway(float value);
  public:

  // float gap = 6;
  void clear_gap();
  float gap() const;
  void set_gap(float value);
  private:
  float _internal_gap() const;
  void _internal_set_gap(float value);
  public:

  // float avDelay = 7;
  void clear_avdelay();
  float avdelay() const;
  void set_avdelay(float value);
  private:
  float _internal_avdelay() const;
  void _internal_set_avdelay(float value);
  public:

  // int32 avStop = 8;
  void clear_avstop();
  int32_t avstop() const;
  void set_avstop(int32_t value);
  private:
  int32_t _internal_avstop() const;
  void _internal_set_avstop(int32_t value);
  public:

  // float speed85 = 9;
  void clear_speed85();
  float speed85() const;
  void set_speed85(float value);
  private:
  float _internal_speed85() const;
  void _internal_set_speed85(float value);
  public:

  // float queueLength = 10;
  void clear_queuelength();
  float queuelength() const;
  void set_queuelength(float value);
  private:
  float _internal_queuelength() const;
  void _internal_set_queuelength(float value);
  public:

  // @@protoc_insertion_point(class_scope:perception.IntersectionCount)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::perception::RoadCount > roadcount_;
    int32_t volume_;
    int32_t pcu_;
    float avspeed_;
    float occupancy_;
    float headway_;
    float gap_;
    float avdelay_;
    int32_t avstop_;
    float speed85_;
    float queuelength_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_perception_2eproto;
};
// -------------------------------------------------------------------

class Event final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:perception.Event) */ {
 public:
  inline Event() : Event(nullptr) {}
  ~Event() override;
  explicit PROTOBUF_CONSTEXPR Event(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Event(const Event& from);
  Event(Event&& from) noexcept
    : Event() {
    *this = ::std::move(from);
  }

  inline Event& operator=(const Event& from) {
    CopyFrom(from);
    return *this;
  }
  inline Event& operator=(Event&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Event& default_instance() {
    return *internal_default_instance();
  }
  enum DataTypeCase {
    kStringData = 12,
    kIntData = 13,
    DATATYPE_NOT_SET = 0,
  };

  static inline const Event* internal_default_instance() {
    return reinterpret_cast<const Event*>(
               &_Event_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    9;

  friend void swap(Event& a, Event& b) {
    a.Swap(&b);
  }
  inline void Swap(Event* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Event* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Event* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<Event>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const Event& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const Event& from) {
    Event::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Event* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "perception.Event";
  }
  protected:
  explicit Event(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kPosFieldNumber = 7,
    kRefpathFieldNumber = 11,
    kDeviceSnFieldNumber = 3,
    kLicensePlateFieldNumber = 8,
    kEventTypeFieldNumber = 1,
    kLevelFieldNumber = 2,
    kLaneIdFieldNumber = 4,
    kFusionStateFieldNumber = 5,
    kTrackTimeFieldNumber = 9,
    kTtlFieldNumber = 10,
    kSourceFieldNumber = 6,
    kStringDataFieldNumber = 12,
    kIntDataFieldNumber = 13,
  };
  // repeated .perception.Position pos = 7;
  int pos_size() const;
  private:
  int _internal_pos_size() const;
  public:
  void clear_pos();
  ::perception::Position* mutable_pos(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::perception::Position >*
      mutable_pos();
  private:
  const ::perception::Position& _internal_pos(int index) const;
  ::perception::Position* _internal_add_pos();
  public:
  const ::perception::Position& pos(int index) const;
  ::perception::Position* add_pos();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::perception::Position >&
      pos() const;

  // repeated .perception.AreaInfo refpath = 11;
  int refpath_size() const;
  private:
  int _internal_refpath_size() const;
  public:
  void clear_refpath();
  ::perception::AreaInfo* mutable_refpath(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::perception::AreaInfo >*
      mutable_refpath();
  private:
  const ::perception::AreaInfo& _internal_refpath(int index) const;
  ::perception::AreaInfo* _internal_add_refpath();
  public:
  const ::perception::AreaInfo& refpath(int index) const;
  ::perception::AreaInfo* add_refpath();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::perception::AreaInfo >&
      refpath() const;

  // string device_sn = 3;
  void clear_device_sn();
  const std::string& device_sn() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_device_sn(ArgT0&& arg0, ArgT... args);
  std::string* mutable_device_sn();
  PROTOBUF_NODISCARD std::string* release_device_sn();
  void set_allocated_device_sn(std::string* device_sn);
  private:
  const std::string& _internal_device_sn() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_device_sn(const std::string& value);
  std::string* _internal_mutable_device_sn();
  public:

  // string license_plate = 8;
  void clear_license_plate();
  const std::string& license_plate() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_license_plate(ArgT0&& arg0, ArgT... args);
  std::string* mutable_license_plate();
  PROTOBUF_NODISCARD std::string* release_license_plate();
  void set_allocated_license_plate(std::string* license_plate);
  private:
  const std::string& _internal_license_plate() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_license_plate(const std::string& value);
  std::string* _internal_mutable_license_plate();
  public:

  // int32 event_type = 1;
  void clear_event_type();
  int32_t event_type() const;
  void set_event_type(int32_t value);
  private:
  int32_t _internal_event_type() const;
  void _internal_set_event_type(int32_t value);
  public:

  // int32 level = 2;
  void clear_level();
  int32_t level() const;
  void set_level(int32_t value);
  private:
  int32_t _internal_level() const;
  void _internal_set_level(int32_t value);
  public:

  // int32 lane_id = 4;
  void clear_lane_id();
  int32_t lane_id() const;
  void set_lane_id(int32_t value);
  private:
  int32_t _internal_lane_id() const;
  void _internal_set_lane_id(int32_t value);
  public:

  // int32 fusion_state = 5;
  void clear_fusion_state();
  int32_t fusion_state() const;
  void set_fusion_state(int32_t value);
  private:
  int32_t _internal_fusion_state() const;
  void _internal_set_fusion_state(int32_t value);
  public:

  // int64 track_time = 9;
  void clear_track_time();
  int64_t track_time() const;
  void set_track_time(int64_t value);
  private:
  int64_t _internal_track_time() const;
  void _internal_set_track_time(int64_t value);
  public:

  // int64 ttl = 10;
  void clear_ttl();
  int64_t ttl() const;
  void set_ttl(int64_t value);
  private:
  int64_t _internal_ttl() const;
  void _internal_set_ttl(int64_t value);
  public:

  // int32 source = 6;
  void clear_source();
  int32_t source() const;
  void set_source(int32_t value);
  private:
  int32_t _internal_source() const;
  void _internal_set_source(int32_t value);
  public:

  // string string_data = 12;
  bool has_string_data() const;
  private:
  bool _internal_has_string_data() const;
  public:
  void clear_string_data();
  const std::string& string_data() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_string_data(ArgT0&& arg0, ArgT... args);
  std::string* mutable_string_data();
  PROTOBUF_NODISCARD std::string* release_string_data();
  void set_allocated_string_data(std::string* string_data);
  private:
  const std::string& _internal_string_data() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_string_data(const std::string& value);
  std::string* _internal_mutable_string_data();
  public:

  // int32 int_data = 13;
  bool has_int_data() const;
  private:
  bool _internal_has_int_data() const;
  public:
  void clear_int_data();
  int32_t int_data() const;
  void set_int_data(int32_t value);
  private:
  int32_t _internal_int_data() const;
  void _internal_set_int_data(int32_t value);
  public:

  void clear_DataType();
  DataTypeCase DataType_case() const;
  // @@protoc_insertion_point(class_scope:perception.Event)
 private:
  class _Internal;
  void set_has_string_data();
  void set_has_int_data();

  inline bool has_DataType() const;
  inline void clear_has_DataType();

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::perception::Position > pos_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::perception::AreaInfo > refpath_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr device_sn_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr license_plate_;
    int32_t event_type_;
    int32_t level_;
    int32_t lane_id_;
    int32_t fusion_state_;
    int64_t track_time_;
    int64_t ttl_;
    int32_t source_;
    union DataTypeUnion {
      constexpr DataTypeUnion() : _constinit_{} {}
        ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized _constinit_;
      ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr string_data_;
      int32_t int_data_;
    } DataType_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
    uint32_t _oneof_case_[1];

  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_perception_2eproto;
};
// -------------------------------------------------------------------

class Events final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:perception.Events) */ {
 public:
  inline Events() : Events(nullptr) {}
  ~Events() override;
  explicit PROTOBUF_CONSTEXPR Events(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Events(const Events& from);
  Events(Events&& from) noexcept
    : Events() {
    *this = ::std::move(from);
  }

  inline Events& operator=(const Events& from) {
    CopyFrom(from);
    return *this;
  }
  inline Events& operator=(Events&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Events& default_instance() {
    return *internal_default_instance();
  }
  static inline const Events* internal_default_instance() {
    return reinterpret_cast<const Events*>(
               &_Events_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    10;

  friend void swap(Events& a, Events& b) {
    a.Swap(&b);
  }
  inline void Swap(Events* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Events* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Events* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<Events>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const Events& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const Events& from) {
    Events::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Events* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "perception.Events";
  }
  protected:
  explicit Events(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kEventFieldNumber = 1,
  };
  // repeated .perception.Event event = 1;
  int event_size() const;
  private:
  int _internal_event_size() const;
  public:
  void clear_event();
  ::perception::Event* mutable_event(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::perception::Event >*
      mutable_event();
  private:
  const ::perception::Event& _internal_event(int index) const;
  ::perception::Event* _internal_add_event();
  public:
  const ::perception::Event& event(int index) const;
  ::perception::Event* add_event();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::perception::Event >&
      event() const;

  // @@protoc_insertion_point(class_scope:perception.Events)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::perception::Event > event_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_perception_2eproto;
};
// -------------------------------------------------------------------

class HeartBeat final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:perception.HeartBeat) */ {
 public:
  inline HeartBeat() : HeartBeat(nullptr) {}
  ~HeartBeat() override;
  explicit PROTOBUF_CONSTEXPR HeartBeat(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  HeartBeat(const HeartBeat& from);
  HeartBeat(HeartBeat&& from) noexcept
    : HeartBeat() {
    *this = ::std::move(from);
  }

  inline HeartBeat& operator=(const HeartBeat& from) {
    CopyFrom(from);
    return *this;
  }
  inline HeartBeat& operator=(HeartBeat&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const HeartBeat& default_instance() {
    return *internal_default_instance();
  }
  static inline const HeartBeat* internal_default_instance() {
    return reinterpret_cast<const HeartBeat*>(
               &_HeartBeat_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    11;

  friend void swap(HeartBeat& a, HeartBeat& b) {
    a.Swap(&b);
  }
  inline void Swap(HeartBeat* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(HeartBeat* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  HeartBeat* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<HeartBeat>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const HeartBeat& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const HeartBeat& from) {
    HeartBeat::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(HeartBeat* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "perception.HeartBeat";
  }
  protected:
  explicit HeartBeat(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kErrCodeFieldNumber = 4,
    kDeviceSnFieldNumber = 3,
    kTimeFieldNumber = 2,
    kSeqCountFieldNumber = 1,
  };
  // repeated int32 err_code = 4;
  int err_code_size() const;
  private:
  int _internal_err_code_size() const;
  public:
  void clear_err_code();
  private:
  int32_t _internal_err_code(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >&
      _internal_err_code() const;
  void _internal_add_err_code(int32_t value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >*
      _internal_mutable_err_code();
  public:
  int32_t err_code(int index) const;
  void set_err_code(int index, int32_t value);
  void add_err_code(int32_t value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >&
      err_code() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >*
      mutable_err_code();

  // string device_sn = 3;
  void clear_device_sn();
  const std::string& device_sn() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_device_sn(ArgT0&& arg0, ArgT... args);
  std::string* mutable_device_sn();
  PROTOBUF_NODISCARD std::string* release_device_sn();
  void set_allocated_device_sn(std::string* device_sn);
  private:
  const std::string& _internal_device_sn() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_device_sn(const std::string& value);
  std::string* _internal_mutable_device_sn();
  public:

  // int64 time = 2;
  void clear_time();
  int64_t time() const;
  void set_time(int64_t value);
  private:
  int64_t _internal_time() const;
  void _internal_set_time(int64_t value);
  public:

  // int32 seq_count = 1;
  void clear_seq_count();
  int32_t seq_count() const;
  void set_seq_count(int32_t value);
  private:
  int32_t _internal_seq_count() const;
  void _internal_set_seq_count(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:perception.HeartBeat)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t > err_code_;
    mutable std::atomic<int> _err_code_cached_byte_size_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr device_sn_;
    int64_t time_;
    int32_t seq_count_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_perception_2eproto;
};
// -------------------------------------------------------------------

class DeviceInfo final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:perception.DeviceInfo) */ {
 public:
  inline DeviceInfo() : DeviceInfo(nullptr) {}
  ~DeviceInfo() override;
  explicit PROTOBUF_CONSTEXPR DeviceInfo(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  DeviceInfo(const DeviceInfo& from);
  DeviceInfo(DeviceInfo&& from) noexcept
    : DeviceInfo() {
    *this = ::std::move(from);
  }

  inline DeviceInfo& operator=(const DeviceInfo& from) {
    CopyFrom(from);
    return *this;
  }
  inline DeviceInfo& operator=(DeviceInfo&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const DeviceInfo& default_instance() {
    return *internal_default_instance();
  }
  static inline const DeviceInfo* internal_default_instance() {
    return reinterpret_cast<const DeviceInfo*>(
               &_DeviceInfo_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    12;

  friend void swap(DeviceInfo& a, DeviceInfo& b) {
    a.Swap(&b);
  }
  inline void Swap(DeviceInfo* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(DeviceInfo* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  DeviceInfo* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<DeviceInfo>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const DeviceInfo& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const DeviceInfo& from) {
    DeviceInfo::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(DeviceInfo* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "perception.DeviceInfo";
  }
  protected:
  explicit DeviceInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kPerceptualAreaFieldNumber = 5,
    kDeviceSnFieldNumber = 1,
    kDescriptionFieldNumber = 6,
    kLocationFieldNumber = 2,
    kOrientationFieldNumber = 3,
    kTypeFieldNumber = 4,
  };
  // repeated .perception.AreaInfo perceptual_area = 5;
  int perceptual_area_size() const;
  private:
  int _internal_perceptual_area_size() const;
  public:
  void clear_perceptual_area();
  ::perception::AreaInfo* mutable_perceptual_area(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::perception::AreaInfo >*
      mutable_perceptual_area();
  private:
  const ::perception::AreaInfo& _internal_perceptual_area(int index) const;
  ::perception::AreaInfo* _internal_add_perceptual_area();
  public:
  const ::perception::AreaInfo& perceptual_area(int index) const;
  ::perception::AreaInfo* add_perceptual_area();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::perception::AreaInfo >&
      perceptual_area() const;

  // string device_sn = 1;
  void clear_device_sn();
  const std::string& device_sn() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_device_sn(ArgT0&& arg0, ArgT... args);
  std::string* mutable_device_sn();
  PROTOBUF_NODISCARD std::string* release_device_sn();
  void set_allocated_device_sn(std::string* device_sn);
  private:
  const std::string& _internal_device_sn() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_device_sn(const std::string& value);
  std::string* _internal_mutable_device_sn();
  public:

  // string description = 6;
  void clear_description();
  const std::string& description() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_description(ArgT0&& arg0, ArgT... args);
  std::string* mutable_description();
  PROTOBUF_NODISCARD std::string* release_description();
  void set_allocated_description(std::string* description);
  private:
  const std::string& _internal_description() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_description(const std::string& value);
  std::string* _internal_mutable_description();
  public:

  // .perception.Position location = 2;
  bool has_location() const;
  private:
  bool _internal_has_location() const;
  public:
  void clear_location();
  const ::perception::Position& location() const;
  PROTOBUF_NODISCARD ::perception::Position* release_location();
  ::perception::Position* mutable_location();
  void set_allocated_location(::perception::Position* location);
  private:
  const ::perception::Position& _internal_location() const;
  ::perception::Position* _internal_mutable_location();
  public:
  void unsafe_arena_set_allocated_location(
      ::perception::Position* location);
  ::perception::Position* unsafe_arena_release_location();

  // float orientation = 3;
  void clear_orientation();
  float orientation() const;
  void set_orientation(float value);
  private:
  float _internal_orientation() const;
  void _internal_set_orientation(float value);
  public:

  // int32 type = 4;
  void clear_type();
  int32_t type() const;
  void set_type(int32_t value);
  private:
  int32_t _internal_type() const;
  void _internal_set_type(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:perception.DeviceInfo)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::perception::AreaInfo > perceptual_area_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr device_sn_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr description_;
    ::perception::Position* location_;
    float orientation_;
    int32_t type_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_perception_2eproto;
};
// -------------------------------------------------------------------

class RoadCounts final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:perception.RoadCounts) */ {
 public:
  inline RoadCounts() : RoadCounts(nullptr) {}
  ~RoadCounts() override;
  explicit PROTOBUF_CONSTEXPR RoadCounts(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  RoadCounts(const RoadCounts& from);
  RoadCounts(RoadCounts&& from) noexcept
    : RoadCounts() {
    *this = ::std::move(from);
  }

  inline RoadCounts& operator=(const RoadCounts& from) {
    CopyFrom(from);
    return *this;
  }
  inline RoadCounts& operator=(RoadCounts&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const RoadCounts& default_instance() {
    return *internal_default_instance();
  }
  static inline const RoadCounts* internal_default_instance() {
    return reinterpret_cast<const RoadCounts*>(
               &_RoadCounts_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    13;

  friend void swap(RoadCounts& a, RoadCounts& b) {
    a.Swap(&b);
  }
  inline void Swap(RoadCounts* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RoadCounts* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  RoadCounts* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<RoadCounts>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const RoadCounts& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const RoadCounts& from) {
    RoadCounts::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RoadCounts* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "perception.RoadCounts";
  }
  protected:
  explicit RoadCounts(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kRsuIdFieldNumber = 1,
    kSeqNumFieldNumber = 2,
    kRsuEsnFieldNumber = 3,
    kProtocolVersionFieldNumber = 4,
    kIntersectionCountFieldNumber = 6,
    kCycleFieldNumber = 5,
  };
  // string rsuId = 1;
  void clear_rsuid();
  const std::string& rsuid() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_rsuid(ArgT0&& arg0, ArgT... args);
  std::string* mutable_rsuid();
  PROTOBUF_NODISCARD std::string* release_rsuid();
  void set_allocated_rsuid(std::string* rsuid);
  private:
  const std::string& _internal_rsuid() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_rsuid(const std::string& value);
  std::string* _internal_mutable_rsuid();
  public:

  // string seqNum = 2;
  void clear_seqnum();
  const std::string& seqnum() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_seqnum(ArgT0&& arg0, ArgT... args);
  std::string* mutable_seqnum();
  PROTOBUF_NODISCARD std::string* release_seqnum();
  void set_allocated_seqnum(std::string* seqnum);
  private:
  const std::string& _internal_seqnum() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_seqnum(const std::string& value);
  std::string* _internal_mutable_seqnum();
  public:

  // string rsuEsn = 3;
  void clear_rsuesn();
  const std::string& rsuesn() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_rsuesn(ArgT0&& arg0, ArgT... args);
  std::string* mutable_rsuesn();
  PROTOBUF_NODISCARD std::string* release_rsuesn();
  void set_allocated_rsuesn(std::string* rsuesn);
  private:
  const std::string& _internal_rsuesn() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_rsuesn(const std::string& value);
  std::string* _internal_mutable_rsuesn();
  public:

  // string protocolVersion = 4;
  void clear_protocolversion();
  const std::string& protocolversion() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_protocolversion(ArgT0&& arg0, ArgT... args);
  std::string* mutable_protocolversion();
  PROTOBUF_NODISCARD std::string* release_protocolversion();
  void set_allocated_protocolversion(std::string* protocolversion);
  private:
  const std::string& _internal_protocolversion() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_protocolversion(const std::string& value);
  std::string* _internal_mutable_protocolversion();
  public:

  // .perception.IntersectionCount intersectionCount = 6;
  bool has_intersectioncount() const;
  private:
  bool _internal_has_intersectioncount() const;
  public:
  void clear_intersectioncount();
  const ::perception::IntersectionCount& intersectioncount() const;
  PROTOBUF_NODISCARD ::perception::IntersectionCount* release_intersectioncount();
  ::perception::IntersectionCount* mutable_intersectioncount();
  void set_allocated_intersectioncount(::perception::IntersectionCount* intersectioncount);
  private:
  const ::perception::IntersectionCount& _internal_intersectioncount() const;
  ::perception::IntersectionCount* _internal_mutable_intersectioncount();
  public:
  void unsafe_arena_set_allocated_intersectioncount(
      ::perception::IntersectionCount* intersectioncount);
  ::perception::IntersectionCount* unsafe_arena_release_intersectioncount();

  // int32 cycle = 5;
  void clear_cycle();
  int32_t cycle() const;
  void set_cycle(int32_t value);
  private:
  int32_t _internal_cycle() const;
  void _internal_set_cycle(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:perception.RoadCounts)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr rsuid_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr seqnum_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr rsuesn_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr protocolversion_;
    ::perception::IntersectionCount* intersectioncount_;
    int32_t cycle_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_perception_2eproto;
};
// -------------------------------------------------------------------

class LaneCountInfo final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:perception.LaneCountInfo) */ {
 public:
  inline LaneCountInfo() : LaneCountInfo(nullptr) {}
  ~LaneCountInfo() override;
  explicit PROTOBUF_CONSTEXPR LaneCountInfo(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  LaneCountInfo(const LaneCountInfo& from);
  LaneCountInfo(LaneCountInfo&& from) noexcept
    : LaneCountInfo() {
    *this = ::std::move(from);
  }

  inline LaneCountInfo& operator=(const LaneCountInfo& from) {
    CopyFrom(from);
    return *this;
  }
  inline LaneCountInfo& operator=(LaneCountInfo&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const LaneCountInfo& default_instance() {
    return *internal_default_instance();
  }
  static inline const LaneCountInfo* internal_default_instance() {
    return reinterpret_cast<const LaneCountInfo*>(
               &_LaneCountInfo_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    14;

  friend void swap(LaneCountInfo& a, LaneCountInfo& b) {
    a.Swap(&b);
  }
  inline void Swap(LaneCountInfo* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(LaneCountInfo* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  LaneCountInfo* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<LaneCountInfo>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const LaneCountInfo& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const LaneCountInfo& from) {
    LaneCountInfo::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(LaneCountInfo* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "perception.LaneCountInfo";
  }
  protected:
  explicit LaneCountInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kLaneNoFieldNumber = 1,
    kVolume1FieldNumber = 2,
    kVolume2FieldNumber = 3,
    kVolume3FieldNumber = 4,
    kVolume4FieldNumber = 5,
    kVolume5FieldNumber = 6,
  };
  // int32 laneNo = 1;
  void clear_laneno();
  int32_t laneno() const;
  void set_laneno(int32_t value);
  private:
  int32_t _internal_laneno() const;
  void _internal_set_laneno(int32_t value);
  public:

  // int32 volume1 = 2;
  void clear_volume1();
  int32_t volume1() const;
  void set_volume1(int32_t value);
  private:
  int32_t _internal_volume1() const;
  void _internal_set_volume1(int32_t value);
  public:

  // int32 volume2 = 3;
  void clear_volume2();
  int32_t volume2() const;
  void set_volume2(int32_t value);
  private:
  int32_t _internal_volume2() const;
  void _internal_set_volume2(int32_t value);
  public:

  // int32 volume3 = 4;
  void clear_volume3();
  int32_t volume3() const;
  void set_volume3(int32_t value);
  private:
  int32_t _internal_volume3() const;
  void _internal_set_volume3(int32_t value);
  public:

  // int32 volume4 = 5;
  void clear_volume4();
  int32_t volume4() const;
  void set_volume4(int32_t value);
  private:
  int32_t _internal_volume4() const;
  void _internal_set_volume4(int32_t value);
  public:

  // int32 volume5 = 6;
  void clear_volume5();
  int32_t volume5() const;
  void set_volume5(int32_t value);
  private:
  int32_t _internal_volume5() const;
  void _internal_set_volume5(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:perception.LaneCountInfo)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    int32_t laneno_;
    int32_t volume1_;
    int32_t volume2_;
    int32_t volume3_;
    int32_t volume4_;
    int32_t volume5_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_perception_2eproto;
};
// -------------------------------------------------------------------

class RoadCountInfo final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:perception.RoadCountInfo) */ {
 public:
  inline RoadCountInfo() : RoadCountInfo(nullptr) {}
  ~RoadCountInfo() override;
  explicit PROTOBUF_CONSTEXPR RoadCountInfo(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  RoadCountInfo(const RoadCountInfo& from);
  RoadCountInfo(RoadCountInfo&& from) noexcept
    : RoadCountInfo() {
    *this = ::std::move(from);
  }

  inline RoadCountInfo& operator=(const RoadCountInfo& from) {
    CopyFrom(from);
    return *this;
  }
  inline RoadCountInfo& operator=(RoadCountInfo&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const RoadCountInfo& default_instance() {
    return *internal_default_instance();
  }
  static inline const RoadCountInfo* internal_default_instance() {
    return reinterpret_cast<const RoadCountInfo*>(
               &_RoadCountInfo_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    15;

  friend void swap(RoadCountInfo& a, RoadCountInfo& b) {
    a.Swap(&b);
  }
  inline void Swap(RoadCountInfo* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RoadCountInfo* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  RoadCountInfo* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<RoadCountInfo>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const RoadCountInfo& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const RoadCountInfo& from) {
    RoadCountInfo::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RoadCountInfo* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "perception.RoadCountInfo";
  }
  protected:
  explicit RoadCountInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kLaneCountFieldNumber = 10,
    kDeviceIdFieldNumber = 1,
    kRoadNameFieldNumber = 3,
    kRoadInCoordFieldNumber = 4,
    kHeadingFieldNumber = 2,
    kVolume1FieldNumber = 5,
    kVolume2FieldNumber = 6,
    kVolume3FieldNumber = 7,
    kVolume4FieldNumber = 8,
    kVolume5FieldNumber = 9,
  };
  // repeated .perception.LaneCountInfo laneCount = 10;
  int lanecount_size() const;
  private:
  int _internal_lanecount_size() const;
  public:
  void clear_lanecount();
  ::perception::LaneCountInfo* mutable_lanecount(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::perception::LaneCountInfo >*
      mutable_lanecount();
  private:
  const ::perception::LaneCountInfo& _internal_lanecount(int index) const;
  ::perception::LaneCountInfo* _internal_add_lanecount();
  public:
  const ::perception::LaneCountInfo& lanecount(int index) const;
  ::perception::LaneCountInfo* add_lanecount();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::perception::LaneCountInfo >&
      lanecount() const;

  // string deviceId = 1;
  void clear_deviceid();
  const std::string& deviceid() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_deviceid(ArgT0&& arg0, ArgT... args);
  std::string* mutable_deviceid();
  PROTOBUF_NODISCARD std::string* release_deviceid();
  void set_allocated_deviceid(std::string* deviceid);
  private:
  const std::string& _internal_deviceid() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_deviceid(const std::string& value);
  std::string* _internal_mutable_deviceid();
  public:

  // string roadName = 3;
  void clear_roadname();
  const std::string& roadname() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_roadname(ArgT0&& arg0, ArgT... args);
  std::string* mutable_roadname();
  PROTOBUF_NODISCARD std::string* release_roadname();
  void set_allocated_roadname(std::string* roadname);
  private:
  const std::string& _internal_roadname() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_roadname(const std::string& value);
  std::string* _internal_mutable_roadname();
  public:

  // .perception.Position roadInCoord = 4;
  bool has_roadincoord() const;
  private:
  bool _internal_has_roadincoord() const;
  public:
  void clear_roadincoord();
  const ::perception::Position& roadincoord() const;
  PROTOBUF_NODISCARD ::perception::Position* release_roadincoord();
  ::perception::Position* mutable_roadincoord();
  void set_allocated_roadincoord(::perception::Position* roadincoord);
  private:
  const ::perception::Position& _internal_roadincoord() const;
  ::perception::Position* _internal_mutable_roadincoord();
  public:
  void unsafe_arena_set_allocated_roadincoord(
      ::perception::Position* roadincoord);
  ::perception::Position* unsafe_arena_release_roadincoord();

  // float heading = 2;
  void clear_heading();
  float heading() const;
  void set_heading(float value);
  private:
  float _internal_heading() const;
  void _internal_set_heading(float value);
  public:

  // int32 volume1 = 5;
  void clear_volume1();
  int32_t volume1() const;
  void set_volume1(int32_t value);
  private:
  int32_t _internal_volume1() const;
  void _internal_set_volume1(int32_t value);
  public:

  // int32 volume2 = 6;
  void clear_volume2();
  int32_t volume2() const;
  void set_volume2(int32_t value);
  private:
  int32_t _internal_volume2() const;
  void _internal_set_volume2(int32_t value);
  public:

  // int32 volume3 = 7;
  void clear_volume3();
  int32_t volume3() const;
  void set_volume3(int32_t value);
  private:
  int32_t _internal_volume3() const;
  void _internal_set_volume3(int32_t value);
  public:

  // int32 volume4 = 8;
  void clear_volume4();
  int32_t volume4() const;
  void set_volume4(int32_t value);
  private:
  int32_t _internal_volume4() const;
  void _internal_set_volume4(int32_t value);
  public:

  // int32 volume5 = 9;
  void clear_volume5();
  int32_t volume5() const;
  void set_volume5(int32_t value);
  private:
  int32_t _internal_volume5() const;
  void _internal_set_volume5(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:perception.RoadCountInfo)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::perception::LaneCountInfo > lanecount_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr deviceid_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr roadname_;
    ::perception::Position* roadincoord_;
    float heading_;
    int32_t volume1_;
    int32_t volume2_;
    int32_t volume3_;
    int32_t volume4_;
    int32_t volume5_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_perception_2eproto;
};
// -------------------------------------------------------------------

class IntersectionInfo final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:perception.IntersectionInfo) */ {
 public:
  inline IntersectionInfo() : IntersectionInfo(nullptr) {}
  ~IntersectionInfo() override;
  explicit PROTOBUF_CONSTEXPR IntersectionInfo(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  IntersectionInfo(const IntersectionInfo& from);
  IntersectionInfo(IntersectionInfo&& from) noexcept
    : IntersectionInfo() {
    *this = ::std::move(from);
  }

  inline IntersectionInfo& operator=(const IntersectionInfo& from) {
    CopyFrom(from);
    return *this;
  }
  inline IntersectionInfo& operator=(IntersectionInfo&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const IntersectionInfo& default_instance() {
    return *internal_default_instance();
  }
  static inline const IntersectionInfo* internal_default_instance() {
    return reinterpret_cast<const IntersectionInfo*>(
               &_IntersectionInfo_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    16;

  friend void swap(IntersectionInfo& a, IntersectionInfo& b) {
    a.Swap(&b);
  }
  inline void Swap(IntersectionInfo* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(IntersectionInfo* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  IntersectionInfo* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<IntersectionInfo>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const IntersectionInfo& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const IntersectionInfo& from) {
    IntersectionInfo::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(IntersectionInfo* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "perception.IntersectionInfo";
  }
  protected:
  explicit IntersectionInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kRoadCountFieldNumber = 6,
    kVolume1FieldNumber = 1,
    kVolume2FieldNumber = 2,
    kVolume3FieldNumber = 3,
    kVolume4FieldNumber = 4,
    kVolume5FieldNumber = 5,
  };
  // repeated .perception.RoadCountInfo roadCount = 6;
  int roadcount_size() const;
  private:
  int _internal_roadcount_size() const;
  public:
  void clear_roadcount();
  ::perception::RoadCountInfo* mutable_roadcount(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::perception::RoadCountInfo >*
      mutable_roadcount();
  private:
  const ::perception::RoadCountInfo& _internal_roadcount(int index) const;
  ::perception::RoadCountInfo* _internal_add_roadcount();
  public:
  const ::perception::RoadCountInfo& roadcount(int index) const;
  ::perception::RoadCountInfo* add_roadcount();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::perception::RoadCountInfo >&
      roadcount() const;

  // int32 volume1 = 1;
  void clear_volume1();
  int32_t volume1() const;
  void set_volume1(int32_t value);
  private:
  int32_t _internal_volume1() const;
  void _internal_set_volume1(int32_t value);
  public:

  // int32 volume2 = 2;
  void clear_volume2();
  int32_t volume2() const;
  void set_volume2(int32_t value);
  private:
  int32_t _internal_volume2() const;
  void _internal_set_volume2(int32_t value);
  public:

  // int32 volume3 = 3;
  void clear_volume3();
  int32_t volume3() const;
  void set_volume3(int32_t value);
  private:
  int32_t _internal_volume3() const;
  void _internal_set_volume3(int32_t value);
  public:

  // int32 volume4 = 4;
  void clear_volume4();
  int32_t volume4() const;
  void set_volume4(int32_t value);
  private:
  int32_t _internal_volume4() const;
  void _internal_set_volume4(int32_t value);
  public:

  // int32 volume5 = 5;
  void clear_volume5();
  int32_t volume5() const;
  void set_volume5(int32_t value);
  private:
  int32_t _internal_volume5() const;
  void _internal_set_volume5(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:perception.IntersectionInfo)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::perception::RoadCountInfo > roadcount_;
    int32_t volume1_;
    int32_t volume2_;
    int32_t volume3_;
    int32_t volume4_;
    int32_t volume5_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_perception_2eproto;
};
// -------------------------------------------------------------------

class RoadInfo final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:perception.RoadInfo) */ {
 public:
  inline RoadInfo() : RoadInfo(nullptr) {}
  ~RoadInfo() override;
  explicit PROTOBUF_CONSTEXPR RoadInfo(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  RoadInfo(const RoadInfo& from);
  RoadInfo(RoadInfo&& from) noexcept
    : RoadInfo() {
    *this = ::std::move(from);
  }

  inline RoadInfo& operator=(const RoadInfo& from) {
    CopyFrom(from);
    return *this;
  }
  inline RoadInfo& operator=(RoadInfo&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const RoadInfo& default_instance() {
    return *internal_default_instance();
  }
  static inline const RoadInfo* internal_default_instance() {
    return reinterpret_cast<const RoadInfo*>(
               &_RoadInfo_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    17;

  friend void swap(RoadInfo& a, RoadInfo& b) {
    a.Swap(&b);
  }
  inline void Swap(RoadInfo* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RoadInfo* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  RoadInfo* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<RoadInfo>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const RoadInfo& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const RoadInfo& from) {
    RoadInfo::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RoadInfo* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "perception.RoadInfo";
  }
  protected:
  explicit RoadInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kRsuIdFieldNumber = 1,
    kSeqNumFieldNumber = 2,
    kRsuEsnFieldNumber = 3,
    kProtocolVersionFieldNumber = 4,
    kIntersectionInfoFieldNumber = 6,
    kCycleFieldNumber = 5,
  };
  // string rsuId = 1;
  void clear_rsuid();
  const std::string& rsuid() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_rsuid(ArgT0&& arg0, ArgT... args);
  std::string* mutable_rsuid();
  PROTOBUF_NODISCARD std::string* release_rsuid();
  void set_allocated_rsuid(std::string* rsuid);
  private:
  const std::string& _internal_rsuid() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_rsuid(const std::string& value);
  std::string* _internal_mutable_rsuid();
  public:

  // string seqNum = 2;
  void clear_seqnum();
  const std::string& seqnum() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_seqnum(ArgT0&& arg0, ArgT... args);
  std::string* mutable_seqnum();
  PROTOBUF_NODISCARD std::string* release_seqnum();
  void set_allocated_seqnum(std::string* seqnum);
  private:
  const std::string& _internal_seqnum() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_seqnum(const std::string& value);
  std::string* _internal_mutable_seqnum();
  public:

  // string rsuEsn = 3;
  void clear_rsuesn();
  const std::string& rsuesn() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_rsuesn(ArgT0&& arg0, ArgT... args);
  std::string* mutable_rsuesn();
  PROTOBUF_NODISCARD std::string* release_rsuesn();
  void set_allocated_rsuesn(std::string* rsuesn);
  private:
  const std::string& _internal_rsuesn() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_rsuesn(const std::string& value);
  std::string* _internal_mutable_rsuesn();
  public:

  // string protocolVersion = 4;
  void clear_protocolversion();
  const std::string& protocolversion() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_protocolversion(ArgT0&& arg0, ArgT... args);
  std::string* mutable_protocolversion();
  PROTOBUF_NODISCARD std::string* release_protocolversion();
  void set_allocated_protocolversion(std::string* protocolversion);
  private:
  const std::string& _internal_protocolversion() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_protocolversion(const std::string& value);
  std::string* _internal_mutable_protocolversion();
  public:

  // .perception.IntersectionInfo intersectionInfo = 6;
  bool has_intersectioninfo() const;
  private:
  bool _internal_has_intersectioninfo() const;
  public:
  void clear_intersectioninfo();
  const ::perception::IntersectionInfo& intersectioninfo() const;
  PROTOBUF_NODISCARD ::perception::IntersectionInfo* release_intersectioninfo();
  ::perception::IntersectionInfo* mutable_intersectioninfo();
  void set_allocated_intersectioninfo(::perception::IntersectionInfo* intersectioninfo);
  private:
  const ::perception::IntersectionInfo& _internal_intersectioninfo() const;
  ::perception::IntersectionInfo* _internal_mutable_intersectioninfo();
  public:
  void unsafe_arena_set_allocated_intersectioninfo(
      ::perception::IntersectionInfo* intersectioninfo);
  ::perception::IntersectionInfo* unsafe_arena_release_intersectioninfo();

  // int32 cycle = 5;
  void clear_cycle();
  int32_t cycle() const;
  void set_cycle(int32_t value);
  private:
  int32_t _internal_cycle() const;
  void _internal_set_cycle(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:perception.RoadInfo)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr rsuid_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr seqnum_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr rsuesn_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr protocolversion_;
    ::perception::IntersectionInfo* intersectioninfo_;
    int32_t cycle_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_perception_2eproto;
};
// -------------------------------------------------------------------

class PerceptionMsg final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:perception.PerceptionMsg) */ {
 public:
  inline PerceptionMsg() : PerceptionMsg(nullptr) {}
  ~PerceptionMsg() override;
  explicit PROTOBUF_CONSTEXPR PerceptionMsg(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  PerceptionMsg(const PerceptionMsg& from);
  PerceptionMsg(PerceptionMsg&& from) noexcept
    : PerceptionMsg() {
    *this = ::std::move(from);
  }

  inline PerceptionMsg& operator=(const PerceptionMsg& from) {
    CopyFrom(from);
    return *this;
  }
  inline PerceptionMsg& operator=(PerceptionMsg&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const PerceptionMsg& default_instance() {
    return *internal_default_instance();
  }
  enum MsgTypeCase {
    kTargetMsg = 2,
    kEventMsg = 3,
    kHeartBeatMsg = 4,
    kDevInfo = 5,
    kRoadCount = 6,
    kRoadInfo = 7,
    MSGTYPE_NOT_SET = 0,
  };

  static inline const PerceptionMsg* internal_default_instance() {
    return reinterpret_cast<const PerceptionMsg*>(
               &_PerceptionMsg_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    18;

  friend void swap(PerceptionMsg& a, PerceptionMsg& b) {
    a.Swap(&b);
  }
  inline void Swap(PerceptionMsg* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(PerceptionMsg* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  PerceptionMsg* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<PerceptionMsg>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const PerceptionMsg& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const PerceptionMsg& from) {
    PerceptionMsg::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(PerceptionMsg* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "perception.PerceptionMsg";
  }
  protected:
  explicit PerceptionMsg(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTimeFieldNumber = 1,
    kTargetMsgFieldNumber = 2,
    kEventMsgFieldNumber = 3,
    kHeartBeatMsgFieldNumber = 4,
    kDevInfoFieldNumber = 5,
    kRoadCountFieldNumber = 6,
    kRoadInfoFieldNumber = 7,
  };
  // int64 time = 1;
  void clear_time();
  int64_t time() const;
  void set_time(int64_t value);
  private:
  int64_t _internal_time() const;
  void _internal_set_time(int64_t value);
  public:

  // .perception.Targets target_msg = 2;
  bool has_target_msg() const;
  private:
  bool _internal_has_target_msg() const;
  public:
  void clear_target_msg();
  const ::perception::Targets& target_msg() const;
  PROTOBUF_NODISCARD ::perception::Targets* release_target_msg();
  ::perception::Targets* mutable_target_msg();
  void set_allocated_target_msg(::perception::Targets* target_msg);
  private:
  const ::perception::Targets& _internal_target_msg() const;
  ::perception::Targets* _internal_mutable_target_msg();
  public:
  void unsafe_arena_set_allocated_target_msg(
      ::perception::Targets* target_msg);
  ::perception::Targets* unsafe_arena_release_target_msg();

  // .perception.Events event_msg = 3;
  bool has_event_msg() const;
  private:
  bool _internal_has_event_msg() const;
  public:
  void clear_event_msg();
  const ::perception::Events& event_msg() const;
  PROTOBUF_NODISCARD ::perception::Events* release_event_msg();
  ::perception::Events* mutable_event_msg();
  void set_allocated_event_msg(::perception::Events* event_msg);
  private:
  const ::perception::Events& _internal_event_msg() const;
  ::perception::Events* _internal_mutable_event_msg();
  public:
  void unsafe_arena_set_allocated_event_msg(
      ::perception::Events* event_msg);
  ::perception::Events* unsafe_arena_release_event_msg();

  // .perception.HeartBeat heart_beat_msg = 4;
  bool has_heart_beat_msg() const;
  private:
  bool _internal_has_heart_beat_msg() const;
  public:
  void clear_heart_beat_msg();
  const ::perception::HeartBeat& heart_beat_msg() const;
  PROTOBUF_NODISCARD ::perception::HeartBeat* release_heart_beat_msg();
  ::perception::HeartBeat* mutable_heart_beat_msg();
  void set_allocated_heart_beat_msg(::perception::HeartBeat* heart_beat_msg);
  private:
  const ::perception::HeartBeat& _internal_heart_beat_msg() const;
  ::perception::HeartBeat* _internal_mutable_heart_beat_msg();
  public:
  void unsafe_arena_set_allocated_heart_beat_msg(
      ::perception::HeartBeat* heart_beat_msg);
  ::perception::HeartBeat* unsafe_arena_release_heart_beat_msg();

  // .perception.DeviceInfo dev_info = 5;
  bool has_dev_info() const;
  private:
  bool _internal_has_dev_info() const;
  public:
  void clear_dev_info();
  const ::perception::DeviceInfo& dev_info() const;
  PROTOBUF_NODISCARD ::perception::DeviceInfo* release_dev_info();
  ::perception::DeviceInfo* mutable_dev_info();
  void set_allocated_dev_info(::perception::DeviceInfo* dev_info);
  private:
  const ::perception::DeviceInfo& _internal_dev_info() const;
  ::perception::DeviceInfo* _internal_mutable_dev_info();
  public:
  void unsafe_arena_set_allocated_dev_info(
      ::perception::DeviceInfo* dev_info);
  ::perception::DeviceInfo* unsafe_arena_release_dev_info();

  // .perception.RoadCounts road_count = 6;
  bool has_road_count() const;
  private:
  bool _internal_has_road_count() const;
  public:
  void clear_road_count();
  const ::perception::RoadCounts& road_count() const;
  PROTOBUF_NODISCARD ::perception::RoadCounts* release_road_count();
  ::perception::RoadCounts* mutable_road_count();
  void set_allocated_road_count(::perception::RoadCounts* road_count);
  private:
  const ::perception::RoadCounts& _internal_road_count() const;
  ::perception::RoadCounts* _internal_mutable_road_count();
  public:
  void unsafe_arena_set_allocated_road_count(
      ::perception::RoadCounts* road_count);
  ::perception::RoadCounts* unsafe_arena_release_road_count();

  // .perception.RoadInfo road_info = 7;
  bool has_road_info() const;
  private:
  bool _internal_has_road_info() const;
  public:
  void clear_road_info();
  const ::perception::RoadInfo& road_info() const;
  PROTOBUF_NODISCARD ::perception::RoadInfo* release_road_info();
  ::perception::RoadInfo* mutable_road_info();
  void set_allocated_road_info(::perception::RoadInfo* road_info);
  private:
  const ::perception::RoadInfo& _internal_road_info() const;
  ::perception::RoadInfo* _internal_mutable_road_info();
  public:
  void unsafe_arena_set_allocated_road_info(
      ::perception::RoadInfo* road_info);
  ::perception::RoadInfo* unsafe_arena_release_road_info();

  void clear_MsgType();
  MsgTypeCase MsgType_case() const;
  // @@protoc_insertion_point(class_scope:perception.PerceptionMsg)
 private:
  class _Internal;
  void set_has_target_msg();
  void set_has_event_msg();
  void set_has_heart_beat_msg();
  void set_has_dev_info();
  void set_has_road_count();
  void set_has_road_info();

  inline bool has_MsgType() const;
  inline void clear_has_MsgType();

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    int64_t time_;
    union MsgTypeUnion {
      constexpr MsgTypeUnion() : _constinit_{} {}
        ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized _constinit_;
      ::perception::Targets* target_msg_;
      ::perception::Events* event_msg_;
      ::perception::HeartBeat* heart_beat_msg_;
      ::perception::DeviceInfo* dev_info_;
      ::perception::RoadCounts* road_count_;
      ::perception::RoadInfo* road_info_;
    } MsgType_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
    uint32_t _oneof_case_[1];

  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_perception_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// Position

// int64 lat = 1;
inline void Position::clear_lat() {
  _impl_.lat_ = int64_t{0};
}
inline int64_t Position::_internal_lat() const {
  return _impl_.lat_;
}
inline int64_t Position::lat() const {
  // @@protoc_insertion_point(field_get:perception.Position.lat)
  return _internal_lat();
}
inline void Position::_internal_set_lat(int64_t value) {
  
  _impl_.lat_ = value;
}
inline void Position::set_lat(int64_t value) {
  _internal_set_lat(value);
  // @@protoc_insertion_point(field_set:perception.Position.lat)
}

// int64 lon = 2;
inline void Position::clear_lon() {
  _impl_.lon_ = int64_t{0};
}
inline int64_t Position::_internal_lon() const {
  return _impl_.lon_;
}
inline int64_t Position::lon() const {
  // @@protoc_insertion_point(field_get:perception.Position.lon)
  return _internal_lon();
}
inline void Position::_internal_set_lon(int64_t value) {
  
  _impl_.lon_ = value;
}
inline void Position::set_lon(int64_t value) {
  _internal_set_lon(value);
  // @@protoc_insertion_point(field_set:perception.Position.lon)
}

// int32 elev = 3;
inline void Position::clear_elev() {
  _impl_.elev_ = 0;
}
inline int32_t Position::_internal_elev() const {
  return _impl_.elev_;
}
inline int32_t Position::elev() const {
  // @@protoc_insertion_point(field_get:perception.Position.elev)
  return _internal_elev();
}
inline void Position::_internal_set_elev(int32_t value) {
  
  _impl_.elev_ = value;
}
inline void Position::set_elev(int32_t value) {
  _internal_set_elev(value);
  // @@protoc_insertion_point(field_set:perception.Position.elev)
}

// -------------------------------------------------------------------

// Size

// int32 length = 1;
inline void Size::clear_length() {
  _impl_.length_ = 0;
}
inline int32_t Size::_internal_length() const {
  return _impl_.length_;
}
inline int32_t Size::length() const {
  // @@protoc_insertion_point(field_get:perception.Size.length)
  return _internal_length();
}
inline void Size::_internal_set_length(int32_t value) {
  
  _impl_.length_ = value;
}
inline void Size::set_length(int32_t value) {
  _internal_set_length(value);
  // @@protoc_insertion_point(field_set:perception.Size.length)
}

// int32 width = 2;
inline void Size::clear_width() {
  _impl_.width_ = 0;
}
inline int32_t Size::_internal_width() const {
  return _impl_.width_;
}
inline int32_t Size::width() const {
  // @@protoc_insertion_point(field_get:perception.Size.width)
  return _internal_width();
}
inline void Size::_internal_set_width(int32_t value) {
  
  _impl_.width_ = value;
}
inline void Size::set_width(int32_t value) {
  _internal_set_width(value);
  // @@protoc_insertion_point(field_set:perception.Size.width)
}

// int32 height = 3;
inline void Size::clear_height() {
  _impl_.height_ = 0;
}
inline int32_t Size::_internal_height() const {
  return _impl_.height_;
}
inline int32_t Size::height() const {
  // @@protoc_insertion_point(field_get:perception.Size.height)
  return _internal_height();
}
inline void Size::_internal_set_height(int32_t value) {
  
  _impl_.height_ = value;
}
inline void Size::set_height(int32_t value) {
  _internal_set_height(value);
  // @@protoc_insertion_point(field_set:perception.Size.height)
}

// -------------------------------------------------------------------

// LaneInfo

// int32 area_type = 1;
inline void LaneInfo::clear_area_type() {
  _impl_.area_type_ = 0;
}
inline int32_t LaneInfo::_internal_area_type() const {
  return _impl_.area_type_;
}
inline int32_t LaneInfo::area_type() const {
  // @@protoc_insertion_point(field_get:perception.LaneInfo.area_type)
  return _internal_area_type();
}
inline void LaneInfo::_internal_set_area_type(int32_t value) {
  
  _impl_.area_type_ = value;
}
inline void LaneInfo::set_area_type(int32_t value) {
  _internal_set_area_type(value);
  // @@protoc_insertion_point(field_set:perception.LaneInfo.area_type)
}

// int32 lane_count = 2;
inline void LaneInfo::clear_lane_count() {
  _impl_.lane_count_ = 0;
}
inline int32_t LaneInfo::_internal_lane_count() const {
  return _impl_.lane_count_;
}
inline int32_t LaneInfo::lane_count() const {
  // @@protoc_insertion_point(field_get:perception.LaneInfo.lane_count)
  return _internal_lane_count();
}
inline void LaneInfo::_internal_set_lane_count(int32_t value) {
  
  _impl_.lane_count_ = value;
}
inline void LaneInfo::set_lane_count(int32_t value) {
  _internal_set_lane_count(value);
  // @@protoc_insertion_point(field_set:perception.LaneInfo.lane_count)
}

// float lane_heading = 3;
inline void LaneInfo::clear_lane_heading() {
  _impl_.lane_heading_ = 0;
}
inline float LaneInfo::_internal_lane_heading() const {
  return _impl_.lane_heading_;
}
inline float LaneInfo::lane_heading() const {
  // @@protoc_insertion_point(field_get:perception.LaneInfo.lane_heading)
  return _internal_lane_heading();
}
inline void LaneInfo::_internal_set_lane_heading(float value) {
  
  _impl_.lane_heading_ = value;
}
inline void LaneInfo::set_lane_heading(float value) {
  _internal_set_lane_heading(value);
  // @@protoc_insertion_point(field_set:perception.LaneInfo.lane_heading)
}

// -------------------------------------------------------------------

// Target

// int64 id = 1;
inline void Target::clear_id() {
  _impl_.id_ = int64_t{0};
}
inline int64_t Target::_internal_id() const {
  return _impl_.id_;
}
inline int64_t Target::id() const {
  // @@protoc_insertion_point(field_get:perception.Target.id)
  return _internal_id();
}
inline void Target::_internal_set_id(int64_t value) {
  
  _impl_.id_ = value;
}
inline void Target::set_id(int64_t value) {
  _internal_set_id(value);
  // @@protoc_insertion_point(field_set:perception.Target.id)
}

// int32 source = 2;
inline void Target::clear_source() {
  _impl_.source_ = 0;
}
inline int32_t Target::_internal_source() const {
  return _impl_.source_;
}
inline int32_t Target::source() const {
  // @@protoc_insertion_point(field_get:perception.Target.source)
  return _internal_source();
}
inline void Target::_internal_set_source(int32_t value) {
  
  _impl_.source_ = value;
}
inline void Target::set_source(int32_t value) {
  _internal_set_source(value);
  // @@protoc_insertion_point(field_set:perception.Target.source)
}

// .perception.Position pos = 3;
inline bool Target::_internal_has_pos() const {
  return this != internal_default_instance() && _impl_.pos_ != nullptr;
}
inline bool Target::has_pos() const {
  return _internal_has_pos();
}
inline void Target::clear_pos() {
  if (GetArenaForAllocation() == nullptr && _impl_.pos_ != nullptr) {
    delete _impl_.pos_;
  }
  _impl_.pos_ = nullptr;
}
inline const ::perception::Position& Target::_internal_pos() const {
  const ::perception::Position* p = _impl_.pos_;
  return p != nullptr ? *p : reinterpret_cast<const ::perception::Position&>(
      ::perception::_Position_default_instance_);
}
inline const ::perception::Position& Target::pos() const {
  // @@protoc_insertion_point(field_get:perception.Target.pos)
  return _internal_pos();
}
inline void Target::unsafe_arena_set_allocated_pos(
    ::perception::Position* pos) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.pos_);
  }
  _impl_.pos_ = pos;
  if (pos) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:perception.Target.pos)
}
inline ::perception::Position* Target::release_pos() {
  
  ::perception::Position* temp = _impl_.pos_;
  _impl_.pos_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::perception::Position* Target::unsafe_arena_release_pos() {
  // @@protoc_insertion_point(field_release:perception.Target.pos)
  
  ::perception::Position* temp = _impl_.pos_;
  _impl_.pos_ = nullptr;
  return temp;
}
inline ::perception::Position* Target::_internal_mutable_pos() {
  
  if (_impl_.pos_ == nullptr) {
    auto* p = CreateMaybeMessage<::perception::Position>(GetArenaForAllocation());
    _impl_.pos_ = p;
  }
  return _impl_.pos_;
}
inline ::perception::Position* Target::mutable_pos() {
  ::perception::Position* _msg = _internal_mutable_pos();
  // @@protoc_insertion_point(field_mutable:perception.Target.pos)
  return _msg;
}
inline void Target::set_allocated_pos(::perception::Position* pos) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.pos_;
  }
  if (pos) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(pos);
    if (message_arena != submessage_arena) {
      pos = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, pos, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.pos_ = pos;
  // @@protoc_insertion_point(field_set_allocated:perception.Target.pos)
}

// .perception.Size size = 4;
inline bool Target::_internal_has_size() const {
  return this != internal_default_instance() && _impl_.size_ != nullptr;
}
inline bool Target::has_size() const {
  return _internal_has_size();
}
inline void Target::clear_size() {
  if (GetArenaForAllocation() == nullptr && _impl_.size_ != nullptr) {
    delete _impl_.size_;
  }
  _impl_.size_ = nullptr;
}
inline const ::perception::Size& Target::_internal_size() const {
  const ::perception::Size* p = _impl_.size_;
  return p != nullptr ? *p : reinterpret_cast<const ::perception::Size&>(
      ::perception::_Size_default_instance_);
}
inline const ::perception::Size& Target::size() const {
  // @@protoc_insertion_point(field_get:perception.Target.size)
  return _internal_size();
}
inline void Target::unsafe_arena_set_allocated_size(
    ::perception::Size* size) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.size_);
  }
  _impl_.size_ = size;
  if (size) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:perception.Target.size)
}
inline ::perception::Size* Target::release_size() {
  
  ::perception::Size* temp = _impl_.size_;
  _impl_.size_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::perception::Size* Target::unsafe_arena_release_size() {
  // @@protoc_insertion_point(field_release:perception.Target.size)
  
  ::perception::Size* temp = _impl_.size_;
  _impl_.size_ = nullptr;
  return temp;
}
inline ::perception::Size* Target::_internal_mutable_size() {
  
  if (_impl_.size_ == nullptr) {
    auto* p = CreateMaybeMessage<::perception::Size>(GetArenaForAllocation());
    _impl_.size_ = p;
  }
  return _impl_.size_;
}
inline ::perception::Size* Target::mutable_size() {
  ::perception::Size* _msg = _internal_mutable_size();
  // @@protoc_insertion_point(field_mutable:perception.Target.size)
  return _msg;
}
inline void Target::set_allocated_size(::perception::Size* size) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.size_;
  }
  if (size) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(size);
    if (message_arena != submessage_arena) {
      size = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, size, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.size_ = size;
  // @@protoc_insertion_point(field_set_allocated:perception.Target.size)
}

// float confidence = 5;
inline void Target::clear_confidence() {
  _impl_.confidence_ = 0;
}
inline float Target::_internal_confidence() const {
  return _impl_.confidence_;
}
inline float Target::confidence() const {
  // @@protoc_insertion_point(field_get:perception.Target.confidence)
  return _internal_confidence();
}
inline void Target::_internal_set_confidence(float value) {
  
  _impl_.confidence_ = value;
}
inline void Target::set_confidence(float value) {
  _internal_set_confidence(value);
  // @@protoc_insertion_point(field_set:perception.Target.confidence)
}

// int32 type = 6;
inline void Target::clear_type() {
  _impl_.type_ = 0;
}
inline int32_t Target::_internal_type() const {
  return _impl_.type_;
}
inline int32_t Target::type() const {
  // @@protoc_insertion_point(field_get:perception.Target.type)
  return _internal_type();
}
inline void Target::_internal_set_type(int32_t value) {
  
  _impl_.type_ = value;
}
inline void Target::set_type(int32_t value) {
  _internal_set_type(value);
  // @@protoc_insertion_point(field_set:perception.Target.type)
}

// int32 speed = 7;
inline void Target::clear_speed() {
  _impl_.speed_ = 0;
}
inline int32_t Target::_internal_speed() const {
  return _impl_.speed_;
}
inline int32_t Target::speed() const {
  // @@protoc_insertion_point(field_get:perception.Target.speed)
  return _internal_speed();
}
inline void Target::_internal_set_speed(int32_t value) {
  
  _impl_.speed_ = value;
}
inline void Target::set_speed(int32_t value) {
  _internal_set_speed(value);
  // @@protoc_insertion_point(field_set:perception.Target.speed)
}

// int32 heading = 8;
inline void Target::clear_heading() {
  _impl_.heading_ = 0;
}
inline int32_t Target::_internal_heading() const {
  return _impl_.heading_;
}
inline int32_t Target::heading() const {
  // @@protoc_insertion_point(field_get:perception.Target.heading)
  return _internal_heading();
}
inline void Target::_internal_set_heading(int32_t value) {
  
  _impl_.heading_ = value;
}
inline void Target::set_heading(int32_t value) {
  _internal_set_heading(value);
  // @@protoc_insertion_point(field_set:perception.Target.heading)
}

// int32 lane_id = 9;
inline void Target::clear_lane_id() {
  _impl_.lane_id_ = 0;
}
inline int32_t Target::_internal_lane_id() const {
  return _impl_.lane_id_;
}
inline int32_t Target::lane_id() const {
  // @@protoc_insertion_point(field_get:perception.Target.lane_id)
  return _internal_lane_id();
}
inline void Target::_internal_set_lane_id(int32_t value) {
  
  _impl_.lane_id_ = value;
}
inline void Target::set_lane_id(int32_t value) {
  _internal_set_lane_id(value);
  // @@protoc_insertion_point(field_set:perception.Target.lane_id)
}

// .perception.LaneInfo lane_info = 10;
inline bool Target::_internal_has_lane_info() const {
  return this != internal_default_instance() && _impl_.lane_info_ != nullptr;
}
inline bool Target::has_lane_info() const {
  return _internal_has_lane_info();
}
inline void Target::clear_lane_info() {
  if (GetArenaForAllocation() == nullptr && _impl_.lane_info_ != nullptr) {
    delete _impl_.lane_info_;
  }
  _impl_.lane_info_ = nullptr;
}
inline const ::perception::LaneInfo& Target::_internal_lane_info() const {
  const ::perception::LaneInfo* p = _impl_.lane_info_;
  return p != nullptr ? *p : reinterpret_cast<const ::perception::LaneInfo&>(
      ::perception::_LaneInfo_default_instance_);
}
inline const ::perception::LaneInfo& Target::lane_info() const {
  // @@protoc_insertion_point(field_get:perception.Target.lane_info)
  return _internal_lane_info();
}
inline void Target::unsafe_arena_set_allocated_lane_info(
    ::perception::LaneInfo* lane_info) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.lane_info_);
  }
  _impl_.lane_info_ = lane_info;
  if (lane_info) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:perception.Target.lane_info)
}
inline ::perception::LaneInfo* Target::release_lane_info() {
  
  ::perception::LaneInfo* temp = _impl_.lane_info_;
  _impl_.lane_info_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::perception::LaneInfo* Target::unsafe_arena_release_lane_info() {
  // @@protoc_insertion_point(field_release:perception.Target.lane_info)
  
  ::perception::LaneInfo* temp = _impl_.lane_info_;
  _impl_.lane_info_ = nullptr;
  return temp;
}
inline ::perception::LaneInfo* Target::_internal_mutable_lane_info() {
  
  if (_impl_.lane_info_ == nullptr) {
    auto* p = CreateMaybeMessage<::perception::LaneInfo>(GetArenaForAllocation());
    _impl_.lane_info_ = p;
  }
  return _impl_.lane_info_;
}
inline ::perception::LaneInfo* Target::mutable_lane_info() {
  ::perception::LaneInfo* _msg = _internal_mutable_lane_info();
  // @@protoc_insertion_point(field_mutable:perception.Target.lane_info)
  return _msg;
}
inline void Target::set_allocated_lane_info(::perception::LaneInfo* lane_info) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.lane_info_;
  }
  if (lane_info) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(lane_info);
    if (message_arena != submessage_arena) {
      lane_info = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, lane_info, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.lane_info_ = lane_info;
  // @@protoc_insertion_point(field_set_allocated:perception.Target.lane_info)
}

// string license_plate = 11;
inline void Target::clear_license_plate() {
  _impl_.license_plate_.ClearToEmpty();
}
inline const std::string& Target::license_plate() const {
  // @@protoc_insertion_point(field_get:perception.Target.license_plate)
  return _internal_license_plate();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void Target::set_license_plate(ArgT0&& arg0, ArgT... args) {
 
 _impl_.license_plate_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:perception.Target.license_plate)
}
inline std::string* Target::mutable_license_plate() {
  std::string* _s = _internal_mutable_license_plate();
  // @@protoc_insertion_point(field_mutable:perception.Target.license_plate)
  return _s;
}
inline const std::string& Target::_internal_license_plate() const {
  return _impl_.license_plate_.Get();
}
inline void Target::_internal_set_license_plate(const std::string& value) {
  
  _impl_.license_plate_.Set(value, GetArenaForAllocation());
}
inline std::string* Target::_internal_mutable_license_plate() {
  
  return _impl_.license_plate_.Mutable(GetArenaForAllocation());
}
inline std::string* Target::release_license_plate() {
  // @@protoc_insertion_point(field_release:perception.Target.license_plate)
  return _impl_.license_plate_.Release();
}
inline void Target::set_allocated_license_plate(std::string* license_plate) {
  if (license_plate != nullptr) {
    
  } else {
    
  }
  _impl_.license_plate_.SetAllocated(license_plate, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.license_plate_.IsDefault()) {
    _impl_.license_plate_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:perception.Target.license_plate)
}

// int32 car_type = 12;
inline void Target::clear_car_type() {
  _impl_.car_type_ = 0;
}
inline int32_t Target::_internal_car_type() const {
  return _impl_.car_type_;
}
inline int32_t Target::car_type() const {
  // @@protoc_insertion_point(field_get:perception.Target.car_type)
  return _internal_car_type();
}
inline void Target::_internal_set_car_type(int32_t value) {
  
  _impl_.car_type_ = value;
}
inline void Target::set_car_type(int32_t value) {
  _internal_set_car_type(value);
  // @@protoc_insertion_point(field_set:perception.Target.car_type)
}

// int32 color = 13;
inline void Target::clear_color() {
  _impl_.color_ = 0;
}
inline int32_t Target::_internal_color() const {
  return _impl_.color_;
}
inline int32_t Target::color() const {
  // @@protoc_insertion_point(field_get:perception.Target.color)
  return _internal_color();
}
inline void Target::_internal_set_color(int32_t value) {
  
  _impl_.color_ = value;
}
inline void Target::set_color(int32_t value) {
  _internal_set_color(value);
  // @@protoc_insertion_point(field_set:perception.Target.color)
}

// int32 status = 14;
inline void Target::clear_status() {
  _impl_.status_ = 0;
}
inline int32_t Target::_internal_status() const {
  return _impl_.status_;
}
inline int32_t Target::status() const {
  // @@protoc_insertion_point(field_get:perception.Target.status)
  return _internal_status();
}
inline void Target::_internal_set_status(int32_t value) {
  
  _impl_.status_ = value;
}
inline void Target::set_status(int32_t value) {
  _internal_set_status(value);
  // @@protoc_insertion_point(field_set:perception.Target.status)
}

// float distance = 15;
inline void Target::clear_distance() {
  _impl_.distance_ = 0;
}
inline float Target::_internal_distance() const {
  return _impl_.distance_;
}
inline float Target::distance() const {
  // @@protoc_insertion_point(field_get:perception.Target.distance)
  return _internal_distance();
}
inline void Target::_internal_set_distance(float value) {
  
  _impl_.distance_ = value;
}
inline void Target::set_distance(float value) {
  _internal_set_distance(value);
  // @@protoc_insertion_point(field_set:perception.Target.distance)
}

// float rcs = 16;
inline void Target::clear_rcs() {
  _impl_.rcs_ = 0;
}
inline float Target::_internal_rcs() const {
  return _impl_.rcs_;
}
inline float Target::rcs() const {
  // @@protoc_insertion_point(field_get:perception.Target.rcs)
  return _internal_rcs();
}
inline void Target::_internal_set_rcs(float value) {
  
  _impl_.rcs_ = value;
}
inline void Target::set_rcs(float value) {
  _internal_set_rcs(value);
  // @@protoc_insertion_point(field_set:perception.Target.rcs)
}

// int64 track_time = 17;
inline void Target::clear_track_time() {
  _impl_.track_time_ = int64_t{0};
}
inline int64_t Target::_internal_track_time() const {
  return _impl_.track_time_;
}
inline int64_t Target::track_time() const {
  // @@protoc_insertion_point(field_get:perception.Target.track_time)
  return _internal_track_time();
}
inline void Target::_internal_set_track_time(int64_t value) {
  
  _impl_.track_time_ = value;
}
inline void Target::set_track_time(int64_t value) {
  _internal_set_track_time(value);
  // @@protoc_insertion_point(field_set:perception.Target.track_time)
}

// int32 vehicle_pose = 18;
inline void Target::clear_vehicle_pose() {
  _impl_.vehicle_pose_ = 0;
}
inline int32_t Target::_internal_vehicle_pose() const {
  return _impl_.vehicle_pose_;
}
inline int32_t Target::vehicle_pose() const {
  // @@protoc_insertion_point(field_get:perception.Target.vehicle_pose)
  return _internal_vehicle_pose();
}
inline void Target::_internal_set_vehicle_pose(int32_t value) {
  
  _impl_.vehicle_pose_ = value;
}
inline void Target::set_vehicle_pose(int32_t value) {
  _internal_set_vehicle_pose(value);
  // @@protoc_insertion_point(field_set:perception.Target.vehicle_pose)
}

// int32 vehicle_type = 19;
inline void Target::clear_vehicle_type() {
  _impl_.vehicle_type_ = 0;
}
inline int32_t Target::_internal_vehicle_type() const {
  return _impl_.vehicle_type_;
}
inline int32_t Target::vehicle_type() const {
  // @@protoc_insertion_point(field_get:perception.Target.vehicle_type)
  return _internal_vehicle_type();
}
inline void Target::_internal_set_vehicle_type(int32_t value) {
  
  _impl_.vehicle_type_ = value;
}
inline void Target::set_vehicle_type(int32_t value) {
  _internal_set_vehicle_type(value);
  // @@protoc_insertion_point(field_set:perception.Target.vehicle_type)
}

// int32 vehicle_marker = 20;
inline void Target::clear_vehicle_marker() {
  _impl_.vehicle_marker_ = 0;
}
inline int32_t Target::_internal_vehicle_marker() const {
  return _impl_.vehicle_marker_;
}
inline int32_t Target::vehicle_marker() const {
  // @@protoc_insertion_point(field_get:perception.Target.vehicle_marker)
  return _internal_vehicle_marker();
}
inline void Target::_internal_set_vehicle_marker(int32_t value) {
  
  _impl_.vehicle_marker_ = value;
}
inline void Target::set_vehicle_marker(int32_t value) {
  _internal_set_vehicle_marker(value);
  // @@protoc_insertion_point(field_set:perception.Target.vehicle_marker)
}

// int32 vehicle_other = 21;
inline void Target::clear_vehicle_other() {
  _impl_.vehicle_other_ = 0;
}
inline int32_t Target::_internal_vehicle_other() const {
  return _impl_.vehicle_other_;
}
inline int32_t Target::vehicle_other() const {
  // @@protoc_insertion_point(field_get:perception.Target.vehicle_other)
  return _internal_vehicle_other();
}
inline void Target::_internal_set_vehicle_other(int32_t value) {
  
  _impl_.vehicle_other_ = value;
}
inline void Target::set_vehicle_other(int32_t value) {
  _internal_set_vehicle_other(value);
  // @@protoc_insertion_point(field_set:perception.Target.vehicle_other)
}

// int32 vehicle_roof = 22;
inline void Target::clear_vehicle_roof() {
  _impl_.vehicle_roof_ = 0;
}
inline int32_t Target::_internal_vehicle_roof() const {
  return _impl_.vehicle_roof_;
}
inline int32_t Target::vehicle_roof() const {
  // @@protoc_insertion_point(field_get:perception.Target.vehicle_roof)
  return _internal_vehicle_roof();
}
inline void Target::_internal_set_vehicle_roof(int32_t value) {
  
  _impl_.vehicle_roof_ = value;
}
inline void Target::set_vehicle_roof(int32_t value) {
  _internal_set_vehicle_roof(value);
  // @@protoc_insertion_point(field_set:perception.Target.vehicle_roof)
}

// int32 vehicle_carColor = 23;
inline void Target::clear_vehicle_carcolor() {
  _impl_.vehicle_carcolor_ = 0;
}
inline int32_t Target::_internal_vehicle_carcolor() const {
  return _impl_.vehicle_carcolor_;
}
inline int32_t Target::vehicle_carcolor() const {
  // @@protoc_insertion_point(field_get:perception.Target.vehicle_carColor)
  return _internal_vehicle_carcolor();
}
inline void Target::_internal_set_vehicle_carcolor(int32_t value) {
  
  _impl_.vehicle_carcolor_ = value;
}
inline void Target::set_vehicle_carcolor(int32_t value) {
  _internal_set_vehicle_carcolor(value);
  // @@protoc_insertion_point(field_set:perception.Target.vehicle_carColor)
}

// int32 vehicle_make = 24;
inline void Target::clear_vehicle_make() {
  _impl_.vehicle_make_ = 0;
}
inline int32_t Target::_internal_vehicle_make() const {
  return _impl_.vehicle_make_;
}
inline int32_t Target::vehicle_make() const {
  // @@protoc_insertion_point(field_get:perception.Target.vehicle_make)
  return _internal_vehicle_make();
}
inline void Target::_internal_set_vehicle_make(int32_t value) {
  
  _impl_.vehicle_make_ = value;
}
inline void Target::set_vehicle_make(int32_t value) {
  _internal_set_vehicle_make(value);
  // @@protoc_insertion_point(field_set:perception.Target.vehicle_make)
}

// int32 vehicle_model = 25;
inline void Target::clear_vehicle_model() {
  _impl_.vehicle_model_ = 0;
}
inline int32_t Target::_internal_vehicle_model() const {
  return _impl_.vehicle_model_;
}
inline int32_t Target::vehicle_model() const {
  // @@protoc_insertion_point(field_get:perception.Target.vehicle_model)
  return _internal_vehicle_model();
}
inline void Target::_internal_set_vehicle_model(int32_t value) {
  
  _impl_.vehicle_model_ = value;
}
inline void Target::set_vehicle_model(int32_t value) {
  _internal_set_vehicle_model(value);
  // @@protoc_insertion_point(field_set:perception.Target.vehicle_model)
}

// int32 vehicle_year = 26;
inline void Target::clear_vehicle_year() {
  _impl_.vehicle_year_ = 0;
}
inline int32_t Target::_internal_vehicle_year() const {
  return _impl_.vehicle_year_;
}
inline int32_t Target::vehicle_year() const {
  // @@protoc_insertion_point(field_get:perception.Target.vehicle_year)
  return _internal_vehicle_year();
}
inline void Target::_internal_set_vehicle_year(int32_t value) {
  
  _impl_.vehicle_year_ = value;
}
inline void Target::set_vehicle_year(int32_t value) {
  _internal_set_vehicle_year(value);
  // @@protoc_insertion_point(field_set:perception.Target.vehicle_year)
}

// int32 vehicle_plate_color = 27;
inline void Target::clear_vehicle_plate_color() {
  _impl_.vehicle_plate_color_ = 0;
}
inline int32_t Target::_internal_vehicle_plate_color() const {
  return _impl_.vehicle_plate_color_;
}
inline int32_t Target::vehicle_plate_color() const {
  // @@protoc_insertion_point(field_get:perception.Target.vehicle_plate_color)
  return _internal_vehicle_plate_color();
}
inline void Target::_internal_set_vehicle_plate_color(int32_t value) {
  
  _impl_.vehicle_plate_color_ = value;
}
inline void Target::set_vehicle_plate_color(int32_t value) {
  _internal_set_vehicle_plate_color(value);
  // @@protoc_insertion_point(field_set:perception.Target.vehicle_plate_color)
}

// int32 vehicle_plate_type = 28;
inline void Target::clear_vehicle_plate_type() {
  _impl_.vehicle_plate_type_ = 0;
}
inline int32_t Target::_internal_vehicle_plate_type() const {
  return _impl_.vehicle_plate_type_;
}
inline int32_t Target::vehicle_plate_type() const {
  // @@protoc_insertion_point(field_get:perception.Target.vehicle_plate_type)
  return _internal_vehicle_plate_type();
}
inline void Target::_internal_set_vehicle_plate_type(int32_t value) {
  
  _impl_.vehicle_plate_type_ = value;
}
inline void Target::set_vehicle_plate_type(int32_t value) {
  _internal_set_vehicle_plate_type(value);
  // @@protoc_insertion_point(field_set:perception.Target.vehicle_plate_type)
}

// int32 face_age = 29;
inline void Target::clear_face_age() {
  _impl_.face_age_ = 0;
}
inline int32_t Target::_internal_face_age() const {
  return _impl_.face_age_;
}
inline int32_t Target::face_age() const {
  // @@protoc_insertion_point(field_get:perception.Target.face_age)
  return _internal_face_age();
}
inline void Target::_internal_set_face_age(int32_t value) {
  
  _impl_.face_age_ = value;
}
inline void Target::set_face_age(int32_t value) {
  _internal_set_face_age(value);
  // @@protoc_insertion_point(field_set:perception.Target.face_age)
}

// int32 face_gender = 30;
inline void Target::clear_face_gender() {
  _impl_.face_gender_ = 0;
}
inline int32_t Target::_internal_face_gender() const {
  return _impl_.face_gender_;
}
inline int32_t Target::face_gender() const {
  // @@protoc_insertion_point(field_get:perception.Target.face_gender)
  return _internal_face_gender();
}
inline void Target::_internal_set_face_gender(int32_t value) {
  
  _impl_.face_gender_ = value;
}
inline void Target::set_face_gender(int32_t value) {
  _internal_set_face_gender(value);
  // @@protoc_insertion_point(field_set:perception.Target.face_gender)
}

// int32 face_mask = 31;
inline void Target::clear_face_mask() {
  _impl_.face_mask_ = 0;
}
inline int32_t Target::_internal_face_mask() const {
  return _impl_.face_mask_;
}
inline int32_t Target::face_mask() const {
  // @@protoc_insertion_point(field_get:perception.Target.face_mask)
  return _internal_face_mask();
}
inline void Target::_internal_set_face_mask(int32_t value) {
  
  _impl_.face_mask_ = value;
}
inline void Target::set_face_mask(int32_t value) {
  _internal_set_face_mask(value);
  // @@protoc_insertion_point(field_set:perception.Target.face_mask)
}

// int32 face_hat = 32;
inline void Target::clear_face_hat() {
  _impl_.face_hat_ = 0;
}
inline int32_t Target::_internal_face_hat() const {
  return _impl_.face_hat_;
}
inline int32_t Target::face_hat() const {
  // @@protoc_insertion_point(field_get:perception.Target.face_hat)
  return _internal_face_hat();
}
inline void Target::_internal_set_face_hat(int32_t value) {
  
  _impl_.face_hat_ = value;
}
inline void Target::set_face_hat(int32_t value) {
  _internal_set_face_hat(value);
  // @@protoc_insertion_point(field_set:perception.Target.face_hat)
}

// int32 face_glass = 33;
inline void Target::clear_face_glass() {
  _impl_.face_glass_ = 0;
}
inline int32_t Target::_internal_face_glass() const {
  return _impl_.face_glass_;
}
inline int32_t Target::face_glass() const {
  // @@protoc_insertion_point(field_get:perception.Target.face_glass)
  return _internal_face_glass();
}
inline void Target::_internal_set_face_glass(int32_t value) {
  
  _impl_.face_glass_ = value;
}
inline void Target::set_face_glass(int32_t value) {
  _internal_set_face_glass(value);
  // @@protoc_insertion_point(field_set:perception.Target.face_glass)
}

// int32 pedestrian_shoecolor = 34;
inline void Target::clear_pedestrian_shoecolor() {
  _impl_.pedestrian_shoecolor_ = 0;
}
inline int32_t Target::_internal_pedestrian_shoecolor() const {
  return _impl_.pedestrian_shoecolor_;
}
inline int32_t Target::pedestrian_shoecolor() const {
  // @@protoc_insertion_point(field_get:perception.Target.pedestrian_shoecolor)
  return _internal_pedestrian_shoecolor();
}
inline void Target::_internal_set_pedestrian_shoecolor(int32_t value) {
  
  _impl_.pedestrian_shoecolor_ = value;
}
inline void Target::set_pedestrian_shoecolor(int32_t value) {
  _internal_set_pedestrian_shoecolor(value);
  // @@protoc_insertion_point(field_set:perception.Target.pedestrian_shoecolor)
}

// int32 pedestrian_shoestyle = 35;
inline void Target::clear_pedestrian_shoestyle() {
  _impl_.pedestrian_shoestyle_ = 0;
}
inline int32_t Target::_internal_pedestrian_shoestyle() const {
  return _impl_.pedestrian_shoestyle_;
}
inline int32_t Target::pedestrian_shoestyle() const {
  // @@protoc_insertion_point(field_get:perception.Target.pedestrian_shoestyle)
  return _internal_pedestrian_shoestyle();
}
inline void Target::_internal_set_pedestrian_shoestyle(int32_t value) {
  
  _impl_.pedestrian_shoestyle_ = value;
}
inline void Target::set_pedestrian_shoestyle(int32_t value) {
  _internal_set_pedestrian_shoestyle(value);
  // @@protoc_insertion_point(field_set:perception.Target.pedestrian_shoestyle)
}

// int32 pedestrian_sleeve = 36;
inline void Target::clear_pedestrian_sleeve() {
  _impl_.pedestrian_sleeve_ = 0;
}
inline int32_t Target::_internal_pedestrian_sleeve() const {
  return _impl_.pedestrian_sleeve_;
}
inline int32_t Target::pedestrian_sleeve() const {
  // @@protoc_insertion_point(field_get:perception.Target.pedestrian_sleeve)
  return _internal_pedestrian_sleeve();
}
inline void Target::_internal_set_pedestrian_sleeve(int32_t value) {
  
  _impl_.pedestrian_sleeve_ = value;
}
inline void Target::set_pedestrian_sleeve(int32_t value) {
  _internal_set_pedestrian_sleeve(value);
  // @@protoc_insertion_point(field_set:perception.Target.pedestrian_sleeve)
}

// int32 pedestrian_hair = 37;
inline void Target::clear_pedestrian_hair() {
  _impl_.pedestrian_hair_ = 0;
}
inline int32_t Target::_internal_pedestrian_hair() const {
  return _impl_.pedestrian_hair_;
}
inline int32_t Target::pedestrian_hair() const {
  // @@protoc_insertion_point(field_get:perception.Target.pedestrian_hair)
  return _internal_pedestrian_hair();
}
inline void Target::_internal_set_pedestrian_hair(int32_t value) {
  
  _impl_.pedestrian_hair_ = value;
}
inline void Target::set_pedestrian_hair(int32_t value) {
  _internal_set_pedestrian_hair(value);
  // @@protoc_insertion_point(field_set:perception.Target.pedestrian_hair)
}

// int32 pedestrian_nation = 38;
inline void Target::clear_pedestrian_nation() {
  _impl_.pedestrian_nation_ = 0;
}
inline int32_t Target::_internal_pedestrian_nation() const {
  return _impl_.pedestrian_nation_;
}
inline int32_t Target::pedestrian_nation() const {
  // @@protoc_insertion_point(field_get:perception.Target.pedestrian_nation)
  return _internal_pedestrian_nation();
}
inline void Target::_internal_set_pedestrian_nation(int32_t value) {
  
  _impl_.pedestrian_nation_ = value;
}
inline void Target::set_pedestrian_nation(int32_t value) {
  _internal_set_pedestrian_nation(value);
  // @@protoc_insertion_point(field_set:perception.Target.pedestrian_nation)
}

// int32 pedestrian_gender = 39;
inline void Target::clear_pedestrian_gender() {
  _impl_.pedestrian_gender_ = 0;
}
inline int32_t Target::_internal_pedestrian_gender() const {
  return _impl_.pedestrian_gender_;
}
inline int32_t Target::pedestrian_gender() const {
  // @@protoc_insertion_point(field_get:perception.Target.pedestrian_gender)
  return _internal_pedestrian_gender();
}
inline void Target::_internal_set_pedestrian_gender(int32_t value) {
  
  _impl_.pedestrian_gender_ = value;
}
inline void Target::set_pedestrian_gender(int32_t value) {
  _internal_set_pedestrian_gender(value);
  // @@protoc_insertion_point(field_set:perception.Target.pedestrian_gender)
}

// int32 pedestrian_lowerstyle = 40;
inline void Target::clear_pedestrian_lowerstyle() {
  _impl_.pedestrian_lowerstyle_ = 0;
}
inline int32_t Target::_internal_pedestrian_lowerstyle() const {
  return _impl_.pedestrian_lowerstyle_;
}
inline int32_t Target::pedestrian_lowerstyle() const {
  // @@protoc_insertion_point(field_get:perception.Target.pedestrian_lowerstyle)
  return _internal_pedestrian_lowerstyle();
}
inline void Target::_internal_set_pedestrian_lowerstyle(int32_t value) {
  
  _impl_.pedestrian_lowerstyle_ = value;
}
inline void Target::set_pedestrian_lowerstyle(int32_t value) {
  _internal_set_pedestrian_lowerstyle(value);
  // @@protoc_insertion_point(field_set:perception.Target.pedestrian_lowerstyle)
}

// int32 pedestrian_upperstyle = 41;
inline void Target::clear_pedestrian_upperstyle() {
  _impl_.pedestrian_upperstyle_ = 0;
}
inline int32_t Target::_internal_pedestrian_upperstyle() const {
  return _impl_.pedestrian_upperstyle_;
}
inline int32_t Target::pedestrian_upperstyle() const {
  // @@protoc_insertion_point(field_get:perception.Target.pedestrian_upperstyle)
  return _internal_pedestrian_upperstyle();
}
inline void Target::_internal_set_pedestrian_upperstyle(int32_t value) {
  
  _impl_.pedestrian_upperstyle_ = value;
}
inline void Target::set_pedestrian_upperstyle(int32_t value) {
  _internal_set_pedestrian_upperstyle(value);
  // @@protoc_insertion_point(field_set:perception.Target.pedestrian_upperstyle)
}

// int32 pedestrian_age = 42;
inline void Target::clear_pedestrian_age() {
  _impl_.pedestrian_age_ = 0;
}
inline int32_t Target::_internal_pedestrian_age() const {
  return _impl_.pedestrian_age_;
}
inline int32_t Target::pedestrian_age() const {
  // @@protoc_insertion_point(field_get:perception.Target.pedestrian_age)
  return _internal_pedestrian_age();
}
inline void Target::_internal_set_pedestrian_age(int32_t value) {
  
  _impl_.pedestrian_age_ = value;
}
inline void Target::set_pedestrian_age(int32_t value) {
  _internal_set_pedestrian_age(value);
  // @@protoc_insertion_point(field_set:perception.Target.pedestrian_age)
}

// int32 pedestrian_lower = 43;
inline void Target::clear_pedestrian_lower() {
  _impl_.pedestrian_lower_ = 0;
}
inline int32_t Target::_internal_pedestrian_lower() const {
  return _impl_.pedestrian_lower_;
}
inline int32_t Target::pedestrian_lower() const {
  // @@protoc_insertion_point(field_get:perception.Target.pedestrian_lower)
  return _internal_pedestrian_lower();
}
inline void Target::_internal_set_pedestrian_lower(int32_t value) {
  
  _impl_.pedestrian_lower_ = value;
}
inline void Target::set_pedestrian_lower(int32_t value) {
  _internal_set_pedestrian_lower(value);
  // @@protoc_insertion_point(field_set:perception.Target.pedestrian_lower)
}

// int32 pedestrian_upper = 44;
inline void Target::clear_pedestrian_upper() {
  _impl_.pedestrian_upper_ = 0;
}
inline int32_t Target::_internal_pedestrian_upper() const {
  return _impl_.pedestrian_upper_;
}
inline int32_t Target::pedestrian_upper() const {
  // @@protoc_insertion_point(field_get:perception.Target.pedestrian_upper)
  return _internal_pedestrian_upper();
}
inline void Target::_internal_set_pedestrian_upper(int32_t value) {
  
  _impl_.pedestrian_upper_ = value;
}
inline void Target::set_pedestrian_upper(int32_t value) {
  _internal_set_pedestrian_upper(value);
  // @@protoc_insertion_point(field_set:perception.Target.pedestrian_upper)
}

// int32 pedestrian_backpack = 45;
inline void Target::clear_pedestrian_backpack() {
  _impl_.pedestrian_backpack_ = 0;
}
inline int32_t Target::_internal_pedestrian_backpack() const {
  return _impl_.pedestrian_backpack_;
}
inline int32_t Target::pedestrian_backpack() const {
  // @@protoc_insertion_point(field_get:perception.Target.pedestrian_backpack)
  return _internal_pedestrian_backpack();
}
inline void Target::_internal_set_pedestrian_backpack(int32_t value) {
  
  _impl_.pedestrian_backpack_ = value;
}
inline void Target::set_pedestrian_backpack(int32_t value) {
  _internal_set_pedestrian_backpack(value);
  // @@protoc_insertion_point(field_set:perception.Target.pedestrian_backpack)
}

// int32 nonmotor_attitude = 46;
inline void Target::clear_nonmotor_attitude() {
  _impl_.nonmotor_attitude_ = 0;
}
inline int32_t Target::_internal_nonmotor_attitude() const {
  return _impl_.nonmotor_attitude_;
}
inline int32_t Target::nonmotor_attitude() const {
  // @@protoc_insertion_point(field_get:perception.Target.nonmotor_attitude)
  return _internal_nonmotor_attitude();
}
inline void Target::_internal_set_nonmotor_attitude(int32_t value) {
  
  _impl_.nonmotor_attitude_ = value;
}
inline void Target::set_nonmotor_attitude(int32_t value) {
  _internal_set_nonmotor_attitude(value);
  // @@protoc_insertion_point(field_set:perception.Target.nonmotor_attitude)
}

// int32 nonmotor_gender = 47;
inline void Target::clear_nonmotor_gender() {
  _impl_.nonmotor_gender_ = 0;
}
inline int32_t Target::_internal_nonmotor_gender() const {
  return _impl_.nonmotor_gender_;
}
inline int32_t Target::nonmotor_gender() const {
  // @@protoc_insertion_point(field_get:perception.Target.nonmotor_gender)
  return _internal_nonmotor_gender();
}
inline void Target::_internal_set_nonmotor_gender(int32_t value) {
  
  _impl_.nonmotor_gender_ = value;
}
inline void Target::set_nonmotor_gender(int32_t value) {
  _internal_set_nonmotor_gender(value);
  // @@protoc_insertion_point(field_set:perception.Target.nonmotor_gender)
}

// int32 nonmotor_nation = 48;
inline void Target::clear_nonmotor_nation() {
  _impl_.nonmotor_nation_ = 0;
}
inline int32_t Target::_internal_nonmotor_nation() const {
  return _impl_.nonmotor_nation_;
}
inline int32_t Target::nonmotor_nation() const {
  // @@protoc_insertion_point(field_get:perception.Target.nonmotor_nation)
  return _internal_nonmotor_nation();
}
inline void Target::_internal_set_nonmotor_nation(int32_t value) {
  
  _impl_.nonmotor_nation_ = value;
}
inline void Target::set_nonmotor_nation(int32_t value) {
  _internal_set_nonmotor_nation(value);
  // @@protoc_insertion_point(field_set:perception.Target.nonmotor_nation)
}

// int32 nonmotor_transportation = 49;
inline void Target::clear_nonmotor_transportation() {
  _impl_.nonmotor_transportation_ = 0;
}
inline int32_t Target::_internal_nonmotor_transportation() const {
  return _impl_.nonmotor_transportation_;
}
inline int32_t Target::nonmotor_transportation() const {
  // @@protoc_insertion_point(field_get:perception.Target.nonmotor_transportation)
  return _internal_nonmotor_transportation();
}
inline void Target::_internal_set_nonmotor_transportation(int32_t value) {
  
  _impl_.nonmotor_transportation_ = value;
}
inline void Target::set_nonmotor_transportation(int32_t value) {
  _internal_set_nonmotor_transportation(value);
  // @@protoc_insertion_point(field_set:perception.Target.nonmotor_transportation)
}

// int32 nonmotor_upperColor = 50;
inline void Target::clear_nonmotor_uppercolor() {
  _impl_.nonmotor_uppercolor_ = 0;
}
inline int32_t Target::_internal_nonmotor_uppercolor() const {
  return _impl_.nonmotor_uppercolor_;
}
inline int32_t Target::nonmotor_uppercolor() const {
  // @@protoc_insertion_point(field_get:perception.Target.nonmotor_upperColor)
  return _internal_nonmotor_uppercolor();
}
inline void Target::_internal_set_nonmotor_uppercolor(int32_t value) {
  
  _impl_.nonmotor_uppercolor_ = value;
}
inline void Target::set_nonmotor_uppercolor(int32_t value) {
  _internal_set_nonmotor_uppercolor(value);
  // @@protoc_insertion_point(field_set:perception.Target.nonmotor_upperColor)
}

// int32 nonmotor_upper = 51;
inline void Target::clear_nonmotor_upper() {
  _impl_.nonmotor_upper_ = 0;
}
inline int32_t Target::_internal_nonmotor_upper() const {
  return _impl_.nonmotor_upper_;
}
inline int32_t Target::nonmotor_upper() const {
  // @@protoc_insertion_point(field_get:perception.Target.nonmotor_upper)
  return _internal_nonmotor_upper();
}
inline void Target::_internal_set_nonmotor_upper(int32_t value) {
  
  _impl_.nonmotor_upper_ = value;
}
inline void Target::set_nonmotor_upper(int32_t value) {
  _internal_set_nonmotor_upper(value);
  // @@protoc_insertion_point(field_set:perception.Target.nonmotor_upper)
}

// int32 nonmotor_transportationColor = 52;
inline void Target::clear_nonmotor_transportationcolor() {
  _impl_.nonmotor_transportationcolor_ = 0;
}
inline int32_t Target::_internal_nonmotor_transportationcolor() const {
  return _impl_.nonmotor_transportationcolor_;
}
inline int32_t Target::nonmotor_transportationcolor() const {
  // @@protoc_insertion_point(field_get:perception.Target.nonmotor_transportationColor)
  return _internal_nonmotor_transportationcolor();
}
inline void Target::_internal_set_nonmotor_transportationcolor(int32_t value) {
  
  _impl_.nonmotor_transportationcolor_ = value;
}
inline void Target::set_nonmotor_transportationcolor(int32_t value) {
  _internal_set_nonmotor_transportationcolor(value);
  // @@protoc_insertion_point(field_set:perception.Target.nonmotor_transportationColor)
}

// -------------------------------------------------------------------

// Targets

// string device_sn = 1;
inline void Targets::clear_device_sn() {
  _impl_.device_sn_.ClearToEmpty();
}
inline const std::string& Targets::device_sn() const {
  // @@protoc_insertion_point(field_get:perception.Targets.device_sn)
  return _internal_device_sn();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void Targets::set_device_sn(ArgT0&& arg0, ArgT... args) {
 
 _impl_.device_sn_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:perception.Targets.device_sn)
}
inline std::string* Targets::mutable_device_sn() {
  std::string* _s = _internal_mutable_device_sn();
  // @@protoc_insertion_point(field_mutable:perception.Targets.device_sn)
  return _s;
}
inline const std::string& Targets::_internal_device_sn() const {
  return _impl_.device_sn_.Get();
}
inline void Targets::_internal_set_device_sn(const std::string& value) {
  
  _impl_.device_sn_.Set(value, GetArenaForAllocation());
}
inline std::string* Targets::_internal_mutable_device_sn() {
  
  return _impl_.device_sn_.Mutable(GetArenaForAllocation());
}
inline std::string* Targets::release_device_sn() {
  // @@protoc_insertion_point(field_release:perception.Targets.device_sn)
  return _impl_.device_sn_.Release();
}
inline void Targets::set_allocated_device_sn(std::string* device_sn) {
  if (device_sn != nullptr) {
    
  } else {
    
  }
  _impl_.device_sn_.SetAllocated(device_sn, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.device_sn_.IsDefault()) {
    _impl_.device_sn_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:perception.Targets.device_sn)
}

// string device_ip = 2;
inline void Targets::clear_device_ip() {
  _impl_.device_ip_.ClearToEmpty();
}
inline const std::string& Targets::device_ip() const {
  // @@protoc_insertion_point(field_get:perception.Targets.device_ip)
  return _internal_device_ip();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void Targets::set_device_ip(ArgT0&& arg0, ArgT... args) {
 
 _impl_.device_ip_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:perception.Targets.device_ip)
}
inline std::string* Targets::mutable_device_ip() {
  std::string* _s = _internal_mutable_device_ip();
  // @@protoc_insertion_point(field_mutable:perception.Targets.device_ip)
  return _s;
}
inline const std::string& Targets::_internal_device_ip() const {
  return _impl_.device_ip_.Get();
}
inline void Targets::_internal_set_device_ip(const std::string& value) {
  
  _impl_.device_ip_.Set(value, GetArenaForAllocation());
}
inline std::string* Targets::_internal_mutable_device_ip() {
  
  return _impl_.device_ip_.Mutable(GetArenaForAllocation());
}
inline std::string* Targets::release_device_ip() {
  // @@protoc_insertion_point(field_release:perception.Targets.device_ip)
  return _impl_.device_ip_.Release();
}
inline void Targets::set_allocated_device_ip(std::string* device_ip) {
  if (device_ip != nullptr) {
    
  } else {
    
  }
  _impl_.device_ip_.SetAllocated(device_ip, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.device_ip_.IsDefault()) {
    _impl_.device_ip_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:perception.Targets.device_ip)
}

// int32 fusion_state = 3;
inline void Targets::clear_fusion_state() {
  _impl_.fusion_state_ = 0;
}
inline int32_t Targets::_internal_fusion_state() const {
  return _impl_.fusion_state_;
}
inline int32_t Targets::fusion_state() const {
  // @@protoc_insertion_point(field_get:perception.Targets.fusion_state)
  return _internal_fusion_state();
}
inline void Targets::_internal_set_fusion_state(int32_t value) {
  
  _impl_.fusion_state_ = value;
}
inline void Targets::set_fusion_state(int32_t value) {
  _internal_set_fusion_state(value);
  // @@protoc_insertion_point(field_set:perception.Targets.fusion_state)
}

// repeated .perception.Target array = 4;
inline int Targets::_internal_array_size() const {
  return _impl_.array_.size();
}
inline int Targets::array_size() const {
  return _internal_array_size();
}
inline void Targets::clear_array() {
  _impl_.array_.Clear();
}
inline ::perception::Target* Targets::mutable_array(int index) {
  // @@protoc_insertion_point(field_mutable:perception.Targets.array)
  return _impl_.array_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::perception::Target >*
Targets::mutable_array() {
  // @@protoc_insertion_point(field_mutable_list:perception.Targets.array)
  return &_impl_.array_;
}
inline const ::perception::Target& Targets::_internal_array(int index) const {
  return _impl_.array_.Get(index);
}
inline const ::perception::Target& Targets::array(int index) const {
  // @@protoc_insertion_point(field_get:perception.Targets.array)
  return _internal_array(index);
}
inline ::perception::Target* Targets::_internal_add_array() {
  return _impl_.array_.Add();
}
inline ::perception::Target* Targets::add_array() {
  ::perception::Target* _add = _internal_add_array();
  // @@protoc_insertion_point(field_add:perception.Targets.array)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::perception::Target >&
Targets::array() const {
  // @@protoc_insertion_point(field_list:perception.Targets.array)
  return _impl_.array_;
}

// -------------------------------------------------------------------

// AreaInfo

// int32 id = 1;
inline void AreaInfo::clear_id() {
  _impl_.id_ = 0;
}
inline int32_t AreaInfo::_internal_id() const {
  return _impl_.id_;
}
inline int32_t AreaInfo::id() const {
  // @@protoc_insertion_point(field_get:perception.AreaInfo.id)
  return _internal_id();
}
inline void AreaInfo::_internal_set_id(int32_t value) {
  
  _impl_.id_ = value;
}
inline void AreaInfo::set_id(int32_t value) {
  _internal_set_id(value);
  // @@protoc_insertion_point(field_set:perception.AreaInfo.id)
}

// float width = 2;
inline void AreaInfo::clear_width() {
  _impl_.width_ = 0;
}
inline float AreaInfo::_internal_width() const {
  return _impl_.width_;
}
inline float AreaInfo::width() const {
  // @@protoc_insertion_point(field_get:perception.AreaInfo.width)
  return _internal_width();
}
inline void AreaInfo::_internal_set_width(float value) {
  
  _impl_.width_ = value;
}
inline void AreaInfo::set_width(float value) {
  _internal_set_width(value);
  // @@protoc_insertion_point(field_set:perception.AreaInfo.width)
}

// repeated .perception.Position dot = 3;
inline int AreaInfo::_internal_dot_size() const {
  return _impl_.dot_.size();
}
inline int AreaInfo::dot_size() const {
  return _internal_dot_size();
}
inline void AreaInfo::clear_dot() {
  _impl_.dot_.Clear();
}
inline ::perception::Position* AreaInfo::mutable_dot(int index) {
  // @@protoc_insertion_point(field_mutable:perception.AreaInfo.dot)
  return _impl_.dot_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::perception::Position >*
AreaInfo::mutable_dot() {
  // @@protoc_insertion_point(field_mutable_list:perception.AreaInfo.dot)
  return &_impl_.dot_;
}
inline const ::perception::Position& AreaInfo::_internal_dot(int index) const {
  return _impl_.dot_.Get(index);
}
inline const ::perception::Position& AreaInfo::dot(int index) const {
  // @@protoc_insertion_point(field_get:perception.AreaInfo.dot)
  return _internal_dot(index);
}
inline ::perception::Position* AreaInfo::_internal_add_dot() {
  return _impl_.dot_.Add();
}
inline ::perception::Position* AreaInfo::add_dot() {
  ::perception::Position* _add = _internal_add_dot();
  // @@protoc_insertion_point(field_add:perception.AreaInfo.dot)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::perception::Position >&
AreaInfo::dot() const {
  // @@protoc_insertion_point(field_list:perception.AreaInfo.dot)
  return _impl_.dot_;
}

// -------------------------------------------------------------------

// LaneCount

// int32 laneNo = 1;
inline void LaneCount::clear_laneno() {
  _impl_.laneno_ = 0;
}
inline int32_t LaneCount::_internal_laneno() const {
  return _impl_.laneno_;
}
inline int32_t LaneCount::laneno() const {
  // @@protoc_insertion_point(field_get:perception.LaneCount.laneNo)
  return _internal_laneno();
}
inline void LaneCount::_internal_set_laneno(int32_t value) {
  
  _impl_.laneno_ = value;
}
inline void LaneCount::set_laneno(int32_t value) {
  _internal_set_laneno(value);
  // @@protoc_insertion_point(field_set:perception.LaneCount.laneNo)
}

// int32 volume = 2;
inline void LaneCount::clear_volume() {
  _impl_.volume_ = 0;
}
inline int32_t LaneCount::_internal_volume() const {
  return _impl_.volume_;
}
inline int32_t LaneCount::volume() const {
  // @@protoc_insertion_point(field_get:perception.LaneCount.volume)
  return _internal_volume();
}
inline void LaneCount::_internal_set_volume(int32_t value) {
  
  _impl_.volume_ = value;
}
inline void LaneCount::set_volume(int32_t value) {
  _internal_set_volume(value);
  // @@protoc_insertion_point(field_set:perception.LaneCount.volume)
}

// int32 pcu = 3;
inline void LaneCount::clear_pcu() {
  _impl_.pcu_ = 0;
}
inline int32_t LaneCount::_internal_pcu() const {
  return _impl_.pcu_;
}
inline int32_t LaneCount::pcu() const {
  // @@protoc_insertion_point(field_get:perception.LaneCount.pcu)
  return _internal_pcu();
}
inline void LaneCount::_internal_set_pcu(int32_t value) {
  
  _impl_.pcu_ = value;
}
inline void LaneCount::set_pcu(int32_t value) {
  _internal_set_pcu(value);
  // @@protoc_insertion_point(field_set:perception.LaneCount.pcu)
}

// float avSpeed = 4;
inline void LaneCount::clear_avspeed() {
  _impl_.avspeed_ = 0;
}
inline float LaneCount::_internal_avspeed() const {
  return _impl_.avspeed_;
}
inline float LaneCount::avspeed() const {
  // @@protoc_insertion_point(field_get:perception.LaneCount.avSpeed)
  return _internal_avspeed();
}
inline void LaneCount::_internal_set_avspeed(float value) {
  
  _impl_.avspeed_ = value;
}
inline void LaneCount::set_avspeed(float value) {
  _internal_set_avspeed(value);
  // @@protoc_insertion_point(field_set:perception.LaneCount.avSpeed)
}

// float occupancy = 5;
inline void LaneCount::clear_occupancy() {
  _impl_.occupancy_ = 0;
}
inline float LaneCount::_internal_occupancy() const {
  return _impl_.occupancy_;
}
inline float LaneCount::occupancy() const {
  // @@protoc_insertion_point(field_get:perception.LaneCount.occupancy)
  return _internal_occupancy();
}
inline void LaneCount::_internal_set_occupancy(float value) {
  
  _impl_.occupancy_ = value;
}
inline void LaneCount::set_occupancy(float value) {
  _internal_set_occupancy(value);
  // @@protoc_insertion_point(field_set:perception.LaneCount.occupancy)
}

// float headWay = 6;
inline void LaneCount::clear_headway() {
  _impl_.headway_ = 0;
}
inline float LaneCount::_internal_headway() const {
  return _impl_.headway_;
}
inline float LaneCount::headway() const {
  // @@protoc_insertion_point(field_get:perception.LaneCount.headWay)
  return _internal_headway();
}
inline void LaneCount::_internal_set_headway(float value) {
  
  _impl_.headway_ = value;
}
inline void LaneCount::set_headway(float value) {
  _internal_set_headway(value);
  // @@protoc_insertion_point(field_set:perception.LaneCount.headWay)
}

// float gap = 7;
inline void LaneCount::clear_gap() {
  _impl_.gap_ = 0;
}
inline float LaneCount::_internal_gap() const {
  return _impl_.gap_;
}
inline float LaneCount::gap() const {
  // @@protoc_insertion_point(field_get:perception.LaneCount.gap)
  return _internal_gap();
}
inline void LaneCount::_internal_set_gap(float value) {
  
  _impl_.gap_ = value;
}
inline void LaneCount::set_gap(float value) {
  _internal_set_gap(value);
  // @@protoc_insertion_point(field_set:perception.LaneCount.gap)
}

// float avDelay = 8;
inline void LaneCount::clear_avdelay() {
  _impl_.avdelay_ = 0;
}
inline float LaneCount::_internal_avdelay() const {
  return _impl_.avdelay_;
}
inline float LaneCount::avdelay() const {
  // @@protoc_insertion_point(field_get:perception.LaneCount.avDelay)
  return _internal_avdelay();
}
inline void LaneCount::_internal_set_avdelay(float value) {
  
  _impl_.avdelay_ = value;
}
inline void LaneCount::set_avdelay(float value) {
  _internal_set_avdelay(value);
  // @@protoc_insertion_point(field_set:perception.LaneCount.avDelay)
}

// int32 avStop = 9;
inline void LaneCount::clear_avstop() {
  _impl_.avstop_ = 0;
}
inline int32_t LaneCount::_internal_avstop() const {
  return _impl_.avstop_;
}
inline int32_t LaneCount::avstop() const {
  // @@protoc_insertion_point(field_get:perception.LaneCount.avStop)
  return _internal_avstop();
}
inline void LaneCount::_internal_set_avstop(int32_t value) {
  
  _impl_.avstop_ = value;
}
inline void LaneCount::set_avstop(int32_t value) {
  _internal_set_avstop(value);
  // @@protoc_insertion_point(field_set:perception.LaneCount.avStop)
}

// float speed85 = 10;
inline void LaneCount::clear_speed85() {
  _impl_.speed85_ = 0;
}
inline float LaneCount::_internal_speed85() const {
  return _impl_.speed85_;
}
inline float LaneCount::speed85() const {
  // @@protoc_insertion_point(field_get:perception.LaneCount.speed85)
  return _internal_speed85();
}
inline void LaneCount::_internal_set_speed85(float value) {
  
  _impl_.speed85_ = value;
}
inline void LaneCount::set_speed85(float value) {
  _internal_set_speed85(value);
  // @@protoc_insertion_point(field_set:perception.LaneCount.speed85)
}

// float queueLength = 11;
inline void LaneCount::clear_queuelength() {
  _impl_.queuelength_ = 0;
}
inline float LaneCount::_internal_queuelength() const {
  return _impl_.queuelength_;
}
inline float LaneCount::queuelength() const {
  // @@protoc_insertion_point(field_get:perception.LaneCount.queueLength)
  return _internal_queuelength();
}
inline void LaneCount::_internal_set_queuelength(float value) {
  
  _impl_.queuelength_ = value;
}
inline void LaneCount::set_queuelength(float value) {
  _internal_set_queuelength(value);
  // @@protoc_insertion_point(field_set:perception.LaneCount.queueLength)
}

// -------------------------------------------------------------------

// RoadCount

// string deviceId = 1;
inline void RoadCount::clear_deviceid() {
  _impl_.deviceid_.ClearToEmpty();
}
inline const std::string& RoadCount::deviceid() const {
  // @@protoc_insertion_point(field_get:perception.RoadCount.deviceId)
  return _internal_deviceid();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void RoadCount::set_deviceid(ArgT0&& arg0, ArgT... args) {
 
 _impl_.deviceid_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:perception.RoadCount.deviceId)
}
inline std::string* RoadCount::mutable_deviceid() {
  std::string* _s = _internal_mutable_deviceid();
  // @@protoc_insertion_point(field_mutable:perception.RoadCount.deviceId)
  return _s;
}
inline const std::string& RoadCount::_internal_deviceid() const {
  return _impl_.deviceid_.Get();
}
inline void RoadCount::_internal_set_deviceid(const std::string& value) {
  
  _impl_.deviceid_.Set(value, GetArenaForAllocation());
}
inline std::string* RoadCount::_internal_mutable_deviceid() {
  
  return _impl_.deviceid_.Mutable(GetArenaForAllocation());
}
inline std::string* RoadCount::release_deviceid() {
  // @@protoc_insertion_point(field_release:perception.RoadCount.deviceId)
  return _impl_.deviceid_.Release();
}
inline void RoadCount::set_allocated_deviceid(std::string* deviceid) {
  if (deviceid != nullptr) {
    
  } else {
    
  }
  _impl_.deviceid_.SetAllocated(deviceid, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.deviceid_.IsDefault()) {
    _impl_.deviceid_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:perception.RoadCount.deviceId)
}

// float heading = 2;
inline void RoadCount::clear_heading() {
  _impl_.heading_ = 0;
}
inline float RoadCount::_internal_heading() const {
  return _impl_.heading_;
}
inline float RoadCount::heading() const {
  // @@protoc_insertion_point(field_get:perception.RoadCount.heading)
  return _internal_heading();
}
inline void RoadCount::_internal_set_heading(float value) {
  
  _impl_.heading_ = value;
}
inline void RoadCount::set_heading(float value) {
  _internal_set_heading(value);
  // @@protoc_insertion_point(field_set:perception.RoadCount.heading)
}

// string roadName = 3;
inline void RoadCount::clear_roadname() {
  _impl_.roadname_.ClearToEmpty();
}
inline const std::string& RoadCount::roadname() const {
  // @@protoc_insertion_point(field_get:perception.RoadCount.roadName)
  return _internal_roadname();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void RoadCount::set_roadname(ArgT0&& arg0, ArgT... args) {
 
 _impl_.roadname_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:perception.RoadCount.roadName)
}
inline std::string* RoadCount::mutable_roadname() {
  std::string* _s = _internal_mutable_roadname();
  // @@protoc_insertion_point(field_mutable:perception.RoadCount.roadName)
  return _s;
}
inline const std::string& RoadCount::_internal_roadname() const {
  return _impl_.roadname_.Get();
}
inline void RoadCount::_internal_set_roadname(const std::string& value) {
  
  _impl_.roadname_.Set(value, GetArenaForAllocation());
}
inline std::string* RoadCount::_internal_mutable_roadname() {
  
  return _impl_.roadname_.Mutable(GetArenaForAllocation());
}
inline std::string* RoadCount::release_roadname() {
  // @@protoc_insertion_point(field_release:perception.RoadCount.roadName)
  return _impl_.roadname_.Release();
}
inline void RoadCount::set_allocated_roadname(std::string* roadname) {
  if (roadname != nullptr) {
    
  } else {
    
  }
  _impl_.roadname_.SetAllocated(roadname, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.roadname_.IsDefault()) {
    _impl_.roadname_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:perception.RoadCount.roadName)
}

// .perception.Position roadInCoord = 4;
inline bool RoadCount::_internal_has_roadincoord() const {
  return this != internal_default_instance() && _impl_.roadincoord_ != nullptr;
}
inline bool RoadCount::has_roadincoord() const {
  return _internal_has_roadincoord();
}
inline void RoadCount::clear_roadincoord() {
  if (GetArenaForAllocation() == nullptr && _impl_.roadincoord_ != nullptr) {
    delete _impl_.roadincoord_;
  }
  _impl_.roadincoord_ = nullptr;
}
inline const ::perception::Position& RoadCount::_internal_roadincoord() const {
  const ::perception::Position* p = _impl_.roadincoord_;
  return p != nullptr ? *p : reinterpret_cast<const ::perception::Position&>(
      ::perception::_Position_default_instance_);
}
inline const ::perception::Position& RoadCount::roadincoord() const {
  // @@protoc_insertion_point(field_get:perception.RoadCount.roadInCoord)
  return _internal_roadincoord();
}
inline void RoadCount::unsafe_arena_set_allocated_roadincoord(
    ::perception::Position* roadincoord) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.roadincoord_);
  }
  _impl_.roadincoord_ = roadincoord;
  if (roadincoord) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:perception.RoadCount.roadInCoord)
}
inline ::perception::Position* RoadCount::release_roadincoord() {
  
  ::perception::Position* temp = _impl_.roadincoord_;
  _impl_.roadincoord_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::perception::Position* RoadCount::unsafe_arena_release_roadincoord() {
  // @@protoc_insertion_point(field_release:perception.RoadCount.roadInCoord)
  
  ::perception::Position* temp = _impl_.roadincoord_;
  _impl_.roadincoord_ = nullptr;
  return temp;
}
inline ::perception::Position* RoadCount::_internal_mutable_roadincoord() {
  
  if (_impl_.roadincoord_ == nullptr) {
    auto* p = CreateMaybeMessage<::perception::Position>(GetArenaForAllocation());
    _impl_.roadincoord_ = p;
  }
  return _impl_.roadincoord_;
}
inline ::perception::Position* RoadCount::mutable_roadincoord() {
  ::perception::Position* _msg = _internal_mutable_roadincoord();
  // @@protoc_insertion_point(field_mutable:perception.RoadCount.roadInCoord)
  return _msg;
}
inline void RoadCount::set_allocated_roadincoord(::perception::Position* roadincoord) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.roadincoord_;
  }
  if (roadincoord) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(roadincoord);
    if (message_arena != submessage_arena) {
      roadincoord = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, roadincoord, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.roadincoord_ = roadincoord;
  // @@protoc_insertion_point(field_set_allocated:perception.RoadCount.roadInCoord)
}

// int32 volume = 5;
inline void RoadCount::clear_volume() {
  _impl_.volume_ = 0;
}
inline int32_t RoadCount::_internal_volume() const {
  return _impl_.volume_;
}
inline int32_t RoadCount::volume() const {
  // @@protoc_insertion_point(field_get:perception.RoadCount.volume)
  return _internal_volume();
}
inline void RoadCount::_internal_set_volume(int32_t value) {
  
  _impl_.volume_ = value;
}
inline void RoadCount::set_volume(int32_t value) {
  _internal_set_volume(value);
  // @@protoc_insertion_point(field_set:perception.RoadCount.volume)
}

// int32 pcu = 6;
inline void RoadCount::clear_pcu() {
  _impl_.pcu_ = 0;
}
inline int32_t RoadCount::_internal_pcu() const {
  return _impl_.pcu_;
}
inline int32_t RoadCount::pcu() const {
  // @@protoc_insertion_point(field_get:perception.RoadCount.pcu)
  return _internal_pcu();
}
inline void RoadCount::_internal_set_pcu(int32_t value) {
  
  _impl_.pcu_ = value;
}
inline void RoadCount::set_pcu(int32_t value) {
  _internal_set_pcu(value);
  // @@protoc_insertion_point(field_set:perception.RoadCount.pcu)
}

// float avSpeed = 7;
inline void RoadCount::clear_avspeed() {
  _impl_.avspeed_ = 0;
}
inline float RoadCount::_internal_avspeed() const {
  return _impl_.avspeed_;
}
inline float RoadCount::avspeed() const {
  // @@protoc_insertion_point(field_get:perception.RoadCount.avSpeed)
  return _internal_avspeed();
}
inline void RoadCount::_internal_set_avspeed(float value) {
  
  _impl_.avspeed_ = value;
}
inline void RoadCount::set_avspeed(float value) {
  _internal_set_avspeed(value);
  // @@protoc_insertion_point(field_set:perception.RoadCount.avSpeed)
}

// float occupancy = 8;
inline void RoadCount::clear_occupancy() {
  _impl_.occupancy_ = 0;
}
inline float RoadCount::_internal_occupancy() const {
  return _impl_.occupancy_;
}
inline float RoadCount::occupancy() const {
  // @@protoc_insertion_point(field_get:perception.RoadCount.occupancy)
  return _internal_occupancy();
}
inline void RoadCount::_internal_set_occupancy(float value) {
  
  _impl_.occupancy_ = value;
}
inline void RoadCount::set_occupancy(float value) {
  _internal_set_occupancy(value);
  // @@protoc_insertion_point(field_set:perception.RoadCount.occupancy)
}

// float headWay = 9;
inline void RoadCount::clear_headway() {
  _impl_.headway_ = 0;
}
inline float RoadCount::_internal_headway() const {
  return _impl_.headway_;
}
inline float RoadCount::headway() const {
  // @@protoc_insertion_point(field_get:perception.RoadCount.headWay)
  return _internal_headway();
}
inline void RoadCount::_internal_set_headway(float value) {
  
  _impl_.headway_ = value;
}
inline void RoadCount::set_headway(float value) {
  _internal_set_headway(value);
  // @@protoc_insertion_point(field_set:perception.RoadCount.headWay)
}

// float gap = 10;
inline void RoadCount::clear_gap() {
  _impl_.gap_ = 0;
}
inline float RoadCount::_internal_gap() const {
  return _impl_.gap_;
}
inline float RoadCount::gap() const {
  // @@protoc_insertion_point(field_get:perception.RoadCount.gap)
  return _internal_gap();
}
inline void RoadCount::_internal_set_gap(float value) {
  
  _impl_.gap_ = value;
}
inline void RoadCount::set_gap(float value) {
  _internal_set_gap(value);
  // @@protoc_insertion_point(field_set:perception.RoadCount.gap)
}

// float avDelay = 11;
inline void RoadCount::clear_avdelay() {
  _impl_.avdelay_ = 0;
}
inline float RoadCount::_internal_avdelay() const {
  return _impl_.avdelay_;
}
inline float RoadCount::avdelay() const {
  // @@protoc_insertion_point(field_get:perception.RoadCount.avDelay)
  return _internal_avdelay();
}
inline void RoadCount::_internal_set_avdelay(float value) {
  
  _impl_.avdelay_ = value;
}
inline void RoadCount::set_avdelay(float value) {
  _internal_set_avdelay(value);
  // @@protoc_insertion_point(field_set:perception.RoadCount.avDelay)
}

// int32 avStop = 12;
inline void RoadCount::clear_avstop() {
  _impl_.avstop_ = 0;
}
inline int32_t RoadCount::_internal_avstop() const {
  return _impl_.avstop_;
}
inline int32_t RoadCount::avstop() const {
  // @@protoc_insertion_point(field_get:perception.RoadCount.avStop)
  return _internal_avstop();
}
inline void RoadCount::_internal_set_avstop(int32_t value) {
  
  _impl_.avstop_ = value;
}
inline void RoadCount::set_avstop(int32_t value) {
  _internal_set_avstop(value);
  // @@protoc_insertion_point(field_set:perception.RoadCount.avStop)
}

// float speed85 = 13;
inline void RoadCount::clear_speed85() {
  _impl_.speed85_ = 0;
}
inline float RoadCount::_internal_speed85() const {
  return _impl_.speed85_;
}
inline float RoadCount::speed85() const {
  // @@protoc_insertion_point(field_get:perception.RoadCount.speed85)
  return _internal_speed85();
}
inline void RoadCount::_internal_set_speed85(float value) {
  
  _impl_.speed85_ = value;
}
inline void RoadCount::set_speed85(float value) {
  _internal_set_speed85(value);
  // @@protoc_insertion_point(field_set:perception.RoadCount.speed85)
}

// float queueLength = 14;
inline void RoadCount::clear_queuelength() {
  _impl_.queuelength_ = 0;
}
inline float RoadCount::_internal_queuelength() const {
  return _impl_.queuelength_;
}
inline float RoadCount::queuelength() const {
  // @@protoc_insertion_point(field_get:perception.RoadCount.queueLength)
  return _internal_queuelength();
}
inline void RoadCount::_internal_set_queuelength(float value) {
  
  _impl_.queuelength_ = value;
}
inline void RoadCount::set_queuelength(float value) {
  _internal_set_queuelength(value);
  // @@protoc_insertion_point(field_set:perception.RoadCount.queueLength)
}

// repeated .perception.LaneCount laneCount = 15;
inline int RoadCount::_internal_lanecount_size() const {
  return _impl_.lanecount_.size();
}
inline int RoadCount::lanecount_size() const {
  return _internal_lanecount_size();
}
inline void RoadCount::clear_lanecount() {
  _impl_.lanecount_.Clear();
}
inline ::perception::LaneCount* RoadCount::mutable_lanecount(int index) {
  // @@protoc_insertion_point(field_mutable:perception.RoadCount.laneCount)
  return _impl_.lanecount_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::perception::LaneCount >*
RoadCount::mutable_lanecount() {
  // @@protoc_insertion_point(field_mutable_list:perception.RoadCount.laneCount)
  return &_impl_.lanecount_;
}
inline const ::perception::LaneCount& RoadCount::_internal_lanecount(int index) const {
  return _impl_.lanecount_.Get(index);
}
inline const ::perception::LaneCount& RoadCount::lanecount(int index) const {
  // @@protoc_insertion_point(field_get:perception.RoadCount.laneCount)
  return _internal_lanecount(index);
}
inline ::perception::LaneCount* RoadCount::_internal_add_lanecount() {
  return _impl_.lanecount_.Add();
}
inline ::perception::LaneCount* RoadCount::add_lanecount() {
  ::perception::LaneCount* _add = _internal_add_lanecount();
  // @@protoc_insertion_point(field_add:perception.RoadCount.laneCount)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::perception::LaneCount >&
RoadCount::lanecount() const {
  // @@protoc_insertion_point(field_list:perception.RoadCount.laneCount)
  return _impl_.lanecount_;
}

// -------------------------------------------------------------------

// IntersectionCount

// int32 volume = 1;
inline void IntersectionCount::clear_volume() {
  _impl_.volume_ = 0;
}
inline int32_t IntersectionCount::_internal_volume() const {
  return _impl_.volume_;
}
inline int32_t IntersectionCount::volume() const {
  // @@protoc_insertion_point(field_get:perception.IntersectionCount.volume)
  return _internal_volume();
}
inline void IntersectionCount::_internal_set_volume(int32_t value) {
  
  _impl_.volume_ = value;
}
inline void IntersectionCount::set_volume(int32_t value) {
  _internal_set_volume(value);
  // @@protoc_insertion_point(field_set:perception.IntersectionCount.volume)
}

// int32 pcu = 2;
inline void IntersectionCount::clear_pcu() {
  _impl_.pcu_ = 0;
}
inline int32_t IntersectionCount::_internal_pcu() const {
  return _impl_.pcu_;
}
inline int32_t IntersectionCount::pcu() const {
  // @@protoc_insertion_point(field_get:perception.IntersectionCount.pcu)
  return _internal_pcu();
}
inline void IntersectionCount::_internal_set_pcu(int32_t value) {
  
  _impl_.pcu_ = value;
}
inline void IntersectionCount::set_pcu(int32_t value) {
  _internal_set_pcu(value);
  // @@protoc_insertion_point(field_set:perception.IntersectionCount.pcu)
}

// float avSpeed = 3;
inline void IntersectionCount::clear_avspeed() {
  _impl_.avspeed_ = 0;
}
inline float IntersectionCount::_internal_avspeed() const {
  return _impl_.avspeed_;
}
inline float IntersectionCount::avspeed() const {
  // @@protoc_insertion_point(field_get:perception.IntersectionCount.avSpeed)
  return _internal_avspeed();
}
inline void IntersectionCount::_internal_set_avspeed(float value) {
  
  _impl_.avspeed_ = value;
}
inline void IntersectionCount::set_avspeed(float value) {
  _internal_set_avspeed(value);
  // @@protoc_insertion_point(field_set:perception.IntersectionCount.avSpeed)
}

// float occupancy = 4;
inline void IntersectionCount::clear_occupancy() {
  _impl_.occupancy_ = 0;
}
inline float IntersectionCount::_internal_occupancy() const {
  return _impl_.occupancy_;
}
inline float IntersectionCount::occupancy() const {
  // @@protoc_insertion_point(field_get:perception.IntersectionCount.occupancy)
  return _internal_occupancy();
}
inline void IntersectionCount::_internal_set_occupancy(float value) {
  
  _impl_.occupancy_ = value;
}
inline void IntersectionCount::set_occupancy(float value) {
  _internal_set_occupancy(value);
  // @@protoc_insertion_point(field_set:perception.IntersectionCount.occupancy)
}

// float headWay = 5;
inline void IntersectionCount::clear_headway() {
  _impl_.headway_ = 0;
}
inline float IntersectionCount::_internal_headway() const {
  return _impl_.headway_;
}
inline float IntersectionCount::headway() const {
  // @@protoc_insertion_point(field_get:perception.IntersectionCount.headWay)
  return _internal_headway();
}
inline void IntersectionCount::_internal_set_headway(float value) {
  
  _impl_.headway_ = value;
}
inline void IntersectionCount::set_headway(float value) {
  _internal_set_headway(value);
  // @@protoc_insertion_point(field_set:perception.IntersectionCount.headWay)
}

// float gap = 6;
inline void IntersectionCount::clear_gap() {
  _impl_.gap_ = 0;
}
inline float IntersectionCount::_internal_gap() const {
  return _impl_.gap_;
}
inline float IntersectionCount::gap() const {
  // @@protoc_insertion_point(field_get:perception.IntersectionCount.gap)
  return _internal_gap();
}
inline void IntersectionCount::_internal_set_gap(float value) {
  
  _impl_.gap_ = value;
}
inline void IntersectionCount::set_gap(float value) {
  _internal_set_gap(value);
  // @@protoc_insertion_point(field_set:perception.IntersectionCount.gap)
}

// float avDelay = 7;
inline void IntersectionCount::clear_avdelay() {
  _impl_.avdelay_ = 0;
}
inline float IntersectionCount::_internal_avdelay() const {
  return _impl_.avdelay_;
}
inline float IntersectionCount::avdelay() const {
  // @@protoc_insertion_point(field_get:perception.IntersectionCount.avDelay)
  return _internal_avdelay();
}
inline void IntersectionCount::_internal_set_avdelay(float value) {
  
  _impl_.avdelay_ = value;
}
inline void IntersectionCount::set_avdelay(float value) {
  _internal_set_avdelay(value);
  // @@protoc_insertion_point(field_set:perception.IntersectionCount.avDelay)
}

// int32 avStop = 8;
inline void IntersectionCount::clear_avstop() {
  _impl_.avstop_ = 0;
}
inline int32_t IntersectionCount::_internal_avstop() const {
  return _impl_.avstop_;
}
inline int32_t IntersectionCount::avstop() const {
  // @@protoc_insertion_point(field_get:perception.IntersectionCount.avStop)
  return _internal_avstop();
}
inline void IntersectionCount::_internal_set_avstop(int32_t value) {
  
  _impl_.avstop_ = value;
}
inline void IntersectionCount::set_avstop(int32_t value) {
  _internal_set_avstop(value);
  // @@protoc_insertion_point(field_set:perception.IntersectionCount.avStop)
}

// float speed85 = 9;
inline void IntersectionCount::clear_speed85() {
  _impl_.speed85_ = 0;
}
inline float IntersectionCount::_internal_speed85() const {
  return _impl_.speed85_;
}
inline float IntersectionCount::speed85() const {
  // @@protoc_insertion_point(field_get:perception.IntersectionCount.speed85)
  return _internal_speed85();
}
inline void IntersectionCount::_internal_set_speed85(float value) {
  
  _impl_.speed85_ = value;
}
inline void IntersectionCount::set_speed85(float value) {
  _internal_set_speed85(value);
  // @@protoc_insertion_point(field_set:perception.IntersectionCount.speed85)
}

// float queueLength = 10;
inline void IntersectionCount::clear_queuelength() {
  _impl_.queuelength_ = 0;
}
inline float IntersectionCount::_internal_queuelength() const {
  return _impl_.queuelength_;
}
inline float IntersectionCount::queuelength() const {
  // @@protoc_insertion_point(field_get:perception.IntersectionCount.queueLength)
  return _internal_queuelength();
}
inline void IntersectionCount::_internal_set_queuelength(float value) {
  
  _impl_.queuelength_ = value;
}
inline void IntersectionCount::set_queuelength(float value) {
  _internal_set_queuelength(value);
  // @@protoc_insertion_point(field_set:perception.IntersectionCount.queueLength)
}

// repeated .perception.RoadCount roadCount = 11;
inline int IntersectionCount::_internal_roadcount_size() const {
  return _impl_.roadcount_.size();
}
inline int IntersectionCount::roadcount_size() const {
  return _internal_roadcount_size();
}
inline void IntersectionCount::clear_roadcount() {
  _impl_.roadcount_.Clear();
}
inline ::perception::RoadCount* IntersectionCount::mutable_roadcount(int index) {
  // @@protoc_insertion_point(field_mutable:perception.IntersectionCount.roadCount)
  return _impl_.roadcount_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::perception::RoadCount >*
IntersectionCount::mutable_roadcount() {
  // @@protoc_insertion_point(field_mutable_list:perception.IntersectionCount.roadCount)
  return &_impl_.roadcount_;
}
inline const ::perception::RoadCount& IntersectionCount::_internal_roadcount(int index) const {
  return _impl_.roadcount_.Get(index);
}
inline const ::perception::RoadCount& IntersectionCount::roadcount(int index) const {
  // @@protoc_insertion_point(field_get:perception.IntersectionCount.roadCount)
  return _internal_roadcount(index);
}
inline ::perception::RoadCount* IntersectionCount::_internal_add_roadcount() {
  return _impl_.roadcount_.Add();
}
inline ::perception::RoadCount* IntersectionCount::add_roadcount() {
  ::perception::RoadCount* _add = _internal_add_roadcount();
  // @@protoc_insertion_point(field_add:perception.IntersectionCount.roadCount)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::perception::RoadCount >&
IntersectionCount::roadcount() const {
  // @@protoc_insertion_point(field_list:perception.IntersectionCount.roadCount)
  return _impl_.roadcount_;
}

// -------------------------------------------------------------------

// Event

// int32 event_type = 1;
inline void Event::clear_event_type() {
  _impl_.event_type_ = 0;
}
inline int32_t Event::_internal_event_type() const {
  return _impl_.event_type_;
}
inline int32_t Event::event_type() const {
  // @@protoc_insertion_point(field_get:perception.Event.event_type)
  return _internal_event_type();
}
inline void Event::_internal_set_event_type(int32_t value) {
  
  _impl_.event_type_ = value;
}
inline void Event::set_event_type(int32_t value) {
  _internal_set_event_type(value);
  // @@protoc_insertion_point(field_set:perception.Event.event_type)
}

// int32 level = 2;
inline void Event::clear_level() {
  _impl_.level_ = 0;
}
inline int32_t Event::_internal_level() const {
  return _impl_.level_;
}
inline int32_t Event::level() const {
  // @@protoc_insertion_point(field_get:perception.Event.level)
  return _internal_level();
}
inline void Event::_internal_set_level(int32_t value) {
  
  _impl_.level_ = value;
}
inline void Event::set_level(int32_t value) {
  _internal_set_level(value);
  // @@protoc_insertion_point(field_set:perception.Event.level)
}

// string device_sn = 3;
inline void Event::clear_device_sn() {
  _impl_.device_sn_.ClearToEmpty();
}
inline const std::string& Event::device_sn() const {
  // @@protoc_insertion_point(field_get:perception.Event.device_sn)
  return _internal_device_sn();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void Event::set_device_sn(ArgT0&& arg0, ArgT... args) {
 
 _impl_.device_sn_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:perception.Event.device_sn)
}
inline std::string* Event::mutable_device_sn() {
  std::string* _s = _internal_mutable_device_sn();
  // @@protoc_insertion_point(field_mutable:perception.Event.device_sn)
  return _s;
}
inline const std::string& Event::_internal_device_sn() const {
  return _impl_.device_sn_.Get();
}
inline void Event::_internal_set_device_sn(const std::string& value) {
  
  _impl_.device_sn_.Set(value, GetArenaForAllocation());
}
inline std::string* Event::_internal_mutable_device_sn() {
  
  return _impl_.device_sn_.Mutable(GetArenaForAllocation());
}
inline std::string* Event::release_device_sn() {
  // @@protoc_insertion_point(field_release:perception.Event.device_sn)
  return _impl_.device_sn_.Release();
}
inline void Event::set_allocated_device_sn(std::string* device_sn) {
  if (device_sn != nullptr) {
    
  } else {
    
  }
  _impl_.device_sn_.SetAllocated(device_sn, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.device_sn_.IsDefault()) {
    _impl_.device_sn_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:perception.Event.device_sn)
}

// int32 lane_id = 4;
inline void Event::clear_lane_id() {
  _impl_.lane_id_ = 0;
}
inline int32_t Event::_internal_lane_id() const {
  return _impl_.lane_id_;
}
inline int32_t Event::lane_id() const {
  // @@protoc_insertion_point(field_get:perception.Event.lane_id)
  return _internal_lane_id();
}
inline void Event::_internal_set_lane_id(int32_t value) {
  
  _impl_.lane_id_ = value;
}
inline void Event::set_lane_id(int32_t value) {
  _internal_set_lane_id(value);
  // @@protoc_insertion_point(field_set:perception.Event.lane_id)
}

// int32 fusion_state = 5;
inline void Event::clear_fusion_state() {
  _impl_.fusion_state_ = 0;
}
inline int32_t Event::_internal_fusion_state() const {
  return _impl_.fusion_state_;
}
inline int32_t Event::fusion_state() const {
  // @@protoc_insertion_point(field_get:perception.Event.fusion_state)
  return _internal_fusion_state();
}
inline void Event::_internal_set_fusion_state(int32_t value) {
  
  _impl_.fusion_state_ = value;
}
inline void Event::set_fusion_state(int32_t value) {
  _internal_set_fusion_state(value);
  // @@protoc_insertion_point(field_set:perception.Event.fusion_state)
}

// int32 source = 6;
inline void Event::clear_source() {
  _impl_.source_ = 0;
}
inline int32_t Event::_internal_source() const {
  return _impl_.source_;
}
inline int32_t Event::source() const {
  // @@protoc_insertion_point(field_get:perception.Event.source)
  return _internal_source();
}
inline void Event::_internal_set_source(int32_t value) {
  
  _impl_.source_ = value;
}
inline void Event::set_source(int32_t value) {
  _internal_set_source(value);
  // @@protoc_insertion_point(field_set:perception.Event.source)
}

// repeated .perception.Position pos = 7;
inline int Event::_internal_pos_size() const {
  return _impl_.pos_.size();
}
inline int Event::pos_size() const {
  return _internal_pos_size();
}
inline void Event::clear_pos() {
  _impl_.pos_.Clear();
}
inline ::perception::Position* Event::mutable_pos(int index) {
  // @@protoc_insertion_point(field_mutable:perception.Event.pos)
  return _impl_.pos_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::perception::Position >*
Event::mutable_pos() {
  // @@protoc_insertion_point(field_mutable_list:perception.Event.pos)
  return &_impl_.pos_;
}
inline const ::perception::Position& Event::_internal_pos(int index) const {
  return _impl_.pos_.Get(index);
}
inline const ::perception::Position& Event::pos(int index) const {
  // @@protoc_insertion_point(field_get:perception.Event.pos)
  return _internal_pos(index);
}
inline ::perception::Position* Event::_internal_add_pos() {
  return _impl_.pos_.Add();
}
inline ::perception::Position* Event::add_pos() {
  ::perception::Position* _add = _internal_add_pos();
  // @@protoc_insertion_point(field_add:perception.Event.pos)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::perception::Position >&
Event::pos() const {
  // @@protoc_insertion_point(field_list:perception.Event.pos)
  return _impl_.pos_;
}

// string license_plate = 8;
inline void Event::clear_license_plate() {
  _impl_.license_plate_.ClearToEmpty();
}
inline const std::string& Event::license_plate() const {
  // @@protoc_insertion_point(field_get:perception.Event.license_plate)
  return _internal_license_plate();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void Event::set_license_plate(ArgT0&& arg0, ArgT... args) {
 
 _impl_.license_plate_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:perception.Event.license_plate)
}
inline std::string* Event::mutable_license_plate() {
  std::string* _s = _internal_mutable_license_plate();
  // @@protoc_insertion_point(field_mutable:perception.Event.license_plate)
  return _s;
}
inline const std::string& Event::_internal_license_plate() const {
  return _impl_.license_plate_.Get();
}
inline void Event::_internal_set_license_plate(const std::string& value) {
  
  _impl_.license_plate_.Set(value, GetArenaForAllocation());
}
inline std::string* Event::_internal_mutable_license_plate() {
  
  return _impl_.license_plate_.Mutable(GetArenaForAllocation());
}
inline std::string* Event::release_license_plate() {
  // @@protoc_insertion_point(field_release:perception.Event.license_plate)
  return _impl_.license_plate_.Release();
}
inline void Event::set_allocated_license_plate(std::string* license_plate) {
  if (license_plate != nullptr) {
    
  } else {
    
  }
  _impl_.license_plate_.SetAllocated(license_plate, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.license_plate_.IsDefault()) {
    _impl_.license_plate_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:perception.Event.license_plate)
}

// int64 track_time = 9;
inline void Event::clear_track_time() {
  _impl_.track_time_ = int64_t{0};
}
inline int64_t Event::_internal_track_time() const {
  return _impl_.track_time_;
}
inline int64_t Event::track_time() const {
  // @@protoc_insertion_point(field_get:perception.Event.track_time)
  return _internal_track_time();
}
inline void Event::_internal_set_track_time(int64_t value) {
  
  _impl_.track_time_ = value;
}
inline void Event::set_track_time(int64_t value) {
  _internal_set_track_time(value);
  // @@protoc_insertion_point(field_set:perception.Event.track_time)
}

// int64 ttl = 10;
inline void Event::clear_ttl() {
  _impl_.ttl_ = int64_t{0};
}
inline int64_t Event::_internal_ttl() const {
  return _impl_.ttl_;
}
inline int64_t Event::ttl() const {
  // @@protoc_insertion_point(field_get:perception.Event.ttl)
  return _internal_ttl();
}
inline void Event::_internal_set_ttl(int64_t value) {
  
  _impl_.ttl_ = value;
}
inline void Event::set_ttl(int64_t value) {
  _internal_set_ttl(value);
  // @@protoc_insertion_point(field_set:perception.Event.ttl)
}

// repeated .perception.AreaInfo refpath = 11;
inline int Event::_internal_refpath_size() const {
  return _impl_.refpath_.size();
}
inline int Event::refpath_size() const {
  return _internal_refpath_size();
}
inline void Event::clear_refpath() {
  _impl_.refpath_.Clear();
}
inline ::perception::AreaInfo* Event::mutable_refpath(int index) {
  // @@protoc_insertion_point(field_mutable:perception.Event.refpath)
  return _impl_.refpath_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::perception::AreaInfo >*
Event::mutable_refpath() {
  // @@protoc_insertion_point(field_mutable_list:perception.Event.refpath)
  return &_impl_.refpath_;
}
inline const ::perception::AreaInfo& Event::_internal_refpath(int index) const {
  return _impl_.refpath_.Get(index);
}
inline const ::perception::AreaInfo& Event::refpath(int index) const {
  // @@protoc_insertion_point(field_get:perception.Event.refpath)
  return _internal_refpath(index);
}
inline ::perception::AreaInfo* Event::_internal_add_refpath() {
  return _impl_.refpath_.Add();
}
inline ::perception::AreaInfo* Event::add_refpath() {
  ::perception::AreaInfo* _add = _internal_add_refpath();
  // @@protoc_insertion_point(field_add:perception.Event.refpath)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::perception::AreaInfo >&
Event::refpath() const {
  // @@protoc_insertion_point(field_list:perception.Event.refpath)
  return _impl_.refpath_;
}

// string string_data = 12;
inline bool Event::_internal_has_string_data() const {
  return DataType_case() == kStringData;
}
inline bool Event::has_string_data() const {
  return _internal_has_string_data();
}
inline void Event::set_has_string_data() {
  _impl_._oneof_case_[0] = kStringData;
}
inline void Event::clear_string_data() {
  if (_internal_has_string_data()) {
    _impl_.DataType_.string_data_.Destroy();
    clear_has_DataType();
  }
}
inline const std::string& Event::string_data() const {
  // @@protoc_insertion_point(field_get:perception.Event.string_data)
  return _internal_string_data();
}
template <typename ArgT0, typename... ArgT>
inline void Event::set_string_data(ArgT0&& arg0, ArgT... args) {
  if (!_internal_has_string_data()) {
    clear_DataType();
    set_has_string_data();
    _impl_.DataType_.string_data_.InitDefault();
  }
  _impl_.DataType_.string_data_.Set( static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:perception.Event.string_data)
}
inline std::string* Event::mutable_string_data() {
  std::string* _s = _internal_mutable_string_data();
  // @@protoc_insertion_point(field_mutable:perception.Event.string_data)
  return _s;
}
inline const std::string& Event::_internal_string_data() const {
  if (_internal_has_string_data()) {
    return _impl_.DataType_.string_data_.Get();
  }
  return ::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited();
}
inline void Event::_internal_set_string_data(const std::string& value) {
  if (!_internal_has_string_data()) {
    clear_DataType();
    set_has_string_data();
    _impl_.DataType_.string_data_.InitDefault();
  }
  _impl_.DataType_.string_data_.Set(value, GetArenaForAllocation());
}
inline std::string* Event::_internal_mutable_string_data() {
  if (!_internal_has_string_data()) {
    clear_DataType();
    set_has_string_data();
    _impl_.DataType_.string_data_.InitDefault();
  }
  return _impl_.DataType_.string_data_.Mutable(      GetArenaForAllocation());
}
inline std::string* Event::release_string_data() {
  // @@protoc_insertion_point(field_release:perception.Event.string_data)
  if (_internal_has_string_data()) {
    clear_has_DataType();
    return _impl_.DataType_.string_data_.Release();
  } else {
    return nullptr;
  }
}
inline void Event::set_allocated_string_data(std::string* string_data) {
  if (has_DataType()) {
    clear_DataType();
  }
  if (string_data != nullptr) {
    set_has_string_data();
    _impl_.DataType_.string_data_.InitAllocated(string_data, GetArenaForAllocation());
  }
  // @@protoc_insertion_point(field_set_allocated:perception.Event.string_data)
}

// int32 int_data = 13;
inline bool Event::_internal_has_int_data() const {
  return DataType_case() == kIntData;
}
inline bool Event::has_int_data() const {
  return _internal_has_int_data();
}
inline void Event::set_has_int_data() {
  _impl_._oneof_case_[0] = kIntData;
}
inline void Event::clear_int_data() {
  if (_internal_has_int_data()) {
    _impl_.DataType_.int_data_ = 0;
    clear_has_DataType();
  }
}
inline int32_t Event::_internal_int_data() const {
  if (_internal_has_int_data()) {
    return _impl_.DataType_.int_data_;
  }
  return 0;
}
inline void Event::_internal_set_int_data(int32_t value) {
  if (!_internal_has_int_data()) {
    clear_DataType();
    set_has_int_data();
  }
  _impl_.DataType_.int_data_ = value;
}
inline int32_t Event::int_data() const {
  // @@protoc_insertion_point(field_get:perception.Event.int_data)
  return _internal_int_data();
}
inline void Event::set_int_data(int32_t value) {
  _internal_set_int_data(value);
  // @@protoc_insertion_point(field_set:perception.Event.int_data)
}

inline bool Event::has_DataType() const {
  return DataType_case() != DATATYPE_NOT_SET;
}
inline void Event::clear_has_DataType() {
  _impl_._oneof_case_[0] = DATATYPE_NOT_SET;
}
inline Event::DataTypeCase Event::DataType_case() const {
  return Event::DataTypeCase(_impl_._oneof_case_[0]);
}
// -------------------------------------------------------------------

// Events

// repeated .perception.Event event = 1;
inline int Events::_internal_event_size() const {
  return _impl_.event_.size();
}
inline int Events::event_size() const {
  return _internal_event_size();
}
inline void Events::clear_event() {
  _impl_.event_.Clear();
}
inline ::perception::Event* Events::mutable_event(int index) {
  // @@protoc_insertion_point(field_mutable:perception.Events.event)
  return _impl_.event_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::perception::Event >*
Events::mutable_event() {
  // @@protoc_insertion_point(field_mutable_list:perception.Events.event)
  return &_impl_.event_;
}
inline const ::perception::Event& Events::_internal_event(int index) const {
  return _impl_.event_.Get(index);
}
inline const ::perception::Event& Events::event(int index) const {
  // @@protoc_insertion_point(field_get:perception.Events.event)
  return _internal_event(index);
}
inline ::perception::Event* Events::_internal_add_event() {
  return _impl_.event_.Add();
}
inline ::perception::Event* Events::add_event() {
  ::perception::Event* _add = _internal_add_event();
  // @@protoc_insertion_point(field_add:perception.Events.event)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::perception::Event >&
Events::event() const {
  // @@protoc_insertion_point(field_list:perception.Events.event)
  return _impl_.event_;
}

// -------------------------------------------------------------------

// HeartBeat

// int32 seq_count = 1;
inline void HeartBeat::clear_seq_count() {
  _impl_.seq_count_ = 0;
}
inline int32_t HeartBeat::_internal_seq_count() const {
  return _impl_.seq_count_;
}
inline int32_t HeartBeat::seq_count() const {
  // @@protoc_insertion_point(field_get:perception.HeartBeat.seq_count)
  return _internal_seq_count();
}
inline void HeartBeat::_internal_set_seq_count(int32_t value) {
  
  _impl_.seq_count_ = value;
}
inline void HeartBeat::set_seq_count(int32_t value) {
  _internal_set_seq_count(value);
  // @@protoc_insertion_point(field_set:perception.HeartBeat.seq_count)
}

// int64 time = 2;
inline void HeartBeat::clear_time() {
  _impl_.time_ = int64_t{0};
}
inline int64_t HeartBeat::_internal_time() const {
  return _impl_.time_;
}
inline int64_t HeartBeat::time() const {
  // @@protoc_insertion_point(field_get:perception.HeartBeat.time)
  return _internal_time();
}
inline void HeartBeat::_internal_set_time(int64_t value) {
  
  _impl_.time_ = value;
}
inline void HeartBeat::set_time(int64_t value) {
  _internal_set_time(value);
  // @@protoc_insertion_point(field_set:perception.HeartBeat.time)
}

// string device_sn = 3;
inline void HeartBeat::clear_device_sn() {
  _impl_.device_sn_.ClearToEmpty();
}
inline const std::string& HeartBeat::device_sn() const {
  // @@protoc_insertion_point(field_get:perception.HeartBeat.device_sn)
  return _internal_device_sn();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void HeartBeat::set_device_sn(ArgT0&& arg0, ArgT... args) {
 
 _impl_.device_sn_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:perception.HeartBeat.device_sn)
}
inline std::string* HeartBeat::mutable_device_sn() {
  std::string* _s = _internal_mutable_device_sn();
  // @@protoc_insertion_point(field_mutable:perception.HeartBeat.device_sn)
  return _s;
}
inline const std::string& HeartBeat::_internal_device_sn() const {
  return _impl_.device_sn_.Get();
}
inline void HeartBeat::_internal_set_device_sn(const std::string& value) {
  
  _impl_.device_sn_.Set(value, GetArenaForAllocation());
}
inline std::string* HeartBeat::_internal_mutable_device_sn() {
  
  return _impl_.device_sn_.Mutable(GetArenaForAllocation());
}
inline std::string* HeartBeat::release_device_sn() {
  // @@protoc_insertion_point(field_release:perception.HeartBeat.device_sn)
  return _impl_.device_sn_.Release();
}
inline void HeartBeat::set_allocated_device_sn(std::string* device_sn) {
  if (device_sn != nullptr) {
    
  } else {
    
  }
  _impl_.device_sn_.SetAllocated(device_sn, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.device_sn_.IsDefault()) {
    _impl_.device_sn_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:perception.HeartBeat.device_sn)
}

// repeated int32 err_code = 4;
inline int HeartBeat::_internal_err_code_size() const {
  return _impl_.err_code_.size();
}
inline int HeartBeat::err_code_size() const {
  return _internal_err_code_size();
}
inline void HeartBeat::clear_err_code() {
  _impl_.err_code_.Clear();
}
inline int32_t HeartBeat::_internal_err_code(int index) const {
  return _impl_.err_code_.Get(index);
}
inline int32_t HeartBeat::err_code(int index) const {
  // @@protoc_insertion_point(field_get:perception.HeartBeat.err_code)
  return _internal_err_code(index);
}
inline void HeartBeat::set_err_code(int index, int32_t value) {
  _impl_.err_code_.Set(index, value);
  // @@protoc_insertion_point(field_set:perception.HeartBeat.err_code)
}
inline void HeartBeat::_internal_add_err_code(int32_t value) {
  _impl_.err_code_.Add(value);
}
inline void HeartBeat::add_err_code(int32_t value) {
  _internal_add_err_code(value);
  // @@protoc_insertion_point(field_add:perception.HeartBeat.err_code)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >&
HeartBeat::_internal_err_code() const {
  return _impl_.err_code_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >&
HeartBeat::err_code() const {
  // @@protoc_insertion_point(field_list:perception.HeartBeat.err_code)
  return _internal_err_code();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >*
HeartBeat::_internal_mutable_err_code() {
  return &_impl_.err_code_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >*
HeartBeat::mutable_err_code() {
  // @@protoc_insertion_point(field_mutable_list:perception.HeartBeat.err_code)
  return _internal_mutable_err_code();
}

// -------------------------------------------------------------------

// DeviceInfo

// string device_sn = 1;
inline void DeviceInfo::clear_device_sn() {
  _impl_.device_sn_.ClearToEmpty();
}
inline const std::string& DeviceInfo::device_sn() const {
  // @@protoc_insertion_point(field_get:perception.DeviceInfo.device_sn)
  return _internal_device_sn();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void DeviceInfo::set_device_sn(ArgT0&& arg0, ArgT... args) {
 
 _impl_.device_sn_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:perception.DeviceInfo.device_sn)
}
inline std::string* DeviceInfo::mutable_device_sn() {
  std::string* _s = _internal_mutable_device_sn();
  // @@protoc_insertion_point(field_mutable:perception.DeviceInfo.device_sn)
  return _s;
}
inline const std::string& DeviceInfo::_internal_device_sn() const {
  return _impl_.device_sn_.Get();
}
inline void DeviceInfo::_internal_set_device_sn(const std::string& value) {
  
  _impl_.device_sn_.Set(value, GetArenaForAllocation());
}
inline std::string* DeviceInfo::_internal_mutable_device_sn() {
  
  return _impl_.device_sn_.Mutable(GetArenaForAllocation());
}
inline std::string* DeviceInfo::release_device_sn() {
  // @@protoc_insertion_point(field_release:perception.DeviceInfo.device_sn)
  return _impl_.device_sn_.Release();
}
inline void DeviceInfo::set_allocated_device_sn(std::string* device_sn) {
  if (device_sn != nullptr) {
    
  } else {
    
  }
  _impl_.device_sn_.SetAllocated(device_sn, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.device_sn_.IsDefault()) {
    _impl_.device_sn_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:perception.DeviceInfo.device_sn)
}

// .perception.Position location = 2;
inline bool DeviceInfo::_internal_has_location() const {
  return this != internal_default_instance() && _impl_.location_ != nullptr;
}
inline bool DeviceInfo::has_location() const {
  return _internal_has_location();
}
inline void DeviceInfo::clear_location() {
  if (GetArenaForAllocation() == nullptr && _impl_.location_ != nullptr) {
    delete _impl_.location_;
  }
  _impl_.location_ = nullptr;
}
inline const ::perception::Position& DeviceInfo::_internal_location() const {
  const ::perception::Position* p = _impl_.location_;
  return p != nullptr ? *p : reinterpret_cast<const ::perception::Position&>(
      ::perception::_Position_default_instance_);
}
inline const ::perception::Position& DeviceInfo::location() const {
  // @@protoc_insertion_point(field_get:perception.DeviceInfo.location)
  return _internal_location();
}
inline void DeviceInfo::unsafe_arena_set_allocated_location(
    ::perception::Position* location) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.location_);
  }
  _impl_.location_ = location;
  if (location) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:perception.DeviceInfo.location)
}
inline ::perception::Position* DeviceInfo::release_location() {
  
  ::perception::Position* temp = _impl_.location_;
  _impl_.location_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::perception::Position* DeviceInfo::unsafe_arena_release_location() {
  // @@protoc_insertion_point(field_release:perception.DeviceInfo.location)
  
  ::perception::Position* temp = _impl_.location_;
  _impl_.location_ = nullptr;
  return temp;
}
inline ::perception::Position* DeviceInfo::_internal_mutable_location() {
  
  if (_impl_.location_ == nullptr) {
    auto* p = CreateMaybeMessage<::perception::Position>(GetArenaForAllocation());
    _impl_.location_ = p;
  }
  return _impl_.location_;
}
inline ::perception::Position* DeviceInfo::mutable_location() {
  ::perception::Position* _msg = _internal_mutable_location();
  // @@protoc_insertion_point(field_mutable:perception.DeviceInfo.location)
  return _msg;
}
inline void DeviceInfo::set_allocated_location(::perception::Position* location) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.location_;
  }
  if (location) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(location);
    if (message_arena != submessage_arena) {
      location = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, location, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.location_ = location;
  // @@protoc_insertion_point(field_set_allocated:perception.DeviceInfo.location)
}

// float orientation = 3;
inline void DeviceInfo::clear_orientation() {
  _impl_.orientation_ = 0;
}
inline float DeviceInfo::_internal_orientation() const {
  return _impl_.orientation_;
}
inline float DeviceInfo::orientation() const {
  // @@protoc_insertion_point(field_get:perception.DeviceInfo.orientation)
  return _internal_orientation();
}
inline void DeviceInfo::_internal_set_orientation(float value) {
  
  _impl_.orientation_ = value;
}
inline void DeviceInfo::set_orientation(float value) {
  _internal_set_orientation(value);
  // @@protoc_insertion_point(field_set:perception.DeviceInfo.orientation)
}

// int32 type = 4;
inline void DeviceInfo::clear_type() {
  _impl_.type_ = 0;
}
inline int32_t DeviceInfo::_internal_type() const {
  return _impl_.type_;
}
inline int32_t DeviceInfo::type() const {
  // @@protoc_insertion_point(field_get:perception.DeviceInfo.type)
  return _internal_type();
}
inline void DeviceInfo::_internal_set_type(int32_t value) {
  
  _impl_.type_ = value;
}
inline void DeviceInfo::set_type(int32_t value) {
  _internal_set_type(value);
  // @@protoc_insertion_point(field_set:perception.DeviceInfo.type)
}

// repeated .perception.AreaInfo perceptual_area = 5;
inline int DeviceInfo::_internal_perceptual_area_size() const {
  return _impl_.perceptual_area_.size();
}
inline int DeviceInfo::perceptual_area_size() const {
  return _internal_perceptual_area_size();
}
inline void DeviceInfo::clear_perceptual_area() {
  _impl_.perceptual_area_.Clear();
}
inline ::perception::AreaInfo* DeviceInfo::mutable_perceptual_area(int index) {
  // @@protoc_insertion_point(field_mutable:perception.DeviceInfo.perceptual_area)
  return _impl_.perceptual_area_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::perception::AreaInfo >*
DeviceInfo::mutable_perceptual_area() {
  // @@protoc_insertion_point(field_mutable_list:perception.DeviceInfo.perceptual_area)
  return &_impl_.perceptual_area_;
}
inline const ::perception::AreaInfo& DeviceInfo::_internal_perceptual_area(int index) const {
  return _impl_.perceptual_area_.Get(index);
}
inline const ::perception::AreaInfo& DeviceInfo::perceptual_area(int index) const {
  // @@protoc_insertion_point(field_get:perception.DeviceInfo.perceptual_area)
  return _internal_perceptual_area(index);
}
inline ::perception::AreaInfo* DeviceInfo::_internal_add_perceptual_area() {
  return _impl_.perceptual_area_.Add();
}
inline ::perception::AreaInfo* DeviceInfo::add_perceptual_area() {
  ::perception::AreaInfo* _add = _internal_add_perceptual_area();
  // @@protoc_insertion_point(field_add:perception.DeviceInfo.perceptual_area)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::perception::AreaInfo >&
DeviceInfo::perceptual_area() const {
  // @@protoc_insertion_point(field_list:perception.DeviceInfo.perceptual_area)
  return _impl_.perceptual_area_;
}

// string description = 6;
inline void DeviceInfo::clear_description() {
  _impl_.description_.ClearToEmpty();
}
inline const std::string& DeviceInfo::description() const {
  // @@protoc_insertion_point(field_get:perception.DeviceInfo.description)
  return _internal_description();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void DeviceInfo::set_description(ArgT0&& arg0, ArgT... args) {
 
 _impl_.description_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:perception.DeviceInfo.description)
}
inline std::string* DeviceInfo::mutable_description() {
  std::string* _s = _internal_mutable_description();
  // @@protoc_insertion_point(field_mutable:perception.DeviceInfo.description)
  return _s;
}
inline const std::string& DeviceInfo::_internal_description() const {
  return _impl_.description_.Get();
}
inline void DeviceInfo::_internal_set_description(const std::string& value) {
  
  _impl_.description_.Set(value, GetArenaForAllocation());
}
inline std::string* DeviceInfo::_internal_mutable_description() {
  
  return _impl_.description_.Mutable(GetArenaForAllocation());
}
inline std::string* DeviceInfo::release_description() {
  // @@protoc_insertion_point(field_release:perception.DeviceInfo.description)
  return _impl_.description_.Release();
}
inline void DeviceInfo::set_allocated_description(std::string* description) {
  if (description != nullptr) {
    
  } else {
    
  }
  _impl_.description_.SetAllocated(description, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.description_.IsDefault()) {
    _impl_.description_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:perception.DeviceInfo.description)
}

// -------------------------------------------------------------------

// RoadCounts

// string rsuId = 1;
inline void RoadCounts::clear_rsuid() {
  _impl_.rsuid_.ClearToEmpty();
}
inline const std::string& RoadCounts::rsuid() const {
  // @@protoc_insertion_point(field_get:perception.RoadCounts.rsuId)
  return _internal_rsuid();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void RoadCounts::set_rsuid(ArgT0&& arg0, ArgT... args) {
 
 _impl_.rsuid_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:perception.RoadCounts.rsuId)
}
inline std::string* RoadCounts::mutable_rsuid() {
  std::string* _s = _internal_mutable_rsuid();
  // @@protoc_insertion_point(field_mutable:perception.RoadCounts.rsuId)
  return _s;
}
inline const std::string& RoadCounts::_internal_rsuid() const {
  return _impl_.rsuid_.Get();
}
inline void RoadCounts::_internal_set_rsuid(const std::string& value) {
  
  _impl_.rsuid_.Set(value, GetArenaForAllocation());
}
inline std::string* RoadCounts::_internal_mutable_rsuid() {
  
  return _impl_.rsuid_.Mutable(GetArenaForAllocation());
}
inline std::string* RoadCounts::release_rsuid() {
  // @@protoc_insertion_point(field_release:perception.RoadCounts.rsuId)
  return _impl_.rsuid_.Release();
}
inline void RoadCounts::set_allocated_rsuid(std::string* rsuid) {
  if (rsuid != nullptr) {
    
  } else {
    
  }
  _impl_.rsuid_.SetAllocated(rsuid, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.rsuid_.IsDefault()) {
    _impl_.rsuid_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:perception.RoadCounts.rsuId)
}

// string seqNum = 2;
inline void RoadCounts::clear_seqnum() {
  _impl_.seqnum_.ClearToEmpty();
}
inline const std::string& RoadCounts::seqnum() const {
  // @@protoc_insertion_point(field_get:perception.RoadCounts.seqNum)
  return _internal_seqnum();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void RoadCounts::set_seqnum(ArgT0&& arg0, ArgT... args) {
 
 _impl_.seqnum_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:perception.RoadCounts.seqNum)
}
inline std::string* RoadCounts::mutable_seqnum() {
  std::string* _s = _internal_mutable_seqnum();
  // @@protoc_insertion_point(field_mutable:perception.RoadCounts.seqNum)
  return _s;
}
inline const std::string& RoadCounts::_internal_seqnum() const {
  return _impl_.seqnum_.Get();
}
inline void RoadCounts::_internal_set_seqnum(const std::string& value) {
  
  _impl_.seqnum_.Set(value, GetArenaForAllocation());
}
inline std::string* RoadCounts::_internal_mutable_seqnum() {
  
  return _impl_.seqnum_.Mutable(GetArenaForAllocation());
}
inline std::string* RoadCounts::release_seqnum() {
  // @@protoc_insertion_point(field_release:perception.RoadCounts.seqNum)
  return _impl_.seqnum_.Release();
}
inline void RoadCounts::set_allocated_seqnum(std::string* seqnum) {
  if (seqnum != nullptr) {
    
  } else {
    
  }
  _impl_.seqnum_.SetAllocated(seqnum, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.seqnum_.IsDefault()) {
    _impl_.seqnum_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:perception.RoadCounts.seqNum)
}

// string rsuEsn = 3;
inline void RoadCounts::clear_rsuesn() {
  _impl_.rsuesn_.ClearToEmpty();
}
inline const std::string& RoadCounts::rsuesn() const {
  // @@protoc_insertion_point(field_get:perception.RoadCounts.rsuEsn)
  return _internal_rsuesn();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void RoadCounts::set_rsuesn(ArgT0&& arg0, ArgT... args) {
 
 _impl_.rsuesn_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:perception.RoadCounts.rsuEsn)
}
inline std::string* RoadCounts::mutable_rsuesn() {
  std::string* _s = _internal_mutable_rsuesn();
  // @@protoc_insertion_point(field_mutable:perception.RoadCounts.rsuEsn)
  return _s;
}
inline const std::string& RoadCounts::_internal_rsuesn() const {
  return _impl_.rsuesn_.Get();
}
inline void RoadCounts::_internal_set_rsuesn(const std::string& value) {
  
  _impl_.rsuesn_.Set(value, GetArenaForAllocation());
}
inline std::string* RoadCounts::_internal_mutable_rsuesn() {
  
  return _impl_.rsuesn_.Mutable(GetArenaForAllocation());
}
inline std::string* RoadCounts::release_rsuesn() {
  // @@protoc_insertion_point(field_release:perception.RoadCounts.rsuEsn)
  return _impl_.rsuesn_.Release();
}
inline void RoadCounts::set_allocated_rsuesn(std::string* rsuesn) {
  if (rsuesn != nullptr) {
    
  } else {
    
  }
  _impl_.rsuesn_.SetAllocated(rsuesn, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.rsuesn_.IsDefault()) {
    _impl_.rsuesn_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:perception.RoadCounts.rsuEsn)
}

// string protocolVersion = 4;
inline void RoadCounts::clear_protocolversion() {
  _impl_.protocolversion_.ClearToEmpty();
}
inline const std::string& RoadCounts::protocolversion() const {
  // @@protoc_insertion_point(field_get:perception.RoadCounts.protocolVersion)
  return _internal_protocolversion();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void RoadCounts::set_protocolversion(ArgT0&& arg0, ArgT... args) {
 
 _impl_.protocolversion_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:perception.RoadCounts.protocolVersion)
}
inline std::string* RoadCounts::mutable_protocolversion() {
  std::string* _s = _internal_mutable_protocolversion();
  // @@protoc_insertion_point(field_mutable:perception.RoadCounts.protocolVersion)
  return _s;
}
inline const std::string& RoadCounts::_internal_protocolversion() const {
  return _impl_.protocolversion_.Get();
}
inline void RoadCounts::_internal_set_protocolversion(const std::string& value) {
  
  _impl_.protocolversion_.Set(value, GetArenaForAllocation());
}
inline std::string* RoadCounts::_internal_mutable_protocolversion() {
  
  return _impl_.protocolversion_.Mutable(GetArenaForAllocation());
}
inline std::string* RoadCounts::release_protocolversion() {
  // @@protoc_insertion_point(field_release:perception.RoadCounts.protocolVersion)
  return _impl_.protocolversion_.Release();
}
inline void RoadCounts::set_allocated_protocolversion(std::string* protocolversion) {
  if (protocolversion != nullptr) {
    
  } else {
    
  }
  _impl_.protocolversion_.SetAllocated(protocolversion, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.protocolversion_.IsDefault()) {
    _impl_.protocolversion_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:perception.RoadCounts.protocolVersion)
}

// int32 cycle = 5;
inline void RoadCounts::clear_cycle() {
  _impl_.cycle_ = 0;
}
inline int32_t RoadCounts::_internal_cycle() const {
  return _impl_.cycle_;
}
inline int32_t RoadCounts::cycle() const {
  // @@protoc_insertion_point(field_get:perception.RoadCounts.cycle)
  return _internal_cycle();
}
inline void RoadCounts::_internal_set_cycle(int32_t value) {
  
  _impl_.cycle_ = value;
}
inline void RoadCounts::set_cycle(int32_t value) {
  _internal_set_cycle(value);
  // @@protoc_insertion_point(field_set:perception.RoadCounts.cycle)
}

// .perception.IntersectionCount intersectionCount = 6;
inline bool RoadCounts::_internal_has_intersectioncount() const {
  return this != internal_default_instance() && _impl_.intersectioncount_ != nullptr;
}
inline bool RoadCounts::has_intersectioncount() const {
  return _internal_has_intersectioncount();
}
inline void RoadCounts::clear_intersectioncount() {
  if (GetArenaForAllocation() == nullptr && _impl_.intersectioncount_ != nullptr) {
    delete _impl_.intersectioncount_;
  }
  _impl_.intersectioncount_ = nullptr;
}
inline const ::perception::IntersectionCount& RoadCounts::_internal_intersectioncount() const {
  const ::perception::IntersectionCount* p = _impl_.intersectioncount_;
  return p != nullptr ? *p : reinterpret_cast<const ::perception::IntersectionCount&>(
      ::perception::_IntersectionCount_default_instance_);
}
inline const ::perception::IntersectionCount& RoadCounts::intersectioncount() const {
  // @@protoc_insertion_point(field_get:perception.RoadCounts.intersectionCount)
  return _internal_intersectioncount();
}
inline void RoadCounts::unsafe_arena_set_allocated_intersectioncount(
    ::perception::IntersectionCount* intersectioncount) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.intersectioncount_);
  }
  _impl_.intersectioncount_ = intersectioncount;
  if (intersectioncount) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:perception.RoadCounts.intersectionCount)
}
inline ::perception::IntersectionCount* RoadCounts::release_intersectioncount() {
  
  ::perception::IntersectionCount* temp = _impl_.intersectioncount_;
  _impl_.intersectioncount_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::perception::IntersectionCount* RoadCounts::unsafe_arena_release_intersectioncount() {
  // @@protoc_insertion_point(field_release:perception.RoadCounts.intersectionCount)
  
  ::perception::IntersectionCount* temp = _impl_.intersectioncount_;
  _impl_.intersectioncount_ = nullptr;
  return temp;
}
inline ::perception::IntersectionCount* RoadCounts::_internal_mutable_intersectioncount() {
  
  if (_impl_.intersectioncount_ == nullptr) {
    auto* p = CreateMaybeMessage<::perception::IntersectionCount>(GetArenaForAllocation());
    _impl_.intersectioncount_ = p;
  }
  return _impl_.intersectioncount_;
}
inline ::perception::IntersectionCount* RoadCounts::mutable_intersectioncount() {
  ::perception::IntersectionCount* _msg = _internal_mutable_intersectioncount();
  // @@protoc_insertion_point(field_mutable:perception.RoadCounts.intersectionCount)
  return _msg;
}
inline void RoadCounts::set_allocated_intersectioncount(::perception::IntersectionCount* intersectioncount) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.intersectioncount_;
  }
  if (intersectioncount) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(intersectioncount);
    if (message_arena != submessage_arena) {
      intersectioncount = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, intersectioncount, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.intersectioncount_ = intersectioncount;
  // @@protoc_insertion_point(field_set_allocated:perception.RoadCounts.intersectionCount)
}

// -------------------------------------------------------------------

// LaneCountInfo

// int32 laneNo = 1;
inline void LaneCountInfo::clear_laneno() {
  _impl_.laneno_ = 0;
}
inline int32_t LaneCountInfo::_internal_laneno() const {
  return _impl_.laneno_;
}
inline int32_t LaneCountInfo::laneno() const {
  // @@protoc_insertion_point(field_get:perception.LaneCountInfo.laneNo)
  return _internal_laneno();
}
inline void LaneCountInfo::_internal_set_laneno(int32_t value) {
  
  _impl_.laneno_ = value;
}
inline void LaneCountInfo::set_laneno(int32_t value) {
  _internal_set_laneno(value);
  // @@protoc_insertion_point(field_set:perception.LaneCountInfo.laneNo)
}

// int32 volume1 = 2;
inline void LaneCountInfo::clear_volume1() {
  _impl_.volume1_ = 0;
}
inline int32_t LaneCountInfo::_internal_volume1() const {
  return _impl_.volume1_;
}
inline int32_t LaneCountInfo::volume1() const {
  // @@protoc_insertion_point(field_get:perception.LaneCountInfo.volume1)
  return _internal_volume1();
}
inline void LaneCountInfo::_internal_set_volume1(int32_t value) {
  
  _impl_.volume1_ = value;
}
inline void LaneCountInfo::set_volume1(int32_t value) {
  _internal_set_volume1(value);
  // @@protoc_insertion_point(field_set:perception.LaneCountInfo.volume1)
}

// int32 volume2 = 3;
inline void LaneCountInfo::clear_volume2() {
  _impl_.volume2_ = 0;
}
inline int32_t LaneCountInfo::_internal_volume2() const {
  return _impl_.volume2_;
}
inline int32_t LaneCountInfo::volume2() const {
  // @@protoc_insertion_point(field_get:perception.LaneCountInfo.volume2)
  return _internal_volume2();
}
inline void LaneCountInfo::_internal_set_volume2(int32_t value) {
  
  _impl_.volume2_ = value;
}
inline void LaneCountInfo::set_volume2(int32_t value) {
  _internal_set_volume2(value);
  // @@protoc_insertion_point(field_set:perception.LaneCountInfo.volume2)
}

// int32 volume3 = 4;
inline void LaneCountInfo::clear_volume3() {
  _impl_.volume3_ = 0;
}
inline int32_t LaneCountInfo::_internal_volume3() const {
  return _impl_.volume3_;
}
inline int32_t LaneCountInfo::volume3() const {
  // @@protoc_insertion_point(field_get:perception.LaneCountInfo.volume3)
  return _internal_volume3();
}
inline void LaneCountInfo::_internal_set_volume3(int32_t value) {
  
  _impl_.volume3_ = value;
}
inline void LaneCountInfo::set_volume3(int32_t value) {
  _internal_set_volume3(value);
  // @@protoc_insertion_point(field_set:perception.LaneCountInfo.volume3)
}

// int32 volume4 = 5;
inline void LaneCountInfo::clear_volume4() {
  _impl_.volume4_ = 0;
}
inline int32_t LaneCountInfo::_internal_volume4() const {
  return _impl_.volume4_;
}
inline int32_t LaneCountInfo::volume4() const {
  // @@protoc_insertion_point(field_get:perception.LaneCountInfo.volume4)
  return _internal_volume4();
}
inline void LaneCountInfo::_internal_set_volume4(int32_t value) {
  
  _impl_.volume4_ = value;
}
inline void LaneCountInfo::set_volume4(int32_t value) {
  _internal_set_volume4(value);
  // @@protoc_insertion_point(field_set:perception.LaneCountInfo.volume4)
}

// int32 volume5 = 6;
inline void LaneCountInfo::clear_volume5() {
  _impl_.volume5_ = 0;
}
inline int32_t LaneCountInfo::_internal_volume5() const {
  return _impl_.volume5_;
}
inline int32_t LaneCountInfo::volume5() const {
  // @@protoc_insertion_point(field_get:perception.LaneCountInfo.volume5)
  return _internal_volume5();
}
inline void LaneCountInfo::_internal_set_volume5(int32_t value) {
  
  _impl_.volume5_ = value;
}
inline void LaneCountInfo::set_volume5(int32_t value) {
  _internal_set_volume5(value);
  // @@protoc_insertion_point(field_set:perception.LaneCountInfo.volume5)
}

// -------------------------------------------------------------------

// RoadCountInfo

// string deviceId = 1;
inline void RoadCountInfo::clear_deviceid() {
  _impl_.deviceid_.ClearToEmpty();
}
inline const std::string& RoadCountInfo::deviceid() const {
  // @@protoc_insertion_point(field_get:perception.RoadCountInfo.deviceId)
  return _internal_deviceid();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void RoadCountInfo::set_deviceid(ArgT0&& arg0, ArgT... args) {
 
 _impl_.deviceid_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:perception.RoadCountInfo.deviceId)
}
inline std::string* RoadCountInfo::mutable_deviceid() {
  std::string* _s = _internal_mutable_deviceid();
  // @@protoc_insertion_point(field_mutable:perception.RoadCountInfo.deviceId)
  return _s;
}
inline const std::string& RoadCountInfo::_internal_deviceid() const {
  return _impl_.deviceid_.Get();
}
inline void RoadCountInfo::_internal_set_deviceid(const std::string& value) {
  
  _impl_.deviceid_.Set(value, GetArenaForAllocation());
}
inline std::string* RoadCountInfo::_internal_mutable_deviceid() {
  
  return _impl_.deviceid_.Mutable(GetArenaForAllocation());
}
inline std::string* RoadCountInfo::release_deviceid() {
  // @@protoc_insertion_point(field_release:perception.RoadCountInfo.deviceId)
  return _impl_.deviceid_.Release();
}
inline void RoadCountInfo::set_allocated_deviceid(std::string* deviceid) {
  if (deviceid != nullptr) {
    
  } else {
    
  }
  _impl_.deviceid_.SetAllocated(deviceid, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.deviceid_.IsDefault()) {
    _impl_.deviceid_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:perception.RoadCountInfo.deviceId)
}

// float heading = 2;
inline void RoadCountInfo::clear_heading() {
  _impl_.heading_ = 0;
}
inline float RoadCountInfo::_internal_heading() const {
  return _impl_.heading_;
}
inline float RoadCountInfo::heading() const {
  // @@protoc_insertion_point(field_get:perception.RoadCountInfo.heading)
  return _internal_heading();
}
inline void RoadCountInfo::_internal_set_heading(float value) {
  
  _impl_.heading_ = value;
}
inline void RoadCountInfo::set_heading(float value) {
  _internal_set_heading(value);
  // @@protoc_insertion_point(field_set:perception.RoadCountInfo.heading)
}

// string roadName = 3;
inline void RoadCountInfo::clear_roadname() {
  _impl_.roadname_.ClearToEmpty();
}
inline const std::string& RoadCountInfo::roadname() const {
  // @@protoc_insertion_point(field_get:perception.RoadCountInfo.roadName)
  return _internal_roadname();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void RoadCountInfo::set_roadname(ArgT0&& arg0, ArgT... args) {
 
 _impl_.roadname_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:perception.RoadCountInfo.roadName)
}
inline std::string* RoadCountInfo::mutable_roadname() {
  std::string* _s = _internal_mutable_roadname();
  // @@protoc_insertion_point(field_mutable:perception.RoadCountInfo.roadName)
  return _s;
}
inline const std::string& RoadCountInfo::_internal_roadname() const {
  return _impl_.roadname_.Get();
}
inline void RoadCountInfo::_internal_set_roadname(const std::string& value) {
  
  _impl_.roadname_.Set(value, GetArenaForAllocation());
}
inline std::string* RoadCountInfo::_internal_mutable_roadname() {
  
  return _impl_.roadname_.Mutable(GetArenaForAllocation());
}
inline std::string* RoadCountInfo::release_roadname() {
  // @@protoc_insertion_point(field_release:perception.RoadCountInfo.roadName)
  return _impl_.roadname_.Release();
}
inline void RoadCountInfo::set_allocated_roadname(std::string* roadname) {
  if (roadname != nullptr) {
    
  } else {
    
  }
  _impl_.roadname_.SetAllocated(roadname, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.roadname_.IsDefault()) {
    _impl_.roadname_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:perception.RoadCountInfo.roadName)
}

// .perception.Position roadInCoord = 4;
inline bool RoadCountInfo::_internal_has_roadincoord() const {
  return this != internal_default_instance() && _impl_.roadincoord_ != nullptr;
}
inline bool RoadCountInfo::has_roadincoord() const {
  return _internal_has_roadincoord();
}
inline void RoadCountInfo::clear_roadincoord() {
  if (GetArenaForAllocation() == nullptr && _impl_.roadincoord_ != nullptr) {
    delete _impl_.roadincoord_;
  }
  _impl_.roadincoord_ = nullptr;
}
inline const ::perception::Position& RoadCountInfo::_internal_roadincoord() const {
  const ::perception::Position* p = _impl_.roadincoord_;
  return p != nullptr ? *p : reinterpret_cast<const ::perception::Position&>(
      ::perception::_Position_default_instance_);
}
inline const ::perception::Position& RoadCountInfo::roadincoord() const {
  // @@protoc_insertion_point(field_get:perception.RoadCountInfo.roadInCoord)
  return _internal_roadincoord();
}
inline void RoadCountInfo::unsafe_arena_set_allocated_roadincoord(
    ::perception::Position* roadincoord) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.roadincoord_);
  }
  _impl_.roadincoord_ = roadincoord;
  if (roadincoord) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:perception.RoadCountInfo.roadInCoord)
}
inline ::perception::Position* RoadCountInfo::release_roadincoord() {
  
  ::perception::Position* temp = _impl_.roadincoord_;
  _impl_.roadincoord_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::perception::Position* RoadCountInfo::unsafe_arena_release_roadincoord() {
  // @@protoc_insertion_point(field_release:perception.RoadCountInfo.roadInCoord)
  
  ::perception::Position* temp = _impl_.roadincoord_;
  _impl_.roadincoord_ = nullptr;
  return temp;
}
inline ::perception::Position* RoadCountInfo::_internal_mutable_roadincoord() {
  
  if (_impl_.roadincoord_ == nullptr) {
    auto* p = CreateMaybeMessage<::perception::Position>(GetArenaForAllocation());
    _impl_.roadincoord_ = p;
  }
  return _impl_.roadincoord_;
}
inline ::perception::Position* RoadCountInfo::mutable_roadincoord() {
  ::perception::Position* _msg = _internal_mutable_roadincoord();
  // @@protoc_insertion_point(field_mutable:perception.RoadCountInfo.roadInCoord)
  return _msg;
}
inline void RoadCountInfo::set_allocated_roadincoord(::perception::Position* roadincoord) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.roadincoord_;
  }
  if (roadincoord) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(roadincoord);
    if (message_arena != submessage_arena) {
      roadincoord = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, roadincoord, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.roadincoord_ = roadincoord;
  // @@protoc_insertion_point(field_set_allocated:perception.RoadCountInfo.roadInCoord)
}

// int32 volume1 = 5;
inline void RoadCountInfo::clear_volume1() {
  _impl_.volume1_ = 0;
}
inline int32_t RoadCountInfo::_internal_volume1() const {
  return _impl_.volume1_;
}
inline int32_t RoadCountInfo::volume1() const {
  // @@protoc_insertion_point(field_get:perception.RoadCountInfo.volume1)
  return _internal_volume1();
}
inline void RoadCountInfo::_internal_set_volume1(int32_t value) {
  
  _impl_.volume1_ = value;
}
inline void RoadCountInfo::set_volume1(int32_t value) {
  _internal_set_volume1(value);
  // @@protoc_insertion_point(field_set:perception.RoadCountInfo.volume1)
}

// int32 volume2 = 6;
inline void RoadCountInfo::clear_volume2() {
  _impl_.volume2_ = 0;
}
inline int32_t RoadCountInfo::_internal_volume2() const {
  return _impl_.volume2_;
}
inline int32_t RoadCountInfo::volume2() const {
  // @@protoc_insertion_point(field_get:perception.RoadCountInfo.volume2)
  return _internal_volume2();
}
inline void RoadCountInfo::_internal_set_volume2(int32_t value) {
  
  _impl_.volume2_ = value;
}
inline void RoadCountInfo::set_volume2(int32_t value) {
  _internal_set_volume2(value);
  // @@protoc_insertion_point(field_set:perception.RoadCountInfo.volume2)
}

// int32 volume3 = 7;
inline void RoadCountInfo::clear_volume3() {
  _impl_.volume3_ = 0;
}
inline int32_t RoadCountInfo::_internal_volume3() const {
  return _impl_.volume3_;
}
inline int32_t RoadCountInfo::volume3() const {
  // @@protoc_insertion_point(field_get:perception.RoadCountInfo.volume3)
  return _internal_volume3();
}
inline void RoadCountInfo::_internal_set_volume3(int32_t value) {
  
  _impl_.volume3_ = value;
}
inline void RoadCountInfo::set_volume3(int32_t value) {
  _internal_set_volume3(value);
  // @@protoc_insertion_point(field_set:perception.RoadCountInfo.volume3)
}

// int32 volume4 = 8;
inline void RoadCountInfo::clear_volume4() {
  _impl_.volume4_ = 0;
}
inline int32_t RoadCountInfo::_internal_volume4() const {
  return _impl_.volume4_;
}
inline int32_t RoadCountInfo::volume4() const {
  // @@protoc_insertion_point(field_get:perception.RoadCountInfo.volume4)
  return _internal_volume4();
}
inline void RoadCountInfo::_internal_set_volume4(int32_t value) {
  
  _impl_.volume4_ = value;
}
inline void RoadCountInfo::set_volume4(int32_t value) {
  _internal_set_volume4(value);
  // @@protoc_insertion_point(field_set:perception.RoadCountInfo.volume4)
}

// int32 volume5 = 9;
inline void RoadCountInfo::clear_volume5() {
  _impl_.volume5_ = 0;
}
inline int32_t RoadCountInfo::_internal_volume5() const {
  return _impl_.volume5_;
}
inline int32_t RoadCountInfo::volume5() const {
  // @@protoc_insertion_point(field_get:perception.RoadCountInfo.volume5)
  return _internal_volume5();
}
inline void RoadCountInfo::_internal_set_volume5(int32_t value) {
  
  _impl_.volume5_ = value;
}
inline void RoadCountInfo::set_volume5(int32_t value) {
  _internal_set_volume5(value);
  // @@protoc_insertion_point(field_set:perception.RoadCountInfo.volume5)
}

// repeated .perception.LaneCountInfo laneCount = 10;
inline int RoadCountInfo::_internal_lanecount_size() const {
  return _impl_.lanecount_.size();
}
inline int RoadCountInfo::lanecount_size() const {
  return _internal_lanecount_size();
}
inline void RoadCountInfo::clear_lanecount() {
  _impl_.lanecount_.Clear();
}
inline ::perception::LaneCountInfo* RoadCountInfo::mutable_lanecount(int index) {
  // @@protoc_insertion_point(field_mutable:perception.RoadCountInfo.laneCount)
  return _impl_.lanecount_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::perception::LaneCountInfo >*
RoadCountInfo::mutable_lanecount() {
  // @@protoc_insertion_point(field_mutable_list:perception.RoadCountInfo.laneCount)
  return &_impl_.lanecount_;
}
inline const ::perception::LaneCountInfo& RoadCountInfo::_internal_lanecount(int index) const {
  return _impl_.lanecount_.Get(index);
}
inline const ::perception::LaneCountInfo& RoadCountInfo::lanecount(int index) const {
  // @@protoc_insertion_point(field_get:perception.RoadCountInfo.laneCount)
  return _internal_lanecount(index);
}
inline ::perception::LaneCountInfo* RoadCountInfo::_internal_add_lanecount() {
  return _impl_.lanecount_.Add();
}
inline ::perception::LaneCountInfo* RoadCountInfo::add_lanecount() {
  ::perception::LaneCountInfo* _add = _internal_add_lanecount();
  // @@protoc_insertion_point(field_add:perception.RoadCountInfo.laneCount)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::perception::LaneCountInfo >&
RoadCountInfo::lanecount() const {
  // @@protoc_insertion_point(field_list:perception.RoadCountInfo.laneCount)
  return _impl_.lanecount_;
}

// -------------------------------------------------------------------

// IntersectionInfo

// int32 volume1 = 1;
inline void IntersectionInfo::clear_volume1() {
  _impl_.volume1_ = 0;
}
inline int32_t IntersectionInfo::_internal_volume1() const {
  return _impl_.volume1_;
}
inline int32_t IntersectionInfo::volume1() const {
  // @@protoc_insertion_point(field_get:perception.IntersectionInfo.volume1)
  return _internal_volume1();
}
inline void IntersectionInfo::_internal_set_volume1(int32_t value) {
  
  _impl_.volume1_ = value;
}
inline void IntersectionInfo::set_volume1(int32_t value) {
  _internal_set_volume1(value);
  // @@protoc_insertion_point(field_set:perception.IntersectionInfo.volume1)
}

// int32 volume2 = 2;
inline void IntersectionInfo::clear_volume2() {
  _impl_.volume2_ = 0;
}
inline int32_t IntersectionInfo::_internal_volume2() const {
  return _impl_.volume2_;
}
inline int32_t IntersectionInfo::volume2() const {
  // @@protoc_insertion_point(field_get:perception.IntersectionInfo.volume2)
  return _internal_volume2();
}
inline void IntersectionInfo::_internal_set_volume2(int32_t value) {
  
  _impl_.volume2_ = value;
}
inline void IntersectionInfo::set_volume2(int32_t value) {
  _internal_set_volume2(value);
  // @@protoc_insertion_point(field_set:perception.IntersectionInfo.volume2)
}

// int32 volume3 = 3;
inline void IntersectionInfo::clear_volume3() {
  _impl_.volume3_ = 0;
}
inline int32_t IntersectionInfo::_internal_volume3() const {
  return _impl_.volume3_;
}
inline int32_t IntersectionInfo::volume3() const {
  // @@protoc_insertion_point(field_get:perception.IntersectionInfo.volume3)
  return _internal_volume3();
}
inline void IntersectionInfo::_internal_set_volume3(int32_t value) {
  
  _impl_.volume3_ = value;
}
inline void IntersectionInfo::set_volume3(int32_t value) {
  _internal_set_volume3(value);
  // @@protoc_insertion_point(field_set:perception.IntersectionInfo.volume3)
}

// int32 volume4 = 4;
inline void IntersectionInfo::clear_volume4() {
  _impl_.volume4_ = 0;
}
inline int32_t IntersectionInfo::_internal_volume4() const {
  return _impl_.volume4_;
}
inline int32_t IntersectionInfo::volume4() const {
  // @@protoc_insertion_point(field_get:perception.IntersectionInfo.volume4)
  return _internal_volume4();
}
inline void IntersectionInfo::_internal_set_volume4(int32_t value) {
  
  _impl_.volume4_ = value;
}
inline void IntersectionInfo::set_volume4(int32_t value) {
  _internal_set_volume4(value);
  // @@protoc_insertion_point(field_set:perception.IntersectionInfo.volume4)
}

// int32 volume5 = 5;
inline void IntersectionInfo::clear_volume5() {
  _impl_.volume5_ = 0;
}
inline int32_t IntersectionInfo::_internal_volume5() const {
  return _impl_.volume5_;
}
inline int32_t IntersectionInfo::volume5() const {
  // @@protoc_insertion_point(field_get:perception.IntersectionInfo.volume5)
  return _internal_volume5();
}
inline void IntersectionInfo::_internal_set_volume5(int32_t value) {
  
  _impl_.volume5_ = value;
}
inline void IntersectionInfo::set_volume5(int32_t value) {
  _internal_set_volume5(value);
  // @@protoc_insertion_point(field_set:perception.IntersectionInfo.volume5)
}

// repeated .perception.RoadCountInfo roadCount = 6;
inline int IntersectionInfo::_internal_roadcount_size() const {
  return _impl_.roadcount_.size();
}
inline int IntersectionInfo::roadcount_size() const {
  return _internal_roadcount_size();
}
inline void IntersectionInfo::clear_roadcount() {
  _impl_.roadcount_.Clear();
}
inline ::perception::RoadCountInfo* IntersectionInfo::mutable_roadcount(int index) {
  // @@protoc_insertion_point(field_mutable:perception.IntersectionInfo.roadCount)
  return _impl_.roadcount_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::perception::RoadCountInfo >*
IntersectionInfo::mutable_roadcount() {
  // @@protoc_insertion_point(field_mutable_list:perception.IntersectionInfo.roadCount)
  return &_impl_.roadcount_;
}
inline const ::perception::RoadCountInfo& IntersectionInfo::_internal_roadcount(int index) const {
  return _impl_.roadcount_.Get(index);
}
inline const ::perception::RoadCountInfo& IntersectionInfo::roadcount(int index) const {
  // @@protoc_insertion_point(field_get:perception.IntersectionInfo.roadCount)
  return _internal_roadcount(index);
}
inline ::perception::RoadCountInfo* IntersectionInfo::_internal_add_roadcount() {
  return _impl_.roadcount_.Add();
}
inline ::perception::RoadCountInfo* IntersectionInfo::add_roadcount() {
  ::perception::RoadCountInfo* _add = _internal_add_roadcount();
  // @@protoc_insertion_point(field_add:perception.IntersectionInfo.roadCount)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::perception::RoadCountInfo >&
IntersectionInfo::roadcount() const {
  // @@protoc_insertion_point(field_list:perception.IntersectionInfo.roadCount)
  return _impl_.roadcount_;
}

// -------------------------------------------------------------------

// RoadInfo

// string rsuId = 1;
inline void RoadInfo::clear_rsuid() {
  _impl_.rsuid_.ClearToEmpty();
}
inline const std::string& RoadInfo::rsuid() const {
  // @@protoc_insertion_point(field_get:perception.RoadInfo.rsuId)
  return _internal_rsuid();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void RoadInfo::set_rsuid(ArgT0&& arg0, ArgT... args) {
 
 _impl_.rsuid_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:perception.RoadInfo.rsuId)
}
inline std::string* RoadInfo::mutable_rsuid() {
  std::string* _s = _internal_mutable_rsuid();
  // @@protoc_insertion_point(field_mutable:perception.RoadInfo.rsuId)
  return _s;
}
inline const std::string& RoadInfo::_internal_rsuid() const {
  return _impl_.rsuid_.Get();
}
inline void RoadInfo::_internal_set_rsuid(const std::string& value) {
  
  _impl_.rsuid_.Set(value, GetArenaForAllocation());
}
inline std::string* RoadInfo::_internal_mutable_rsuid() {
  
  return _impl_.rsuid_.Mutable(GetArenaForAllocation());
}
inline std::string* RoadInfo::release_rsuid() {
  // @@protoc_insertion_point(field_release:perception.RoadInfo.rsuId)
  return _impl_.rsuid_.Release();
}
inline void RoadInfo::set_allocated_rsuid(std::string* rsuid) {
  if (rsuid != nullptr) {
    
  } else {
    
  }
  _impl_.rsuid_.SetAllocated(rsuid, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.rsuid_.IsDefault()) {
    _impl_.rsuid_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:perception.RoadInfo.rsuId)
}

// string seqNum = 2;
inline void RoadInfo::clear_seqnum() {
  _impl_.seqnum_.ClearToEmpty();
}
inline const std::string& RoadInfo::seqnum() const {
  // @@protoc_insertion_point(field_get:perception.RoadInfo.seqNum)
  return _internal_seqnum();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void RoadInfo::set_seqnum(ArgT0&& arg0, ArgT... args) {
 
 _impl_.seqnum_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:perception.RoadInfo.seqNum)
}
inline std::string* RoadInfo::mutable_seqnum() {
  std::string* _s = _internal_mutable_seqnum();
  // @@protoc_insertion_point(field_mutable:perception.RoadInfo.seqNum)
  return _s;
}
inline const std::string& RoadInfo::_internal_seqnum() const {
  return _impl_.seqnum_.Get();
}
inline void RoadInfo::_internal_set_seqnum(const std::string& value) {
  
  _impl_.seqnum_.Set(value, GetArenaForAllocation());
}
inline std::string* RoadInfo::_internal_mutable_seqnum() {
  
  return _impl_.seqnum_.Mutable(GetArenaForAllocation());
}
inline std::string* RoadInfo::release_seqnum() {
  // @@protoc_insertion_point(field_release:perception.RoadInfo.seqNum)
  return _impl_.seqnum_.Release();
}
inline void RoadInfo::set_allocated_seqnum(std::string* seqnum) {
  if (seqnum != nullptr) {
    
  } else {
    
  }
  _impl_.seqnum_.SetAllocated(seqnum, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.seqnum_.IsDefault()) {
    _impl_.seqnum_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:perception.RoadInfo.seqNum)
}

// string rsuEsn = 3;
inline void RoadInfo::clear_rsuesn() {
  _impl_.rsuesn_.ClearToEmpty();
}
inline const std::string& RoadInfo::rsuesn() const {
  // @@protoc_insertion_point(field_get:perception.RoadInfo.rsuEsn)
  return _internal_rsuesn();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void RoadInfo::set_rsuesn(ArgT0&& arg0, ArgT... args) {
 
 _impl_.rsuesn_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:perception.RoadInfo.rsuEsn)
}
inline std::string* RoadInfo::mutable_rsuesn() {
  std::string* _s = _internal_mutable_rsuesn();
  // @@protoc_insertion_point(field_mutable:perception.RoadInfo.rsuEsn)
  return _s;
}
inline const std::string& RoadInfo::_internal_rsuesn() const {
  return _impl_.rsuesn_.Get();
}
inline void RoadInfo::_internal_set_rsuesn(const std::string& value) {
  
  _impl_.rsuesn_.Set(value, GetArenaForAllocation());
}
inline std::string* RoadInfo::_internal_mutable_rsuesn() {
  
  return _impl_.rsuesn_.Mutable(GetArenaForAllocation());
}
inline std::string* RoadInfo::release_rsuesn() {
  // @@protoc_insertion_point(field_release:perception.RoadInfo.rsuEsn)
  return _impl_.rsuesn_.Release();
}
inline void RoadInfo::set_allocated_rsuesn(std::string* rsuesn) {
  if (rsuesn != nullptr) {
    
  } else {
    
  }
  _impl_.rsuesn_.SetAllocated(rsuesn, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.rsuesn_.IsDefault()) {
    _impl_.rsuesn_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:perception.RoadInfo.rsuEsn)
}

// string protocolVersion = 4;
inline void RoadInfo::clear_protocolversion() {
  _impl_.protocolversion_.ClearToEmpty();
}
inline const std::string& RoadInfo::protocolversion() const {
  // @@protoc_insertion_point(field_get:perception.RoadInfo.protocolVersion)
  return _internal_protocolversion();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void RoadInfo::set_protocolversion(ArgT0&& arg0, ArgT... args) {
 
 _impl_.protocolversion_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:perception.RoadInfo.protocolVersion)
}
inline std::string* RoadInfo::mutable_protocolversion() {
  std::string* _s = _internal_mutable_protocolversion();
  // @@protoc_insertion_point(field_mutable:perception.RoadInfo.protocolVersion)
  return _s;
}
inline const std::string& RoadInfo::_internal_protocolversion() const {
  return _impl_.protocolversion_.Get();
}
inline void RoadInfo::_internal_set_protocolversion(const std::string& value) {
  
  _impl_.protocolversion_.Set(value, GetArenaForAllocation());
}
inline std::string* RoadInfo::_internal_mutable_protocolversion() {
  
  return _impl_.protocolversion_.Mutable(GetArenaForAllocation());
}
inline std::string* RoadInfo::release_protocolversion() {
  // @@protoc_insertion_point(field_release:perception.RoadInfo.protocolVersion)
  return _impl_.protocolversion_.Release();
}
inline void RoadInfo::set_allocated_protocolversion(std::string* protocolversion) {
  if (protocolversion != nullptr) {
    
  } else {
    
  }
  _impl_.protocolversion_.SetAllocated(protocolversion, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.protocolversion_.IsDefault()) {
    _impl_.protocolversion_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:perception.RoadInfo.protocolVersion)
}

// int32 cycle = 5;
inline void RoadInfo::clear_cycle() {
  _impl_.cycle_ = 0;
}
inline int32_t RoadInfo::_internal_cycle() const {
  return _impl_.cycle_;
}
inline int32_t RoadInfo::cycle() const {
  // @@protoc_insertion_point(field_get:perception.RoadInfo.cycle)
  return _internal_cycle();
}
inline void RoadInfo::_internal_set_cycle(int32_t value) {
  
  _impl_.cycle_ = value;
}
inline void RoadInfo::set_cycle(int32_t value) {
  _internal_set_cycle(value);
  // @@protoc_insertion_point(field_set:perception.RoadInfo.cycle)
}

// .perception.IntersectionInfo intersectionInfo = 6;
inline bool RoadInfo::_internal_has_intersectioninfo() const {
  return this != internal_default_instance() && _impl_.intersectioninfo_ != nullptr;
}
inline bool RoadInfo::has_intersectioninfo() const {
  return _internal_has_intersectioninfo();
}
inline void RoadInfo::clear_intersectioninfo() {
  if (GetArenaForAllocation() == nullptr && _impl_.intersectioninfo_ != nullptr) {
    delete _impl_.intersectioninfo_;
  }
  _impl_.intersectioninfo_ = nullptr;
}
inline const ::perception::IntersectionInfo& RoadInfo::_internal_intersectioninfo() const {
  const ::perception::IntersectionInfo* p = _impl_.intersectioninfo_;
  return p != nullptr ? *p : reinterpret_cast<const ::perception::IntersectionInfo&>(
      ::perception::_IntersectionInfo_default_instance_);
}
inline const ::perception::IntersectionInfo& RoadInfo::intersectioninfo() const {
  // @@protoc_insertion_point(field_get:perception.RoadInfo.intersectionInfo)
  return _internal_intersectioninfo();
}
inline void RoadInfo::unsafe_arena_set_allocated_intersectioninfo(
    ::perception::IntersectionInfo* intersectioninfo) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.intersectioninfo_);
  }
  _impl_.intersectioninfo_ = intersectioninfo;
  if (intersectioninfo) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:perception.RoadInfo.intersectionInfo)
}
inline ::perception::IntersectionInfo* RoadInfo::release_intersectioninfo() {
  
  ::perception::IntersectionInfo* temp = _impl_.intersectioninfo_;
  _impl_.intersectioninfo_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::perception::IntersectionInfo* RoadInfo::unsafe_arena_release_intersectioninfo() {
  // @@protoc_insertion_point(field_release:perception.RoadInfo.intersectionInfo)
  
  ::perception::IntersectionInfo* temp = _impl_.intersectioninfo_;
  _impl_.intersectioninfo_ = nullptr;
  return temp;
}
inline ::perception::IntersectionInfo* RoadInfo::_internal_mutable_intersectioninfo() {
  
  if (_impl_.intersectioninfo_ == nullptr) {
    auto* p = CreateMaybeMessage<::perception::IntersectionInfo>(GetArenaForAllocation());
    _impl_.intersectioninfo_ = p;
  }
  return _impl_.intersectioninfo_;
}
inline ::perception::IntersectionInfo* RoadInfo::mutable_intersectioninfo() {
  ::perception::IntersectionInfo* _msg = _internal_mutable_intersectioninfo();
  // @@protoc_insertion_point(field_mutable:perception.RoadInfo.intersectionInfo)
  return _msg;
}
inline void RoadInfo::set_allocated_intersectioninfo(::perception::IntersectionInfo* intersectioninfo) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.intersectioninfo_;
  }
  if (intersectioninfo) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(intersectioninfo);
    if (message_arena != submessage_arena) {
      intersectioninfo = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, intersectioninfo, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.intersectioninfo_ = intersectioninfo;
  // @@protoc_insertion_point(field_set_allocated:perception.RoadInfo.intersectionInfo)
}

// -------------------------------------------------------------------

// PerceptionMsg

// int64 time = 1;
inline void PerceptionMsg::clear_time() {
  _impl_.time_ = int64_t{0};
}
inline int64_t PerceptionMsg::_internal_time() const {
  return _impl_.time_;
}
inline int64_t PerceptionMsg::time() const {
  // @@protoc_insertion_point(field_get:perception.PerceptionMsg.time)
  return _internal_time();
}
inline void PerceptionMsg::_internal_set_time(int64_t value) {
  
  _impl_.time_ = value;
}
inline void PerceptionMsg::set_time(int64_t value) {
  _internal_set_time(value);
  // @@protoc_insertion_point(field_set:perception.PerceptionMsg.time)
}

// .perception.Targets target_msg = 2;
inline bool PerceptionMsg::_internal_has_target_msg() const {
  return MsgType_case() == kTargetMsg;
}
inline bool PerceptionMsg::has_target_msg() const {
  return _internal_has_target_msg();
}
inline void PerceptionMsg::set_has_target_msg() {
  _impl_._oneof_case_[0] = kTargetMsg;
}
inline void PerceptionMsg::clear_target_msg() {
  if (_internal_has_target_msg()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.MsgType_.target_msg_;
    }
    clear_has_MsgType();
  }
}
inline ::perception::Targets* PerceptionMsg::release_target_msg() {
  // @@protoc_insertion_point(field_release:perception.PerceptionMsg.target_msg)
  if (_internal_has_target_msg()) {
    clear_has_MsgType();
    ::perception::Targets* temp = _impl_.MsgType_.target_msg_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.MsgType_.target_msg_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::perception::Targets& PerceptionMsg::_internal_target_msg() const {
  return _internal_has_target_msg()
      ? *_impl_.MsgType_.target_msg_
      : reinterpret_cast< ::perception::Targets&>(::perception::_Targets_default_instance_);
}
inline const ::perception::Targets& PerceptionMsg::target_msg() const {
  // @@protoc_insertion_point(field_get:perception.PerceptionMsg.target_msg)
  return _internal_target_msg();
}
inline ::perception::Targets* PerceptionMsg::unsafe_arena_release_target_msg() {
  // @@protoc_insertion_point(field_unsafe_arena_release:perception.PerceptionMsg.target_msg)
  if (_internal_has_target_msg()) {
    clear_has_MsgType();
    ::perception::Targets* temp = _impl_.MsgType_.target_msg_;
    _impl_.MsgType_.target_msg_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void PerceptionMsg::unsafe_arena_set_allocated_target_msg(::perception::Targets* target_msg) {
  clear_MsgType();
  if (target_msg) {
    set_has_target_msg();
    _impl_.MsgType_.target_msg_ = target_msg;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:perception.PerceptionMsg.target_msg)
}
inline ::perception::Targets* PerceptionMsg::_internal_mutable_target_msg() {
  if (!_internal_has_target_msg()) {
    clear_MsgType();
    set_has_target_msg();
    _impl_.MsgType_.target_msg_ = CreateMaybeMessage< ::perception::Targets >(GetArenaForAllocation());
  }
  return _impl_.MsgType_.target_msg_;
}
inline ::perception::Targets* PerceptionMsg::mutable_target_msg() {
  ::perception::Targets* _msg = _internal_mutable_target_msg();
  // @@protoc_insertion_point(field_mutable:perception.PerceptionMsg.target_msg)
  return _msg;
}

// .perception.Events event_msg = 3;
inline bool PerceptionMsg::_internal_has_event_msg() const {
  return MsgType_case() == kEventMsg;
}
inline bool PerceptionMsg::has_event_msg() const {
  return _internal_has_event_msg();
}
inline void PerceptionMsg::set_has_event_msg() {
  _impl_._oneof_case_[0] = kEventMsg;
}
inline void PerceptionMsg::clear_event_msg() {
  if (_internal_has_event_msg()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.MsgType_.event_msg_;
    }
    clear_has_MsgType();
  }
}
inline ::perception::Events* PerceptionMsg::release_event_msg() {
  // @@protoc_insertion_point(field_release:perception.PerceptionMsg.event_msg)
  if (_internal_has_event_msg()) {
    clear_has_MsgType();
    ::perception::Events* temp = _impl_.MsgType_.event_msg_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.MsgType_.event_msg_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::perception::Events& PerceptionMsg::_internal_event_msg() const {
  return _internal_has_event_msg()
      ? *_impl_.MsgType_.event_msg_
      : reinterpret_cast< ::perception::Events&>(::perception::_Events_default_instance_);
}
inline const ::perception::Events& PerceptionMsg::event_msg() const {
  // @@protoc_insertion_point(field_get:perception.PerceptionMsg.event_msg)
  return _internal_event_msg();
}
inline ::perception::Events* PerceptionMsg::unsafe_arena_release_event_msg() {
  // @@protoc_insertion_point(field_unsafe_arena_release:perception.PerceptionMsg.event_msg)
  if (_internal_has_event_msg()) {
    clear_has_MsgType();
    ::perception::Events* temp = _impl_.MsgType_.event_msg_;
    _impl_.MsgType_.event_msg_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void PerceptionMsg::unsafe_arena_set_allocated_event_msg(::perception::Events* event_msg) {
  clear_MsgType();
  if (event_msg) {
    set_has_event_msg();
    _impl_.MsgType_.event_msg_ = event_msg;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:perception.PerceptionMsg.event_msg)
}
inline ::perception::Events* PerceptionMsg::_internal_mutable_event_msg() {
  if (!_internal_has_event_msg()) {
    clear_MsgType();
    set_has_event_msg();
    _impl_.MsgType_.event_msg_ = CreateMaybeMessage< ::perception::Events >(GetArenaForAllocation());
  }
  return _impl_.MsgType_.event_msg_;
}
inline ::perception::Events* PerceptionMsg::mutable_event_msg() {
  ::perception::Events* _msg = _internal_mutable_event_msg();
  // @@protoc_insertion_point(field_mutable:perception.PerceptionMsg.event_msg)
  return _msg;
}

// .perception.HeartBeat heart_beat_msg = 4;
inline bool PerceptionMsg::_internal_has_heart_beat_msg() const {
  return MsgType_case() == kHeartBeatMsg;
}
inline bool PerceptionMsg::has_heart_beat_msg() const {
  return _internal_has_heart_beat_msg();
}
inline void PerceptionMsg::set_has_heart_beat_msg() {
  _impl_._oneof_case_[0] = kHeartBeatMsg;
}
inline void PerceptionMsg::clear_heart_beat_msg() {
  if (_internal_has_heart_beat_msg()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.MsgType_.heart_beat_msg_;
    }
    clear_has_MsgType();
  }
}
inline ::perception::HeartBeat* PerceptionMsg::release_heart_beat_msg() {
  // @@protoc_insertion_point(field_release:perception.PerceptionMsg.heart_beat_msg)
  if (_internal_has_heart_beat_msg()) {
    clear_has_MsgType();
    ::perception::HeartBeat* temp = _impl_.MsgType_.heart_beat_msg_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.MsgType_.heart_beat_msg_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::perception::HeartBeat& PerceptionMsg::_internal_heart_beat_msg() const {
  return _internal_has_heart_beat_msg()
      ? *_impl_.MsgType_.heart_beat_msg_
      : reinterpret_cast< ::perception::HeartBeat&>(::perception::_HeartBeat_default_instance_);
}
inline const ::perception::HeartBeat& PerceptionMsg::heart_beat_msg() const {
  // @@protoc_insertion_point(field_get:perception.PerceptionMsg.heart_beat_msg)
  return _internal_heart_beat_msg();
}
inline ::perception::HeartBeat* PerceptionMsg::unsafe_arena_release_heart_beat_msg() {
  // @@protoc_insertion_point(field_unsafe_arena_release:perception.PerceptionMsg.heart_beat_msg)
  if (_internal_has_heart_beat_msg()) {
    clear_has_MsgType();
    ::perception::HeartBeat* temp = _impl_.MsgType_.heart_beat_msg_;
    _impl_.MsgType_.heart_beat_msg_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void PerceptionMsg::unsafe_arena_set_allocated_heart_beat_msg(::perception::HeartBeat* heart_beat_msg) {
  clear_MsgType();
  if (heart_beat_msg) {
    set_has_heart_beat_msg();
    _impl_.MsgType_.heart_beat_msg_ = heart_beat_msg;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:perception.PerceptionMsg.heart_beat_msg)
}
inline ::perception::HeartBeat* PerceptionMsg::_internal_mutable_heart_beat_msg() {
  if (!_internal_has_heart_beat_msg()) {
    clear_MsgType();
    set_has_heart_beat_msg();
    _impl_.MsgType_.heart_beat_msg_ = CreateMaybeMessage< ::perception::HeartBeat >(GetArenaForAllocation());
  }
  return _impl_.MsgType_.heart_beat_msg_;
}
inline ::perception::HeartBeat* PerceptionMsg::mutable_heart_beat_msg() {
  ::perception::HeartBeat* _msg = _internal_mutable_heart_beat_msg();
  // @@protoc_insertion_point(field_mutable:perception.PerceptionMsg.heart_beat_msg)
  return _msg;
}

// .perception.DeviceInfo dev_info = 5;
inline bool PerceptionMsg::_internal_has_dev_info() const {
  return MsgType_case() == kDevInfo;
}
inline bool PerceptionMsg::has_dev_info() const {
  return _internal_has_dev_info();
}
inline void PerceptionMsg::set_has_dev_info() {
  _impl_._oneof_case_[0] = kDevInfo;
}
inline void PerceptionMsg::clear_dev_info() {
  if (_internal_has_dev_info()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.MsgType_.dev_info_;
    }
    clear_has_MsgType();
  }
}
inline ::perception::DeviceInfo* PerceptionMsg::release_dev_info() {
  // @@protoc_insertion_point(field_release:perception.PerceptionMsg.dev_info)
  if (_internal_has_dev_info()) {
    clear_has_MsgType();
    ::perception::DeviceInfo* temp = _impl_.MsgType_.dev_info_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.MsgType_.dev_info_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::perception::DeviceInfo& PerceptionMsg::_internal_dev_info() const {
  return _internal_has_dev_info()
      ? *_impl_.MsgType_.dev_info_
      : reinterpret_cast< ::perception::DeviceInfo&>(::perception::_DeviceInfo_default_instance_);
}
inline const ::perception::DeviceInfo& PerceptionMsg::dev_info() const {
  // @@protoc_insertion_point(field_get:perception.PerceptionMsg.dev_info)
  return _internal_dev_info();
}
inline ::perception::DeviceInfo* PerceptionMsg::unsafe_arena_release_dev_info() {
  // @@protoc_insertion_point(field_unsafe_arena_release:perception.PerceptionMsg.dev_info)
  if (_internal_has_dev_info()) {
    clear_has_MsgType();
    ::perception::DeviceInfo* temp = _impl_.MsgType_.dev_info_;
    _impl_.MsgType_.dev_info_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void PerceptionMsg::unsafe_arena_set_allocated_dev_info(::perception::DeviceInfo* dev_info) {
  clear_MsgType();
  if (dev_info) {
    set_has_dev_info();
    _impl_.MsgType_.dev_info_ = dev_info;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:perception.PerceptionMsg.dev_info)
}
inline ::perception::DeviceInfo* PerceptionMsg::_internal_mutable_dev_info() {
  if (!_internal_has_dev_info()) {
    clear_MsgType();
    set_has_dev_info();
    _impl_.MsgType_.dev_info_ = CreateMaybeMessage< ::perception::DeviceInfo >(GetArenaForAllocation());
  }
  return _impl_.MsgType_.dev_info_;
}
inline ::perception::DeviceInfo* PerceptionMsg::mutable_dev_info() {
  ::perception::DeviceInfo* _msg = _internal_mutable_dev_info();
  // @@protoc_insertion_point(field_mutable:perception.PerceptionMsg.dev_info)
  return _msg;
}

// .perception.RoadCounts road_count = 6;
inline bool PerceptionMsg::_internal_has_road_count() const {
  return MsgType_case() == kRoadCount;
}
inline bool PerceptionMsg::has_road_count() const {
  return _internal_has_road_count();
}
inline void PerceptionMsg::set_has_road_count() {
  _impl_._oneof_case_[0] = kRoadCount;
}
inline void PerceptionMsg::clear_road_count() {
  if (_internal_has_road_count()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.MsgType_.road_count_;
    }
    clear_has_MsgType();
  }
}
inline ::perception::RoadCounts* PerceptionMsg::release_road_count() {
  // @@protoc_insertion_point(field_release:perception.PerceptionMsg.road_count)
  if (_internal_has_road_count()) {
    clear_has_MsgType();
    ::perception::RoadCounts* temp = _impl_.MsgType_.road_count_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.MsgType_.road_count_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::perception::RoadCounts& PerceptionMsg::_internal_road_count() const {
  return _internal_has_road_count()
      ? *_impl_.MsgType_.road_count_
      : reinterpret_cast< ::perception::RoadCounts&>(::perception::_RoadCounts_default_instance_);
}
inline const ::perception::RoadCounts& PerceptionMsg::road_count() const {
  // @@protoc_insertion_point(field_get:perception.PerceptionMsg.road_count)
  return _internal_road_count();
}
inline ::perception::RoadCounts* PerceptionMsg::unsafe_arena_release_road_count() {
  // @@protoc_insertion_point(field_unsafe_arena_release:perception.PerceptionMsg.road_count)
  if (_internal_has_road_count()) {
    clear_has_MsgType();
    ::perception::RoadCounts* temp = _impl_.MsgType_.road_count_;
    _impl_.MsgType_.road_count_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void PerceptionMsg::unsafe_arena_set_allocated_road_count(::perception::RoadCounts* road_count) {
  clear_MsgType();
  if (road_count) {
    set_has_road_count();
    _impl_.MsgType_.road_count_ = road_count;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:perception.PerceptionMsg.road_count)
}
inline ::perception::RoadCounts* PerceptionMsg::_internal_mutable_road_count() {
  if (!_internal_has_road_count()) {
    clear_MsgType();
    set_has_road_count();
    _impl_.MsgType_.road_count_ = CreateMaybeMessage< ::perception::RoadCounts >(GetArenaForAllocation());
  }
  return _impl_.MsgType_.road_count_;
}
inline ::perception::RoadCounts* PerceptionMsg::mutable_road_count() {
  ::perception::RoadCounts* _msg = _internal_mutable_road_count();
  // @@protoc_insertion_point(field_mutable:perception.PerceptionMsg.road_count)
  return _msg;
}

// .perception.RoadInfo road_info = 7;
inline bool PerceptionMsg::_internal_has_road_info() const {
  return MsgType_case() == kRoadInfo;
}
inline bool PerceptionMsg::has_road_info() const {
  return _internal_has_road_info();
}
inline void PerceptionMsg::set_has_road_info() {
  _impl_._oneof_case_[0] = kRoadInfo;
}
inline void PerceptionMsg::clear_road_info() {
  if (_internal_has_road_info()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.MsgType_.road_info_;
    }
    clear_has_MsgType();
  }
}
inline ::perception::RoadInfo* PerceptionMsg::release_road_info() {
  // @@protoc_insertion_point(field_release:perception.PerceptionMsg.road_info)
  if (_internal_has_road_info()) {
    clear_has_MsgType();
    ::perception::RoadInfo* temp = _impl_.MsgType_.road_info_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.MsgType_.road_info_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::perception::RoadInfo& PerceptionMsg::_internal_road_info() const {
  return _internal_has_road_info()
      ? *_impl_.MsgType_.road_info_
      : reinterpret_cast< ::perception::RoadInfo&>(::perception::_RoadInfo_default_instance_);
}
inline const ::perception::RoadInfo& PerceptionMsg::road_info() const {
  // @@protoc_insertion_point(field_get:perception.PerceptionMsg.road_info)
  return _internal_road_info();
}
inline ::perception::RoadInfo* PerceptionMsg::unsafe_arena_release_road_info() {
  // @@protoc_insertion_point(field_unsafe_arena_release:perception.PerceptionMsg.road_info)
  if (_internal_has_road_info()) {
    clear_has_MsgType();
    ::perception::RoadInfo* temp = _impl_.MsgType_.road_info_;
    _impl_.MsgType_.road_info_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void PerceptionMsg::unsafe_arena_set_allocated_road_info(::perception::RoadInfo* road_info) {
  clear_MsgType();
  if (road_info) {
    set_has_road_info();
    _impl_.MsgType_.road_info_ = road_info;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:perception.PerceptionMsg.road_info)
}
inline ::perception::RoadInfo* PerceptionMsg::_internal_mutable_road_info() {
  if (!_internal_has_road_info()) {
    clear_MsgType();
    set_has_road_info();
    _impl_.MsgType_.road_info_ = CreateMaybeMessage< ::perception::RoadInfo >(GetArenaForAllocation());
  }
  return _impl_.MsgType_.road_info_;
}
inline ::perception::RoadInfo* PerceptionMsg::mutable_road_info() {
  ::perception::RoadInfo* _msg = _internal_mutable_road_info();
  // @@protoc_insertion_point(field_mutable:perception.PerceptionMsg.road_info)
  return _msg;
}

inline bool PerceptionMsg::has_MsgType() const {
  return MsgType_case() != MSGTYPE_NOT_SET;
}
inline void PerceptionMsg::clear_has_MsgType() {
  _impl_._oneof_case_[0] = MSGTYPE_NOT_SET;
}
inline PerceptionMsg::MsgTypeCase PerceptionMsg::MsgType_case() const {
  return PerceptionMsg::MsgTypeCase(_impl_._oneof_case_[0]);
}
#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace perception

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_perception_2eproto
