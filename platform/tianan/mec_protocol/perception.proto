/*
    通信协议 : 2022-10-24
        --希迪融合深瞳-加统计类消息
*/

syntax = "proto3";
package perception;     
                                                                                                                                                                               
// 位置                                                     
message Position{                                           
    int64 lat                          = 1;        // 单位,1e-7度
    int64 lon                          = 2;        // 单位,1e-7度
    int32 elev                         = 3;        // 海拔高度,0.1米
}     

// 三维尺寸                                                     
message Size{                                               
    int32          length              = 1;        // 单位,cm
    int32          width               = 2;        // 单位,cm
    int32          height              = 3;        // 单位,5cm
}                                                           
                                                                                                                                                                              
// 车道信息                                                     
message LaneInfo{                                               
    int32          area_type              = 1;        // 区域类型,自定义    
    int32          lane_count             = 2;        // 车道总数
    float          lane_heading           = 3;        // 道路航向角,单位为度
}   

// 单个检测到的目标                                         
message Target {                                            
    int64          id                     = 1;        // 目标 id 
    int32          source                 = 2;        // 数据来源:Unknown：0; v2x：2; Video：3; microwaveRadar：4; Loop：5; Lidar：6; Integrated：7    
    Position       pos                    = 3;        // 位置
    Size           size                   = 4;        // 三维尺寸,长宽高
    float          confidence             = 5;        // 置信度
    int32          type                   = 6;        // 目标类型: Unknown：0; Motor：1; non-motor：2; Pedestrian：3; rsu obstacle：4
    int32          speed                  = 7;        // 速度,单位0.02m/s
    int32          heading                = 8;        // 目标航向角,单位0.0125度
    int32          lane_id                = 9;        // 车道号
    LaneInfo       lane_info              = 10;       // 车道信息
    string         license_plate          = 11;       // 车牌
    int32          car_type               = 12;       // 未知：0 ; 乘用车：10; 轻卡：20;卡车：25; 摩托车 :40; 紧急车:60; 细分车辆类型参考asn标准定义 5.2.4.5 章节
    int32          color                  = 13;       // RGB 格式,后三个字节表示,如 0xFFF 表示白色,0xF00 表示红色 
    int32          status                 = 14;       // 车辆运动状态,自定义
    float          distance               = 15;       // 相对距离,米
    float          rcs                    = 16;       // 雷达散射截面
    int64          track_time             = 17;       // 跟踪起始时间
    // ************以下人，车，非，脸四类目标属性，为格灵深瞳扩展的数据类型
	//以下是车辆属性信息，当type字段为Motor机动车时有效，属性字典参考文档
	int32      vehicle_pose             =18;    // 车辆角度
    int32      vehicle_type             =19;   // 车型
    int32      vehicle_marker           =20;   // 标志物
    int32      vehicle_other            =21;   // 特殊车辆
    int32      vehicle_roof             =22;   // 车顶物件
    int32      vehicle_carColor         =23;   // 车身颜色
    int32      vehicle_make             =24;  // 车辆制造商
    int32      vehicle_model            =25;  // 车辆型号
    int32      vehicle_year             =26;  // 车辆生产日期
	int32      vehicle_plate_color      =27;  // 车牌颜色
	int32      vehicle_plate_type       =28;  // 车牌类型 
    //以下是人脸属性信息，类型为Face人脸时有效
	int32     face_age                        =29;  // 年龄，单位：年
	int32     face_gender                     =30;  // 性别  1 表示男，2 表示女，3 表示其他
    int32     face_mask                       =31;  // 口罩，1 表示无，2 表示有
    int32     face_hat                        =32;  // 帽子，1 表示无，2 表示有，3 表示有头巾
    int32     face_glass                      =33; // 眼镜，1 表示无，2 表示有，3 表示有太阳镜
    //以下是行人属性信息，类型为Pedestrian行人时有效，属性字典参考文档
	int32     pedestrian_shoecolor        =34; // 鞋子颜色
    int32     pedestrian_shoestyle        =35;   // 鞋子款式
    int32     pedestrian_sleeve           =36;    // 上衣款式
    int32     pedestrian_hair             =37;   // 发型
    int32     pedestrian_nation           =38;   // 民族
    int32     pedestrian_gender           =39;   // 性别
    int32     pedestrian_lowerstyle       =40;  // 下身类别
    int32     pedestrian_upperstyle       =41;   // 上身纹理
    int32     pedestrian_age              =42;  // 年龄段
    int32     pedestrian_lower            =43;  // 下身颜色
    int32     pedestrian_upper            =44;  // 上身颜色
    int32     pedestrian_backpack         =45; // 随身的包的类别
    //非机动车信息，类型为non-motors非机动车时有效，属性字典参考文档
	int32     nonmotor_attitude           =46;       // 非机动车角度
    int32     nonmotor_gender             =47;      //  驾驶者性别
    int32     nonmotor_nation             =48;      // 驾驶者民族
    int32     nonmotor_transportation     =49;          //  非机动车车型
    int32     nonmotor_upperColor         =50;          //  上身颜色
    int32     nonmotor_upper              =51;      // 上身纹理
    int32     nonmotor_transportationColor  =52;  // 非机动车车身颜色
}                                                           
                                                            
                                                                                                                                                                           
// 目标类型                                     
message Targets{    
    string                  device_sn       = 1;    // 设备序列号,用于标识数据来源,来自哪种传感器设备
    string                  device_ip       = 2;    // 设备编号,对于设备序列号相同的设备,通过设备IP进行区分    
    int32                   fusion_state    = 3;    // 1 代表融合输出,2 代表单一传感器输出                                      
    repeated Target         array           = 4;    // 融合目标列表        
}                                                           

// 形容refpath或感知区域的数据结构
message AreaInfo {
    int32               id                      = 1;    // 区域ID
    float               width                   = 2;    // 横向宽度
    repeated Position   dot                     = 3;    // 车道线中点序列（由远及近）
}

//车道级统计信息
message LaneCount{
    int32               laneNo                  = 1;     // 车道编号 
    int32               volume                  = 2;     // 车道机动车流量
    int32               pcu                     = 3;     // 车道交通当量 
    float               avSpeed                 = 4;     // 车道平均速度,km/h 
    float               occupancy               = 5;     // 车道时间占有率,% 
    float               headWay                 = 6;     // 车道平均车头时距,s 
    float               gap                     = 7;     // 车道平均车身间距,m 
    float               avDelay                 = 8;     // 车道平均延误时间 
    int32               avStop                  = 9;     // 车道平均停车次数 
    float               speed85                 = 10;    // 车道85 位速度,km/h  
    float               queueLength             = 11;    // 车道来向车道排队长度,m 
}

//道路统计信息
message RoadCount{
    string              deviceId                = 1;     // 雷达编号,雷达厂家提供 
    float               heading                 = 2;     // 雷达厂家提供,道路与道路入口中心点与正北方向的顺时针夹角,单位为度
    string              roadName                = 3;     // 道路名称,雷达厂家提供  
    Position            roadInCoord             = 4;     // 雷达厂家提供,道路入口坐标 
    int32               volume                  = 5;     // 道路机动车流量
    int32               pcu                     = 6;     // 道路交通当量 
    float               avSpeed                 = 7;     // 道路平均速度,km/h 
    float               occupancy               = 8;     // 道路时间占有率,% 
    float               headWay                 = 9;     // 道路平均车头时距,s
    float               gap                     = 10;    // 道路平均车身间距,m  
    float               avDelay                 = 11;    // 道路平均延误时间
    int32               avStop                  = 12;    // 道路平均停车次数 
    float               speed85                 = 13;    // 道路85 位速度,km/h 
    float               queueLength             = 14;    // 道路来向车道排队长度,m 
    repeated LaneCount  laneCount               = 15;    // 车道级统计信息
}

//路口统计信息
message IntersectionCount{
    int32               volume                  = 1;     // 路口机动车流量
    int32               pcu                     = 2;     // 路口交通当量
    float               avSpeed                 = 3;     // 路口平均速度
    float               occupancy               = 4;     // 路口时间占有率
    float               headWay                 = 5;     // 路口平均车头时距
    float               gap                     = 6;     // 路口平均车身间距 
    float               avDelay                 = 7;     // 路口平均延误时间 
    int32               avStop                  = 8;     // 路口平均停车次数
    float               speed85                 = 9;     // 路口85 位速度
    float               queueLength             = 10;    // 路口来向车道排队长度
    repeated RoadCount  roadCount               = 11;    // 道路级统计信息
}

// 一个事件
message Event {
    int32               event_type              = 1;    // 事件类型,十进制,高2位为主事件,低2位为子事件(参看事件字典)
    int32               level                   = 2;    // 事件等级
    string              device_sn               = 3;    // 检测的设备id
    int32               lane_id                 = 4;    // 车道号,0xff表示不在车道内
    int32               fusion_state            = 5;    // 1代表融合输出,2代表单一传感器输出
    int32               source                  = 6;    // 数据来源，bit0:毫米波雷达，bit1:摄像头，bit2:激光雷达，bit3:V2X，见perception_content.h 
    repeated Position   pos                     = 7;    // 区域类型或者点类型位置坐标
    string              license_plate           = 8;    // 车牌,造成事件的车牌
    int64               track_time              = 9;    // 事件起始时间
    int64               ttl                     = 10;   // time to live；预计事件消散的时间,单位ms
    repeated AreaInfo   refpath                 = 11;   // 触发路径,预留,可多条
    oneof DataType{
        string          string_data             = 12;   // 字符串附加属性,根据事件不同赋值
        int32           int_data                = 13;   // int型附加属性,根据事件不同赋值
    }
}

// 所有事件
message Events{
    repeated Event      event                   = 1;
}

// 心跳包
message HeartBeat {
    int32               seq_count               = 1;    // 包count
    int64               time                    = 2;    // 时间戳,单位ms
    string              device_sn               = 3;    // 检测的设备id
    repeated int32      err_code                = 4;    // 错误码
}

//设备信息
message DeviceInfo{
    string              device_sn               = 1;     // 设备序列号
    Position            location                = 2;     // 安装位置经纬度
    float               orientation             = 3;     // 设备朝向,度数
    int32               type                    = 4;     // 传感器类型; bit0:毫米波雷达,bit1:摄像头,bit2:激光雷达,bit3:V2X,bit4：雷视一体机
    repeated AreaInfo   perceptual_area         = 5;     // 设备感知区域,可以描述为一个Link或Lane的集合,Link与Lane描述参考CV2X空口协议
    string              description             = 6;     // 设备描述信息,可以描述路口、杆件等位置信息
}

//统计消息包
message RoadCounts{
    string              rsuId               = 1;     // Rsu ID,唯一,云平台匹配用
    string              seqNum              = 2;     // 消息序号,有序递增
    string              rsuEsn              = 3;     // RSU序列号 
    string              protocolVersion     = 4;     // 接口协议版本
    int32               cycle               = 5;     // 统计周期,1~3600秒
    IntersectionCount   intersectionCount   = 6;     // 设备描述信息,可以描述路口、杆件等位置信息
}

//车道级在途量统计内容
message LaneCountInfo{
    int32                   laneNo          = 1;     // 车道号,雷达厂家提供 
    int32                   volume1         = 2;     // 流量1,车道行人流量
    int32                   volume2         = 3;     // 流量2,车道非机动车流量
    int32                   volume3         = 4;     // 流量3,车道小车流量
    int32                   volume4         = 5;     // 流量4,车道中车流量
    int32                   volume5         = 6;     // 流量5,车道大车流量
}

//道路级在途量统计内容
message RoadCountInfo{
    string                  deviceId        = 1;     // 雷达编号,雷达厂家提供
    float                   heading         = 2;     // 道路航向角,雷达厂家提供,道路与道路入口中心点与正北方向的顺时针夹角,单位为度
    string                  roadName        = 3;     // 道路名称,雷达厂家提供
    Position                roadInCoord     = 4;     // 雷达厂家提供,道路入口坐标 
    int32                   volume1         = 5;     // 流量1,道路行人流量
    int32                   volume2         = 6;     // 流量2,道路非机动车流量
    int32                   volume3         = 7;     // 流量3,道路小车流量
    int32                   volume4         = 8;     // 流量4,道路中车流量
    int32                   volume5         = 9;     // 流量5,道路大车流量
    repeated LaneCountInfo  laneCount       = 10;    // 车道级在途量统计 
}

//路口级在途量统计内容
message IntersectionInfo{
    int32                   volume1         = 1;     // 流量1,路口行人流量
    int32                   volume2         = 2;     // 流量2,路口非机动车流量
    int32                   volume3         = 3;     // 流量3,路口小车流量
    int32                   volume4         = 4;     // 流量4,路口中车流量
    int32                   volume5         = 5;     // 流量5,路口大车流量
    repeated RoadCountInfo  roadCount       = 6;     // 道路级在途量统计,道路级,直接解析雷达厂家数据
}

//在途量消息包
message RoadInfo{
    string              rsuId               = 1;     // Rsu ID,唯一,云平台匹配用
    string              seqNum              = 2;     // 消息序号,有序递增
    string              rsuEsn              = 3;     // RSU序列号 
    string              protocolVersion     = 4;     // 接口协议版本
    int32               cycle               = 5;     // 统计周期,1~3600秒
    IntersectionInfo    intersectionInfo    = 6;     // 路口级在途量统计
}

//  ---------------- 要发送的数据包 -----------------------------------------------------
message PerceptionMsg{                                      
    int64      time                = 1;     // 时间戳,单位ms
    oneof MsgType{                                          
        Targets    target_msg      = 2;     // 目标类型   
        Events     event_msg       = 3;     // 事件类型  
        HeartBeat  heart_beat_msg  = 4;     // 心跳包   
        DeviceInfo dev_info        = 5;     // 设备信息包     
        RoadCounts road_count      = 6;     // 统计消息包
        RoadInfo   road_info       = 7;     // 在途量消息包
    }                                                       
}                                                           

