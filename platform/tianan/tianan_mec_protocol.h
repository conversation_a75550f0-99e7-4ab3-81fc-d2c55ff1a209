/***********************************************
版本信息： 金溢科技版本权所有，保留一切权利
文件名称： tianan_mec_protocol.h
作者：张明
开始日期：2024-07-01 17:23:10
完成日期：
当前版本号: v 0.0.1
主要功能: 将  希迪融合深瞳 ,协议转换成 gv_json 格式进行发送
版本历史:
***********************************************/

#ifndef _TIANAN_MEC_PROTOCOL_H_
#define _TIANAN_MEC_PROTOCOL_H_
#include "stdafx.h"

#include "nlohmann/json.hpp"
using json = nlohmann::json;

#include "perception.pb.h"
using namespace perception; 

class tianan_mec_protocol
{
public:
	tianan_mec_protocol();
	~tianan_mec_protocol();
	int rsi_ta2gv(const perception::PerceptionMsg & msg, string & gv_json);
	int rsm_ta2gv(const perception::PerceptionMsg & msg, string & gv_json);


	int pos_ta2gv(const perception::Position  & pos,nlohmann::json &gv_json);
	int posoffset_ta2gv(const perception::Position  & pos,nlohmann::json &gv_json);
	int descript_ta2gv(const string &desc, nlohmann::json & gv_json); 

	int rsi_rte_ta2gv(const perception::Event &event, nlohmann::json *gv_json_par);
	int participant_ta2gv(const perception::Target &target, nlohmann::json *gv_json_par); 
	int size_ta2gv(const perception::Size & size, nlohmann::json &gv_json);
	int vehicleClass_ta2gv(int car_type,nlohmann::json &gv_json);

	string get_v2x_id(); 
	int get_v2x_msgcnt();
	int get_rsitimedetails(int64_t beg_ms, int64_t end_ms, nlohmann::json& gv_json); 
	int get_v2x_rteid(const perception::Event &event);

	int get_participant_posconfidence(const perception::Target &target,nlohmann::json &gv_json); 

	string get_bsm_id(int64_t id); 
	
	template<typename AT,typename FUN>
	void array_ta2gv(const AT & at,nlohmann::json & gv_json, FUN funobj)
	{
		for(auto & item : at )
		{
			auto j_i = nlohmann::json();
			funobj(item,&j_i);
			gv_json.emplace_back(std::move(j_i)); 
		}
	}

};
#endif //_TIANAN_MEC_PROTOCOL_H_
