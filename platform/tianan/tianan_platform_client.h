/***********************************************
版本信息： 金溢科技版本权所有，保留一切权利
文件名称： tianan_platform_client.h
作者：张明
开始日期：2024-01-29 10:52:36
完成日期：
当前版本号: v 0.0.1
主要功能: 
版本历史:
***********************************************/

#ifndef _TIANAN_PLATFORM_CLIENT_H_
#define _TIANAN_PLATFORM_CLIENT_H_

#include "icloud_platform_client.h"
#include "iengine.h"
#include "status_control.hpp"
#include "ireceiver.h"
#include "tianan_http_client.h"
#include "tianan_mqtt_client.h"

class tianan_platform_client:public icloud_platform_client
{
public:
	tianan_platform_client();
	~tianan_platform_client();
public : // interface icloud_platform_client 
	virtual int init() override;
	virtual int destory() override;
	virtual void get_cloud_platform_id(string &id) override;
	virtual int get_cloud_platform_status() override;
	virtual void ontime_check(long long cur_ms)override;
private:
	tianan_http_client m_tianan_http_client;
	tianan_mqtt_client m_tianan_mqtt_client;


};
#endif //_TIANAN_PLATFORM_CLIENT_H_
