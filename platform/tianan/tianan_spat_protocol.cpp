#include "tianan_spat_protocol.h"

#include "stdafx.h"
#include "engine_module_macro.h"
#include "engine_module_log.h"

tianan_spat_protocol::tianan_spat_protocol()
{

}

tianan_spat_protocol::~tianan_spat_protocol()
{
	
}
	
int tianan_spat_protocol::fetch_frame(const char* pdata, uint32_t len,ta_spat_frame_head &frame_head)
{
    if(len < sizeof(ta_spat_frame_head)) 
    {
        WLE("len[%d] is invalid ",len); 
        return RET_FAIL;
    }
    ta_spat_frame_head *pHead =  (ta_spat_frame_head*) pdata; 
    frame_head =  *pHead; 
    if(RET_OK!=check_frame(frame_head,len))
    {
        return RET_FAIL;
    }

    

    return RET_OK;
}


int tianan_spat_protocol::check_frame(ta_spat_frame_head & frame_head,uint32_t len)
{
    
    WLD(" command %d",frame_head.command); 
    if(frame_head.command != 1 )
    {// 命令非 1 刚不是 spat 相关消息，则直接退出
        WLD("invalue commmand %d ",frame_head.command); 
        return RET_FAIL; 
    }
    if(len != frame_head.frame_len + 4)
    {
        WLD("frame_head.frame_len %d len %d ",frame_head.frame_len, len); 
        return RET_FAIL; 
    }

    return RET_OK;
}