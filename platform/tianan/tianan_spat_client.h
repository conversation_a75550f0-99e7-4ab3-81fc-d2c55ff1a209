/***********************************************
版本信息： 金溢科技版本权所有，保留一切权利
文件名称： tianan_spat_client.h
作者：张明
开始日期：2024-11-29 12:18:22
完成日期：
当前版本号: v 0.0.1
主要功能: 接入天安-襄阳信号机协议
版本历史:
***********************************************/

#ifndef _TIANAN_SPAT_CLIENT_H_
#define _TIANAN_SPAT_CLIENT_H_
#include "error_code.h"
#include "stdafx.h"
#include "engine_module_macro.h"
#include "iengine.h"
#include "ireceiver.h"

class tianan_spat_client : public ireceiver
{
public:
	tianan_spat_client();
	virtual  ~tianan_spat_client();
	int init();
	int destory();
public: // ireceiver 
    virtual void read_cb(engine_conn_handler & handler, const char* msg, int len); 
    virtual void event_cb(int fd, short events );

private:
	string m_ip{"0.0.0.0"}; 
	int m_port{8877}; 

	engine_conn_handler m_udp_handler; 


};
#endif //_TIANAN_SPAT_CLIENT_H_
