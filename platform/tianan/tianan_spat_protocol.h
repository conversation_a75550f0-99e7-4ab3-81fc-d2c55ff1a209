/***********************************************
版本信息： 金溢科技版本权所有，保留一切权利
文件名称： tianan_spat_protocol.h
作者：张明
开始日期：2024-11-29 13:27:11
完成日期：
当前版本号: v 0.0.1
主要功能: 天安-襄阳信号机协议,解释
版本历史:
***********************************************/

#ifndef _TIANAN_SPAT_PROTOCOL_H_
#define _TIANAN_SPAT_PROTOCOL_H_

#include "stdafx.h"
#include <stdint.h>

#pragma pack (1)
struct ta_spat_frame_head
{
	uint8_t head_flag[4];    //2 byte	魔术字-协议包开始字段 ST
    uint8_t command;
	uint8_t frame_len;
	uint8_t error_code; 
	uint8_t cross_id; 
};

struct ta_spat_frame_data
{

}; 
#pragma pack()

class tianan_spat_protocol
{
public:
	tianan_spat_protocol();
	~tianan_spat_protocol();
	int fetch_frame(const char* pdata, uint32_t len,ta_spat_frame_head &frame_head);
private:
	int check_frame(ta_spat_frame_head & frame_head,uint32_t len);


};
#endif //_TIANAN_SPAT_PROTOCOL_H_
