local proto_name = "v2xmec";  
local v2x_proto_port = 2819;
local v2x_proto = Proto(proto_name,"v2x mec proto");


--定义各个字段
local magic_num   = ProtoField.uint32 (proto_name .. ".magic_num", "magic_num");
local reserve_1   = ProtoField.uint16 (proto_name .. ".reserve_1", "reserve_1");
local data_len   = ProtoField.uint16 (proto_name .. ".data_len", "data_len");
local src_id   = ProtoField.uint16 (proto_name .. ".src_id", "src_id");
local des_id   = ProtoField.uint16 (proto_name .. ".des_id", "des_id");
local time   = ProtoField.uint32 (proto_name .. ".time", "time");
local ms   = ProtoField.uint16 (proto_name .. ".ms", "ms");
local reserve_2   = ProtoField.uint16 (proto_name .. ".reserve_2", "reserve_2");
local seq_id   = ProtoField.uint16 (proto_name .. ".seq_id", "seq_id");
local reserve_3   = ProtoField.uint16 (proto_name .. ".reserve_3", "reserve_3");
local packet_type   = ProtoField.uint8 (proto_name .. ".packet_type", "packet_type");
local packet_encrypt_len   = ProtoField.uint8 (proto_name .. ".packet_encrypt_len", "packet_encrypt_len");
local pakcet_devtype   = ProtoField.uint8 (proto_name .. ".pakcet_devtype", "pakcet_devtype");
local pakcet_type_reserve   = ProtoField.uint8 (proto_name .. ".pakcet_type_reserve", "pakcet_type_reserve");
local version   = ProtoField.uint16 (proto_name .. ".version", "version");
local crc16   = ProtoField.uint16 (proto_name .. ".crc16", "crc16");
local body = ProtoField.bytes(proto_name .. ".body","body")



-- 添加到协议解释里面
v2x_proto.fields = { magic_num, reserve_1,data_len,
                    src_id,des_id,time,ms,reserve_2,
                    seq_id,reserve_3,packet_type,packet_encrypt_len,
                    pakcet_devtype,pakcet_type_reserve,version,
                    crc16,body };


--这里是获取data这个解析器
local data_dis = Dissector.get("data")


function v2x_proto.dissector (buf,pkt,root)


    local buf_len = buf:len();
    -- set the protocol column to show our protocol name
    pkt.cols.protocol:set("v2x_mec")
    
    local v_magic_num = buf(0, 4)
    local v_reserve_1 = buf(4, 2)
    local v_data_len = buf(6, 2)
    local v_src_id = buf(8, 2)
    local v_des_id = buf(10, 2)
    local v_time = buf(12, 4)
    local v_ms = buf(16, 2)
    local v_reserve_2 = buf(18, 2)
    local v_seq_id = buf(20, 2)
    local v_reserve_3 = buf(22, 2)
    local v_packet_type = buf(24, 1)
    local v_packet_encrypt_len = buf(25, 1)
    local v_pakcet_devtype = buf(26, 1)
    local v_pakcet_type_reserve = buf(27, 1)
    local v_version = buf(28, 2)
    local v_crc16 = buf(30, 2)
    local v_body = buf(32, buf_len-32)

    --现在知道是我的协议了，放心大胆添加Packet Details
    --local t = root:add(proto,buf);
    local t = root:add(v2x_proto, buf)

        --在Packet List窗格的Protocol列可以展示出协议的名称
    pkt.cols.protocol = proto_name;
    
    t:add(magic_num,v_magic_num)
    t:add(reserve_1,v_reserve_1)
    t:add(data_len,v_data_len)
    t:add(src_id,v_src_id)
    t:add(des_id,v_des_id)
    t:add(time,v_time)
    t:add(ms,v_ms)
    t:add(reserve_2,v_reserve_2)
    t:add(seq_id,v_seq_id)
    t:add(reserve_3,v_reserve_3)
    t:add(packet_type,v_packet_type)
    t:add(packet_encrypt_len,v_packet_encrypt_len)
    t:add(pakcet_devtype,v_pakcet_devtype)
    t:add(pakcet_type_reserve,v_pakcet_type_reserve)
    t:add(version,v_version)
    t:add(crc16,v_crc16);
    t:add(body,v_body);

    return buf_len;
end

local data_dis = Dissector.get("data")


--------------------------------------------------------------------------------
-- We want to have our protocol dissection invoked for a specific TCP port,
-- so get the TCP dissector table and add our protocol to it.
local function enableDissector()
    -- using DissectorTable:set() removes existing dissector(s), whereas the
    -- DissectorTable:add() one adds ours before any existing ones, but
    -- leaves the other ones alone, which is better
    DissectorTable.get("tcp.port"):add(v2x_proto_port, v2x_proto)
end
-- call it now, because we're enabled by default
enableDissector()


