
local proto_name = "rus";  
local rsu_port = 31009;

local rsu_proto = Proto(proto_name,"rsu proto");

--定义各个字段
local f_magic_num   = ProtoField.uint16 (proto_name .. ".magic_num", "magic_num");
local f_version   = ProtoField.uint8 (proto_name .. ".version", "version");
local f_data_len   = ProtoField.uint32 (proto_name .. ".data_len", "data_len");
local f_command = ProtoField.uint8(proto_name .. ".commmand","command");

local f_body = ProtoField.bytes(proto_name .. ".body","body")



-- 添加到协议解释里面
rsu_proto.fields = { f_magic_num, f_version,f_data_len,f_command,f_body};


--这里是获取data这个解析器
local data_dis = Dissector.get("data")


function rsu_proto.dissector (buf,pkt,root)


    local buf_len = buf:len();
    -- set the protocol column to show our protocol name
    --pkt.cols.protocol:set("rsu_proto")
    
    local v_magic_num = buf(0, 2)
    local v_version =  buf(2,1)
    local v_data_len =  buf(3,4)
    local v_command =  buf(7,1)
    local v_body = buf(8, buf_len-8);


    --现在知道是我的协议了，放心大胆添加Packet Details
    local t = root:add(rsu_proto, buf)

    --在Packet List窗格的Protocol列可以展示出协议的名称
    pkt.cols.protocol = proto_name;
    
    t:add(f_magic_num,v_magic_num);
    t:add(f_version,v_version);
    t:add(f_data_len,v_data_len);
    t:add(f_command,v_command);
    t:add(f_body,v_body);

    return buf_len;
end



--------------------------------------------------------------------------------
-- We want to have our protocol dissection invoked for a specific TCP port,
-- so get the TCP dissector table and add our protocol to it.
local function enableDissector()
    -- using DissectorTable:set() removes existing dissector(s), whereas the
    -- DissectorTable:add() one adds ours before any existing ones, but
    -- leaves the other ones alone, which is better
    DissectorTable.get("tcp.port"):add(rsu_port, rsu_proto)
end
-- call it now, because we're enabled by default
enableDissector()

