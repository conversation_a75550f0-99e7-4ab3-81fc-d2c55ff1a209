dev_per_disk_name=mmcblk3    #disk_name
dev_per_disk_sector_size=512    #sector_size
dev_per_net_interface=eth0    #net_interface_name
dev_regist_httpurl=http://v2x.genvict.com:8000/dev/api/device/    #云平台注册URL
dvin_debug_client_port=7130    #调试选项-连接7130端口,方便输出调试信息
edgeid=0    #MEC设备ID/接入边缘计算单元时有此值
esn=gv_esn_0    #设备序列号
gantryno=GVKJ-0-0    #门架编号
gv_mec_ip=127.0.0.1    #金溢MEC通信IP
gv_mec_port=30024    #金溢MEC本地监听端口
gv_sdk_enable=0    #金溢SDK是否开启，0:关闭；1:开启，默认 0
gv_sdk_ip=127.0.0.1    #金溢SDK通信IP
gv_sdk_port=30025    #金溢SDK本地监听端口
log_dir=/genvict/log/mec_channel/    #log_save_path
log_file_num=20    #total log file num  [1,40]
log_file_size=52428800    #log_file_size defalut 52428800 50MB [10485760,104857600]
log_flush_ms=5000    #log flush ms,defalut 5000 [1000,60000]
log_keep_sec=604800    #log_keep second ,defalut 259200 (3 day ) [86400,8640000]
log_level=1    #0:debug; 1:info ; 2 warn ; 3 error, default 1  [0,3]
log_std_out=1    # 0: logfile; 1:cout,  default 0 [0,1]
mqtt_ca_path=/genvict/etc/ca.pem    #mqtt ca
mqtt_client_id=    #mqtt clientid 
mqtt_host=127.0.0.1    #mqtt host
mqtt_passwd=test_passwd    #mqtt passwd
mqtt_port=2888    #mqtt port
mqtt_tls=0    # 0:close; 1:open
mqtt_user=test_user    #mqtt username 
sys_dvin_debug_client_port_enable=0    #调试选项-是否开启打印7130端口调试信息,默认0  [0,1]
sys_hz=2    #服务运行 hz, 默认 10  [1,10000]
sys_platform=2    #接入平台类型 0:调试; 1:空，不接入平台; 2:金溢; 3:百度; 4:博远; 5:高兴新 ;6:天安; 7 华为 [0,8]
sys_print_protocol=0    #是否打印解码后的协议帧，方便调试，默认不打印 [0,1]
test_tcp_client_tool_send_size=1    #tcp_client_tool 测试参数，共发送多少条请求 [1,10000000]
v2x_heartbeat_enable=1    #RSU设备是否开启心跳发送, 0:不开启, 1:开启,默认1 开启 [0,1]
v2x_heartbeat_ms=5000    #RSU设备心跳发送间隔,单位:毫秒，默认 5000 [1000,60000]
v2x_reconnect_interval_ms=3000    #与RSU重连接间隔,单体：毫秒，默认 3000 [1000,60000]
v2x_reconnect_timeout_ms=10000    #与RSU重连接超时,单体：毫秒，默认 10000 [1000,60000]
v2x_service_ip=127.0.0.1    #RSU IP,默认 127.0.0.1
v2x_service_port=31009    #RSU 端口,默认31009
v2x_type=1    #v2x设备类型, 1:OBU 2:RSU [1,2]
