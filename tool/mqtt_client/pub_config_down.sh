mosquitto_pub  -t "/obu/20000000/config/down" -m '{"config":{"rsu":{"map":{"msg_file_path":"/genvict/etc/cfg_msg_file/map_data.json"}},"system":{"ant_sw":0,"coordinate_type":0,"id":"3u1Syv37","log_level":4,"ntrip":{"enable":0,"password":"cdgvwh1","username":"qxcmobu005"},"security":{"enable_security":0,"security_cert_dir":"/home/<USER>/certs","security_module":1}},"v2x_mass":{"debug":true,"edgeid":3,"esn":"rsutest1","gantryno":"LJFJ-W-0","gv_httpurl":"https://v2x.genvict.com:8000/dev/api/device/","logfile":"/tmp/log/mass.txt","loglevel":1,"logsize":52428800,"mqtt_ca":"/genvict/etc/ca.pem","mqtt_host":"*************","mqtt_port":1883,"mqtt_tls":false,"rsutype":1,"v2x_host":"127.0.0.1","v2x_port":31009,"v2x_type":1}},"deviceId":123456,"messageId":1688634463276,"sessionId":"2","timestamp":1688634463276}' -d
