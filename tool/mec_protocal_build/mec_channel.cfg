baidu_mec_udp_ip=127.0.0.1    #百度MEC 监听IP,默认 127.0.0.1
baidu_mec_udp_port=30501    #百度MEC 监听端口,默认 30501 
baidu_offline_sec=15    #百度MEC离线最大超时, 单机秒, 默认15, 超过15秒无心跳包则认为MEC离线，0: 表示不检查 [0,60]
baidu_rsu_udp_ip=127.0.0.1    #RSU udp监听IP, 接收百度MEC发送数据,默认 127.0.0.1
baidu_rsu_udp_port=30500    #RSU udp监听PORT , 默认30500
debug_cloud_platform_status=0    #调试选项-云平台状态码
debug_dev_id=00000000    #调试选项-云平台下发设备ID
debug_mec_json_save_path=/data1/work/datum/rsu-config/debug_mec_json/    #调试选项-设备收发的数据保存的路径
debug_mec_print_asn=0    #是否打印收到asn消息, 0 不打印;1 打印, 默认0
debug_mec_status=0    #调试选项-mec 状态码
dvin_debug_client_port=7130    #调试选项-连接7130端口,方便输出调试信息
gv_edgeid=0    #金溢-MEC设备ID/接入边缘计算单元时有此值
gv_esn=gv_esn_0    #金溢-设备序列号
gv_gantryno=GVKJ-0-0    #金溢-门架编号
gv_httpurl=http://v2x.genvict.com:8000/dev/api/device/    #金溢云平台注册URL
gv_v2x_type=1    #1为OBU ; 2 RUS,默认为 1
gxx_mec_heartbeat_ms=300    #MEC设备心跳发送间隔,单位：毫秒,默认 300 [100,60000]
gxx_mec_ip=127.0.0.1    #MEC IP地址
gxx_mec_login_wait_ack_ms=3000    #等待MEC 登录回包最长时间间隔，单体：毫秒，默认 3000 [1000,10000]
gxx_mec_passwd=gosuncn@2013    #MEC 登录用户ID 对应的密码
gxx_mec_port=2819    #MEC 端口号
gxx_mec_reconnect_interval_ms=3000    #与MEC重连接间隔,单体：毫秒，默认 3000 [1000,60000]
gxx_mec_reconnect_timeout_ms=10000    #与MEC重连接超时,单体：毫秒，默认 10000 [1000,60000]
gxx_mec_send_pack_size=10240    #与MEC通信时,发送包体最大buffer,单位：字节,默认10240 [10240,1024000]
gxx_mec_username=GOSUNCN_MEC_DEFAULT_ESN    #MEC 登录用户ID
log_dir=/genvict/log/mec_channel/    #日志文件保存路径
log_flush_ms=5000    #日志刷写到磁盘间隔，单位：毫秒，默认 5000 [1000,60000]
log_keep_sec=604800    #日志保留天数，单位：秒, 默认 604800(7天) [86400,8640000]
log_level=1    #日志输出等级, 0:debug; 1:info ; 2 warn ; 3 error, 默认 1  [0,3]
log_split_type=2    #日志切分类型, 1:按分钟; 2:按小时; 3:按天 ,默认 2 [1,3]
log_std_out=0    #调试配置项-日志是否转出到标准输出, 0: 转出日志到日志文件; 1:输出日志到标准输出, 默认0 [0,1]
mqtt_ca_path=/genvict/etc/ca.pem    #mqtt服务器证书
mqtt_host=127.0.0.1    #mqtt host
mqtt_passwd=test_passwd    #mqtt passwd
mqtt_port=2888    #mqtt port
mqtt_regist_url=    #云平台注册URL 
mqtt_tls=0    #是否开启安全 0:不开; 1:开
mqtt_type=0    #mqtt 协议,0:不使用mqtt ; 1:GV; 2:Gosun ; 3:福建长峰
mqtt_user=test_user    #mqtt username 
rsu_heartbeat_enable=1    #RSU设备是否开启心跳发送, 0:不开启, 1:开启,默认1 开启 [0,1]
rsu_heartbeat_ms=5000    #RSU设备心跳发送间隔,单位:毫秒，默认 5000 [1000,60000]
rsu_reconnect_interval_ms=3000    #与RSU重连接间隔,单体：毫秒，默认 3000 [1000,60000]
rsu_reconnect_timeout_ms=10000    #与RSU重连接超时,单体：毫秒，默认 10000 [1000,60000]
rsu_service_ip=127.0.0.1    #RSU IP,默认 127.0.0.1
rsu_service_port=31009    #RSU 端口,默认31009
sys_dvin_debug_client_port_enable=0    #调试选项-是否开启打印7130端口调试信息,默认0  [0,1]
sys_hz=10    #服务运行 hz, 默认 10  [1,10000]
sys_mec_type=4    #MEC 类型 0:gevient 1:debug; 2:高兴新;  3:百度 , 4: 空 mec圾示无mec;  默认1 [0,4]
sys_print_protocol=0    #是否打印解码后的协议帧，方便调试，默认不打印 [0,1]
