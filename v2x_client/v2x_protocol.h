/***********************************************
版本信息： 金溢科技版本权所有，保留一切权利
文件名称： v2x_protocol.h
作者：张明
完成日期：
当前版本号: v 0.0.1
主要功能:
版本历史:
***********************************************/

#ifndef _RSU_V2X_PROTOCOL_H_
#define _RSU_V2X_PROTOCOL_H_

#include "crc16.h"
#include "stdafx.h"
#include <list>
#include "hex_str.h"

using namespace std;


enum v2x_opt_enum
{
    c8d_msg_format_json = 1, //  
    c8d_msg_format_bin = 2,

};


/**
* @brief 数据方向 0为接收，1为发送
*/
enum v2x_report_direct_enum
{
    vrde_recv = 0,
    vrde_send = 1,
};



#pragma pack (1)

/**
* @brief v2x 协议帧-帧头
*/
struct  V2xHead
{
    uint8_t magic_num[2];    //2 byte	魔术字-协议包开始字段 FF
    uint8_t version;
    uint32_t data_len;  // 1字节命令+DATA域的长度, 不包括 crc 16
    uint8_t command;   //0x88 v2x消息，0x83 心跳
};

/**
* @brief v2x 确认帧
*/
struct V2xAck
{
    uint8_t seq;
};

/**
* @brief v2x 协议帧-心跳帧
*/
struct V2xHeartBeat
{
    char cloud_id[50];
    uint8_t status;
    char version[50];
    uint8_t mec_link_status;
    uint8_t cloud_link_status;
    uint8_t bsm_id[10];  // 暂时无用，为了协议兼容
};

/**
* @brief v2x 协议帧-配置管理
*/
struct V2xConfig
{
    uint8_t type; //1= OBU，2= RSU 
    uint8_t opt;  //1：设置 2：获取
};



/**
* @brief v2x 协议帧-扩展帧 0x8D
*/
struct V2xExtend
{
    uint8_t dev_type;   ///< 终端类型 1= OBU，2= RSU
    uint8_t msg_format; ///< 消息格式 1：json 2:二进制数据
    uint32_t data_len;  ///< 数据长度
};


/**
* @brief v2x 协议帧-类型定义
*/
enum rsp_type
{
    RSP_COMMMAND = 1,  ///< 消息命令确认帧（01H）
    RSP_CTRL = 2,      ///< 控制命令确认帧（02H）
    RSP_HEARTBEAT = 3, ///< 心跳指令（03H）
    RSP_WH_TEST = 4 , ///< 大规模测试（04H）未实现
    RSP_REPORT  = 5,   ///< 上报消息（05H）
    RSP_OBU_ALARM = 6,  ///< 上报OBU预警信息（06H）
    RSP_DEVALARM = 7,  ///< 上报设备告警信息（07H）
    RSP_UPER = 8,      ///< 确认帧（8H）
    RSP_ASN_BIN = 9,    ///< asn二进制帧（09H）
    RSP_DEBUG_REPORT = 0x0A, ///< 上报调试信息给 MEC (0x0aH)
    RSP_CONFIG_REPORT = 0x0C ,  ///< 配置上报(0x0c)
    RSP_REPORT_EXTEND  = 0x0D, ///<上报扩展信息（0DH）
    RSP_REPORT_MONITOR =  0x0E, ///<上报监控数据(0EH)

};



/**
* @brief v2x 协议帧-消息命令确认帧（01H）
*/
struct V2xRspCommand
{
};

/**
* @brief v2x 协议帧-控制命令确认帧（02H）
*/
struct V2xRspCtrl
{
    uint8_t status;	//0:成功  非0：失败
};

/**
* @brief v2x 协议帧-心跳指令（03H）
*/
struct V2xRspHeartBeat
{
    uint8_t dev_type;// 终端类型 1= OBU，2= RSU
    char id[8]; //终端ID
    uint8_t dev_status;   ///<  0正常；1异常
    uint8_t v2x_status;   ///<  0正常；1异常
    uint8_t gnss_status;  ///<  0正常；1异常
    char soft_ver[50];
    uint32_t pid; 
    uint32_t runtime; 
    uint32_t longitude; ///<设备坐标 
    uint32_t latitude;  ///<设备坐标
};



//上报大规模测试数据帧（04H） -- 先不支持

/**
* @brief v2x 协议帧-上报消息（05H）
*/
struct V2xRspReport
{
    uint8_t director ; //数据方向 0为接收，1为发送
    uint8_t *json ;
    //string json;
};

// 报OBU预警信息（06H）
struct V2xRspObuAlarm
{
    uint8_t *json;
};
/**
* @brief v2x 协议帧-上报设备告警信息（07H）
*/
struct V2xRspDevAlarm
{
    uint8_t reboot; //非0为重启
    char reserve[50]; //保留字节
};

/**
* @brief v2x 协议帧-确认帧（8H）
*/
struct V2xRspUPER
{

};

/**
* @brief v2x 协议帧-asn二进制帧（09H）
*/
struct V2xAsnBin
{
    uint8_t *json;

};

/**
* @brief v2x 协议帧-debugReport（0AH）
*/
struct V2xDebugReport
{
    uint8_t *json;

};




/**
* @brief v2x 协议帧-配置上报（0CH）
*/
struct V2xRspConfigReport
{
    uint8_t type; //1= OBU，2= RSU
    uint8_t opt;  //1：设置响应 2：获取响应
    uint8_t *json; //采用json格式
};



/**
* @brief v2x 协议帧-上报扩展帧 0x0D
*/
struct V2xReportExtend
{
    uint8_t dev_type;   ///< 终端类型 1= OBU，2= RSU
    uint8_t msg_format; ///< 消息格式 1：json 2:二进制数据
    uint32_t data_len;  ///< 数据长度
    uint8_t *json ;
};

/**
* @brief v2x 协议帧-上报扩展帧 0x0D
*/
struct V2xReportMonitor
{
    uint8_t *json;
};



/**
* @brief v2x 客户端协议回包协议帧
*/
struct v2x_client_respond
{
    V2xHead head;
    union DataFrame
    {
        V2xRspCommand commandRsp;
        V2xRspCtrl ctrlRsp;
        V2xRspHeartBeat heartBeatRsp;
        V2xRspReport  reportRsp;
        V2xRspObuAlarm obuAlarm;
        V2xRspDevAlarm alarmRsp;
        V2xRspUPER uperRsp;
        V2xAsnBin asnUper;
        V2xDebugReport debugRsp;
        V2xRspConfigReport configRsp;
        V2xReportExtend reportExtend;
        V2xReportMonitor reportMonitor;
    } data;
    int json_data_len;
};
#pragma pack()

struct asn_TYPE_descriptor_s;
struct V2xHead;


/**
* @brief v2x 客户端协议解释类
*/
class v2x_protocol final
{
public:
    v2x_protocol();
    ~v2x_protocol();
    int init();
public:
    int build_uper(const asn_TYPE_descriptor_s *td,const void *sptr,
                   const string & key,packet_info & encode_buf,packet_info & send_data);

    int build_heartbeat(const packet_info & buf,packet_info & data);
    
    int build_v2x_json_msg(const packet_info & json_data,packet_info & send_buf);

    int build_config(int opt ,const packet_info & json_data,packet_info &send_buf);

    int build_extend(uint8_t msg_format ,const packet_info & json_data,packet_info &send_buf);



    int fetch_frame(const char* pdata, uint32_t len,list<v2x_client_respond> &frame_list);

    int build_ack(const uint8_t command,packet_info & send_buf);
private:
    int ntoh_head(V2xHead & head);
    bool check_head(V2xHead & head);

    int build_head(V2xHead & head,int data_len,uint8_t command);
    int fetch_body(v2x_client_respond & rsp,const char *p);
    
    int calc_crc16(packet_info & send_data,V2xHead & head,int data_len);
private:
    hex_str m_hex_str;
    string m_2df_buffer;
    crc16 m_crc16;
    string m_fetch_buffer; // 必须使用双缓冲（m_fetch_buffer+m_left_buffer），因为 fetch_body 会返回 m_fetch_buffer 内数据
    string m_left_buffer;  //
};
#endif //_RSU_V2X_PROTOCOL_H_
