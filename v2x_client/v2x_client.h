/***********************************************
版本信息： 金溢科技版本权所有，保留一切权利
文件名称： v2x_client.h
作者：张明
完成日期：
当前版本号: v 0.0.1
主要功能:
版本历史:
***********************************************/

#ifndef _V2X_CLIENT_H_
#define _V2X_CLIENT_H_
#include <stdint.h>
#include "ireceiver.h"
#include "iengine.h"
#include "stdafx.h"
#include "status_control.hpp"
#include "log.h"
#include "v2x_protocol.h"
#include <unordered_map>
#include "imec_client.h"
#include "mec_asn/GoEMECMessageFrame.h"
#include "command_pattern.hpp"
#include "engine_event_notice.hpp"
#include "nlohmann/json.hpp"
#include "thread_worker.hpp"
#include "time_task.h"
#include <list>

using json = nlohmann::json;
using namespace std;

struct bufferevent ;

#define NOTICE_REPORT_PAR_TYPE_LIST string,uint8_t,string,string,shared_ptr<json>

/**
* @brief rsu 客户端类
*/
class v2x_client final :public ireceiver
{

    typedef status_control<v2x_client,status_enum> status_control_type;
    typedef status_control_type::status_info  status_info;

    typedef command_pattern<NOTICE_REPORT_PAR_TYPE_LIST>  cp_rsp_report_type;
    typedef command_pattern<rsp_type,v2x_client_respond>  cp_rsp_command_type;
public:
    v2x_client();
    ~v2x_client();
    int init();
    void destory();

    int send_cv2x_asn_88H(const MessageFrame_t & cv2xFrame); //0x88 命令
    int send_cv2x_json_81H(char *json_data, uint32_t json_data_len );// 0x81 命令
    int send_config_8CH(int opt,char * json_data, uint32_t json_data_len); //0x8c 命令
    int send_cv2x_extend_8DH(uint8_t msg_format, char *data, uint32_t data_len); //0x8D 命令
    int send_last_command_ack(); 

    bool connect_is_ok(); 

    void ontime_check(long long cur_ms);

    inline cp_rsp_command_type & get_command_pattern()
    {
        return m_command_pattern;
    }
    inline cp_rsp_report_type & get_rsp_report_command()
    {
        return m_rsp_report_command; 
    }
    inline status_control_type & get_statsu_control()
    {
        return m_status_control; 
    }
    
    
    inline V2xRspHeartBeat get_heartbeat_info()
    {
        return m_heartBeatRsp;
    }
public: // ireceiver interface
    virtual void read_cb(engine_conn_handler & handler, const char* msg, int len) override;
    virtual void event_cb(int fd, short events ) override ;

private:
    int load_send_data();
    bool add_send_data(char *pLineData,size_t line_size); 
    // 转发接口
    int transmit_cv2x_msg(const asn_TYPE_descriptor_s *td,const void *sptr,
                          uint16_t max_data_len,const string & key);

    int get_frame_key(const MessageFrame_t & cv2xFrame,string &key);
    int send_data(const char *pData,int len);
    int cal_frame_key_id(int type,int subtype);


    int proc_status_disconnect(status_info & status);
    int proc_status_connected(status_info & status);
    int proc_status_connecting(status_info & status);

    //int proc_sta

    int proc_rsp_command(const rsp_type & type,v2x_client_respond & frame);
    int proc_rsp_ctrl(const rsp_type & type,v2x_client_respond & frame);
    int proc_rsp_heartbeat(const rsp_type & type,v2x_client_respond & frame);
    int proc_rsp_report(const rsp_type & type,v2x_client_respond & frame);
    int proc_rsp_obu_alarm(const rsp_type & type,v2x_client_respond & frame);
    int proc_rsp_devalarm(const rsp_type & type,v2x_client_respond & frame);
    int proc_rsp_uper(const rsp_type & type,v2x_client_respond & frame);
    int proc_rsp_asn_bin(const rsp_type & type,v2x_client_respond & frame);
    int proc_rsp_debug_report(const rsp_type & type,v2x_client_respond & frame);
    int proc_rsp_config_report(const rsp_type & type,v2x_client_respond & frame);
    int proc_rsp_report_extend(const rsp_type & type,v2x_client_respond & frame);
    int proc_rsp_report_monitor(const rsp_type & type,v2x_client_respond & frame); 
    

    int rsp_report_decode_thread(v2x_client_respond & frame,string&json);
    

    int send_ack(uint8_t command);


    void time_task_heartbeat_send(long long cur_ms); 
    void time_task_send_file(long long cur_ms);
    void time_task_check_connect(long long cur_ms); 

private:
    v2x_protocol m_v2x_protocol;
    engine_conn_handler m_rd_handler;
    int m_heartbeat_ms {300};
    string m_rsu_uper_buffer;
    unordered_map <int,string> m_frame_key_map;
    status_control_type m_status_control;
    cp_rsp_command_type m_command_pattern;

    cp_rsp_report_type m_rsp_report_command;

    bool m_heartbeat_enable {true};
    V2xRspHeartBeat m_heartBeatRsp;

    uint8_t m_lastcmd{0};

    time_task m_time_task;
    int64_t m_last_recv_heartbeat_sec{0}; 

    list<string> m_send_data_list; 

    thread_worker_group m_report_decode_threads;

};
#endif //_V2X_CLIENT_H_
