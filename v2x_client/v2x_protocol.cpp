#include "v2x_protocol.h"
#include "stdafx.h"
#include "mec_asn/GoEMECMessageFrame.h"
#include "service.h"
#include "nlohmann/json.hpp"

using json = nlohmann::json;

#define COMMAND_V2X_JSON 0x81 //mqtt 下发的 josn 转发给 v2x 设备 
#define COMMAND_V2X_CONFIG 0x8c //2.1.5配置信息命令（8CH）
#define COMMAND_V2X 0x88
#define COMMAND_HEART_BEAT 0x83
#define COMMAND_EXTEND 0x8D

static const uint32_t V2xHeadSize =  sizeof(V2xHead);
static const uint32_t V2xHeartBeatSize = sizeof(V2xHeartBeat) ;
static const uint32_t V2xAckSize =  sizeof(V2xAck) ; 


v2x_protocol::v2x_protocol()
{

}

v2x_protocol::~v2x_protocol()
{

}

int v2x_protocol::init()
{
    m_hex_str.set_style(hex_str::c_type);
    return RET_OK;
}

int v2x_protocol::build_uper(const asn_TYPE_descriptor_s *asn_DEF,const void *sptr,
                                 const string & key,packet_info & encode_buf,packet_info & send_buf)
{
    V2xHead &head =  *(V2xHead*)encode_buf.pdata;

    // uper 编码
    char *uper_body =  encode_buf.pdata +  V2xHeadSize;
    int uper_body_size =  encode_buf.len  - V2xHeadSize;
    asn_enc_rval_t ec;
    ec = uper_encode_to_buffer(asn_DEF,NULL, sptr, uper_body, uper_body_size);
    if(ec.encoded == -1)
    {
        // encode fail;
        WLE("can not encode key %s faile_name %s",
            key.c_str(),ec.failed_type->name);

        return RET_FAIL;
    }
    // bit to byte
    uint32_t encode_byte =  (ec.encoded+7) / 8 ;

    m_hex_str.encode((unsigned char *)uper_body,encode_byte,m_2df_buffer);

    json j;
    j["type"] =  key;
    j["value"] = m_2df_buffer.c_str();
    string json_body = j.dump();
    //cout<<""<<json_body<<endl;
    uint32_t json_body_size = json_body.size();

    //copy json to  encode_buf
    memcpy(uper_body,json_body.c_str(),json_body_size);

    build_head(head,json_body.size(),COMMAND_V2X);

    // 计算 crc 16 -- 从VER到DATA所有字节的CRC16校验值，2字节
    char *p_crc_data = encode_buf.pdata+sizeof(head.magic_num);
    int crc_data_size = V2xHeadSize+json_body_size-sizeof(head.magic_num);
    uint16_t *pCRC16 = (uint16_t*)(uper_body+json_body_size);
    *pCRC16 = htons(m_crc16.encode_x25(p_crc_data,crc_data_size) );

    send_buf.pdata =  encode_buf.pdata;
    send_buf.len =  V2xHeadSize + json_body_size + sizeof(uint16_t);// head + body + crc16

    return RET_OK;
}


int v2x_protocol::build_heartbeat(const packet_info & buf,packet_info & send_buf)
{
    V2xHead &head =  *(V2xHead*)(buf.pdata);
    V2xHeartBeat &headbeat =  *(V2xHeartBeat*)(buf.pdata+V2xHeadSize);

    memset(&headbeat,0,V2xHeartBeatSize);
    memset(headbeat.cloud_id,0,sizeof(headbeat.cloud_id));
    string cloud_dev_id;
    mqtt_client_ref.get_cloud_platform_id(cloud_dev_id);
    size_t  cloud_id_size = cloud_dev_id.size();

    if(cloud_id_size>sizeof(headbeat.cloud_id))
    {
        cloud_id_size = sizeof(headbeat.cloud_id);
    }
    if(cloud_id_size > 0)
    {
        memcpy(headbeat.cloud_id,cloud_dev_id.c_str(),cloud_id_size);
    }
    headbeat.status = 0;
    strncpy(headbeat.version,VERSION_DEFINE,sizeof(headbeat.version)-1);
    headbeat.mec_link_status = mec_client_ref.get_mec_status();//mec_status;
    
    headbeat.cloud_link_status = mqtt_client_ref.get_cloud_platform_status();

    build_head(head,V2xHeartBeatSize,COMMAND_HEART_BEAT);
    char *p_crc_data = buf.pdata + sizeof(head.magic_num);
    int crc_data_size = V2xHeadSize + V2xHeartBeatSize - sizeof(head.magic_num);
    uint16_t *pCRC16 = (uint16_t*)(buf.pdata + V2xHeadSize + V2xHeartBeatSize );

    *pCRC16 = htons(m_crc16.encode_x25(p_crc_data,crc_data_size) );
    send_buf.pdata =  buf.pdata;
    send_buf.len  = V2xHeadSize+V2xHeartBeatSize+sizeof(uint16_t); // head + body + crc16

    return RET_OK;
}

int v2x_protocol::build_ack(const uint8_t command,packet_info & send_buf)
{
    V2xHead &head =  *(V2xHead*)(send_buf.pdata);
    V2xAck &ack =  *(V2xAck*)(send_buf.pdata+V2xHeadSize);
    static uint8_t seq = 0; 
    ack.seq = seq++;
    
    build_head(head,V2xAckSize,command|0x80);
    calc_crc16(send_buf,head,V2xAckSize);
    return RET_OK;
}

int v2x_protocol::build_v2x_json_msg(const packet_info & json_data,packet_info & send_buf)
{
    V2xHead &head =  *(V2xHead*)(send_buf.pdata);
    memcpy(send_buf.pdata+V2xHeadSize, json_data.pdata,json_data.len);
    build_head(head,json_data.len,COMMAND_V2X_JSON);
    calc_crc16(send_buf,head,json_data.len);
    return RET_OK;
}


int v2x_protocol::build_config(int opt ,const packet_info & json_data,packet_info &send_buf)
{
    V2xHead &head =  *(V2xHead*)(send_buf.pdata); 
    V2xConfig *pConfig = (V2xConfig*)(send_buf.pdata+ V2xHeadSize);
    int dev_type = config_ref.get_def(CK_V2X_TYPE,0);
    pConfig->type = dev_type;//1= OBU，2= RSU
    pConfig->opt = opt ; //1：设置 2：获取
    int len_config = sizeof(V2xConfig);
    if(opt == 1)
    {
        char *data =  send_buf.pdata+ V2xHeadSize+ len_config;
        len_config += json_data.len;
        memcpy(data,json_data.pdata,json_data.len);
    }
    build_head(head,len_config,COMMAND_V2X_CONFIG);
    calc_crc16(send_buf,head,len_config);
    return RET_OK;

}

int v2x_protocol::build_extend(uint8_t msg_format ,const packet_info & json_data,packet_info &send_buf)
{
    V2xHead &head =  *(V2xHead*)(send_buf.pdata); 
    V2xExtend *pExtend = (V2xExtend*)(send_buf.pdata+ V2xHeadSize);
    int dev_type = config_ref.get_def(CK_V2X_TYPE,0);
    pExtend->dev_type = dev_type;//1= OBU，2= RSU
    pExtend->msg_format =  msg_format;
    pExtend->data_len = htonl(json_data.len);

    int len_Extend = sizeof(V2xExtend) + json_data.len;
    char *data =  send_buf.pdata+ V2xHeadSize+ sizeof(V2xExtend);
    memcpy(data,json_data.pdata,json_data.len);

    build_head(head,len_Extend,COMMAND_EXTEND);
    calc_crc16(send_buf,head,len_Extend);
    return RET_OK;
}


int v2x_protocol::calc_crc16(packet_info & send_buf,V2xHead & head,int data_len)
{
    // 计算 crc 16 -- 从VER到DATA所有字节的CRC16校验值，2字节
    
    char *p_crc_data = send_buf.pdata+sizeof(head.magic_num);
    int crc_data_size = V2xHeadSize+data_len-sizeof(head.magic_num);
    uint16_t *pCRC16 = (uint16_t*)(send_buf.pdata + V2xHeadSize + data_len );
    *pCRC16 = htons(m_crc16.encode_x25(p_crc_data,crc_data_size) );
    send_buf.len =  V2xHeadSize + data_len + sizeof(uint16_t); // head + body + crc16


    return RET_OK;
}

int v2x_protocol::build_head(V2xHead & head,int data_len,uint8_t command)
{
    head.magic_num[0] =  0xff;
    head.magic_num[1] =  0xff;
    head.version = 0;
    head.data_len = htonl(data_len+1); // 1字节命令+DATA域的长度,不包括 crc16
    head.command =  command;//0x88;
    return RET_OK;
}

int v2x_protocol::fetch_frame(const char* pdata, uint32_t len,list<v2x_client_respond> &frame_list)
{
    if(m_left_buffer.size()!=0)
    {
        m_left_buffer.append(pdata,len);
        m_fetch_buffer.assign(move(m_left_buffer));
        m_left_buffer =  "";
        len = m_fetch_buffer.size();
        pdata = (const char*)m_fetch_buffer.c_str();
    }
    do
    {
        // 处理多帧
        if(V2xHeadSize > len)
        {
            // 回包不足一帧
            //temp_buf.append(pdata,len);
            m_left_buffer.append(pdata,len);
            break;
        }
        // copy 头，可能重复转码
        v2x_client_respond rsp;
        rsp.head = *(V2xHead*)(pdata);
        V2xHead & head =  rsp.head;
        ntoh_head(head);

        if(false == check_head(head))
        {
            m_left_buffer = "";
            break;
        }
        // 一帧数据 =  头大小 + data_len -  command
        uint32_t frame_len =  V2xHeadSize +  head.data_len - sizeof(head.command) + sizeof(uint16_t);

#if 0
        if(head.command == 5)
        {
            WLD("len %d  frame_len %d  head.data_len %d ",len, frame_len,head.data_len);
        }
#endif

        if(head.data_len > 0 )
        {
            if(frame_len > len)
            {
                // 缓冲
                m_left_buffer.append(pdata,len);
                break;
            }
        }

        if(RET_OK==fetch_body(rsp,pdata+V2xHeadSize))
        {
            frame_list.emplace_back(move(rsp));
        }
        len = len - frame_len;
        pdata =  pdata + frame_len;

    }
    while(len > 0 );

    if(frame_list.empty())
    {
        return RET_FAIL;
    }
    return RET_OK;
}


int v2x_protocol::ntoh_head(V2xHead & head)
{
    //head.magic_num  //uint8_t
    //head.version //uint8_t
    head.data_len =   ntohl(head.data_len);
    //head.command  //uint8_t

    return RET_OK;
}

bool v2x_protocol::check_head(V2xHead & head)
{
    if(0xff == head.magic_num[0] && 0xff == head.magic_num[1] )
    {
        return true;
    }
    return false;
}

int v2x_protocol::fetch_body(v2x_client_respond & rsp,const char *p)
{
    //WLD("command %d ",rsp.head.command);
    switch(rsp.head.command)
    {
        case rsp_type::RSP_COMMMAND : //消息命令确认帧（01H）
        {
            // do nothing
            // rsp.data.commandRsp;
            break;
        };
        case rsp_type::RSP_CTRL : //控制命令确认帧（02H）
        {
            rsp.data.ctrlRsp = *(V2xRspCtrl*)(p);
            break;
        };
        case rsp_type::RSP_HEARTBEAT : //心跳指令（03H）
        {
            rsp.data.heartBeatRsp = *(V2xRspHeartBeat*)(p);
            rsp.data.heartBeatRsp.pid = ntohl(rsp.data.heartBeatRsp.pid);
            rsp.data.heartBeatRsp.runtime = ntohl(rsp.data.heartBeatRsp.runtime);
            rsp.data.heartBeatRsp.longitude = ntohl(rsp.data.heartBeatRsp.longitude);
            rsp.data.heartBeatRsp.latitude = ntohl(rsp.data.heartBeatRsp.latitude);
            break;
        };
        case rsp_type::RSP_REPORT: // 上报数据 （05H）
        {
            rsp.data.reportRsp.director =  *(uint8_t*)(p);
            rsp.data.reportRsp.json = (uint8_t*)(p + sizeof(rsp.data.reportRsp.director));
            rsp.json_data_len =  rsp.head.data_len - sizeof(rsp.head.command) - sizeof(rsp.data.reportRsp.director);

            break;
        };
        case rsp_type::RSP_OBU_ALARM: ///< 上报OBU预警信息（06H）
        {
            rsp.data.obuAlarm.json = (uint8_t*)p;
            rsp.json_data_len = rsp.head.data_len - 1;

            break;
        }
        case rsp_type::RSP_DEVALARM : // 上报设备告警信息（07H）
        {
            rsp.data.alarmRsp = *(V2xRspDevAlarm*)(p);
            break;
        }
        case rsp_type::RSP_UPER : // V2X消息帧确认帧，取值08H
        {
            // do nothing
            //rsp.data.uperRsp = ;
            break;
        }

        case rsp_type::RSP_ASN_BIN :// v2x消息帧asn二进制帧，取值09H
        {
            rsp.data.asnUper.json = (uint8_t*)p;
            rsp.json_data_len = rsp.head.data_len - 1;
            break;
        }
        case rsp_type::RSP_DEBUG_REPORT : ///< 上报调试信息  取值0AH
        {
            rsp.data.debugRsp.json = (uint8_t*)p;
            rsp.json_data_len = rsp.head.data_len - 1;
            break;
        }
        case rsp_type::RSP_CONFIG_REPORT : ///< 配置上报(0x0c)
        {
            rsp.data.configRsp =  *(V2xRspConfigReport*)(p);
            rsp.data.configRsp.json =  (uint8_t*)(p + sizeof(V2xRspConfigReport)-sizeof(rsp.data.configRsp.json));
            rsp.json_data_len =  rsp.head.data_len-1 - sizeof(V2xRspConfigReport) + sizeof(rsp.data.configRsp.json);
            break;
        }
        case rsp_type::RSP_REPORT_EXTEND :  ///<上报扩展信息（0DH）
        {
            rsp.data.reportExtend =  *(V2xReportExtend*)(p); 
            rsp.data.reportExtend.data_len =  ntohl(rsp.data.reportExtend.data_len);
            rsp.data.reportExtend.json =  (uint8_t*)(p + sizeof(V2xReportExtend) - sizeof(rsp.data.reportExtend.json));
            rsp.json_data_len =  rsp.data.reportExtend.data_len;
            break;
        }
        case rsp_type::RSP_REPORT_MONITOR : ///<上报监控数据(0EH)
        {
            rsp.data.reportMonitor.json = (uint8_t*)p;
            rsp.json_data_len = rsp.head.data_len - 1;
            break;
        }  
        
        default:
        {

            return RET_FAIL;
        }
    }

    //进行 crc16 校验



    //帧尾处理一下。 如json 
    char *pTail =  (char*)(p + (rsp.head.data_len-1)); 
    *pTail = '\0';// 此处会把 crc16 干 掉

    return RET_OK;
}


