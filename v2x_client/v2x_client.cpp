#include "v2x_client.h"
#include "stdafx.h"
#include "event_engine.h"
#include "event2/event.h"
#include "event2/bufferevent.h"
#include <sys/types.h>
#include <sys/socket.h>
#include <unistd.h>
#include <fcntl.h>
#include <arpa/inet.h>
#include <netinet/tcp.h>
#include "service.h"
#include "hex_str.h"
#include <list>
#include <atomic>
#include "dir_op.h"
using namespace std;


#define BUFF_APPEND_SIZE  20

v2x_client::v2x_client():m_status_control(status_enum::disconnect,"v2x_client_status",this)
    ,m_report_decode_threads(config_ref.get_def(CK_V2X_DECODE_THREAD_SIZE,0))
{
    m_heartBeatRsp.dev_type = 0;// 表示没有收到心跳包
    m_rsu_uper_buffer.resize(1024*1024);
    int reconnect_interval_ms  = config_ref.get_def(CK_V2X_RECONNECT_INTERVAL_MS,3000) ;
    int reconnetc_timeout_ms = config_ref.get_def(CK_V2X_RECONNECT_TIMEOUT_MS,10000) ;
    
 
    m_heartbeat_enable = config_ref.is_enable(CK_V2X_HEARTBEAT_ENABLE);

    m_status_control.regist_status(status_enum::disconnect,&v2x_client::proc_status_disconnect,"disconnect",reconnect_interval_ms);
    m_status_control.regist_status(status_enum::connected,&v2x_client::proc_status_connected,"connected",1000);
    m_status_control.regist_status(status_enum::connecting,&v2x_client::proc_status_connecting,"connecting",reconnetc_timeout_ms);

    // 注册回包处理函数
    m_command_pattern.regist_command(rsp_type::RSP_COMMMAND,this,std::bind(&v2x_client::proc_rsp_command, FUN_BIND_2));
    m_command_pattern.regist_command(rsp_type::RSP_CTRL,this,std::bind(&v2x_client::proc_rsp_ctrl, FUN_BIND_2));
    m_command_pattern.regist_command(rsp_type::RSP_HEARTBEAT,this,std::bind(&v2x_client::proc_rsp_heartbeat, FUN_BIND_2));
    m_command_pattern.regist_command(rsp_type::RSP_REPORT,this,std::bind(&v2x_client::proc_rsp_report, FUN_BIND_2));
    m_command_pattern.regist_command(rsp_type::RSP_OBU_ALARM,this,std::bind(&v2x_client::proc_rsp_obu_alarm, FUN_BIND_2));
    
    m_command_pattern.regist_command(rsp_type::RSP_DEVALARM,this,std::bind(&v2x_client::proc_rsp_devalarm, FUN_BIND_2));
    m_command_pattern.regist_command(rsp_type::RSP_UPER,this,std::bind(&v2x_client::proc_rsp_uper, FUN_BIND_2));
    m_command_pattern.regist_command(rsp_type::RSP_ASN_BIN,this,std::bind(&v2x_client::proc_rsp_asn_bin, FUN_BIND_2));
    m_command_pattern.regist_command(rsp_type::RSP_CONFIG_REPORT,this,std::bind(&v2x_client::proc_rsp_config_report, FUN_BIND_2));
    m_command_pattern.regist_command(rsp_type::RSP_DEBUG_REPORT,this,std::bind(&v2x_client::proc_rsp_debug_report, FUN_BIND_2));
    m_command_pattern.regist_command(rsp_type::RSP_REPORT_EXTEND,this,std::bind(&v2x_client::proc_rsp_report_extend, FUN_BIND_2));
    m_command_pattern.regist_command(rsp_type::RSP_REPORT_MONITOR,this,std::bind(&v2x_client::proc_rsp_report_monitor, FUN_BIND_2));


    m_frame_key_map[cal_frame_key_id(MessageFrame_PR_bsmFrame,0)]  = "bsm";
    m_frame_key_map[cal_frame_key_id(MessageFrame_PR_mapFrame,0)]  = "map";
    m_frame_key_map[cal_frame_key_id(MessageFrame_PR_rsmFrame,0)]  = "rsm";
    m_frame_key_map[cal_frame_key_id(MessageFrame_PR_spatFrame,0)] = "spat";
    m_frame_key_map[cal_frame_key_id(MessageFrame_PR_rsiFrame,0)]  = "rsi";


    m_frame_key_map[cal_frame_key_id(MessageFrame_PR_msgFrameExt,value_PR_TestMsg)] = "test";
    m_frame_key_map[cal_frame_key_id(MessageFrame_PR_msgFrameExt,value_PR_RTCMcorrections)] = "rtcm";
    m_frame_key_map[cal_frame_key_id(MessageFrame_PR_msgFrameExt,value_PR_PAMData)] = "pam";
    m_frame_key_map[cal_frame_key_id(MessageFrame_PR_msgFrameExt,value_PR_CLPMM)] = "clpmm";
    m_frame_key_map[cal_frame_key_id(MessageFrame_PR_msgFrameExt,value_PR_PersonalSafetyMessage)] = "psm";
    m_frame_key_map[cal_frame_key_id(MessageFrame_PR_msgFrameExt,value_PR_RoadsideCoordination)] = "rsc";
    m_frame_key_map[cal_frame_key_id(MessageFrame_PR_msgFrameExt,value_PR_SensorSharingMsg)] = "ssm";
    m_frame_key_map[cal_frame_key_id(MessageFrame_PR_msgFrameExt,value_PR_VehIntentionAndRequest)] = "vir";
    m_frame_key_map[cal_frame_key_id(MessageFrame_PR_msgFrameExt,value_PR_VehiclePaymentMessage)] = "vpm";


    // 定时 任务配置
    int rsu_heartbeat_ms = config_ref.get_def(CK_V2X_HEARTBEAT_MS,5000) ;

    m_time_task.regist_task(std::bind(&v2x_client::time_task_heartbeat_send,FUN_BIND_1), rsu_heartbeat_ms); 
    m_time_task.regist_task(std::bind(&v2x_client::time_task_send_file,FUN_BIND_1), 1000); 
    m_time_task.regist_task(std::bind(&v2x_client::time_task_check_connect,FUN_BIND_1), 5000); 
    m_last_recv_heartbeat_sec =  app_clock_ref.now_sec(); // 秒数

}

v2x_client::~v2x_client()
{

}

int v2x_client::init()
{
    m_v2x_protocol.init();
    m_report_decode_threads.start();
    string ip= config_ref.get_def(CK_V2X_SERVICE_IP,"127.0.0.1");
    int port = config_ref.get_def(CK_V2X_SERVICE_PORT,31009);
    return engine_ref.regist_tcp_client(this,ip,port,m_rd_handler);

}

int v2x_client::load_send_data()
{

    if(false==config_ref.is_enable(CK_V2X_SEND_FILE_ENABLE))
    {
        return RET_OK;
    }
    const int line_buf_size = 128*1024;  
    string file_path; 
    config_ref.get(CK_V2X_SEND_FILE_PATH,file_path);
    auto fun =  std::bind(&v2x_client::add_send_data,FUN_BIND_2);
    dir_op::read_file(file_path,line_buf_size,fun);
    return RET_OK;
}
bool v2x_client::add_send_data(char *pLineData,size_t line_size)
{
    if(line_size == 0)
    {
        return true;
    }

    m_send_data_list.push_back(pLineData); 
    return true;
}

void v2x_client::destory()
{
    m_report_decode_threads.stop();
    engine_ref.unregist(this,m_rd_handler);
}

void v2x_client::ontime_check(long long cur_ms)
{
    m_status_control.call_status_fun(cur_ms);
}

void v2x_client::read_cb(engine_conn_handler & handler, const char* msg, int len)
{
    //WLD("Enter");
    list<v2x_client_respond> frame_list;
    m_v2x_protocol.fetch_frame(msg,len,frame_list);
    if(frame_list.empty())
    {
        return ;
    }
    for(auto & frame: frame_list)
    {
        m_command_pattern.do_command((rsp_type)frame.head.command,frame);
    }
}

int v2x_client::proc_rsp_command(const rsp_type & type,v2x_client_respond & frame)
{
    //WLD("Enter");
    return RET_OK;
}

int v2x_client::proc_rsp_ctrl(const rsp_type & type,v2x_client_respond & frame)
{
    //WLD("Enter");
    return RET_OK;
}

int v2x_client::proc_rsp_heartbeat(const rsp_type & type,v2x_client_respond & frame)
{
    m_heartBeatRsp =  frame.data.heartBeatRsp;
    WLD("Enter dev_type %d dev_status %d v2x_status %d gnss_status %d  soft_ver[%s] pid[%d] runtime %d pos[%d , %d]",
            m_heartBeatRsp.dev_type,
            m_heartBeatRsp.dev_status,
            m_heartBeatRsp.v2x_status,
            m_heartBeatRsp.gnss_status,
            m_heartBeatRsp.soft_ver,
            m_heartBeatRsp.pid,
            m_heartBeatRsp.runtime,
            m_heartBeatRsp.longitude,
            m_heartBeatRsp.latitude
        );
    
    m_last_recv_heartbeat_sec =  app_clock_ref.now_sec();
    return RET_OK;
}

int v2x_client::proc_rsp_report(const rsp_type & type,v2x_client_respond & frame)
{

    string json_s((const char*)frame.data.reportRsp.json,frame.json_data_len);
    send_ack(type);

    auto job_fun =  std::bind(&v2x_client::rsp_report_decode_thread,this,frame,std::move(json_s)); 
    if( false == m_report_decode_threads.accept_job(std::move(job_fun))) 
    {
        WLE("!!!!!!!!!!!!! Error m_report_decode_threads queue is full. drop this msg"); 
    }
    return RET_OK;
}

int v2x_client::rsp_report_decode_thread(v2x_client_respond & frame,string& json_s)
{
    try
    {
        auto j = json::parse(json_s);
        string type(move(j["type"].get<string>()));
        string str_value(move(j["value"].get<string>()));
        auto j_value_json = make_shared<json>(json::parse(str_value));
        if(type ==  "")
        {
            WLE("type is null, return %s ",json_s.c_str());
            return RET_OK;
        }
        auto job  = std::bind(&cp_rsp_report_type::do_command,
                                &m_rsp_report_command, 
                                std::move(type),
                                frame.data.reportRsp.director,
                                std::move(json_s),
                                std::move(str_value),
                                std::move(j_value_json)
                            ); 

        engine_ref.accept_job(std::move(job)); 
    }
    catch(exception & e)
    {
        WLE("catch error %s json %s ",e.what(),json_s.c_str());
        return RET_FAIL;
    }
    return RET_OK;
}

int v2x_client::proc_rsp_obu_alarm(const rsp_type & type,v2x_client_respond & frame)
{
    send_ack(type);
    return RET_OK;
}

int v2x_client::proc_rsp_devalarm(const rsp_type & type,v2x_client_respond & frame)
{
    //WLD("Enter");
    m_lastcmd =  type;
    return RET_OK;
}

int v2x_client::proc_rsp_uper(const rsp_type & type,v2x_client_respond & frame)
{
    //WLD("Enter");
    return RET_OK;
}

int v2x_client::proc_rsp_asn_bin(const rsp_type & type,v2x_client_respond & frame)
{
    //WLD("Enter");
    return RET_OK;
}

int v2x_client::proc_rsp_config_report(const rsp_type & type,v2x_client_respond & frame)
{
    return RET_OK;
}

int v2x_client::proc_rsp_debug_report(const rsp_type & type,v2x_client_respond & frame)
{
    m_lastcmd =  type;
    return RET_OK;
}

int v2x_client::proc_rsp_report_extend(const rsp_type & type,v2x_client_respond & frame)
{
    return RET_OK;
}
int v2x_client::proc_rsp_report_monitor(const rsp_type & type,v2x_client_respond & frame)
{
    return RET_OK;
}

void v2x_client::event_cb(int fd,short events )
{
    WLD("fd %d events %d",fd,events );

    if (events & BEV_EVENT_ERROR)
    {
        WLE("fd %d BEV_EVENT_ERROR %s",fd,strerror(EVUTIL_SOCKET_ERROR()));
        m_rd_handler.init();
        m_status_control.enter_status(status_enum::disconnect);
        return ;
    }
    if (events & BEV_EVENT_CONNECTED )
    {
        m_status_control.enter_status(status_enum::connected);
        WLI("fd %d BEV_EVENT_CONNECTED ",fd );
        return ;
    }

}

int v2x_client::cal_frame_key_id(int type,int subtype)
{
    return (type<<8) + subtype;
}

int v2x_client::get_frame_key(const MessageFrame_t & cv2xFrame,string &key)
{
    int sub_type = 0;
    if(cv2xFrame.present == MessageFrame_PR_msgFrameExt)
    {
        sub_type = cv2xFrame.choice.msgFrameExt.value.present;
    }
    int key_id = cal_frame_key_id(cv2xFrame.present,sub_type);
    auto it_find  = m_frame_key_map.find(key_id);
    if(it_find == m_frame_key_map.end())
    {
        WLE("Error type %d sub_type %d",cv2xFrame.present,sub_type);
        return RET_FAIL;
    }
    key = it_find->second;

    return RET_OK;
}

int v2x_client::send_cv2x_asn_88H(const MessageFrame_t & cv2xFrame)
{
    string key;
    if(RET_OK != get_frame_key(cv2xFrame,key))
    {
        return RET_FAIL;
    }
    static int max_data_len = m_rsu_uper_buffer.size()/4;
    return transmit_cv2x_msg(&asn_DEF_MessageFrame,&cv2xFrame,max_data_len,key);
}

int v2x_client::send_cv2x_json_81H(char *json_data, uint32_t json_data_len )
{
    packet_info json_buf{json_data,json_data_len};
    size_t packet_req_len = json_data_len + sizeof(V2xHead) + BUFF_APPEND_SIZE;
    
    if(m_rsu_uper_buffer.size()< packet_req_len)
    {
        m_rsu_uper_buffer.resize(packet_req_len);
    }

    packet_info send_buf ;
    send_buf.pdata =  (char*)m_rsu_uper_buffer.c_str();

    if(RET_OK != m_v2x_protocol.build_v2x_json_msg(json_buf,send_buf))
    {
        return RET_FAIL;
    }
    return send_data(send_buf.pdata,send_buf.len);
}

/***********************************************************
 * 函数名称: send_config
 * 功能描述: 配置 
 * 输入参数: opt  //1：设置 2：获取（没有后续字段） 0C指令响应
 * 输入参数: json_data
 * 输出参数:
 * 返 回 值:
 ***********************************************************/
int v2x_client::send_config_8CH(int opt,char * json_data, uint32_t json_data_len)
{
    packet_info json_buf{json_data,json_data_len};
    size_t packe_req_len = json_data_len + sizeof(V2xHead) + sizeof(V2xConfig) + BUFF_APPEND_SIZE; 
    if(m_rsu_uper_buffer.size()< packe_req_len)
    {
        m_rsu_uper_buffer.resize(packe_req_len);
    }

    packet_info send_buf ;
    send_buf.pdata =  (char*)m_rsu_uper_buffer.c_str();
    if(RET_OK!= m_v2x_protocol.build_config(opt,json_buf,send_buf))
    {
        return RET_FAIL;
    }
    WLI(" send_buf.len %d ",send_buf.len);
    return send_data(send_buf.pdata,send_buf.len);
}
/***********************************************************
 * 函数名称: send_cv2x_extend_8DH
 * 功能描述: 发送扩展消息 0x8D
 * 输入参数: msg_format 消息格式  1：json 2:二进制数据
 * 输出参数:
 * 返 回 值:
 ***********************************************************/
int v2x_client::send_cv2x_extend_8DH(uint8_t msg_format, char *data, uint32_t data_len)
{
    packet_info json_buf{data,data_len};
    size_t packe_req_len = data_len + sizeof(V2xHead) + sizeof(V2xExtend) + BUFF_APPEND_SIZE; 
    if(m_rsu_uper_buffer.size()< packe_req_len)
    {
        m_rsu_uper_buffer.resize(packe_req_len);
    }

    packet_info send_buf ;
    send_buf.pdata =  (char*)m_rsu_uper_buffer.c_str();
    if(RET_OK!= m_v2x_protocol.build_extend(msg_format,json_buf,send_buf))
    {
        return RET_FAIL;
    }
    WLI(" send_buf.len %d ",send_buf.len);
    return send_data(send_buf.pdata,send_buf.len);

}

int v2x_client::send_ack(uint8_t command)
{
    packet_info send_buf;
    send_buf.pdata = (char *)m_rsu_uper_buffer.c_str();
    send_buf.len =  m_rsu_uper_buffer.size();

    m_v2x_protocol.build_ack(command,send_buf);
    send_data(send_buf.pdata,send_buf.len);
    return RET_OK;
}

int v2x_client::transmit_cv2x_msg(const asn_TYPE_descriptor_s *td,const void *sptr,
                                      uint16_t max_data_len,const string & key)
{

    // encode
    if(max_data_len * 4 > m_rsu_uper_buffer.size())
    {
        m_rsu_uper_buffer.resize( 4 * max_data_len);
    }

    packet_info encode_buf;
    encode_buf.pdata = (char *)m_rsu_uper_buffer.c_str();
    encode_buf.len =  m_rsu_uper_buffer.size();

    packet_info send_buf;
    send_buf.pdata = nullptr;
    send_buf.len = 0;

    if(RET_FAIL == m_v2x_protocol.build_uper(td,sptr,key,encode_buf,send_buf))
    {
        return RET_FAIL;
    }

    return send_data(send_buf.pdata,send_buf.len);
}

int v2x_client::proc_status_disconnect(status_info & status)
{
    WLD("Enter");
    destory();
    init();
    m_status_control.enter_status(status_enum::connecting);
    return RET_OK;
}

int v2x_client::proc_status_connected(status_info & status)
{
    WLD(" ENTER"); 
    m_time_task.on_time(status.enter_tm_ms); 
    return RET_OK;
}

int v2x_client::proc_status_connecting(status_info & status)
{
    WLD("Enter");
    m_status_control.enter_status(status_enum::disconnect);
    return RET_OK;
}

int v2x_client::send_data(const char *pData,int len)
{
    if(m_status_control.get_cur_status() != status_enum::connected)
    {
        return RET_FAIL;
    }
    int ret = engine_ref.send_tcp_data(m_rd_handler,pData,len);
    if(RET_OK!= ret)
    {
        m_status_control.enter_status(status_enum::disconnect); 
    }
    return ret; 
}

int v2x_client::send_last_command_ack()
{
    if(m_lastcmd != 0)
    {
        send_ack(m_lastcmd);
        m_lastcmd = 0;
    }
    return RET_OK;
}   

void v2x_client::time_task_heartbeat_send(long long cur_ms)
{
    WLD(" m_heartbeat_enable %d ",m_heartbeat_enable); 
    if(m_heartbeat_enable == false)
    {
        return ;
    }
    packet_info encode_buf;
    encode_buf.pdata = (char *)m_rsu_uper_buffer.c_str();
    encode_buf.len =  m_rsu_uper_buffer.size();
    packet_info send_buf;
    send_buf.pdata = nullptr;
    send_buf.len = 0;

    m_v2x_protocol.build_heartbeat(encode_buf,send_buf);
    send_data(send_buf.pdata,send_buf.len); 
    
}

void v2x_client::time_task_send_file(long long cur_ms)
{
    if(m_send_data_list.empty())
    {
        return ; 
    }
    for(auto & data : m_send_data_list)
    {
        send_cv2x_json_81H((char*)data.c_str(),data.size());
    }

}

void v2x_client::time_task_check_connect(long long cur_ms)
{
    auto cur_sec = app_clock_ref.now_sec();
    if(cur_sec - m_last_recv_heartbeat_sec >= 15)
    {
        WLE("detect not recv heart_beat over 15 sec "); 
        m_status_control.enter_status(status_enum::disconnect);
    }
}

bool v2x_client::connect_is_ok()
{
    if(m_status_control.get_cur_status() >= status_enum::connected && m_heartBeatRsp.dev_type != 0)
    {
        return true;
    }
    return false;
}
