cmake_minimum_required (VERSION 3.0)

# 切换编译平台时 需要 先清掉原来 cmake 生成的文件  
# rm -rf ./CMakeCache.txt ./CMakeFiles cmake_install.cmake  ./Makefile

# cmake -D APP_TYPE=TEST_APP .
# cmake -D TARGET_MACHINE=x86 .
# cmake -D TARGET_MACHINE=arm32 . 
# cmake -D TARGET_MACHINE=arm32_7012 . 
# cmake -D TARGET_MACHINE=arm64 .
# cmake -D TARGET_MACHINE=7022 .

project (mec_channel)

set(APP_TYPE APP CACHE STRING "Set app type to TEST_APP/APP (default APP)" )
if ("${APP_TYPE}" STREQUAL "TEST_APP")
    add_definitions(-DTEST_CODE)
endif()

#选项-配置目标机器
set(TARGET_MACHINE x86 CACHE STRING 
    "Set machine type to x86/arm32/arm32_7012/arm64/7022 (default x86)")
    
#指定编译器-- 本机
if ("${TARGET_MACHINE}" STREQUAL "x86")
    set(CMAKE_C_COMPILER "gcc")
    set(CMAKE_CXX_COMPILER "g++")
    add_definitions(-DPLATFORM_x86)
    add_definitions(-DCOMPILE_MACHINE)
    LINK_DIRECTORIES("./engine_module/lib")
    #add_compile_options(--std=c++11 -Wall -O3 )
    add_compile_options(--std=c++11 -Wall -g )
    set(CMAKE_BUILD_TYPE "Debug")
    #set(CMAKE_BUILD_TYPE "Release")
    
endif()


#arm32
if ("${TARGET_MACHINE}" STREQUAL "arm32")
    set(CMAKE_C_COMPILER "arm-linux-gcc")
	#set(CMAKE_C_COMPILER "arm-linux-gnueabihf-gcc")
    set(CMAKE_CXX_COMPILER "arm-linux-g++")
	#set(CMAKE_CXX_COMPILER "arm-linux-gnueabihf-g++")
    add_definitions(-DPLATFORM_ARM32)
    #set(CMAKE_BUILD_TYPE "")
    LINK_DIRECTORIES("./engine_module/lib_arm")
    add_compile_options(--std=c++11 -Wall -O3 )
    set(CMAKE_BUILD_TYPE "Release")
endif()

#arm32_7012 降本rsu 
if ("${TARGET_MACHINE}" STREQUAL "arm32_7012")
    set(CMAKE_C_COMPILER "arm-linux-gnueabi-gcc")
    set(CMAKE_CXX_COMPILER "arm-linux-gnueabi-g++")
    add_definitions(-DPLATFORM_ARM32)
    #set(CMAKE_BUILD_TYPE "")
    LINK_DIRECTORIES("./engine_module/lib_arm32_7012")
    add_compile_options(--std=c++11 -Wall -O3  -Wno-psabi  )
    set(CMAKE_BUILD_TYPE "Release")
endif()


#arm-64
if ("${TARGET_MACHINE}" STREQUAL "arm64")
    set(CMAKE_C_COMPILER "aarch64-linux-gnu-gcc")
    set(CMAKE_CXX_COMPILER "aarch64-linux-gnu-g++")
    add_definitions(-DPLATFORM_ARM64)
    LINK_DIRECTORIES("./engine_module/lib_arm64")
    add_compile_options(--std=c++11 -Wall -O3 )
    set(CMAKE_BUILD_TYPE "Release")
endif()

#7022 降本5Grsu 
if ("${TARGET_MACHINE}" STREQUAL "7022")
    set(CMAKE_C_COMPILER "arm-none-linux-gnueabihf-gcc")
    set(CMAKE_CXX_COMPILER "arm-none-linux-gnueabihf-g++")
    add_definitions(-DPLATFORM_ARM64)
    LINK_DIRECTORIES("./engine_module/lib_arm_7022")
    add_compile_options(--std=c++11 -Wall -O3  -Wno-psabi )
    set(CMAKE_BUILD_TYPE "Release")
endif()


#指定头文件包含路径 
include_directories(
                        "./" 
                        "./mec_asn" 
                        "./include" 
                        "./engine_module/include" 
                        "./engine_module/include/mosquitto"
                        "./engine_module/include/zmq"
                        "./engine_module/utility" 
                        "./engine_module/signal" 
                        "./engine_module/log" 
                        "./engine_module/config" 
                        "./engine_module/mem" 
                        "./engine_module/engine" 
                        "./engine_module/thread_resource"
                        "./mec_client" 
                        "./mqtt_client" 
                        "./dvin_client" 
                        "./v2x_client" 
                        "./platform"
                        "./platform/genvict"
                        "./platform/genvict_sdk"
                        "./platform/debug"
                        "./platform/empty"
                        "./platform/baidu"
                        "./platform/boyuan"
                        "./platform/gosuncn"
                        "./platform/huawei"
                        "./platform/multi"
                        "./platform/tianan_obu"
                        "./platform/tianan"
                        "./platform/tianan/mec_protocol"
                        "./test"
                        "./app"
                    )

#指定 gflags static lib
#find_package(gflags COMPONENTS nothreads_static)

aux_source_directory(. DIR_SRCS )
aux_source_directory(./engine_module/utility DIR_SRCS )
aux_source_directory(./engine_module/signal DIR_SRCS )
aux_source_directory(./engine_module/log DIR_SRCS )
aux_source_directory(./engine_module/config DIR_SRCS )
aux_source_directory(./engine_module/mem DIR_SRCS )
aux_source_directory(./engine_module/engine DIR_SRCS )
aux_source_directory(./engine_module/thread_resource DIR_SRCS )

aux_source_directory(./mec_client DIR_SRCS )
aux_source_directory(./mqtt_client DIR_SRCS )
aux_source_directory(./dvin_client DIR_SRCS )
aux_source_directory(./v2x_client DIR_SRCS )
aux_source_directory(./test DIR_SRCS )

aux_source_directory(./app DIR_SRCS )
aux_source_directory(./platform DIR_SRCS )
aux_source_directory(./platform/genvict DIR_SRCS )
aux_source_directory(./platform/genvict_sdk DIR_SRCS )
aux_source_directory(./platform/baidu DIR_SRCS )
aux_source_directory(./platform/boyuan DIR_SRCS )
aux_source_directory(./platform/gosuncn DIR_SRCS )
aux_source_directory(./platform/tianan DIR_SRCS )
aux_source_directory(./platform/tianan/mec_protocol DIR_SRCS )
aux_source_directory(./platform/debug DIR_SRCS )
aux_source_directory(./platform/empty DIR_SRCS )
aux_source_directory(./platform/huawei DIR_SRCS )
aux_source_directory(./platform/multi DIR_SRCS )
aux_source_directory(./platform/tianan_obu DIR_SRCS )


add_executable(${PROJECT_NAME} ${DIR_SRCS})
target_link_libraries(${PROJECT_NAME} PUBLIC protobuf protoc pthread mosquitto gflags event event_core event_pthreads mec_asn crypto curl ssl cares zmq ) 
#target_link_libraries(${PROJECT_NAME} PUBLIC pthread mosquitto gflags  event event_pthreads mec_asn_debug crypto curl ssl cares  ) 


# 连接前会执行
#add_custom_command( TARGET mec_channel PRE_BUILD  
#    COMMAND ${CMAKE_CURRENT_SOURCE_DIR}/getgitver.sh 
#    COMMENT "execute getgitver.sh"
#)

# 执行
add_dependencies(${PROJECT_NAME} git_version.h )

add_custom_target(
    git_version.h
    ALL
    COMMAND /bin/sh ${CMAKE_CURRENT_SOURCE_DIR}/getgitver.sh
    COMMENT "create ./app/git_version.h" 
)
# ;sshpass -p "cdgenvict" scp -p 22 ./mec_channel root@192.168.110:/genvict/bin/
add_custom_target(
    send110
    COMMAND sshpass -p "cdgenvict" root@192.168.110 stopobu
    COMMENT "send 146" 
)
add_custom_target(
    send113
    COMMAND sshpass -p "cdgenvict" scp -P 34113 ./mec_channel root@192.168.10.242:/genvict/bin/
    COMMENT "send 146" 
)


add_custom_target(
    send144
    #COMMAND sshpass -p "cdgenvict" scp -p 22 ./mec_channel root@10.1.4.144:/genvict/bin/
    COMMAND sshpass -p "cdgenvict" ssh  -P 22 root@10.1.4.144 stopmec
    COMMENT "send rsu-4g 144" 
)

#深圳OBU 146
add_custom_target(
    send146
    COMMAND sshpass -p "cdgenvict" ssh -p 22  root@10.1.4.146 systemctl stop mec_channel.service 
    COMMAND sshpass -p "cdgenvict" scp -P 22 /data1/work/code/genvict/mec_channel/mec_channel root@10.1.4.146:/genvict/bin/
    COMMAND sshpass -p "cdgenvict" ssh -p 22  root@10.1.4.146 ls -al /genvict/bin/
    COMMENT "146 stop mec and syn file " 
)

add_custom_target(
    send247
    COMMAND sshpass -p "gv0200759" scp -P 34247 ./mec_channel root@192.168.10.242:/genvict/bin/
    COMMENT "send rsu-5g 247" 
)
