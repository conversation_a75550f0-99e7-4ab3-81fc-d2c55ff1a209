/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "SignalPhaseAndTiming"
 * 	found in "./SignalPhaseAndTiming.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#ifndef	_Phase_H_
#define	_Phase_H_


#include <asn_application.h>

/* Including external dependencies */
#include "PhaseID.h"
#include "PhaseStateList.h"
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Phase */
typedef struct Phase {
	PhaseID_t	 id;
	PhaseStateList_t	 phaseStates;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} Phase_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_Phase;
extern asn_SEQUENCE_specifics_t asn_SPC_Phase_specs_1;
extern asn_TYPE_member_t asn_MBR_Phase_1[2];

#ifdef __cplusplus
}
#endif

#endif	/* _Phase_H_ */
#include <asn_internal.h>
