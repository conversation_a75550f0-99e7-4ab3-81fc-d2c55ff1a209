/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "RSM"
 * 	found in "./RSM.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#ifndef	_ParticipantType_H_
#define	_ParticipantType_H_


#include <asn_application.h>

/* Including external dependencies */
#include <NativeEnumerated.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Dependencies */
typedef enum ParticipantType {
	ParticipantType_unknown	= 0,
	ParticipantType_motor	= 1,
	ParticipantType_non_motor	= 2,
	ParticipantType_pedestrian	= 3,
	ParticipantType_rsu	= 4
	/*
	 * Enumeration is extensible
	 */
} e_ParticipantType;

/* ParticipantType */
typedef long	 ParticipantType_t;

/* Implementation */
extern asn_per_constraints_t asn_PER_type_ParticipantType_constr_1;
extern asn_TYPE_descriptor_t asn_DEF_ParticipantType;
extern const asn_INTEGER_specifics_t asn_SPC_ParticipantType_specs_1;
asn_struct_free_f ParticipantType_free;
asn_struct_print_f ParticipantType_print;
asn_constr_check_f ParticipantType_constraint;
ber_type_decoder_f ParticipantType_decode_ber;
der_type_encoder_f ParticipantType_encode_der;
xer_type_decoder_f ParticipantType_decode_xer;
xer_type_encoder_f ParticipantType_encode_xer;
oer_type_decoder_f ParticipantType_decode_oer;
oer_type_encoder_f ParticipantType_encode_oer;
per_type_decoder_f ParticipantType_decode_uper;
per_type_encoder_f ParticipantType_encode_uper;

#ifdef __cplusplus
}
#endif

#endif	/* _ParticipantType_H_ */
#include <asn_internal.h>
