/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "PAM"
 * 	found in "./PAM.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#include "PAMDrive.h"

static int
memb_driveID_constraint_1(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	long value;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	value = *(const long *)sptr;
	
	if((value >= 0 && value <= 255)) {
		/* Constraint check succeeded */
		return 0;
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_heightRestriction_constraint_1(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	long value;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	value = *(const long *)sptr;
	
	if((value >= 0 && value <= 100)) {
		/* Constraint check succeeded */
		return 0;
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_laneNum_constraint_1(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	long value;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	value = *(const long *)sptr;
	
	if((value >= 0 && value <= 100)) {
		/* Constraint check succeeded */
		return 0;
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static asn_oer_constraints_t asn_OER_memb_driveID_constr_3 CC_NOTUSED = {
	{ 1, 1 }	/* (0..255) */,
	-1};
static asn_per_constraints_t asn_PER_memb_driveID_constr_3 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 8,  8,  0,  255 }	/* (0..255) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
static asn_oer_constraints_t asn_OER_memb_heightRestriction_constr_6 CC_NOTUSED = {
	{ 1, 1 }	/* (0..100) */,
	-1};
static asn_per_constraints_t asn_PER_memb_heightRestriction_constr_6 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 7,  7,  0,  100 }	/* (0..100) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
static asn_oer_constraints_t asn_OER_memb_laneNum_constr_8 CC_NOTUSED = {
	{ 1, 1 }	/* (0..100) */,
	-1};
static asn_per_constraints_t asn_PER_memb_laneNum_constr_8 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 7,  7,  0,  100 }	/* (0..100) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
asn_TYPE_member_t asn_MBR_PAMDrive_1[] = {
	{ ATF_NOFLAGS, 0, offsetof(struct PAMDrive, upstreamPAMNodeId),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_PAMNodeID,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"upstreamPAMNodeId"
		},
	{ ATF_POINTER, 9, offsetof(struct PAMDrive, driveID),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NativeInteger,
		0,
		{ &asn_OER_memb_driveID_constr_3, &asn_PER_memb_driveID_constr_3,  memb_driveID_constraint_1 },
		0, 0, /* No default value */
		"driveID"
		},
	{ ATF_POINTER, 8, offsetof(struct PAMDrive, twowaySepration),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_BOOLEAN,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"twowaySepration"
		},
	{ ATF_POINTER, 7, offsetof(struct PAMDrive, speedLimit),
		(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_Speed,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"speedLimit"
		},
	{ ATF_POINTER, 6, offsetof(struct PAMDrive, heightRestriction),
		(ASN_TAG_CLASS_CONTEXT | (4 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NativeInteger,
		0,
		{ &asn_OER_memb_heightRestriction_constr_6, &asn_PER_memb_heightRestriction_constr_6,  memb_heightRestriction_constraint_1 },
		0, 0, /* No default value */
		"heightRestriction"
		},
	{ ATF_POINTER, 5, offsetof(struct PAMDrive, driveWidth),
		(ASN_TAG_CLASS_CONTEXT | (5 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_LaneWidth,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"driveWidth"
		},
	{ ATF_POINTER, 4, offsetof(struct PAMDrive, laneNum),
		(ASN_TAG_CLASS_CONTEXT | (6 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NativeInteger,
		0,
		{ &asn_OER_memb_laneNum_constr_8, &asn_PER_memb_laneNum_constr_8,  memb_laneNum_constraint_1 },
		0, 0, /* No default value */
		"laneNum"
		},
	{ ATF_POINTER, 3, offsetof(struct PAMDrive, points),
		(ASN_TAG_CLASS_CONTEXT | (7 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_PointList,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"points"
		},
	{ ATF_POINTER, 2, offsetof(struct PAMDrive, movements),
		(ASN_TAG_CLASS_CONTEXT | (8 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_PAMMovementList,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"movements"
		},
	{ ATF_POINTER, 1, offsetof(struct PAMDrive, parkingSlots),
		(ASN_TAG_CLASS_CONTEXT | (9 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_ParkingSlots,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"parkingSlots"
		},
};
static const int asn_MAP_PAMDrive_oms_1[] = { 1, 2, 3, 4, 5, 6, 7, 8, 9 };
static const ber_tlv_tag_t asn_DEF_PAMDrive_tags_1[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_PAMDrive_tag2el_1[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* upstreamPAMNodeId */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* driveID */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 }, /* twowaySepration */
    { (ASN_TAG_CLASS_CONTEXT | (3 << 2)), 3, 0, 0 }, /* speedLimit */
    { (ASN_TAG_CLASS_CONTEXT | (4 << 2)), 4, 0, 0 }, /* heightRestriction */
    { (ASN_TAG_CLASS_CONTEXT | (5 << 2)), 5, 0, 0 }, /* driveWidth */
    { (ASN_TAG_CLASS_CONTEXT | (6 << 2)), 6, 0, 0 }, /* laneNum */
    { (ASN_TAG_CLASS_CONTEXT | (7 << 2)), 7, 0, 0 }, /* points */
    { (ASN_TAG_CLASS_CONTEXT | (8 << 2)), 8, 0, 0 }, /* movements */
    { (ASN_TAG_CLASS_CONTEXT | (9 << 2)), 9, 0, 0 } /* parkingSlots */
};
asn_SEQUENCE_specifics_t asn_SPC_PAMDrive_specs_1 = {
	sizeof(struct PAMDrive),
	offsetof(struct PAMDrive, _asn_ctx),
	asn_MAP_PAMDrive_tag2el_1,
	10,	/* Count of tags in the map */
	asn_MAP_PAMDrive_oms_1,	/* Optional members */
	9, 0,	/* Root/Additions */
	10,	/* First extension addition */
};
asn_TYPE_descriptor_t asn_DEF_PAMDrive = {
	"PAMDrive",
	"PAMDrive",
	&asn_OP_SEQUENCE,
	asn_DEF_PAMDrive_tags_1,
	sizeof(asn_DEF_PAMDrive_tags_1)
		/sizeof(asn_DEF_PAMDrive_tags_1[0]), /* 1 */
	asn_DEF_PAMDrive_tags_1,	/* Same as above */
	sizeof(asn_DEF_PAMDrive_tags_1)
		/sizeof(asn_DEF_PAMDrive_tags_1[0]), /* 1 */
	{ 0, 0, SEQUENCE_constraint },
	asn_MBR_PAMDrive_1,
	10,	/* Elements count */
	&asn_SPC_PAMDrive_specs_1	/* Additional specs */
};

