/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "OPS"
 * 	found in "./OPS.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#ifndef	_ReferenceInfo_H_
#define	_ReferenceInfo_H_


#include <asn_application.h>

/* Including external dependencies */
#include "DeviceType.h"
#include <NativeInteger.h>
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* ReferenceInfo */
typedef struct ReferenceInfo {
	DeviceType_t	 type;
	long	 id;
	long	 distance;
	/*
	 * This type is extensible,
	 * possible extensions are below.
	 */
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} ReferenceInfo_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_ReferenceInfo;
extern asn_SEQUENCE_specifics_t asn_SPC_ReferenceInfo_specs_1;
extern asn_TYPE_member_t asn_MBR_ReferenceInfo_1[3];

#ifdef __cplusplus
}
#endif

#endif	/* _ReferenceInfo_H_ */
#include <asn_internal.h>
