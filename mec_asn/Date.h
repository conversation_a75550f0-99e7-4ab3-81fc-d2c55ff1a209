/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "VPM"
 * 	found in "./VPM.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#ifndef	_Date_H_
#define	_Date_H_


#include <asn_application.h>

/* Including external dependencies */
#include "DYear.h"
#include "DMonth.h"
#include "DDay.h"
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Date */
typedef struct Date {
	DYear_t	 year;
	DMonth_t	 month;
	DDay_t	 day;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} Date_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_Date;
extern asn_SEQUENCE_specifics_t asn_SPC_Date_specs_1;
extern asn_TYPE_member_t asn_MBR_Date_1[3];

#ifdef __cplusplus
}
#endif

#endif	/* _Date_H_ */
#include <asn_internal.h>
