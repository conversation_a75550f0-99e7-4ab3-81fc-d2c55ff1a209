cmake_minimum_required (VERSION 3.0)

include_directories("./")

# cmake -D TARGET_MACHINE=x86 .
# cmake -D TARGET_MACHINE=arm32 . 
# cmake -D TARGET_MACHINE=arm32_7012 .
# cmake -D TARGET_MACHINE=arm64 .
# cmake -D TARGET_MACHINE=7022 .

#配置默认值 编译器
set(CMAKE_C_COMPILER "gcc")
set(CMAKE_CXX_COMPILER "g++")

#选项-配置目标机器
set(TARGET_MACHINE x86 CACHE STRING 
    "Set machine type to x86/arm32/arm32_7012/arm64/7022 (default x86)")

#指定编译器-- 本机
if ("${TARGET_MACHINE}" STREQUAL "x86")
    set(CMAKE_C_COMPILER "gcc")
    set(CMAKE_CXX_COMPILER "g++")
    add_definitions(-DPLATFORM_X86)
    add_definitions(-DCOMPILE_MACHINE) #编译机器方便调试
    add_compile_options( -Wall -g )
endif()



if ("${TARGET_MACHINE}" STREQUAL "arm32_7012")
    set(CMAKE_C_COMPILER "arm-linux-gnueabi-gcc")
    set(CMAKE_CXX_COMPILER "arm-linux-gnueabi-g++")
    #set(CMAKE_BUILD_TYPE Release)
    add_definitions(-DPLATFORM_ARM32_7012)
    add_compile_options(-Wall -O3 )
endif()

#arm32
if ("${TARGET_MACHINE}" STREQUAL "arm32")
    set(CMAKE_C_COMPILER "arm-linux-gcc")
    set(CMAKE_CXX_COMPILER "arm-linux-g++")
    add_definitions(-DPLATFORM_ARM32)
    add_compile_options(-Wall -O3 )
endif()

#arm-64
if ("${TARGET_MACHINE}" STREQUAL "arm64")
    set(CMAKE_C_COMPILER "aarch64-linux-gnu-gcc")
    set(CMAKE_CXX_COMPILER "aarch64-linux-gnu-g++")
    add_definitions(-DPLATFORM_ARM64)
    add_compile_options(-Wall -O3 )
endif()

#7022 降本5Grsu 
if ("${TARGET_MACHINE}" STREQUAL "7022")
    set(CMAKE_C_COMPILER "arm-none-linux-gnueabihf-gcc")
    set(CMAKE_CXX_COMPILER "arm-none-linux-gnueabihf-g++")
    add_definitions(-DPLATFORM_ARM64)
	 add_compile_options(-Wall -O3 )
endif()

aux_source_directory(. DIR_SRCS_MEC)

#add_library(mec_asn_${TARGET_MACHINE} SHARED ${DIR_SRCS_MEC})
add_library(mec_asn STATIC ${DIR_SRCS_MEC})
#add_library(mec_asn ${DIR_SRCS_MEC})


