/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "DefPositionOffset"
 * 	found in "./DefPositionOffset.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#ifndef	_OffsetLL_B16_H_
#define	_OffsetLL_B16_H_


#include <asn_application.h>

/* Including external dependencies */
#include <NativeInteger.h>

#ifdef __cplusplus
extern "C" {
#endif

/* OffsetLL-B16 */
typedef long	 OffsetLL_B16_t;

/* Implementation */
extern asn_per_constraints_t asn_PER_type_OffsetLL_B16_constr_1;
extern asn_TYPE_descriptor_t asn_DEF_OffsetLL_B16;
asn_struct_free_f OffsetLL_B16_free;
asn_struct_print_f OffsetLL_B16_print;
asn_constr_check_f OffsetLL_B16_constraint;
ber_type_decoder_f OffsetLL_B16_decode_ber;
der_type_encoder_f OffsetLL_B16_encode_der;
xer_type_decoder_f OffsetLL_B16_decode_xer;
xer_type_encoder_f OffsetLL_B16_encode_xer;
oer_type_decoder_f OffsetLL_B16_decode_oer;
oer_type_encoder_f OffsetLL_B16_encode_oer;
per_type_decoder_f OffsetLL_B16_decode_uper;
per_type_encoder_f OffsetLL_B16_encode_uper;

#ifdef __cplusplus
}
#endif

#endif	/* _OffsetLL_B16_H_ */
#include <asn_internal.h>
