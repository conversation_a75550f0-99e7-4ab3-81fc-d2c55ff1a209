/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "Map"
 * 	found in "./Map.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#ifndef	_LaneRefID_H_
#define	_LaneRefID_H_


#include <asn_application.h>

/* Including external dependencies */
#include <NativeInteger.h>

#ifdef __cplusplus
extern "C" {
#endif

/* LaneRefID */
typedef long	 LaneRefID_t;

/* Implementation */
extern asn_per_constraints_t asn_PER_type_LaneRefID_constr_1;
extern asn_TYPE_descriptor_t asn_DEF_LaneRefID;
asn_struct_free_f LaneRefID_free;
asn_struct_print_f LaneRefID_print;
asn_constr_check_f LaneRefID_constraint;
ber_type_decoder_f LaneRefID_decode_ber;
der_type_encoder_f LaneRefID_encode_der;
xer_type_decoder_f LaneRef<PERSON>_decode_xer;
xer_type_encoder_f LaneRefID_encode_xer;
oer_type_decoder_f LaneRefID_decode_oer;
oer_type_encoder_f LaneRefID_encode_oer;
per_type_decoder_f LaneRefID_decode_uper;
per_type_encoder_f LaneRefID_encode_uper;

#ifdef __cplusplus
}
#endif

#endif	/* _LaneRefID_H_ */
#include <asn_internal.h>
