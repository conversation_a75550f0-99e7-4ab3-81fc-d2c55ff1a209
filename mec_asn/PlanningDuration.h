/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "SensorSharing"
 * 	found in "./SensorSharing.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#ifndef	_PlanningDuration_H_
#define	_PlanningDuration_H_


#include <asn_application.h>

/* Including external dependencies */
#include <NativeInteger.h>

#ifdef __cplusplus
extern "C" {
#endif

/* PlanningDuration */
typedef long	 PlanningDuration_t;

/* Implementation */
extern asn_per_constraints_t asn_PER_type_PlanningDuration_constr_1;
extern asn_TYPE_descriptor_t asn_DEF_PlanningDuration;
asn_struct_free_f PlanningDuration_free;
asn_struct_print_f PlanningDuration_print;
asn_constr_check_f PlanningDuration_constraint;
ber_type_decoder_f PlanningDuration_decode_ber;
der_type_encoder_f PlanningDuration_encode_der;
xer_type_decoder_f PlanningDuration_decode_xer;
xer_type_encoder_f PlanningDuration_encode_xer;
oer_type_decoder_f PlanningDuration_decode_oer;
oer_type_encoder_f PlanningDuration_encode_oer;
per_type_decoder_f PlanningDuration_decode_uper;
per_type_encoder_f PlanningDuration_encode_uper;

#ifdef __cplusplus
}
#endif

#endif	/* _PlanningDuration_H_ */
#include <asn_internal.h>
