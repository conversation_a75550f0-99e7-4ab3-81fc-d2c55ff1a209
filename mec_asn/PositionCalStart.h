/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "OPS"
 * 	found in "./OPS.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#ifndef	_PositionCalStart_H_
#define	_PositionCalStart_H_


#include <asn_application.h>

/* Including external dependencies */
#include <NativeInteger.h>
#include "AnchorList.h"
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* PositionCalStart */
typedef struct PositionCalStart {
	long	 anchorGroupNum;
	AnchorList_t	 anchorList;
	/*
	 * This type is extensible,
	 * possible extensions are below.
	 */
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} PositionCalStart_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_PositionCalStart;
extern asn_SEQUENCE_specifics_t asn_SPC_PositionCalStart_specs_1;
extern asn_TYPE_member_t asn_MBR_PositionCalStart_1[2];

#ifdef __cplusplus
}
#endif

#endif	/* _PositionCalStart_H_ */
#include <asn_internal.h>
