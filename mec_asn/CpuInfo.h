/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "OPS"
 * 	found in "./OPS.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#ifndef	_CpuInfo_H_
#define	_CpuInfo_H_


#include <asn_application.h>

/* Including external dependencies */
#include "Load.h"
#include "UtiList.h"
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* CpuInfo */
typedef struct CpuInfo {
	Load_t	 load;
	UtiList_t	 utis;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} CpuInfo_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_CpuInfo;
extern asn_SEQUENCE_specifics_t asn_SPC_CpuInfo_specs_1;
extern asn_TYPE_member_t asn_MBR_CpuInfo_1[2];

#ifdef __cplusplus
}
#endif

#endif	/* _CpuInfo_H_ */
#include <asn_internal.h>
