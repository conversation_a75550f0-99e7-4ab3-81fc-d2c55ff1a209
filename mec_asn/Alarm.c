/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "OPS"
 * 	found in "./OPS.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#include "Alarm.h"

static int
timestamp_4_constraint(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	unsigned long value;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	value = *(const unsigned long *)sptr;
	
	if((value >= 1 && value <= 4294967295)) {
		/* Constraint check succeeded */
		return 0;
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

/*
 * This type is implemented using NativeInteger,
 * so here we adjust the DEF accordingly.
 */
static int
memb_timestamp_constraint_1(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	unsigned long value;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	value = *(const unsigned long *)sptr;
	
	if((value >= 1 && value <= 4294967295)) {
		/* Constraint check succeeded */
		return 0;
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static asn_oer_constraints_t asn_OER_type_timestamp_constr_4 CC_NOTUSED = {
	{ 4, 1 }	/* (1..4294967295) */,
	-1};
static asn_per_constraints_t asn_PER_type_timestamp_constr_4 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 32, -1,  1,  4294967295 }	/* (1..4294967295) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
static asn_oer_constraints_t asn_OER_memb_timestamp_constr_4 CC_NOTUSED = {
	{ 4, 1 }	/* (1..4294967295) */,
	-1};
static asn_per_constraints_t asn_PER_memb_timestamp_constr_4 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 32, -1,  1,  4294967295 }	/* (1..4294967295) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
static const asn_INTEGER_specifics_t asn_SPC_timestamp_specs_4 = {
	0,	0,	0,	0,	0,
	0,	/* Native long size */
	1	/* Unsigned representation */
};
static const ber_tlv_tag_t asn_DEF_timestamp_tags_4[] = {
	(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (2 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_timestamp_4 = {
	"timestamp",
	"timestamp",
	&asn_OP_NativeInteger,
	asn_DEF_timestamp_tags_4,
	sizeof(asn_DEF_timestamp_tags_4)
		/sizeof(asn_DEF_timestamp_tags_4[0]) - 1, /* 1 */
	asn_DEF_timestamp_tags_4,	/* Same as above */
	sizeof(asn_DEF_timestamp_tags_4)
		/sizeof(asn_DEF_timestamp_tags_4[0]), /* 2 */
	{ &asn_OER_type_timestamp_constr_4, &asn_PER_type_timestamp_constr_4, timestamp_4_constraint },
	0, 0,	/* No members */
	&asn_SPC_timestamp_specs_4	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_Alarm_1[] = {
	{ ATF_NOFLAGS, 0, offsetof(struct Alarm, type),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_AlarmType,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"type"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct Alarm, status),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_AlarmState,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"status"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct Alarm, timestamp),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_timestamp_4,
		0,
		{ &asn_OER_memb_timestamp_constr_4, &asn_PER_memb_timestamp_constr_4,  memb_timestamp_constraint_1 },
		0, 0, /* No default value */
		"timestamp"
		},
	{ ATF_POINTER, 1, offsetof(struct Alarm, info),
		(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
		+1,	/* EXPLICIT tag at current level */
		&asn_DEF_AlarmInfo,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"info"
		},
};
static const int asn_MAP_Alarm_oms_1[] = { 3 };
static const ber_tlv_tag_t asn_DEF_Alarm_tags_1[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_Alarm_tag2el_1[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* type */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* status */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 }, /* timestamp */
    { (ASN_TAG_CLASS_CONTEXT | (3 << 2)), 3, 0, 0 } /* info */
};
asn_SEQUENCE_specifics_t asn_SPC_Alarm_specs_1 = {
	sizeof(struct Alarm),
	offsetof(struct Alarm, _asn_ctx),
	asn_MAP_Alarm_tag2el_1,
	4,	/* Count of tags in the map */
	asn_MAP_Alarm_oms_1,	/* Optional members */
	1, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
asn_TYPE_descriptor_t asn_DEF_Alarm = {
	"Alarm",
	"Alarm",
	&asn_OP_SEQUENCE,
	asn_DEF_Alarm_tags_1,
	sizeof(asn_DEF_Alarm_tags_1)
		/sizeof(asn_DEF_Alarm_tags_1[0]), /* 1 */
	asn_DEF_Alarm_tags_1,	/* Same as above */
	sizeof(asn_DEF_Alarm_tags_1)
		/sizeof(asn_DEF_Alarm_tags_1[0]), /* 1 */
	{ 0, 0, SEQUENCE_constraint },
	asn_MBR_Alarm_1,
	4,	/* Elements count */
	&asn_SPC_Alarm_specs_1	/* Additional specs */
};

