/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "RSI"
 * 	found in "./RSI.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#include "AuxiliarySignDirection.h"

/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
static asn_oer_constraints_t asn_OER_type_AuxiliarySignDirection_constr_1 CC_NOTUSED = {
	{ 0, 0 },
	-1};
asn_per_constraints_t asn_PER_type_AuxiliarySignDirection_constr_1 CC_NOTUSED = {
	{ APC_CONSTRAINED | APC_EXTENSIBLE,  3,  3,  0,  7 }	/* (0..7,...) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
static const asn_INTEGER_enum_map_t asn_MAP_AuxiliarySignDirection_value2enum_1[] = {
	{ 0,	8,	"straight" },
	{ 1,	12,	"leftAndRight" },
	{ 2,	5,	"right" },
	{ 3,	4,	"left" },
	{ 4,	13,	"leftFrontTurn" },
	{ 5,	13,	"rightFronTurn" },
	{ 6,	13,	"rightRearTurn" },
	{ 7,	12,	"leftRearTurn" }
	/* This list is extensible */
};
static const unsigned int asn_MAP_AuxiliarySignDirection_enum2value_1[] = {
	3,	/* left(3) */
	1,	/* leftAndRight(1) */
	4,	/* leftFrontTurn(4) */
	7,	/* leftRearTurn(7) */
	2,	/* right(2) */
	5,	/* rightFronTurn(5) */
	6,	/* rightRearTurn(6) */
	0	/* straight(0) */
	/* This list is extensible */
};
const asn_INTEGER_specifics_t asn_SPC_AuxiliarySignDirection_specs_1 = {
	asn_MAP_AuxiliarySignDirection_value2enum_1,	/* "tag" => N; sorted by tag */
	asn_MAP_AuxiliarySignDirection_enum2value_1,	/* N => "tag"; sorted by N */
	8,	/* Number of elements in the maps */
	9,	/* Extensions before this member */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_AuxiliarySignDirection_tags_1[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
asn_TYPE_descriptor_t asn_DEF_AuxiliarySignDirection = {
	"AuxiliarySignDirection",
	"AuxiliarySignDirection",
	&asn_OP_NativeEnumerated,
	asn_DEF_AuxiliarySignDirection_tags_1,
	sizeof(asn_DEF_AuxiliarySignDirection_tags_1)
		/sizeof(asn_DEF_AuxiliarySignDirection_tags_1[0]), /* 1 */
	asn_DEF_AuxiliarySignDirection_tags_1,	/* Same as above */
	sizeof(asn_DEF_AuxiliarySignDirection_tags_1)
		/sizeof(asn_DEF_AuxiliarySignDirection_tags_1[0]), /* 1 */
	{ &asn_OER_type_AuxiliarySignDirection_constr_1, &asn_PER_type_AuxiliarySignDirection_constr_1, NativeEnumerated_constraint },
	0, 0,	/* Defined elsewhere */
	&asn_SPC_AuxiliarySignDirection_specs_1	/* Additional specs */
};

