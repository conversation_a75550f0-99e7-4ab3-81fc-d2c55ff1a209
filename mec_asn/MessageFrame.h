/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "MsgFrame"
 * 	found in "./MsgFrame.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#ifndef	_MessageFrame_H_
#define	_MessageFrame_H_


#include <asn_application.h>

/* Including external dependencies */
#include "BasicSafetyMessage.h"
#include "MapData.h"
#include "RoadsideSafetyMessage.h"
#include "SPAT.h"
#include "RoadSideInformation.h"
#include "MessageFrameExt.h"
#include <constr_CHOICE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Dependencies */
typedef enum MessageFrame_PR {
	MessageFrame_PR_NOTHING,	/* No components present */
	MessageFrame_PR_bsmFrame,
	MessageFrame_PR_mapFrame,
	MessageFrame_PR_rsmFrame,
	MessageFrame_PR_spatFrame,
	MessageFrame_PR_rsiFrame,
	/* Extensions may appear below */
	MessageFrame_PR_msgFrameExt
} MessageFrame_PR;

/* MessageFrame */
typedef struct MessageFrame {
	MessageFrame_PR present;
	union MessageFrame_u {
		BasicSafetyMessage_t	 bsmFrame;
		MapData_t	 mapFrame;
		RoadsideSafetyMessage_t	 rsmFrame;
		SPAT_t	 spatFrame;
		RoadSideInformation_t	 rsiFrame;
		/*
		 * This type is extensible,
		 * possible extensions are below.
		 */
		MessageFrameExt_t	 msgFrameExt;
	} choice;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} MessageFrame_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_MessageFrame;
extern asn_CHOICE_specifics_t asn_SPC_MessageFrame_specs_1;
extern asn_TYPE_member_t asn_MBR_MessageFrame_1[6];
extern asn_per_constraints_t asn_PER_type_MessageFrame_constr_1;

#ifdef __cplusplus
}
#endif

#endif	/* _MessageFrame_H_ */
#include <asn_internal.h>
