/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "RTCM"
 * 	found in "./RTCM.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#include "RTCM-Revision.h"

/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
static asn_oer_constraints_t asn_OER_type_RTCM_Revision_constr_1 CC_NOTUSED = {
	{ 0, 0 },
	-1};
asn_per_constraints_t asn_PER_type_RTCM_Revision_constr_1 CC_NOTUSED = {
	{ APC_CONSTRAINED | APC_EXTENSIBLE,  5,  5,  0,  17 }	/* (0..17,...) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
static const asn_INTEGER_enum_map_t asn_MAP_RTCM_Revision_value2enum_1[] = {
	{ 0,	7,	"unknown" },
	{ 1,	8,	"reserved" },
	{ 2,	7,	"rtcmCMR" },
	{ 3,	12,	"rtcmCMR-Plus" },
	{ 4,	9,	"rtcmSAPOS" },
	{ 5,	13,	"rtcmSAPOS-Adv" },
	{ 6,	8,	"rtcmRTCA" },
	{ 7,	7,	"rtcmRAW" },
	{ 8,	9,	"rtcmRINEX" },
	{ 9,	7,	"rtcmSP3" },
	{ 10,	9,	"rtcmBINEX" },
	{ 19,	10,	"rtcmRev2-x" },
	{ 20,	10,	"rtcmRev2-0" },
	{ 21,	10,	"rtcmRev2-1" },
	{ 23,	10,	"rtcmRev2-3" },
	{ 30,	10,	"rtcmRev3-0" },
	{ 31,	10,	"rtcmRev3-1" },
	{ 32,	10,	"rtcmRev3-2" }
	/* This list is extensible */
};
static const unsigned int asn_MAP_RTCM_Revision_enum2value_1[] = {
	1,	/* reserved(1) */
	10,	/* rtcmBINEX(10) */
	2,	/* rtcmCMR(2) */
	3,	/* rtcmCMR-Plus(3) */
	7,	/* rtcmRAW(7) */
	8,	/* rtcmRINEX(8) */
	6,	/* rtcmRTCA(6) */
	12,	/* rtcmRev2-0(20) */
	13,	/* rtcmRev2-1(21) */
	14,	/* rtcmRev2-3(23) */
	11,	/* rtcmRev2-x(19) */
	15,	/* rtcmRev3-0(30) */
	16,	/* rtcmRev3-1(31) */
	17,	/* rtcmRev3-2(32) */
	4,	/* rtcmSAPOS(4) */
	5,	/* rtcmSAPOS-Adv(5) */
	9,	/* rtcmSP3(9) */
	0	/* unknown(0) */
	/* This list is extensible */
};
const asn_INTEGER_specifics_t asn_SPC_RTCM_Revision_specs_1 = {
	asn_MAP_RTCM_Revision_value2enum_1,	/* "tag" => N; sorted by tag */
	asn_MAP_RTCM_Revision_enum2value_1,	/* N => "tag"; sorted by N */
	18,	/* Number of elements in the maps */
	19,	/* Extensions before this member */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_RTCM_Revision_tags_1[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
asn_TYPE_descriptor_t asn_DEF_RTCM_Revision = {
	"RTCM-Revision",
	"RTCM-Revision",
	&asn_OP_NativeEnumerated,
	asn_DEF_RTCM_Revision_tags_1,
	sizeof(asn_DEF_RTCM_Revision_tags_1)
		/sizeof(asn_DEF_RTCM_Revision_tags_1[0]), /* 1 */
	asn_DEF_RTCM_Revision_tags_1,	/* Same as above */
	sizeof(asn_DEF_RTCM_Revision_tags_1)
		/sizeof(asn_DEF_RTCM_Revision_tags_1[0]), /* 1 */
	{ &asn_OER_type_RTCM_Revision_constr_1, &asn_PER_type_RTCM_Revision_constr_1, NativeEnumerated_constraint },
	0, 0,	/* Defined elsewhere */
	&asn_SPC_RTCM_Revision_specs_1	/* Additional specs */
};

