/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "SensorSharing"
 * 	found in "./SensorSharing.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#include "MotorDataExtension.h"

asn_TYPE_member_t asn_MBR_MotorDataExtension_1[] = {
	{ ATF_POINTER, 5, offsetof(struct MotorDataExtension, lights),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_ExteriorLights,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"lights"
		},
	{ ATF_POINTER, 4, offsetof(struct MotorDataExtension, vehAttitude),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_Attitude,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"vehAttitude"
		},
	{ ATF_POINTER, 3, offsetof(struct MotorDataExtension, vehAttitudeConfidence),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_AttitudeConfidence,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"vehAttitudeConfidence"
		},
	{ ATF_POINTER, 2, offsetof(struct MotorDataExtension, vehAngVel),
		(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_AngularVelocity,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"vehAngVel"
		},
	{ ATF_POINTER, 1, offsetof(struct MotorDataExtension, vehAngVelConfidence),
		(ASN_TAG_CLASS_CONTEXT | (4 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_AngularVelocityConfidence,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"vehAngVelConfidence"
		},
};
static const int asn_MAP_MotorDataExtension_oms_1[] = { 0, 1, 2, 3, 4 };
static const ber_tlv_tag_t asn_DEF_MotorDataExtension_tags_1[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_MotorDataExtension_tag2el_1[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* lights */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* vehAttitude */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 }, /* vehAttitudeConfidence */
    { (ASN_TAG_CLASS_CONTEXT | (3 << 2)), 3, 0, 0 }, /* vehAngVel */
    { (ASN_TAG_CLASS_CONTEXT | (4 << 2)), 4, 0, 0 } /* vehAngVelConfidence */
};
asn_SEQUENCE_specifics_t asn_SPC_MotorDataExtension_specs_1 = {
	sizeof(struct MotorDataExtension),
	offsetof(struct MotorDataExtension, _asn_ctx),
	asn_MAP_MotorDataExtension_tag2el_1,
	5,	/* Count of tags in the map */
	asn_MAP_MotorDataExtension_oms_1,	/* Optional members */
	5, 0,	/* Root/Additions */
	5,	/* First extension addition */
};
asn_TYPE_descriptor_t asn_DEF_MotorDataExtension = {
	"MotorDataExtension",
	"MotorDataExtension",
	&asn_OP_SEQUENCE,
	asn_DEF_MotorDataExtension_tags_1,
	sizeof(asn_DEF_MotorDataExtension_tags_1)
		/sizeof(asn_DEF_MotorDataExtension_tags_1[0]), /* 1 */
	asn_DEF_MotorDataExtension_tags_1,	/* Same as above */
	sizeof(asn_DEF_MotorDataExtension_tags_1)
		/sizeof(asn_DEF_MotorDataExtension_tags_1[0]), /* 1 */
	{ 0, 0, SEQUENCE_constraint },
	asn_MBR_MotorDataExtension_1,
	5,	/* Elements count */
	&asn_SPC_MotorDataExtension_specs_1	/* Additional specs */
};

