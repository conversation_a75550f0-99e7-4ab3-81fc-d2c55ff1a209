/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "SensorSharing"
 * 	found in "./SensorSharing.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#ifndef	_Attitude_H_
#define	_Attitude_H_


#include <asn_application.h>

/* Including external dependencies */
#include "Pitch.h"
#include "Roll.h"
#include "Yaw.h"
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Attitude */
typedef struct Attitude {
	Pitch_t	 pitch;
	Roll_t	 roll;
	Yaw_t	 yaw;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} Attitude_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_Attitude;
extern asn_SEQUENCE_specifics_t asn_SPC_Attitude_specs_1;
extern asn_TYPE_member_t asn_MBR_Attitude_1[3];

#ifdef __cplusplus
}
#endif

#endif	/* _Attitude_H_ */
#include <asn_internal.h>
