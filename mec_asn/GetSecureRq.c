/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "VPM"
 * 	found in "./VPM.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#include "GetSecureRq.h"

static int
memb_keyIdForAuthen_constraint_1(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	long value;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	value = *(const long *)sptr;
	
	if((value >= 0 && value <= 255)) {
		/* Constraint check succeeded */
		return 0;
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_keyIdForEncrypt_constraint_1(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	long value;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	value = *(const long *)sptr;
	
	if((value >= 0 && value <= 255)) {
		/* Constraint check succeeded */
		return 0;
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static asn_oer_constraints_t asn_OER_memb_keyIdForAuthen_constr_4 CC_NOTUSED = {
	{ 1, 1 }	/* (0..255) */,
	-1};
static asn_per_constraints_t asn_PER_memb_keyIdForAuthen_constr_4 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 8,  8,  0,  255 }	/* (0..255) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
static asn_oer_constraints_t asn_OER_memb_keyIdForEncrypt_constr_5 CC_NOTUSED = {
	{ 1, 1 }	/* (0..255) */,
	-1};
static asn_per_constraints_t asn_PER_memb_keyIdForEncrypt_constr_5 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 8,  8,  0,  255 }	/* (0..255) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
asn_TYPE_member_t asn_MBR_GetSecureRq_1[] = {
	{ ATF_NOFLAGS, 0, offsetof(struct GetSecureRq, vehicleInfo),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_RangeOfFile,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"vehicleInfo"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct GetSecureRq, rndRsuForAuthen),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_RandStr8,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"rndRsuForAuthen"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct GetSecureRq, keyIdForAuthen),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NativeInteger,
		0,
		{ &asn_OER_memb_keyIdForAuthen_constr_4, &asn_PER_memb_keyIdForAuthen_constr_4,  memb_keyIdForAuthen_constraint_1 },
		0, 0, /* No default value */
		"keyIdForAuthen"
		},
	{ ATF_POINTER, 1, offsetof(struct GetSecureRq, keyIdForEncrypt),
		(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NativeInteger,
		0,
		{ &asn_OER_memb_keyIdForEncrypt_constr_5, &asn_PER_memb_keyIdForEncrypt_constr_5,  memb_keyIdForEncrypt_constraint_1 },
		0, 0, /* No default value */
		"keyIdForEncrypt"
		},
};
static const int asn_MAP_GetSecureRq_oms_1[] = { 3 };
static const ber_tlv_tag_t asn_DEF_GetSecureRq_tags_1[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_GetSecureRq_tag2el_1[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* vehicleInfo */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* rndRsuForAuthen */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 }, /* keyIdForAuthen */
    { (ASN_TAG_CLASS_CONTEXT | (3 << 2)), 3, 0, 0 } /* keyIdForEncrypt */
};
asn_SEQUENCE_specifics_t asn_SPC_GetSecureRq_specs_1 = {
	sizeof(struct GetSecureRq),
	offsetof(struct GetSecureRq, _asn_ctx),
	asn_MAP_GetSecureRq_tag2el_1,
	4,	/* Count of tags in the map */
	asn_MAP_GetSecureRq_oms_1,	/* Optional members */
	1, 0,	/* Root/Additions */
	4,	/* First extension addition */
};
asn_TYPE_descriptor_t asn_DEF_GetSecureRq = {
	"GetSecureRq",
	"GetSecureRq",
	&asn_OP_SEQUENCE,
	asn_DEF_GetSecureRq_tags_1,
	sizeof(asn_DEF_GetSecureRq_tags_1)
		/sizeof(asn_DEF_GetSecureRq_tags_1[0]), /* 1 */
	asn_DEF_GetSecureRq_tags_1,	/* Same as above */
	sizeof(asn_DEF_GetSecureRq_tags_1)
		/sizeof(asn_DEF_GetSecureRq_tags_1[0]), /* 1 */
	{ 0, 0, SEQUENCE_constraint },
	asn_MBR_GetSecureRq_1,
	4,	/* Elements count */
	&asn_SPC_GetSecureRq_specs_1	/* Additional specs */
};

