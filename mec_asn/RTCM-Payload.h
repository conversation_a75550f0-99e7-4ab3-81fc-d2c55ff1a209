/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "RTCM"
 * 	found in "./RTCM.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#ifndef	_RTCM_Payload_H_
#define	_RTCM_Payload_H_


#include <asn_application.h>

/* Including external dependencies */
#include <OCTET_STRING.h>

#ifdef __cplusplus
extern "C" {
#endif

/* RTCM-Payload */
typedef OCTET_STRING_t	 RTCM_Payload_t;

/* Implementation */
extern asn_per_constraints_t asn_PER_type_RTCM_Payload_constr_1;
extern asn_TYPE_descriptor_t asn_DEF_RTCM_Payload;
asn_struct_free_f RTCM_Payload_free;
asn_struct_print_f RTCM_Payload_print;
asn_constr_check_f RTCM_Payload_constraint;
ber_type_decoder_f RTCM_Payload_decode_ber;
der_type_encoder_f RTCM_Payload_encode_der;
xer_type_decoder_f RTCM_Payload_decode_xer;
xer_type_encoder_f RTCM_Payload_encode_xer;
oer_type_decoder_f RTCM_Payload_decode_oer;
oer_type_encoder_f RTCM_Payload_encode_oer;
per_type_decoder_f RTCM_Payload_decode_uper;
per_type_encoder_f RTCM_Payload_encode_uper;

#ifdef __cplusplus
}
#endif

#endif	/* _RTCM_Payload_H_ */
#include <asn_internal.h>
