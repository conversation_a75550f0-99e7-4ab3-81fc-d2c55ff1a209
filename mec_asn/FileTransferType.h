/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "OPS"
 * 	found in "./OPS.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#ifndef	_FileTransferType_H_
#define	_FileTransferType_H_


#include <asn_application.h>

/* Including external dependencies */
#include <NativeEnumerated.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Dependencies */
typedef enum FileTransferType {
	FileTransferType_upload	= 0,
	FileTransferType_download	= 1
} e_FileTransferType;

/* FileTransferType */
typedef long	 FileTransferType_t;

/* Implementation */
extern asn_per_constraints_t asn_PER_type_FileTransferType_constr_1;
extern asn_TYPE_descriptor_t asn_DEF_FileTransferType;
extern const asn_INTEGER_specifics_t asn_SPC_FileTransferType_specs_1;
asn_struct_free_f FileTransferType_free;
asn_struct_print_f FileTransferType_print;
asn_constr_check_f FileTransferType_constraint;
ber_type_decoder_f FileTransferType_decode_ber;
der_type_encoder_f FileTransferType_encode_der;
xer_type_decoder_f FileTransferType_decode_xer;
xer_type_encoder_f FileTransferType_encode_xer;
oer_type_decoder_f FileTransferType_decode_oer;
oer_type_encoder_f FileTransferType_encode_oer;
per_type_decoder_f FileTransferType_decode_uper;
per_type_encoder_f FileTransferType_encode_uper;

#ifdef __cplusplus
}
#endif

#endif	/* _FileTransferType_H_ */
#include <asn_internal.h>
