/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "OPS"
 * 	found in "./OPS.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#ifndef	_AnchorInfo_H_
#define	_AnchorInfo_H_


#include <asn_application.h>

/* Including external dependencies */
#include <NativeInteger.h>
#include "Position3D.h"
#include "PositionXYZ.h"
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* AnchorInfo */
typedef struct AnchorInfo {
	long	 id;
	Position3D_t	 location;
	PositionXYZ_t	 xyz;
	/*
	 * This type is extensible,
	 * possible extensions are below.
	 */
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} AnchorInfo_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_AnchorInfo;
extern asn_SEQUENCE_specifics_t asn_SPC_AnchorInfo_specs_1;
extern asn_TYPE_member_t asn_MBR_AnchorInfo_1[3];

#ifdef __cplusplus
}
#endif

#endif	/* _AnchorInfo_H_ */
#include <asn_internal.h>
