/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "Map"
 * 	found in "./Map.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#ifndef	_ConnectsToExList_H_
#define	_ConnectsToExList_H_


#include <asn_application.h>

/* Including external dependencies */
#include <asn_SEQUENCE_OF.h>
#include <constr_SEQUENCE_OF.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Forward declarations */
struct ConnectionEx;

/* ConnectsToExList */
typedef struct ConnectsToExList {
	A_SEQUENCE_OF(struct ConnectionEx) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} ConnectsToExList_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_ConnectsToExList;
extern asn_SET_OF_specifics_t asn_SPC_ConnectsToExList_specs_1;
extern asn_TYPE_member_t asn_MBR_ConnectsToExList_1[1];
extern asn_per_constraints_t asn_PER_type_ConnectsToExList_constr_1;

#ifdef __cplusplus
}
#endif

/* Referred external types */
#include "ConnectionEx.h"

#endif	/* _ConnectsToExList_H_ */
#include <asn_internal.h>
