/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "Map"
 * 	found in "./Map.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#ifndef	_LaneWidth_H_
#define	_LaneWidth_H_


#include <asn_application.h>

/* Including external dependencies */
#include <NativeInteger.h>

#ifdef __cplusplus
extern "C" {
#endif

/* LaneWidth */
typedef long	 LaneWidth_t;

/* Implementation */
extern asn_per_constraints_t asn_PER_type_LaneWidth_constr_1;
extern asn_TYPE_descriptor_t asn_DEF_LaneWidth;
asn_struct_free_f LaneWidth_free;
asn_struct_print_f LaneWidth_print;
asn_constr_check_f LaneWidth_constraint;
ber_type_decoder_f LaneWidth_decode_ber;
der_type_encoder_f <PERSON><PERSON>idth_encode_der;
xer_type_decoder_f <PERSON><PERSON>idth_decode_xer;
xer_type_encoder_f <PERSON>idth_encode_xer;
oer_type_decoder_f LaneWidth_decode_oer;
oer_type_encoder_f LaneWidth_encode_oer;
per_type_decoder_f LaneWidth_decode_uper;
per_type_encoder_f LaneWidth_encode_uper;

#ifdef __cplusplus
}
#endif

#endif	/* _LaneWidth_H_ */
#include <asn_internal.h>
