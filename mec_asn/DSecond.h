/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "DefTime"
 * 	found in "./DefTime.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#ifndef	_DSecond_H_
#define	_DSecond_H_


#include <asn_application.h>

/* Including external dependencies */
#include <NativeInteger.h>

#ifdef __cplusplus
extern "C" {
#endif

/* DSecond */
typedef long	 DSecond_t;

/* Implementation */
extern asn_per_constraints_t asn_PER_type_DSecond_constr_1;
extern asn_TYPE_descriptor_t asn_DEF_DSecond;
asn_struct_free_f DSecond_free;
asn_struct_print_f DSecond_print;
asn_constr_check_f DSecond_constraint;
ber_type_decoder_f DSecond_decode_ber;
der_type_encoder_f DSecond_encode_der;
xer_type_decoder_f DSecond_decode_xer;
xer_type_encoder_f DSecond_encode_xer;
oer_type_decoder_f DSecond_decode_oer;
oer_type_encoder_f DSecond_encode_oer;
per_type_decoder_f DSecond_decode_uper;
per_type_encoder_f DSecond_encode_uper;

#ifdef __cplusplus
}
#endif

#endif	/* _DSecond_H_ */
#include <asn_internal.h>
