/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "OPS"
 * 	found in "./OPS.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#include "OPSFrame.h"

static asn_oer_constraints_t asn_OER_type_OPSFrame_constr_1 CC_NOTUSED = {
	{ 0, 0 },
	-1};
asn_per_constraints_t asn_PER_type_OPSFrame_constr_1 CC_NOTUSED = {
	{ APC_CONSTRAINED | APC_EXTENSIBLE,  5,  5,  0,  16 }	/* (0..16,...) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
asn_TYPE_member_t asn_MBR_OPSFrame_1[] = {
	{ ATF_NOFLAGS, 0, offsetof(struct OPSFrame, choice.commandFrame),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_Command,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"commandFrame"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct OPSFrame, choice.infoFrame),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_DeviceInfo,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"infoFrame"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct OPSFrame, choice.networkFrame),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_Network,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"networkFrame"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct OPSFrame, choice.ntpFrame),
		(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NTP,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"ntpFrame"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct OPSFrame, choice.systemStatus),
		(ASN_TAG_CLASS_CONTEXT | (4 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_SystemStatus,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"systemStatus"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct OPSFrame, choice.fileTransfer),
		(ASN_TAG_CLASS_CONTEXT | (5 << 2)),
		+1,	/* EXPLICIT tag at current level */
		&asn_DEF_FileTransfer,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"fileTransfer"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct OPSFrame, choice.alarmFrame),
		(ASN_TAG_CLASS_CONTEXT | (6 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_Alarm,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"alarmFrame"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct OPSFrame, choice.v2xStatus),
		(ASN_TAG_CLASS_CONTEXT | (7 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_V2XStatus,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"v2xStatus"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct OPSFrame, choice.ires),
		(ASN_TAG_CLASS_CONTEXT | (8 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_IREList,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"ires"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct OPSFrame, choice.aiParams),
		(ASN_TAG_CLASS_CONTEXT | (9 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_AIParamList,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"aiParams"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct OPSFrame, choice.envParams),
		(ASN_TAG_CLASS_CONTEXT | (10 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_EnvParamInfo,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"envParams"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct OPSFrame, choice.referenceList),
		(ASN_TAG_CLASS_CONTEXT | (11 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_ReferenceList,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"referenceList"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct OPSFrame, choice.positionCalStart),
		(ASN_TAG_CLASS_CONTEXT | (12 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_PositionCalStart,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"positionCalStart"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct OPSFrame, choice.positionConfirm),
		(ASN_TAG_CLASS_CONTEXT | (13 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_PositionConfirm,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"positionConfirm"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct OPSFrame, choice.uwbRangingContrl),
		(ASN_TAG_CLASS_CONTEXT | (14 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_UwbRangingContrl,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"uwbRangingContrl"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct OPSFrame, choice.positionSet),
		(ASN_TAG_CLASS_CONTEXT | (15 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_PositionSet,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"positionSet"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct OPSFrame, choice.deviceIdSet),
		(ASN_TAG_CLASS_CONTEXT | (16 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_DeviceIdSet,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"deviceIdSet"
		},
};
static const asn_TYPE_tag2member_t asn_MAP_OPSFrame_tag2el_1[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* commandFrame */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* infoFrame */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 }, /* networkFrame */
    { (ASN_TAG_CLASS_CONTEXT | (3 << 2)), 3, 0, 0 }, /* ntpFrame */
    { (ASN_TAG_CLASS_CONTEXT | (4 << 2)), 4, 0, 0 }, /* systemStatus */
    { (ASN_TAG_CLASS_CONTEXT | (5 << 2)), 5, 0, 0 }, /* fileTransfer */
    { (ASN_TAG_CLASS_CONTEXT | (6 << 2)), 6, 0, 0 }, /* alarmFrame */
    { (ASN_TAG_CLASS_CONTEXT | (7 << 2)), 7, 0, 0 }, /* v2xStatus */
    { (ASN_TAG_CLASS_CONTEXT | (8 << 2)), 8, 0, 0 }, /* ires */
    { (ASN_TAG_CLASS_CONTEXT | (9 << 2)), 9, 0, 0 }, /* aiParams */
    { (ASN_TAG_CLASS_CONTEXT | (10 << 2)), 10, 0, 0 }, /* envParams */
    { (ASN_TAG_CLASS_CONTEXT | (11 << 2)), 11, 0, 0 }, /* referenceList */
    { (ASN_TAG_CLASS_CONTEXT | (12 << 2)), 12, 0, 0 }, /* positionCalStart */
    { (ASN_TAG_CLASS_CONTEXT | (13 << 2)), 13, 0, 0 }, /* positionConfirm */
    { (ASN_TAG_CLASS_CONTEXT | (14 << 2)), 14, 0, 0 }, /* uwbRangingContrl */
    { (ASN_TAG_CLASS_CONTEXT | (15 << 2)), 15, 0, 0 }, /* positionSet */
    { (ASN_TAG_CLASS_CONTEXT | (16 << 2)), 16, 0, 0 } /* deviceIdSet */
};
asn_CHOICE_specifics_t asn_SPC_OPSFrame_specs_1 = {
	sizeof(struct OPSFrame),
	offsetof(struct OPSFrame, _asn_ctx),
	offsetof(struct OPSFrame, present),
	sizeof(((struct OPSFrame *)0)->present),
	asn_MAP_OPSFrame_tag2el_1,
	17,	/* Count of tags in the map */
	0, 0,
	17	/* Extensions start */
};
asn_TYPE_descriptor_t asn_DEF_OPSFrame = {
	"OPSFrame",
	"OPSFrame",
	&asn_OP_CHOICE,
	0,	/* No effective tags (pointer) */
	0,	/* No effective tags (count) */
	0,	/* No tags (pointer) */
	0,	/* No tags (count) */
	{ &asn_OER_type_OPSFrame_constr_1, &asn_PER_type_OPSFrame_constr_1, CHOICE_constraint },
	asn_MBR_OPSFrame_1,
	17,	/* Elements count */
	&asn_SPC_OPSFrame_specs_1	/* Additional specs */
};

