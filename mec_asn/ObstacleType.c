/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "SensorSharing"
 * 	found in "./SensorSharing.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#include "ObstacleType.h"

/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
static asn_oer_constraints_t asn_OER_type_ObstacleType_constr_1 CC_NOTUSED = {
	{ 0, 0 },
	-1};
asn_per_constraints_t asn_PER_type_ObstacleType_constr_1 CC_NOTUSED = {
	{ APC_CONSTRAINED | APC_EXTENSIBLE,  4,  4,  0,  12 }	/* (0..12,...) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
static const asn_INTEGER_enum_map_t asn_MAP_ObstacleType_value2enum_1[] = {
	{ 0,	7,	"unknown" },
	{ 1,	8,	"rockfall" },
	{ 2,	9,	"landslide" },
	{ 3,	16,	"animal-intrusion" },
	{ 4,	12,	"liquid-spill" },
	{ 5,	15,	"goods-scattered" },
	{ 6,	11,	"trafficcone" },
	{ 7,	15,	"safety-triangle" },
	{ 8,	17,	"traffic-roadblock" },
	{ 9,	30,	"inspection-shaft-without-cover" },
	{ 10,	17,	"unknown-fragments" },
	{ 11,	19,	"unknown-hard-object" },
	{ 12,	19,	"unknown-soft-object" }
	/* This list is extensible */
};
static const unsigned int asn_MAP_ObstacleType_enum2value_1[] = {
	3,	/* animal-intrusion(3) */
	5,	/* goods-scattered(5) */
	9,	/* inspection-shaft-without-cover(9) */
	2,	/* landslide(2) */
	4,	/* liquid-spill(4) */
	1,	/* rockfall(1) */
	7,	/* safety-triangle(7) */
	8,	/* traffic-roadblock(8) */
	6,	/* trafficcone(6) */
	0,	/* unknown(0) */
	10,	/* unknown-fragments(10) */
	11,	/* unknown-hard-object(11) */
	12	/* unknown-soft-object(12) */
	/* This list is extensible */
};
const asn_INTEGER_specifics_t asn_SPC_ObstacleType_specs_1 = {
	asn_MAP_ObstacleType_value2enum_1,	/* "tag" => N; sorted by tag */
	asn_MAP_ObstacleType_enum2value_1,	/* N => "tag"; sorted by N */
	13,	/* Number of elements in the maps */
	14,	/* Extensions before this member */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_ObstacleType_tags_1[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
asn_TYPE_descriptor_t asn_DEF_ObstacleType = {
	"ObstacleType",
	"ObstacleType",
	&asn_OP_NativeEnumerated,
	asn_DEF_ObstacleType_tags_1,
	sizeof(asn_DEF_ObstacleType_tags_1)
		/sizeof(asn_DEF_ObstacleType_tags_1[0]), /* 1 */
	asn_DEF_ObstacleType_tags_1,	/* Same as above */
	sizeof(asn_DEF_ObstacleType_tags_1)
		/sizeof(asn_DEF_ObstacleType_tags_1[0]), /* 1 */
	{ &asn_OER_type_ObstacleType_constr_1, &asn_PER_type_ObstacleType_constr_1, NativeEnumerated_constraint },
	0, 0,	/* Defined elsewhere */
	&asn_SPC_ObstacleType_specs_1	/* Additional specs */
};

