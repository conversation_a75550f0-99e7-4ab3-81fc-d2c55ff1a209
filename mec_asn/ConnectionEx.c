/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "Map"
 * 	found in "./Map.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#include "ConnectionEx.h"

static int
memb_connectingLane_constraint_1(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	size_t size;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	/* Determine the number of elements */
	size = _A_CSEQUENCE_FROM_VOID(sptr)->count;
	
	if((size >= 1 && size <= 16)) {
		/* Perform validation of the inner elements */
		return SEQUENCE_OF_constraint(td, sptr, ctfailcb, app_key);
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static asn_oer_constraints_t asn_OER_type_connectingLane_constr_4 CC_NOTUSED = {
	{ 0, 0 },
	-1	/* (SIZE(1..16)) */};
static asn_per_constraints_t asn_PER_type_connectingLane_constr_4 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 4,  4,  1,  16 }	/* (SIZE(1..16)) */,
	0, 0	/* No PER value map */
};
static asn_oer_constraints_t asn_OER_memb_connectingLane_constr_4 CC_NOTUSED = {
	{ 0, 0 },
	-1	/* (SIZE(1..16)) */};
static asn_per_constraints_t asn_PER_memb_connectingLane_constr_4 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 4,  4,  1,  16 }	/* (SIZE(1..16)) */,
	0, 0	/* No PER value map */
};
static asn_TYPE_member_t asn_MBR_connectingLane_4[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_ConnectingLaneEx,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_connectingLane_tags_4[] = {
	(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static asn_SET_OF_specifics_t asn_SPC_connectingLane_specs_4 = {
	sizeof(struct connectingLane),
	offsetof(struct connectingLane, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_connectingLane_4 = {
	"connectingLane",
	"connectingLane",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_connectingLane_tags_4,
	sizeof(asn_DEF_connectingLane_tags_4)
		/sizeof(asn_DEF_connectingLane_tags_4[0]) - 1, /* 1 */
	asn_DEF_connectingLane_tags_4,	/* Same as above */
	sizeof(asn_DEF_connectingLane_tags_4)
		/sizeof(asn_DEF_connectingLane_tags_4[0]), /* 2 */
	{ &asn_OER_type_connectingLane_constr_4, &asn_PER_type_connectingLane_constr_4, SEQUENCE_OF_constraint },
	asn_MBR_connectingLane_4,
	1,	/* Single element */
	&asn_SPC_connectingLane_specs_4	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_ConnectionEx_1[] = {
	{ ATF_NOFLAGS, 0, offsetof(struct ConnectionEx, remoteIntersection),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NodeReferenceID,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"remoteIntersection"
		},
	{ ATF_POINTER, 4, offsetof(struct ConnectionEx, swl),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_SignalWaitingLane,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"swl"
		},
	{ ATF_POINTER, 3, offsetof(struct ConnectionEx, connectingLane),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		0,
		&asn_DEF_connectingLane_4,
		0,
		{ &asn_OER_memb_connectingLane_constr_4, &asn_PER_memb_connectingLane_constr_4,  memb_connectingLane_constraint_1 },
		0, 0, /* No default value */
		"connectingLane"
		},
	{ ATF_POINTER, 2, offsetof(struct ConnectionEx, phaseId),
		(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_PhaseID,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"phaseId"
		},
	{ ATF_POINTER, 1, offsetof(struct ConnectionEx, turn_direction),
		(ASN_TAG_CLASS_CONTEXT | (4 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_Maneuver,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"turn-direction"
		},
};
static const int asn_MAP_ConnectionEx_oms_1[] = { 1, 2, 3, 4 };
static const ber_tlv_tag_t asn_DEF_ConnectionEx_tags_1[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_ConnectionEx_tag2el_1[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* remoteIntersection */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* swl */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 }, /* connectingLane */
    { (ASN_TAG_CLASS_CONTEXT | (3 << 2)), 3, 0, 0 }, /* phaseId */
    { (ASN_TAG_CLASS_CONTEXT | (4 << 2)), 4, 0, 0 } /* turn-direction */
};
asn_SEQUENCE_specifics_t asn_SPC_ConnectionEx_specs_1 = {
	sizeof(struct ConnectionEx),
	offsetof(struct ConnectionEx, _asn_ctx),
	asn_MAP_ConnectionEx_tag2el_1,
	5,	/* Count of tags in the map */
	asn_MAP_ConnectionEx_oms_1,	/* Optional members */
	4, 0,	/* Root/Additions */
	5,	/* First extension addition */
};
asn_TYPE_descriptor_t asn_DEF_ConnectionEx = {
	"ConnectionEx",
	"ConnectionEx",
	&asn_OP_SEQUENCE,
	asn_DEF_ConnectionEx_tags_1,
	sizeof(asn_DEF_ConnectionEx_tags_1)
		/sizeof(asn_DEF_ConnectionEx_tags_1[0]), /* 1 */
	asn_DEF_ConnectionEx_tags_1,	/* Same as above */
	sizeof(asn_DEF_ConnectionEx_tags_1)
		/sizeof(asn_DEF_ConnectionEx_tags_1[0]), /* 1 */
	{ 0, 0, SEQUENCE_constraint },
	asn_MBR_ConnectionEx_1,
	5,	/* Elements count */
	&asn_SPC_ConnectionEx_specs_1	/* Additional specs */
};

