/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "Map"
 * 	found in "./Map.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#include "LaneEx.h"

asn_TYPE_member_t asn_MBR_LaneEx_1[] = {
	{ ATF_NOFLAGS, 0, offsetof(struct LaneEx, laneRefID),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_LaneRefID,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"laneRefID"
		},
	{ ATF_POINTER, 7, offsetof(struct LaneEx, laneWidth),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_LaneWidth,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"laneWidth"
		},
	{ ATF_POINTER, 6, offsetof(struct LaneEx, laneAttributes),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_LaneAttributes,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"laneAttributes"
		},
	{ ATF_POINTER, 5, offsetof(struct LaneEx, maneuvers),
		(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_AllowedManeuvers,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"maneuvers"
		},
	{ ATF_POINTER, 4, offsetof(struct LaneEx, connectsTo_ex),
		(ASN_TAG_CLASS_CONTEXT | (4 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_ConnectsToExList,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"connectsTo-ex"
		},
	{ ATF_POINTER, 3, offsetof(struct LaneEx, speedLimits),
		(ASN_TAG_CLASS_CONTEXT | (5 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_SpeedLimitList,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"speedLimits"
		},
	{ ATF_POINTER, 2, offsetof(struct LaneEx, st_points),
		(ASN_TAG_CLASS_CONTEXT | (6 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_STPointList,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"st-points"
		},
	{ ATF_POINTER, 1, offsetof(struct LaneEx, laneLineType),
		(ASN_TAG_CLASS_CONTEXT | (7 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_LaneLineType,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"laneLineType"
		},
};
static const int asn_MAP_LaneEx_oms_1[] = { 1, 2, 3, 4, 5, 6, 7 };
static const ber_tlv_tag_t asn_DEF_LaneEx_tags_1[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_LaneEx_tag2el_1[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* laneRefID */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* laneWidth */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 }, /* laneAttributes */
    { (ASN_TAG_CLASS_CONTEXT | (3 << 2)), 3, 0, 0 }, /* maneuvers */
    { (ASN_TAG_CLASS_CONTEXT | (4 << 2)), 4, 0, 0 }, /* connectsTo-ex */
    { (ASN_TAG_CLASS_CONTEXT | (5 << 2)), 5, 0, 0 }, /* speedLimits */
    { (ASN_TAG_CLASS_CONTEXT | (6 << 2)), 6, 0, 0 }, /* st-points */
    { (ASN_TAG_CLASS_CONTEXT | (7 << 2)), 7, 0, 0 } /* laneLineType */
};
asn_SEQUENCE_specifics_t asn_SPC_LaneEx_specs_1 = {
	sizeof(struct LaneEx),
	offsetof(struct LaneEx, _asn_ctx),
	asn_MAP_LaneEx_tag2el_1,
	8,	/* Count of tags in the map */
	asn_MAP_LaneEx_oms_1,	/* Optional members */
	7, 0,	/* Root/Additions */
	8,	/* First extension addition */
};
asn_TYPE_descriptor_t asn_DEF_LaneEx = {
	"LaneEx",
	"LaneEx",
	&asn_OP_SEQUENCE,
	asn_DEF_LaneEx_tags_1,
	sizeof(asn_DEF_LaneEx_tags_1)
		/sizeof(asn_DEF_LaneEx_tags_1[0]), /* 1 */
	asn_DEF_LaneEx_tags_1,	/* Same as above */
	sizeof(asn_DEF_LaneEx_tags_1)
		/sizeof(asn_DEF_LaneEx_tags_1[0]), /* 1 */
	{ 0, 0, SEQUENCE_constraint },
	asn_MBR_LaneEx_1,
	8,	/* Elements count */
	&asn_SPC_LaneEx_specs_1	/* Additional specs */
};

