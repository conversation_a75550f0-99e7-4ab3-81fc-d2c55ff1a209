/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "VPM"
 * 	found in "./VPM.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#ifndef	_AccountInfo_H_
#define	_AccountInfo_H_


#include <asn_application.h>

/* Including external dependencies */
#include <OCTET_STRING.h>
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* AccountInfo */
typedef struct AccountInfo {
	OCTET_STRING_t	 account;
	/*
	 * This type is extensible,
	 * possible extensions are below.
	 */
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} AccountInfo_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_AccountInfo;
extern asn_SEQUENCE_specifics_t asn_SPC_AccountInfo_specs_1;
extern asn_TYPE_member_t asn_MBR_AccountInfo_1[1];

#ifdef __cplusplus
}
#endif

#endif	/* _AccountInfo_H_ */
#include <asn_internal.h>
