/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "SensorSharing"
 * 	found in "./SensorSharing.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#include "AccSet4WayConfidence.h"

asn_TYPE_member_t asn_MBR_AccSet4WayConfidence_1[] = {
	{ ATF_NOFLAGS, 0, offsetof(struct AccSet4WayConfidence, lonAccConfidence),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_AccConfidence,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"lonAccConfidence"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct AccSet4WayConfidence, latAccConfidence),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_AccConfidence,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"latAccConfidence"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct AccSet4WayConfidence, vertAccConfidence),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_AccConfidence,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"vertAccConfidence"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct AccSet4WayConfidence, yawRateCon),
		(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_AngularVConfidence,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"yawRateCon"
		},
};
static const ber_tlv_tag_t asn_DEF_AccSet4WayConfidence_tags_1[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_AccSet4WayConfidence_tag2el_1[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* lonAccConfidence */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* latAccConfidence */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 }, /* vertAccConfidence */
    { (ASN_TAG_CLASS_CONTEXT | (3 << 2)), 3, 0, 0 } /* yawRateCon */
};
asn_SEQUENCE_specifics_t asn_SPC_AccSet4WayConfidence_specs_1 = {
	sizeof(struct AccSet4WayConfidence),
	offsetof(struct AccSet4WayConfidence, _asn_ctx),
	asn_MAP_AccSet4WayConfidence_tag2el_1,
	4,	/* Count of tags in the map */
	0, 0, 0,	/* Optional elements (not needed) */
	-1,	/* First extension addition */
};
asn_TYPE_descriptor_t asn_DEF_AccSet4WayConfidence = {
	"AccSet4WayConfidence",
	"AccSet4WayConfidence",
	&asn_OP_SEQUENCE,
	asn_DEF_AccSet4WayConfidence_tags_1,
	sizeof(asn_DEF_AccSet4WayConfidence_tags_1)
		/sizeof(asn_DEF_AccSet4WayConfidence_tags_1[0]), /* 1 */
	asn_DEF_AccSet4WayConfidence_tags_1,	/* Same as above */
	sizeof(asn_DEF_AccSet4WayConfidence_tags_1)
		/sizeof(asn_DEF_AccSet4WayConfidence_tags_1[0]), /* 1 */
	{ 0, 0, SEQUENCE_constraint },
	asn_MBR_AccSet4WayConfidence_1,
	4,	/* Elements count */
	&asn_SPC_AccSet4WayConfidence_specs_1	/* Additional specs */
};

