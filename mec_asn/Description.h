/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "RSI"
 * 	found in "./RSI.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#ifndef	_Description_H_
#define	_Description_H_


#include <asn_application.h>

/* Including external dependencies */
#include <IA5String.h>
#include <OCTET_STRING.h>
#include <constr_CHOICE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Dependencies */
typedef enum Description_PR {
	Description_PR_NOTHING,	/* No components present */
	Description_PR_textString,
	Description_PR_textGB2312
} Description_PR;

/* Description */
typedef struct Description {
	Description_PR present;
	union Description_u {
		IA5String_t	 textString;
		OCTET_STRING_t	 textGB2312;
	} choice;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} Description_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_Description;
extern asn_CHOICE_specifics_t asn_SPC_Description_specs_1;
extern asn_TYPE_member_t asn_MBR_Description_1[2];
extern asn_per_constraints_t asn_PER_type_Description_constr_1;

#ifdef __cplusplus
}
#endif

#endif	/* _Description_H_ */
#include <asn_internal.h>
