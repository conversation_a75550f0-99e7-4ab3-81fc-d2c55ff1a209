/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "OPS"
 * 	found in "./OPS.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#include "PositionCalStart.h"

static int
memb_anchorGroupNum_constraint_1(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	long value;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	value = *(const long *)sptr;
	
	if((value >= 0 && value <= 7)) {
		/* Constraint check succeeded */
		return 0;
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static asn_oer_constraints_t asn_OER_memb_anchorGroupNum_constr_2 CC_NOTUSED = {
	{ 1, 1 }	/* (0..7) */,
	-1};
static asn_per_constraints_t asn_PER_memb_anchorGroupNum_constr_2 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 3,  3,  0,  7 }	/* (0..7) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
asn_TYPE_member_t asn_MBR_PositionCalStart_1[] = {
	{ ATF_NOFLAGS, 0, offsetof(struct PositionCalStart, anchorGroupNum),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NativeInteger,
		0,
		{ &asn_OER_memb_anchorGroupNum_constr_2, &asn_PER_memb_anchorGroupNum_constr_2,  memb_anchorGroupNum_constraint_1 },
		0, 0, /* No default value */
		"anchorGroupNum"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct PositionCalStart, anchorList),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_AnchorList,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"anchorList"
		},
};
static const ber_tlv_tag_t asn_DEF_PositionCalStart_tags_1[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_PositionCalStart_tag2el_1[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* anchorGroupNum */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 } /* anchorList */
};
asn_SEQUENCE_specifics_t asn_SPC_PositionCalStart_specs_1 = {
	sizeof(struct PositionCalStart),
	offsetof(struct PositionCalStart, _asn_ctx),
	asn_MAP_PositionCalStart_tag2el_1,
	2,	/* Count of tags in the map */
	0, 0, 0,	/* Optional elements (not needed) */
	2,	/* First extension addition */
};
asn_TYPE_descriptor_t asn_DEF_PositionCalStart = {
	"PositionCalStart",
	"PositionCalStart",
	&asn_OP_SEQUENCE,
	asn_DEF_PositionCalStart_tags_1,
	sizeof(asn_DEF_PositionCalStart_tags_1)
		/sizeof(asn_DEF_PositionCalStart_tags_1[0]), /* 1 */
	asn_DEF_PositionCalStart_tags_1,	/* Same as above */
	sizeof(asn_DEF_PositionCalStart_tags_1)
		/sizeof(asn_DEF_PositionCalStart_tags_1[0]), /* 1 */
	{ 0, 0, SEQUENCE_constraint },
	asn_MBR_PositionCalStart_1,
	2,	/* Elements count */
	&asn_SPC_PositionCalStart_specs_1	/* Additional specs */
};

