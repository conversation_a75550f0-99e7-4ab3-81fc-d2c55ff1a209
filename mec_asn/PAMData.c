/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "PAM"
 * 	found in "./PAM.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#include "PAMData.h"

static int
memb_parkingAreaGuidance_constraint_1(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	size_t size;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	/* Determine the number of elements */
	size = _A_CSEQUENCE_FROM_VOID(sptr)->count;
	
	if((size >= 1 && size <= 16)) {
		/* Perform validation of the inner elements */
		return SEQUENCE_OF_constraint(td, sptr, ctfailcb, app_key);
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static asn_oer_constraints_t asn_OER_type_parkingAreaGuidance_constr_6 CC_NOTUSED = {
	{ 0, 0 },
	-1	/* (SIZE(1..16)) */};
static asn_per_constraints_t asn_PER_type_parkingAreaGuidance_constr_6 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 4,  4,  1,  16 }	/* (SIZE(1..16)) */,
	0, 0	/* No PER value map */
};
static asn_oer_constraints_t asn_OER_memb_parkingAreaGuidance_constr_6 CC_NOTUSED = {
	{ 0, 0 },
	-1	/* (SIZE(1..16)) */};
static asn_per_constraints_t asn_PER_memb_parkingAreaGuidance_constr_6 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 4,  4,  1,  16 }	/* (SIZE(1..16)) */,
	0, 0	/* No PER value map */
};
static asn_TYPE_member_t asn_MBR_parkingAreaGuidance_6[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_ParkingGuide,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_parkingAreaGuidance_tags_6[] = {
	(ASN_TAG_CLASS_CONTEXT | (4 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static asn_SET_OF_specifics_t asn_SPC_parkingAreaGuidance_specs_6 = {
	sizeof(struct parkingAreaGuidance),
	offsetof(struct parkingAreaGuidance, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_parkingAreaGuidance_6 = {
	"parkingAreaGuidance",
	"parkingAreaGuidance",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_parkingAreaGuidance_tags_6,
	sizeof(asn_DEF_parkingAreaGuidance_tags_6)
		/sizeof(asn_DEF_parkingAreaGuidance_tags_6[0]) - 1, /* 1 */
	asn_DEF_parkingAreaGuidance_tags_6,	/* Same as above */
	sizeof(asn_DEF_parkingAreaGuidance_tags_6)
		/sizeof(asn_DEF_parkingAreaGuidance_tags_6[0]), /* 2 */
	{ &asn_OER_type_parkingAreaGuidance_constr_6, &asn_PER_type_parkingAreaGuidance_constr_6, SEQUENCE_OF_constraint },
	asn_MBR_parkingAreaGuidance_6,
	1,	/* Single element */
	&asn_SPC_parkingAreaGuidance_specs_6	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_PAMData_1[] = {
	{ ATF_NOFLAGS, 0, offsetof(struct PAMData, msgCnt),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_MsgCount,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"msgCnt"
		},
	{ ATF_POINTER, 1, offsetof(struct PAMData, timeStamp),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_MinuteOfTheYear,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"timeStamp"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct PAMData, parkingLotInfo),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_ParkingLotInfo,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"parkingLotInfo"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct PAMData, pamNodes),
		(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_PAMNodeList,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"pamNodes"
		},
	{ ATF_POINTER, 1, offsetof(struct PAMData, parkingAreaGuidance),
		(ASN_TAG_CLASS_CONTEXT | (4 << 2)),
		0,
		&asn_DEF_parkingAreaGuidance_6,
		0,
		{ &asn_OER_memb_parkingAreaGuidance_constr_6, &asn_PER_memb_parkingAreaGuidance_constr_6,  memb_parkingAreaGuidance_constraint_1 },
		0, 0, /* No default value */
		"parkingAreaGuidance"
		},
};
static const int asn_MAP_PAMData_oms_1[] = { 1, 4 };
static const ber_tlv_tag_t asn_DEF_PAMData_tags_1[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_PAMData_tag2el_1[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* msgCnt */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* timeStamp */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 }, /* parkingLotInfo */
    { (ASN_TAG_CLASS_CONTEXT | (3 << 2)), 3, 0, 0 }, /* pamNodes */
    { (ASN_TAG_CLASS_CONTEXT | (4 << 2)), 4, 0, 0 } /* parkingAreaGuidance */
};
asn_SEQUENCE_specifics_t asn_SPC_PAMData_specs_1 = {
	sizeof(struct PAMData),
	offsetof(struct PAMData, _asn_ctx),
	asn_MAP_PAMData_tag2el_1,
	5,	/* Count of tags in the map */
	asn_MAP_PAMData_oms_1,	/* Optional members */
	2, 0,	/* Root/Additions */
	5,	/* First extension addition */
};
asn_TYPE_descriptor_t asn_DEF_PAMData = {
	"PAMData",
	"PAMData",
	&asn_OP_SEQUENCE,
	asn_DEF_PAMData_tags_1,
	sizeof(asn_DEF_PAMData_tags_1)
		/sizeof(asn_DEF_PAMData_tags_1[0]), /* 1 */
	asn_DEF_PAMData_tags_1,	/* Same as above */
	sizeof(asn_DEF_PAMData_tags_1)
		/sizeof(asn_DEF_PAMData_tags_1[0]), /* 1 */
	{ 0, 0, SEQUENCE_constraint },
	asn_MBR_PAMData_1,
	5,	/* Elements count */
	&asn_SPC_PAMData_specs_1	/* Additional specs */
};

