/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "DefPositionOffset"
 * 	found in "./DefPositionOffset.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#include "Position-LL-24B.h"

asn_TYPE_member_t asn_MBR_Position_LL_24B_1[] = {
	{ ATF_NOFLAGS, 0, offsetof(struct Position_LL_24B, lon),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_OffsetLL_B12,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"lon"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct Position_LL_24B, lat),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_OffsetLL_B12,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"lat"
		},
};
static const ber_tlv_tag_t asn_DEF_Position_LL_24B_tags_1[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_Position_LL_24B_tag2el_1[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* lon */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 } /* lat */
};
asn_SEQUENCE_specifics_t asn_SPC_Position_LL_24B_specs_1 = {
	sizeof(struct Position_LL_24B),
	offsetof(struct Position_LL_24B, _asn_ctx),
	asn_MAP_Position_LL_24B_tag2el_1,
	2,	/* Count of tags in the map */
	0, 0, 0,	/* Optional elements (not needed) */
	-1,	/* First extension addition */
};
asn_TYPE_descriptor_t asn_DEF_Position_LL_24B = {
	"Position-LL-24B",
	"Position-LL-24B",
	&asn_OP_SEQUENCE,
	asn_DEF_Position_LL_24B_tags_1,
	sizeof(asn_DEF_Position_LL_24B_tags_1)
		/sizeof(asn_DEF_Position_LL_24B_tags_1[0]), /* 1 */
	asn_DEF_Position_LL_24B_tags_1,	/* Same as above */
	sizeof(asn_DEF_Position_LL_24B_tags_1)
		/sizeof(asn_DEF_Position_LL_24B_tags_1[0]), /* 1 */
	{ 0, 0, SEQUENCE_constraint },
	asn_MBR_Position_LL_24B_1,
	2,	/* Elements count */
	&asn_SPC_Position_LL_24B_specs_1	/* Additional specs */
};

