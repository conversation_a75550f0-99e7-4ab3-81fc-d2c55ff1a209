/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "GoEMEC"
 * 	found in "./GoEMEC.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#ifndef	_LinkFrame_H_
#define	_LinkFrame_H_


#include <asn_application.h>

/* Including external dependencies */
#include "Login.h"
#include "LoginAck.h"
#include "Logout.h"
#include "ACK.h"
#include "HeartBeat.h"
#include <constr_CHOICE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Dependencies */
typedef enum LinkFrame_PR {
	LinkFrame_PR_NOTHING,	/* No components present */
	LinkFrame_PR_login,
	LinkFrame_PR_loginAck,
	LinkFrame_PR_logout,
	Link<PERSON>rame_PR_ack,
	Link<PERSON>rame_PR_heartbeat
	/* Extensions may appear below */
	
} LinkFrame_PR;

/* LinkFrame */
typedef struct LinkFrame {
	LinkFrame_PR present;
	union LinkFrame_u {
		Login_t	 login;
		LoginAck_t	 loginAck;
		Logout_t	 logout;
		ACK_t	 ack;
		HeartBeat_t	 heartbeat;
		/*
		 * This type is extensible,
		 * possible extensions are below.
		 */
	} choice;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} LinkFrame_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_LinkFrame;
extern asn_CHOICE_specifics_t asn_SPC_LinkFrame_specs_1;
extern asn_TYPE_member_t asn_MBR_LinkFrame_1[5];
extern asn_per_constraints_t asn_PER_type_LinkFrame_constr_1;

#ifdef __cplusplus
}
#endif

#endif	/* _LinkFrame_H_ */
#include <asn_internal.h>
