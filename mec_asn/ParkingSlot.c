/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "PAM"
 * 	found in "./PAM.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#include "ParkingSlot.h"

static int
memb_slotID_constraint_1(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	long value;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	value = *(const long *)sptr;
	
	if((value >= 0 && value <= 65535)) {
		/* Constraint check succeeded */
		return 0;
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static asn_oer_constraints_t asn_OER_memb_slotID_constr_2 CC_NOTUSED = {
	{ 2, 1 }	/* (0..65535) */,
	-1};
static asn_per_constraints_t asn_PER_memb_slotID_constr_2 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 16,  16,  0,  65535 }	/* (0..65535) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
asn_TYPE_member_t asn_MBR_ParkingSlot_1[] = {
	{ ATF_NOFLAGS, 0, offsetof(struct ParkingSlot, slotID),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NativeInteger,
		0,
		{ &asn_OER_memb_slotID_constr_2, &asn_PER_memb_slotID_constr_2,  memb_slotID_constraint_1 },
		0, 0, /* No default value */
		"slotID"
		},
	{ ATF_POINTER, 2, offsetof(struct ParkingSlot, position),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_ParkingSlotPosition,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"position"
		},
	{ ATF_POINTER, 1, offsetof(struct ParkingSlot, sign),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_DescriptiveName,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"sign"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct ParkingSlot, parkingType),
		(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_ParkingType,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"parkingType"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct ParkingSlot, status),
		(ASN_TAG_CLASS_CONTEXT | (4 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_SlotStatus,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"status"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct ParkingSlot, parkingSpaceTheta),
		(ASN_TAG_CLASS_CONTEXT | (5 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_ParkingSpaceTheta,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"parkingSpaceTheta"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct ParkingSlot, parkingLock),
		(ASN_TAG_CLASS_CONTEXT | (6 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_ParkingLock,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"parkingLock"
		},
};
static const int asn_MAP_ParkingSlot_oms_1[] = { 1, 2 };
static const ber_tlv_tag_t asn_DEF_ParkingSlot_tags_1[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_ParkingSlot_tag2el_1[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* slotID */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* position */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 }, /* sign */
    { (ASN_TAG_CLASS_CONTEXT | (3 << 2)), 3, 0, 0 }, /* parkingType */
    { (ASN_TAG_CLASS_CONTEXT | (4 << 2)), 4, 0, 0 }, /* status */
    { (ASN_TAG_CLASS_CONTEXT | (5 << 2)), 5, 0, 0 }, /* parkingSpaceTheta */
    { (ASN_TAG_CLASS_CONTEXT | (6 << 2)), 6, 0, 0 } /* parkingLock */
};
asn_SEQUENCE_specifics_t asn_SPC_ParkingSlot_specs_1 = {
	sizeof(struct ParkingSlot),
	offsetof(struct ParkingSlot, _asn_ctx),
	asn_MAP_ParkingSlot_tag2el_1,
	7,	/* Count of tags in the map */
	asn_MAP_ParkingSlot_oms_1,	/* Optional members */
	2, 0,	/* Root/Additions */
	7,	/* First extension addition */
};
asn_TYPE_descriptor_t asn_DEF_ParkingSlot = {
	"ParkingSlot",
	"ParkingSlot",
	&asn_OP_SEQUENCE,
	asn_DEF_ParkingSlot_tags_1,
	sizeof(asn_DEF_ParkingSlot_tags_1)
		/sizeof(asn_DEF_ParkingSlot_tags_1[0]), /* 1 */
	asn_DEF_ParkingSlot_tags_1,	/* Same as above */
	sizeof(asn_DEF_ParkingSlot_tags_1)
		/sizeof(asn_DEF_ParkingSlot_tags_1[0]), /* 1 */
	{ 0, 0, SEQUENCE_constraint },
	asn_MBR_ParkingSlot_1,
	7,	/* Elements count */
	&asn_SPC_ParkingSlot_specs_1	/* Additional specs */
};

