/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "VPM"
 * 	found in "./VPM.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#include "Action-Response.h"

static int
memb_sourceId_constraint_1(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	const OCTET_STRING_t *st = (const OCTET_STRING_t *)sptr;
	size_t size;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	size = st->size;
	
	if((size == 8)) {
		/* Constraint check succeeded */
		return 0;
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_targetId_constraint_1(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	const OCTET_STRING_t *st = (const OCTET_STRING_t *)sptr;
	size_t size;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	size = st->size;
	
	if((size == 8)) {
		/* Constraint check succeeded */
		return 0;
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static asn_oer_constraints_t asn_OER_memb_sourceId_constr_3 CC_NOTUSED = {
	{ 0, 0 },
	8	/* (SIZE(8..8)) */};
static asn_per_constraints_t asn_PER_memb_sourceId_constr_3 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 0,  0,  8,  8 }	/* (SIZE(8..8)) */,
	0, 0	/* No PER value map */
};
static asn_oer_constraints_t asn_OER_memb_targetId_constr_4 CC_NOTUSED = {
	{ 0, 0 },
	8	/* (SIZE(8..8)) */};
static asn_per_constraints_t asn_PER_memb_targetId_constr_4 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 0,  0,  8,  8 }	/* (SIZE(8..8)) */,
	0, 0	/* No PER value map */
};
asn_TYPE_member_t asn_MBR_Action_Response_1[] = {
	{ ATF_NOFLAGS, 0, offsetof(struct Action_Response, time),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_DDateTime,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"time"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct Action_Response, sourceId),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_OCTET_STRING,
		0,
		{ &asn_OER_memb_sourceId_constr_3, &asn_PER_memb_sourceId_constr_3,  memb_sourceId_constraint_1 },
		0, 0, /* No default value */
		"sourceId"
		},
	{ ATF_POINTER, 1, offsetof(struct Action_Response, targetId),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_OCTET_STRING,
		0,
		{ &asn_OER_memb_targetId_constr_4, &asn_PER_memb_targetId_constr_4,  memb_targetId_constraint_1 },
		0, 0, /* No default value */
		"targetId"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct Action_Response, paymentEntityId),
		(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_PaymentEntityID,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"paymentEntityId"
		},
	{ ATF_POINTER, 1, offsetof(struct Action_Response, responseParameter),
		(ASN_TAG_CLASS_CONTEXT | (4 << 2)),
		+1,	/* EXPLICIT tag at current level */
		&asn_DEF_TransInfo,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"responseParameter"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct Action_Response, ret),
		(ASN_TAG_CLASS_CONTEXT | (5 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_ReturnStatus,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"ret"
		},
};
static const int asn_MAP_Action_Response_oms_1[] = { 2, 4 };
static const ber_tlv_tag_t asn_DEF_Action_Response_tags_1[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_Action_Response_tag2el_1[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* time */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* sourceId */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 }, /* targetId */
    { (ASN_TAG_CLASS_CONTEXT | (3 << 2)), 3, 0, 0 }, /* paymentEntityId */
    { (ASN_TAG_CLASS_CONTEXT | (4 << 2)), 4, 0, 0 }, /* responseParameter */
    { (ASN_TAG_CLASS_CONTEXT | (5 << 2)), 5, 0, 0 } /* ret */
};
asn_SEQUENCE_specifics_t asn_SPC_Action_Response_specs_1 = {
	sizeof(struct Action_Response),
	offsetof(struct Action_Response, _asn_ctx),
	asn_MAP_Action_Response_tag2el_1,
	6,	/* Count of tags in the map */
	asn_MAP_Action_Response_oms_1,	/* Optional members */
	2, 0,	/* Root/Additions */
	6,	/* First extension addition */
};
asn_TYPE_descriptor_t asn_DEF_Action_Response = {
	"Action-Response",
	"Action-Response",
	&asn_OP_SEQUENCE,
	asn_DEF_Action_Response_tags_1,
	sizeof(asn_DEF_Action_Response_tags_1)
		/sizeof(asn_DEF_Action_Response_tags_1[0]), /* 1 */
	asn_DEF_Action_Response_tags_1,	/* Same as above */
	sizeof(asn_DEF_Action_Response_tags_1)
		/sizeof(asn_DEF_Action_Response_tags_1[0]), /* 1 */
	{ 0, 0, SEQUENCE_constraint },
	asn_MBR_Action_Response_1,
	6,	/* Elements count */
	&asn_SPC_Action_Response_specs_1	/* Additional specs */
};

