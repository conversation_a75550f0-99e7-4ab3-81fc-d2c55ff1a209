/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "VPM"
 * 	found in "./VPM.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#ifndef	_PassedSitesInfo_H_
#define	_PassedSitesInfo_H_


#include <asn_application.h>

/* Including external dependencies */
#include <asn_SEQUENCE_OF.h>
#include <constr_SEQUENCE_OF.h>
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Forward declarations */
struct PassedPos;

/* PassedSitesInfo */
typedef struct PassedSitesInfo {
	struct PassedPos	*entranceInfo	/* OPTIONAL */;
	struct passedPos {
		A_SEQUENCE_OF(struct PassedPos) list;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *passedPos;
	/*
	 * This type is extensible,
	 * possible extensions are below.
	 */
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} PassedSitesInfo_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_PassedSitesInfo;
extern asn_SEQUENCE_specifics_t asn_SPC_PassedSitesInfo_specs_1;
extern asn_TYPE_member_t asn_MBR_PassedSitesInfo_1[2];

#ifdef __cplusplus
}
#endif

/* Referred external types */
#include "PassedPos.h"

#endif	/* _PassedSitesInfo_H_ */
#include <asn_internal.h>
