/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "OPS"
 * 	found in "./OPS.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#ifndef	_DataRateKByte_H_
#define	_DataRateKByte_H_


#include <asn_application.h>

/* Including external dependencies */
#include <NativeReal.h>

#ifdef __cplusplus
extern "C" {
#endif

/* DataRateKByte */
typedef double	 DataRateKByte_t;

/* Implementation */
extern asn_per_constraints_t asn_PER_type_DataRateKByte_constr_1;
extern asn_TYPE_descriptor_t asn_DEF_DataRateKByte;
asn_struct_free_f DataRateKByte_free;
asn_struct_print_f DataRateKByte_print;
asn_constr_check_f DataRateKByte_constraint;
ber_type_decoder_f DataRateKByte_decode_ber;
der_type_encoder_f DataRateKByte_encode_der;
xer_type_decoder_f DataRateKByte_decode_xer;
xer_type_encoder_f DataRateKByte_encode_xer;
oer_type_decoder_f DataRateKByte_decode_oer;
oer_type_encoder_f DataRateKByte_encode_oer;
per_type_decoder_f DataRateKByte_decode_uper;
per_type_encoder_f DataRateKByte_encode_uper;

#ifdef __cplusplus
}
#endif

#endif	/* _DataRateKByte_H_ */
#include <asn_internal.h>
