/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "GoEMEC"
 * 	found in "./GoEMEC.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#ifndef	_GoEMECMessageFrame_H_
#define	_GoEMECMessageFrame_H_


#include <asn_application.h>

/* Including external dependencies */
#include "LinkFrame.h"
#include "MessageFrame.h"
#include "OPSFrame.h"
#include <constr_CHOICE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Dependencies */
typedef enum GoEMECMessageFrame_PR {
	GoEMECMessageFrame_PR_NOTHING,	/* No components present */
	GoEMECMessageFrame_PR_linkFrame,
	GoEMECMessageFrame_PR_cv2xFrame,
	GoEMECMessageFrame_PR_opsFrame
	/* Extensions may appear below */
	
} GoEMECMessageFrame_PR;

/* GoEMECMessageFrame */
typedef struct GoEMECMessageFrame {
	GoEMECMessageFrame_PR present;
	union GoEMECMessageFrame_u {
		LinkFrame_t	 linkFrame;
		MessageFrame_t	 cv2xFrame;
		OPSFrame_t	 opsFrame;
		/*
		 * This type is extensible,
		 * possible extensions are below.
		 */
	} choice;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} GoEMECMessageFrame_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_GoEMECMessageFrame;

#ifdef __cplusplus
}
#endif

#endif	/* _GoEMECMessageFrame_H_ */
#include <asn_internal.h>
