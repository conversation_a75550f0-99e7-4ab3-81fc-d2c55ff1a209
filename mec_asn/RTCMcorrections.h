/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "RTCM"
 * 	found in "./RTCM.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#ifndef	_RTCMcorrections_H_
#define	_RTCMcorrections_H_


#include <asn_application.h>

/* Including external dependencies */
#include "MsgCount.h"
#include <asn_SEQUENCE_OF.h>
#include <constr_SEQUENCE_OF.h>
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Forward declarations */
struct RTCMmsg;

/* RTCMcorrections */
typedef struct RTCMcorrections {
	MsgCount_t	 msgCnt;
	struct corrections {
		A_SEQUENCE_OF(struct RTCMmsg) list;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} corrections;
	/*
	 * This type is extensible,
	 * possible extensions are below.
	 */
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} RTCMcorrections_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_RTCMcorrections;
extern asn_SEQUENCE_specifics_t asn_SPC_RTCMcorrections_specs_1;
extern asn_TYPE_member_t asn_MBR_RTCMcorrections_1[2];

#ifdef __cplusplus
}
#endif

/* Referred external types */
#include "RTCMmsg.h"

#endif	/* _RTCMcorrections_H_ */
#include <asn_internal.h>
