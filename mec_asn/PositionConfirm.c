/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "OPS"
 * 	found in "./OPS.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#include "PositionConfirm.h"

asn_TYPE_member_t asn_MBR_PositionConfirm_1[] = {
	{ ATF_NOFLAGS, 0, offsetof(struct PositionConfirm, location),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_Position3D,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"location"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct PositionConfirm, xyz),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_PositionXYZ,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"xyz"
		},
};
static const ber_tlv_tag_t asn_DEF_PositionConfirm_tags_1[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_PositionConfirm_tag2el_1[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* location */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 } /* xyz */
};
asn_SEQUENCE_specifics_t asn_SPC_PositionConfirm_specs_1 = {
	sizeof(struct PositionConfirm),
	offsetof(struct PositionConfirm, _asn_ctx),
	asn_MAP_PositionConfirm_tag2el_1,
	2,	/* Count of tags in the map */
	0, 0, 0,	/* Optional elements (not needed) */
	2,	/* First extension addition */
};
asn_TYPE_descriptor_t asn_DEF_PositionConfirm = {
	"PositionConfirm",
	"PositionConfirm",
	&asn_OP_SEQUENCE,
	asn_DEF_PositionConfirm_tags_1,
	sizeof(asn_DEF_PositionConfirm_tags_1)
		/sizeof(asn_DEF_PositionConfirm_tags_1[0]), /* 1 */
	asn_DEF_PositionConfirm_tags_1,	/* Same as above */
	sizeof(asn_DEF_PositionConfirm_tags_1)
		/sizeof(asn_DEF_PositionConfirm_tags_1[0]), /* 1 */
	{ 0, 0, SEQUENCE_constraint },
	asn_MBR_PositionConfirm_1,
	2,	/* Elements count */
	&asn_SPC_PositionConfirm_specs_1	/* Additional specs */
};

