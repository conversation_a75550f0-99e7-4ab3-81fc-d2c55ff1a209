/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "DefMotion"
 * 	found in "./DefMotion.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#ifndef	_CoarseHeading_H_
#define	_CoarseHeading_H_


#include <asn_application.h>

/* Including external dependencies */
#include <NativeInteger.h>

#ifdef __cplusplus
extern "C" {
#endif

/* CoarseHeading */
typedef long	 CoarseHeading_t;

/* Implementation */
extern asn_per_constraints_t asn_PER_type_CoarseHeading_constr_1;
extern asn_TYPE_descriptor_t asn_DEF_CoarseHeading;
asn_struct_free_f CoarseHeading_free;
asn_struct_print_f CoarseHeading_print;
asn_constr_check_f CoarseHeading_constraint;
ber_type_decoder_f CoarseHeading_decode_ber;
der_type_encoder_f CoarseHeading_encode_der;
xer_type_decoder_f CoarseHeading_decode_xer;
xer_type_encoder_f CoarseHeading_encode_xer;
oer_type_decoder_f CoarseHeading_decode_oer;
oer_type_encoder_f CoarseHeading_encode_oer;
per_type_decoder_f CoarseHeading_decode_uper;
per_type_encoder_f CoarseHeading_encode_uper;

#ifdef __cplusplus
}
#endif

#endif	/* _CoarseHeading_H_ */
#include <asn_internal.h>
