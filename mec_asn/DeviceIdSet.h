/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "OPS"
 * 	found in "./OPS.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#ifndef	_DeviceIdSet_H_
#define	_DeviceIdSet_H_


#include <asn_application.h>

/* Including external dependencies */
#include <NativeInteger.h>
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* DeviceIdSet */
typedef struct DeviceIdSet {
	long	 id;
	/*
	 * This type is extensible,
	 * possible extensions are below.
	 */
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} DeviceIdSet_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_DeviceIdSet;
extern asn_SEQUENCE_specifics_t asn_SPC_DeviceIdSet_specs_1;
extern asn_TYPE_member_t asn_MBR_DeviceIdSet_1[1];

#ifdef __cplusplus
}
#endif

#endif	/* _DeviceIdSet_H_ */
#include <asn_internal.h>
