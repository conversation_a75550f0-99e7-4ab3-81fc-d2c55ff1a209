/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "OPS"
 * 	found in "./OPS.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#ifndef	_RangingMode_H_
#define	_RangingMode_H_


#include <asn_application.h>

/* Including external dependencies */
#include <NativeEnumerated.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Dependencies */
typedef enum RangingMode {
	RangingMode_tag	= 0,
	RangingMode_anchor	= 1
} e_RangingMode;

/* RangingMode */
typedef long	 RangingMode_t;

/* Implementation */
extern asn_per_constraints_t asn_PER_type_RangingMode_constr_1;
extern asn_TYPE_descriptor_t asn_DEF_RangingMode;
extern const asn_INTEGER_specifics_t asn_SPC_RangingMode_specs_1;
asn_struct_free_f RangingMode_free;
asn_struct_print_f RangingMode_print;
asn_constr_check_f RangingMode_constraint;
ber_type_decoder_f RangingMode_decode_ber;
der_type_encoder_f RangingMode_encode_der;
xer_type_decoder_f RangingMode_decode_xer;
xer_type_encoder_f RangingMode_encode_xer;
oer_type_decoder_f RangingMode_decode_oer;
oer_type_encoder_f RangingMode_encode_oer;
per_type_decoder_f RangingMode_decode_uper;
per_type_encoder_f RangingMode_encode_uper;

#ifdef __cplusplus
}
#endif

#endif	/* _RangingMode_H_ */
#include <asn_internal.h>
