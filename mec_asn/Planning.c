/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "SensorSharing"
 * 	found in "./SensorSharing.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#include "Planning.h"

asn_TYPE_member_t asn_MBR_Planning_1[] = {
	{ ATF_POINTER, 4, offsetof(struct Planning, duration),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_PlanningDuration,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"duration"
		},
	{ ATF_POINTER, 3, offsetof(struct Planning, planConfidence),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_Confidence,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"planConfidence"
		},
	{ ATF_POINTER, 2, offsetof(struct Planning, drivingBehavior),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_DriveBehavior,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"drivingBehavior"
		},
	{ ATF_POINTER, 1, offsetof(struct Planning, pathPlanning),
		(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_PathPlanning,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"pathPlanning"
		},
};
static const int asn_MAP_Planning_oms_1[] = { 0, 1, 2, 3 };
static const ber_tlv_tag_t asn_DEF_Planning_tags_1[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_Planning_tag2el_1[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* duration */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* planConfidence */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 }, /* drivingBehavior */
    { (ASN_TAG_CLASS_CONTEXT | (3 << 2)), 3, 0, 0 } /* pathPlanning */
};
asn_SEQUENCE_specifics_t asn_SPC_Planning_specs_1 = {
	sizeof(struct Planning),
	offsetof(struct Planning, _asn_ctx),
	asn_MAP_Planning_tag2el_1,
	4,	/* Count of tags in the map */
	asn_MAP_Planning_oms_1,	/* Optional members */
	4, 0,	/* Root/Additions */
	4,	/* First extension addition */
};
asn_TYPE_descriptor_t asn_DEF_Planning = {
	"Planning",
	"Planning",
	&asn_OP_SEQUENCE,
	asn_DEF_Planning_tags_1,
	sizeof(asn_DEF_Planning_tags_1)
		/sizeof(asn_DEF_Planning_tags_1[0]), /* 1 */
	asn_DEF_Planning_tags_1,	/* Same as above */
	sizeof(asn_DEF_Planning_tags_1)
		/sizeof(asn_DEF_Planning_tags_1[0]), /* 1 */
	{ 0, 0, SEQUENCE_constraint },
	asn_MBR_Planning_1,
	4,	/* Elements count */
	&asn_SPC_Planning_specs_1	/* Additional specs */
};

