/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "VehClass"
 * 	found in "./VehClass.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#ifndef	_BasicVehicleClass_H_
#define	_BasicVehicleClass_H_


#include <asn_application.h>

/* Including external dependencies */
#include <NativeInteger.h>

#ifdef __cplusplus
extern "C" {
#endif

/* BasicVehicleClass */
typedef long	 BasicVehicleClass_t;

/* Implementation */
extern asn_per_constraints_t asn_PER_type_BasicVehicleClass_constr_1;
extern asn_TYPE_descriptor_t asn_DEF_BasicVehicleClass;
asn_struct_free_f BasicVehicleClass_free;
asn_struct_print_f BasicVehicleClass_print;
asn_constr_check_f BasicVehicleClass_constraint;
ber_type_decoder_f BasicVehicleClass_decode_ber;
der_type_encoder_f BasicVehicleClass_encode_der;
xer_type_decoder_f BasicVehicleClass_decode_xer;
xer_type_encoder_f BasicVehicleClass_encode_xer;
oer_type_decoder_f BasicVehicleClass_decode_oer;
oer_type_encoder_f BasicVehicleClass_encode_oer;
per_type_decoder_f BasicVehicleClass_decode_uper;
per_type_encoder_f BasicVehicleClass_encode_uper;

#ifdef __cplusplus
}
#endif

#endif	/* _BasicVehicleClass_H_ */
#include <asn_internal.h>
