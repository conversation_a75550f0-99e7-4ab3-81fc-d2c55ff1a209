/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "VPM"
 * 	found in "./VPM.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#ifndef	_PaymentInfoType1_H_
#define	_PaymentInfoType1_H_


#include <asn_application.h>

/* Including external dependencies */
#include <OCTET_STRING.h>
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Forward declarations */
struct TollingNodeInfo;
struct TollInfo;

/* PaymentInfoType1 */
typedef struct PaymentInfoType1 {
	struct TollingNodeInfo	*tollingNodeInfo	/* OPTIONAL */;
	struct TollInfo	*tollInfo	/* OPTIONAL */;
	OCTET_STRING_t	*serviceInfo	/* OPTIONAL */;
	/*
	 * This type is extensible,
	 * possible extensions are below.
	 */
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} PaymentInfoType1_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_PaymentInfoType1;
extern asn_SEQUENCE_specifics_t asn_SPC_PaymentInfoType1_specs_1;
extern asn_TYPE_member_t asn_MBR_PaymentInfoType1_1[3];

#ifdef __cplusplus
}
#endif

/* Referred external types */
#include "TollingNodeInfo.h"
#include "TollInfo.h"

#endif	/* _PaymentInfoType1_H_ */
#include <asn_internal.h>
