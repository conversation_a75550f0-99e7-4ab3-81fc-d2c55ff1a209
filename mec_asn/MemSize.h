/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "OPS"
 * 	found in "./OPS.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#ifndef	_MemSize_H_
#define	_MemSize_H_


#include <asn_application.h>

/* Including external dependencies */
#include <NativeReal.h>

#ifdef __cplusplus
extern "C" {
#endif

/* MemSize */
typedef double	 MemSize_t;

/* Implementation */
extern asn_per_constraints_t asn_PER_type_MemSize_constr_1;
extern asn_TYPE_descriptor_t asn_DEF_MemSize;
asn_struct_free_f MemSize_free;
asn_struct_print_f MemSize_print;
asn_constr_check_f MemSize_constraint;
ber_type_decoder_f MemSize_decode_ber;
der_type_encoder_f MemSize_encode_der;
xer_type_decoder_f MemSize_decode_xer;
xer_type_encoder_f MemSize_encode_xer;
oer_type_decoder_f MemSize_decode_oer;
oer_type_encoder_f MemSize_encode_oer;
per_type_decoder_f MemSize_decode_uper;
per_type_encoder_f MemSize_encode_uper;

#ifdef __cplusplus
}
#endif

#endif	/* _MemSize_H_ */
#include <asn_internal.h>
