/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "OPS"
 * 	found in "./OPS.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#ifndef	_Name_H_
#define	_Name_H_


#include <asn_application.h>

/* Including external dependencies */
#include <IA5String.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Name */
typedef IA5String_t	 Name_t;

/* Implementation */
extern asn_per_constraints_t asn_PER_type_Name_constr_1;
extern asn_TYPE_descriptor_t asn_DEF_Name;
asn_struct_free_f Name_free;
asn_struct_print_f Name_print;
asn_constr_check_f Name_constraint;
ber_type_decoder_f Name_decode_ber;
der_type_encoder_f Name_encode_der;
xer_type_decoder_f Name_decode_xer;
xer_type_encoder_f Name_encode_xer;
oer_type_decoder_f Name_decode_oer;
oer_type_encoder_f Name_encode_oer;
per_type_decoder_f Name_decode_uper;
per_type_encoder_f Name_encode_uper;

#ifdef __cplusplus
}
#endif

#endif	/* _Name_H_ */
#include <asn_internal.h>
