/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "OPS"
 * 	found in "./OPS.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#ifndef	_PositionConfirm_H_
#define	_PositionConfirm_H_


#include <asn_application.h>

/* Including external dependencies */
#include "Position3D.h"
#include "PositionXYZ.h"
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* PositionConfirm */
typedef struct PositionConfirm {
	Position3D_t	 location;
	PositionXYZ_t	 xyz;
	/*
	 * This type is extensible,
	 * possible extensions are below.
	 */
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} PositionConfirm_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_PositionConfirm;
extern asn_SEQUENCE_specifics_t asn_SPC_PositionConfirm_specs_1;
extern asn_TYPE_member_t asn_MBR_PositionConfirm_1[2];

#ifdef __cplusplus
}
#endif

#endif	/* _PositionConfirm_H_ */
#include <asn_internal.h>
