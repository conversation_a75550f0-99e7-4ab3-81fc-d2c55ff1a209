/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "PAM"
 * 	found in "./PAM.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#ifndef	_ParkingLock_H_
#define	_ParkingLock_H_


#include <asn_application.h>

/* Including external dependencies */
#include <NativeEnumerated.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Dependencies */
typedef enum ParkingLock {
	ParkingLock_unknown	= 0,
	ParkingLock_nolock	= 1,
	ParkingLock_locked	= 2,
	ParkingLock_unlocked	= 3
	/*
	 * Enumeration is extensible
	 */
} e_ParkingLock;

/* ParkingLock */
typedef long	 ParkingLock_t;

/* Implementation */
extern asn_per_constraints_t asn_PER_type_ParkingLock_constr_1;
extern asn_TYPE_descriptor_t asn_DEF_ParkingLock;
extern const asn_INTEGER_specifics_t asn_SPC_ParkingLock_specs_1;
asn_struct_free_f ParkingLock_free;
asn_struct_print_f ParkingLock_print;
asn_constr_check_f ParkingLock_constraint;
ber_type_decoder_f ParkingLock_decode_ber;
der_type_encoder_f ParkingLock_encode_der;
xer_type_decoder_f ParkingLock_decode_xer;
xer_type_encoder_f ParkingLock_encode_xer;
oer_type_decoder_f ParkingLock_decode_oer;
oer_type_encoder_f ParkingLock_encode_oer;
per_type_decoder_f ParkingLock_decode_uper;
per_type_encoder_f ParkingLock_encode_uper;

#ifdef __cplusplus
}
#endif

#endif	/* _ParkingLock_H_ */
#include <asn_internal.h>
