/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "OPS"
 * 	found in "./OPS.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#ifndef	_DeviceInfo_H_
#define	_DeviceInfo_H_


#include <asn_application.h>

/* Including external dependencies */
#include <OCTET_STRING.h>
#include "DeviceType.h"
#include "Esn.h"
#include <IA5String.h>
#include "Position3D.h"
#include "Version.h"
#include "NetworkList.h"
#include "V2XStatus.h"
#include "IREList.h"
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* DeviceInfo */
typedef struct DeviceInfo {
	OCTET_STRING_t	 id;
	DeviceType_t	 type;
	Esn_t	 esn;
	IA5String_t	 model;
	Position3D_t	 location;
	Version_t	 softVersion;
	Version_t	 hardVersion;
	Version_t	 protocolVersion;
	NetworkList_t	 networks;
	V2XStatus_t	 appStatus;
	IREList_t	 ires;
	/*
	 * This type is extensible,
	 * possible extensions are below.
	 */
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} DeviceInfo_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_DeviceInfo;
extern asn_SEQUENCE_specifics_t asn_SPC_DeviceInfo_specs_1;
extern asn_TYPE_member_t asn_MBR_DeviceInfo_1[11];

#ifdef __cplusplus
}
#endif

#endif	/* _DeviceInfo_H_ */
#include <asn_internal.h>
