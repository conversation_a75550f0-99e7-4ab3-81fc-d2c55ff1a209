/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "Map"
 * 	found in "./Map.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#include "ConnectingLaneEx.h"

asn_TYPE_member_t asn_MBR_ConnectingLaneEx_1[] = {
	{ ATF_NOFLAGS, 0, offsetof(struct ConnectingLaneEx, target_section),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_SectionId,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"target-section"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct ConnectingLaneEx, target_lane),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_LaneRefID,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"target-lane"
		},
	{ ATF_POINTER, 3, offsetof(struct ConnectingLaneEx, connectingLaneWidth),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_LaneWidth,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"connectingLaneWidth"
		},
	{ ATF_POINTER, 2, offsetof(struct ConnectingLaneEx, connectingLanePoints),
		(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_PointList,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"connectingLanePoints"
		},
	{ ATF_POINTER, 1, offsetof(struct ConnectingLaneEx, isolatedConnectingLane),
		(ASN_TAG_CLASS_CONTEXT | (4 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_BOOLEAN,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"isolatedConnectingLane"
		},
};
static const int asn_MAP_ConnectingLaneEx_oms_1[] = { 2, 3, 4 };
static const ber_tlv_tag_t asn_DEF_ConnectingLaneEx_tags_1[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_ConnectingLaneEx_tag2el_1[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* target-section */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* target-lane */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 }, /* connectingLaneWidth */
    { (ASN_TAG_CLASS_CONTEXT | (3 << 2)), 3, 0, 0 }, /* connectingLanePoints */
    { (ASN_TAG_CLASS_CONTEXT | (4 << 2)), 4, 0, 0 } /* isolatedConnectingLane */
};
asn_SEQUENCE_specifics_t asn_SPC_ConnectingLaneEx_specs_1 = {
	sizeof(struct ConnectingLaneEx),
	offsetof(struct ConnectingLaneEx, _asn_ctx),
	asn_MAP_ConnectingLaneEx_tag2el_1,
	5,	/* Count of tags in the map */
	asn_MAP_ConnectingLaneEx_oms_1,	/* Optional members */
	3, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
asn_TYPE_descriptor_t asn_DEF_ConnectingLaneEx = {
	"ConnectingLaneEx",
	"ConnectingLaneEx",
	&asn_OP_SEQUENCE,
	asn_DEF_ConnectingLaneEx_tags_1,
	sizeof(asn_DEF_ConnectingLaneEx_tags_1)
		/sizeof(asn_DEF_ConnectingLaneEx_tags_1[0]), /* 1 */
	asn_DEF_ConnectingLaneEx_tags_1,	/* Same as above */
	sizeof(asn_DEF_ConnectingLaneEx_tags_1)
		/sizeof(asn_DEF_ConnectingLaneEx_tags_1[0]), /* 1 */
	{ 0, 0, SEQUENCE_constraint },
	asn_MBR_ConnectingLaneEx_1,
	5,	/* Elements count */
	&asn_SPC_ConnectingLaneEx_specs_1	/* Additional specs */
};

