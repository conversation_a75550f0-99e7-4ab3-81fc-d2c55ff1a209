/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "OPS"
 * 	found in "./OPS.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#ifndef	_NTP_H_
#define	_NTP_H_


#include <asn_application.h>

/* Including external dependencies */
#include "Name.h"
#include "IP.h"
#include <NativeInteger.h>
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* NTP */
typedef struct NTP {
	Name_t	 domain;
	IP_t	 adress;
	long	 port;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} NTP_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_NTP;
extern asn_SEQUENCE_specifics_t asn_SPC_NTP_specs_1;
extern asn_TYPE_member_t asn_MBR_NTP_1[3];

#ifdef __cplusplus
}
#endif

#endif	/* _NTP_H_ */
#include <asn_internal.h>
