/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "SensorSharing"
 * 	found in "./SensorSharing.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#ifndef	_ObjectSizeConfidence_H_
#define	_ObjectSizeConfidence_H_


#include <asn_application.h>

/* Including external dependencies */
#include "SizeValueConfidence.h"
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* ObjectSizeConfidence */
typedef struct ObjectSizeConfidence {
	SizeValueConfidence_t	 widthConf;
	SizeValueConfidence_t	 lengthConf;
	SizeValueConfidence_t	*heightConf	/* OPTIONAL */;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} ObjectSizeConfidence_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_ObjectSizeConfidence;
extern asn_SEQUENCE_specifics_t asn_SPC_ObjectSizeConfidence_specs_1;
extern asn_TYPE_member_t asn_MBR_ObjectSizeConfidence_1[3];

#ifdef __cplusplus
}
#endif

#endif	/* _ObjectSizeConfidence_H_ */
#include <asn_internal.h>
