/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "VPM"
 * 	found in "./VPM.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#include "PassedSitesInfo.h"

static int
memb_passedPos_constraint_1(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	size_t size;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	/* Determine the number of elements */
	size = _A_CSEQUENCE_FROM_VOID(sptr)->count;
	
	if((size >= 2 && size <= 512)) {
		/* Perform validation of the inner elements */
		return SEQUENCE_OF_constraint(td, sptr, ctfailcb, app_key);
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static asn_oer_constraints_t asn_OER_type_passedPos_constr_3 CC_NOTUSED = {
	{ 0, 0 },
	-1	/* (SIZE(2..512)) */};
static asn_per_constraints_t asn_PER_type_passedPos_constr_3 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 9,  9,  2,  512 }	/* (SIZE(2..512)) */,
	0, 0	/* No PER value map */
};
static asn_oer_constraints_t asn_OER_memb_passedPos_constr_3 CC_NOTUSED = {
	{ 0, 0 },
	-1	/* (SIZE(2..512)) */};
static asn_per_constraints_t asn_PER_memb_passedPos_constr_3 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 9,  9,  2,  512 }	/* (SIZE(2..512)) */,
	0, 0	/* No PER value map */
};
static asn_TYPE_member_t asn_MBR_passedPos_3[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_PassedPos,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_passedPos_tags_3[] = {
	(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static asn_SET_OF_specifics_t asn_SPC_passedPos_specs_3 = {
	sizeof(struct passedPos),
	offsetof(struct passedPos, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_passedPos_3 = {
	"passedPos",
	"passedPos",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_passedPos_tags_3,
	sizeof(asn_DEF_passedPos_tags_3)
		/sizeof(asn_DEF_passedPos_tags_3[0]) - 1, /* 1 */
	asn_DEF_passedPos_tags_3,	/* Same as above */
	sizeof(asn_DEF_passedPos_tags_3)
		/sizeof(asn_DEF_passedPos_tags_3[0]), /* 2 */
	{ &asn_OER_type_passedPos_constr_3, &asn_PER_type_passedPos_constr_3, SEQUENCE_OF_constraint },
	asn_MBR_passedPos_3,
	1,	/* Single element */
	&asn_SPC_passedPos_specs_3	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_PassedSitesInfo_1[] = {
	{ ATF_POINTER, 2, offsetof(struct PassedSitesInfo, entranceInfo),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_PassedPos,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"entranceInfo"
		},
	{ ATF_POINTER, 1, offsetof(struct PassedSitesInfo, passedPos),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		0,
		&asn_DEF_passedPos_3,
		0,
		{ &asn_OER_memb_passedPos_constr_3, &asn_PER_memb_passedPos_constr_3,  memb_passedPos_constraint_1 },
		0, 0, /* No default value */
		"passedPos"
		},
};
static const int asn_MAP_PassedSitesInfo_oms_1[] = { 0, 1 };
static const ber_tlv_tag_t asn_DEF_PassedSitesInfo_tags_1[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_PassedSitesInfo_tag2el_1[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* entranceInfo */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 } /* passedPos */
};
asn_SEQUENCE_specifics_t asn_SPC_PassedSitesInfo_specs_1 = {
	sizeof(struct PassedSitesInfo),
	offsetof(struct PassedSitesInfo, _asn_ctx),
	asn_MAP_PassedSitesInfo_tag2el_1,
	2,	/* Count of tags in the map */
	asn_MAP_PassedSitesInfo_oms_1,	/* Optional members */
	2, 0,	/* Root/Additions */
	2,	/* First extension addition */
};
asn_TYPE_descriptor_t asn_DEF_PassedSitesInfo = {
	"PassedSitesInfo",
	"PassedSitesInfo",
	&asn_OP_SEQUENCE,
	asn_DEF_PassedSitesInfo_tags_1,
	sizeof(asn_DEF_PassedSitesInfo_tags_1)
		/sizeof(asn_DEF_PassedSitesInfo_tags_1[0]), /* 1 */
	asn_DEF_PassedSitesInfo_tags_1,	/* Same as above */
	sizeof(asn_DEF_PassedSitesInfo_tags_1)
		/sizeof(asn_DEF_PassedSitesInfo_tags_1[0]), /* 1 */
	{ 0, 0, SEQUENCE_constraint },
	asn_MBR_PassedSitesInfo_1,
	2,	/* Elements count */
	&asn_SPC_PassedSitesInfo_specs_1	/* Additional specs */
};

