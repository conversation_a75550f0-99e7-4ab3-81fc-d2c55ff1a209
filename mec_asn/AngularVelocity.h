/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "SensorSharing"
 * 	found in "./SensorSharing.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#ifndef	_AngularVelocity_H_
#define	_AngularVelocity_H_


#include <asn_application.h>

/* Including external dependencies */
#include "PitchRate.h"
#include "RollRate.h"
#include "YawRate.h"
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* AngularVelocity */
typedef struct AngularVelocity {
	PitchRate_t	 pitchRate;
	RollRate_t	 rollRate;
	YawRate_t	 yawRate;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} AngularVelocity_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_AngularVelocity;
extern asn_SEQUENCE_specifics_t asn_SPC_AngularVelocity_specs_1;
extern asn_TYPE_member_t asn_MBR_AngularVelocity_1[3];

#ifdef __cplusplus
}
#endif

#endif	/* _AngularVelocity_H_ */
#include <asn_internal.h>
