/**
 * Creator: ASNDT (http://www.asnlab.org)
 * Author: WA<PERSON><PERSON><PERSON>hi
 * Created: Mon Jul 25 10:48:37 CST 2016
 */
DefPosition DEFINITIONS AUTOMATIC TAGS ::= BEGIN

-- imports and exports

EXPORTS Latitude, Longitude, Elevation, PositionConfidenceSet, Position3D, PositionalAccuracy;
IMPORTS ;
	
	Latitude ::= INTEGER (-900000000..900000001) 
	-- Units of 1/10 micro degree 
	-- Providing a range of plus-minus 90 degrees
	
	Longitude ::= INTEGER (-1799999999..1800000001) 
	-- Units of 1/10 micro degree 
	-- Providing a range of plus-minus 180 degrees
	
	Elevation ::= INTEGER (-4096..61439)
	-- Units of 10 cm steps above or below the reference ellipsoid
	-- Providing a range of -409.5 to + 6143.9 meters
	-- The value -4096 shall be used when Unknown is to be sent
	
	PositionConfidenceSet ::= SEQUENCE {
		pos PositionConfidence, -- for both horizontal directions
		elevation ElevationConfidence OPTIONAL
	}
	
	PositionConfidence ::= ENUMERATED {
		unavailable (0), -- Not Equipped or unavailable
		a500m (1), -- 500m or about 5 * 10 ^ -3 decimal degrees
		a200m (2), -- 200m or about 2 * 10 ^ -3 decimal degrees
		a100m (3), -- 100m or about 1 * 10 ^ -3 decimal degrees
		a50m (4), -- 50m or about 5 * 10 ^ -4 decimal degrees
		a20m (5), -- 20m or about 2 * 10 ^ -4 decimal degrees
		a10m (6), -- 10m or about 1 * 10 ^ -4 decimal degrees
		a5m (7), -- 5m or about 5 * 10 ^ -5 decimal degrees
		a2m (8), -- 2m or about 2 * 10 ^ -5 decimal degrees
		a1m (9), -- 1m or about 1 * 10 ^ -5 decimal degrees
		a50cm (10), -- 0.50m or about 5 * 10 ^ -6 decimal degrees
		a20cm (11), -- 0.20m or about 2 * 10 ^ -6 decimal degrees
		a10cm (12), -- 0.10m or about 1 * 10 ^ -6 decimal degrees
		a5cm (13), -- 0.05m or about 5 * 10 ^ -7 decimal degrees
		a2cm (14), -- 0.02m or about 2 * 10 ^ -7 decimal degrees
		a1cm (15) -- 0.01m or about 1 * 10 ^ -7 decimal degrees
	}
	
	ElevationConfidence ::= ENUMERATED {
		unavailable (0), -- Not Equipped or unavailable
		elev-500-00 (1), -- (500 m)
		elev-200-00 (2), -- (200 m)
		elev-100-00 (3), -- (100 m)
		elev-050-00 (4), -- (50 m)
		elev-020-00 (5), -- (20 m)
		elev-010-00 (6), -- (10 m)
		elev-005-00 (7), -- (5 m)
		elev-002-00 (8), -- B(2 m)
		elev-001-00 (9), -- (1 m)
		elev-000-50 (10), -- (50 cm)
		elev-000-20 (11), -- (20 cm)
		elev-000-10 (12), -- (10 cm)
		elev-000-05 (13), -- (5 cm)
		elev-000-02 (14), -- B(2 cm)
		elev-000-01 (15) -- (1 cm)
	}
	
	Position3D ::= SEQUENCE {
		lat Latitude, 
		-- in 1/10th micro degrees
		long Longitude, 
		-- in 1/10th micro degrees
		elevation Elevation OPTIONAL
		-- in 10 cm units
	}
	
	SemiMajorAxisAccuracy ::= INTEGER (0..255)
	-- semi-major axis accuracy at one standard dev
	-- range 0-12.7 meter, LSB = .05m
	-- 254 = any value equal or greater than 12.70 meter
	-- 255 = unavailable semi-major axis value
	
	SemiMinorAxisAccuracy ::= INTEGER (0..255)
	-- semi-minor axis accuracy at one standard dev
	-- range 0-12.7 meter, LSB = .05m
	-- 254 = any value equal or greater than 12.70 meter
	-- 255 = unavailable semi-minor axis value
	
	SemiMajorAxisOrientation ::= INTEGER (0..65535)
	-- orientation of semi-major axis
	-- relative to true north (0~359.9945078786 degrees)
	-- Units of 360/65535 deg = 0.0054932479
	-- a value of 0 shall be 0 degrees
	-- a value of 1 shall be 0.0054932479 degrees
	-- a value of 65534 shall be 359.9945078786 deg
	-- a value of 65535 shall be used for orientation unavailable
	
	PositionalAccuracy ::= SEQUENCE {
		-- NMEA-183 values expressed in strict ASN form
		semiMajor SemiMajorAxisAccuracy,
		semiMinor SemiMinorAxisAccuracy,
		orientation SemiMajorAxisOrientation
	}
END
