/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "OPS"
 * 	found in "./OPS.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#ifndef	_IP_H_
#define	_IP_H_


#include <asn_application.h>

/* Including external dependencies */
#include <OCTET_STRING.h>

#ifdef __cplusplus
extern "C" {
#endif

/* IP */
typedef OCTET_STRING_t	 IP_t;

/* Implementation */
extern asn_per_constraints_t asn_PER_type_IP_constr_1;
extern asn_TYPE_descriptor_t asn_DEF_IP;
asn_struct_free_f IP_free;
asn_struct_print_f IP_print;
asn_constr_check_f IP_constraint;
ber_type_decoder_f IP_decode_ber;
der_type_encoder_f IP_encode_der;
xer_type_decoder_f IP_decode_xer;
xer_type_encoder_f IP_encode_xer;
oer_type_decoder_f IP_decode_oer;
oer_type_encoder_f IP_encode_oer;
per_type_decoder_f IP_decode_uper;
per_type_encoder_f IP_encode_uper;

#ifdef __cplusplus
}
#endif

#endif	/* _IP_H_ */
#include <asn_internal.h>
