/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "VPM"
 * 	found in "./VPM.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#ifndef	_PaymentEntityID_H_
#define	_PaymentEntityID_H_


#include <asn_application.h>

/* Including external dependencies */
#include <NativeEnumerated.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Dependencies */
typedef enum PaymentEntityID {
	PaymentEntityID_system	= 0,
	PaymentEntityID_freeflow	= 1,
	PaymentEntityID_enclosed	= 2,
	PaymentEntityID_congestion	= 3,
	PaymentEntityID_charging	= 4,
	PaymentEntityID_parking	= 5
	/*
	 * Enumeration is extensible
	 */
} e_PaymentEntityID;

/* PaymentEntityID */
typedef long	 PaymentEntityID_t;

/* Implementation */
extern asn_per_constraints_t asn_PER_type_PaymentEntityID_constr_1;
extern asn_TYPE_descriptor_t asn_DEF_PaymentEntityID;
extern const asn_INTEGER_specifics_t asn_SPC_PaymentEntityID_specs_1;
asn_struct_free_f PaymentEntityID_free;
asn_struct_print_f PaymentEntityID_print;
asn_constr_check_f PaymentEntityID_constraint;
ber_type_decoder_f PaymentEntityID_decode_ber;
der_type_encoder_f PaymentEntityID_encode_der;
xer_type_decoder_f PaymentEntityID_decode_xer;
xer_type_encoder_f PaymentEntityID_encode_xer;
oer_type_decoder_f PaymentEntityID_decode_oer;
oer_type_encoder_f PaymentEntityID_encode_oer;
per_type_decoder_f PaymentEntityID_decode_uper;
per_type_encoder_f PaymentEntityID_encode_uper;

#ifdef __cplusplus
}
#endif

#endif	/* _PaymentEntityID_H_ */
#include <asn_internal.h>
