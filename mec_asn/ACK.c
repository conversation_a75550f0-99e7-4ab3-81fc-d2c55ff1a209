/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "GoEMEC"
 * 	found in "./GoEMEC.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#include "ACK.h"

asn_TYPE_member_t asn_MBR_ACK_1[] = {
	{ ATF_NOFLAGS, 0, offsetof(struct ACK, errorCode),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_ErrorCode,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"errorCode"
		},
};
static const ber_tlv_tag_t asn_DEF_ACK_tags_1[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_ACK_tag2el_1[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 } /* errorCode */
};
asn_SEQUENCE_specifics_t asn_SPC_ACK_specs_1 = {
	sizeof(struct ACK),
	offsetof(struct ACK, _asn_ctx),
	asn_MAP_ACK_tag2el_1,
	1,	/* Count of tags in the map */
	0, 0, 0,	/* Optional elements (not needed) */
	-1,	/* First extension addition */
};
asn_TYPE_descriptor_t asn_DEF_ACK = {
	"ACK",
	"ACK",
	&asn_OP_SEQUENCE,
	asn_DEF_ACK_tags_1,
	sizeof(asn_DEF_ACK_tags_1)
		/sizeof(asn_DEF_ACK_tags_1[0]), /* 1 */
	asn_DEF_ACK_tags_1,	/* Same as above */
	sizeof(asn_DEF_ACK_tags_1)
		/sizeof(asn_DEF_ACK_tags_1[0]), /* 1 */
	{ 0, 0, SEQUENCE_constraint },
	asn_MBR_ACK_1,
	1,	/* Elements count */
	&asn_SPC_ACK_specs_1	/* Additional specs */
};

