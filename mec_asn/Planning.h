/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "SensorSharing"
 * 	found in "./SensorSharing.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#ifndef	_Planning_H_
#define	_Planning_H_


#include <asn_application.h>

/* Including external dependencies */
#include "PlanningDuration.h"
#include "Confidence.h"
#include "DriveBehavior.h"
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Forward declarations */
struct PathPlanning;

/* Planning */
typedef struct Planning {
	PlanningDuration_t	*duration	/* OPTIONAL */;
	Confidence_t	*planConfidence	/* OPTIONAL */;
	DriveBehavior_t	*drivingBehavior	/* OPTIONAL */;
	struct PathPlanning	*pathPlanning	/* OPTIONAL */;
	/*
	 * This type is extensible,
	 * possible extensions are below.
	 */
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} Planning_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_Planning;
extern asn_SEQUENCE_specifics_t asn_SPC_Planning_specs_1;
extern asn_TYPE_member_t asn_MBR_Planning_1[4];

#ifdef __cplusplus
}
#endif

/* Referred external types */
#include "PathPlanning.h"

#endif	/* _Planning_H_ */
#include <asn_internal.h>
