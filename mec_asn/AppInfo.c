/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "OPS"
 * 	found in "./OPS.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#include "AppInfo.h"

asn_TYPE_member_t asn_MBR_AppInfo_1[] = {
	{ ATF_NOFLAGS, 0, offsetof(struct AppInfo, appType),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_AppType,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"appType"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct AppInfo, appName),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_TextUTF8,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"appName"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct AppInfo, appStatus),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_Status,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"appStatus"
		},
};
static const ber_tlv_tag_t asn_DEF_AppInfo_tags_1[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_AppInfo_tag2el_1[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* appType */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* appName */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 } /* appStatus */
};
asn_SEQUENCE_specifics_t asn_SPC_AppInfo_specs_1 = {
	sizeof(struct AppInfo),
	offsetof(struct AppInfo, _asn_ctx),
	asn_MAP_AppInfo_tag2el_1,
	3,	/* Count of tags in the map */
	0, 0, 0,	/* Optional elements (not needed) */
	3,	/* First extension addition */
};
asn_TYPE_descriptor_t asn_DEF_AppInfo = {
	"AppInfo",
	"AppInfo",
	&asn_OP_SEQUENCE,
	asn_DEF_AppInfo_tags_1,
	sizeof(asn_DEF_AppInfo_tags_1)
		/sizeof(asn_DEF_AppInfo_tags_1[0]), /* 1 */
	asn_DEF_AppInfo_tags_1,	/* Same as above */
	sizeof(asn_DEF_AppInfo_tags_1)
		/sizeof(asn_DEF_AppInfo_tags_1[0]), /* 1 */
	{ 0, 0, SEQUENCE_constraint },
	asn_MBR_AppInfo_1,
	3,	/* Elements count */
	&asn_SPC_AppInfo_specs_1	/* Additional specs */
};

