/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "OPS"
 * 	found in "./OPS.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#ifndef	_AlarmInfo_H_
#define	_AlarmInfo_H_


#include <asn_application.h>

/* Including external dependencies */
#include "CpuInfo.h"
#include "MemInfo.h"
#include "DiskInfo.h"
#include "NetInfo.h"
#include "IREList.h"
#include "ThreadList.h"
#include "AppInfo.h"
#include <constr_CHOICE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Dependencies */
typedef enum AlarmInfo_PR {
	AlarmInfo_PR_NOTHING,	/* No components present */
	AlarmInfo_PR_cpuinfo,
	AlarmInfo_PR_meminfo,
	AlarmInfo_PR_diskInfo,
	AlarmInfo_PR_netInfo,
	AlarmInfo_PR_ireInfos,
	AlarmInfo_PR_threadInfos,
	AlarmInfo_PR_appInfo
	/* Extensions may appear below */
	
} AlarmInfo_PR;

/* AlarmInfo */
typedef struct AlarmInfo {
	AlarmInfo_PR present;
	union AlarmInfo_u {
		CpuInfo_t	 cpuinfo;
		MemInfo_t	 meminfo;
		DiskInfo_t	 diskInfo;
		NetInfo_t	 netInfo;
		IREList_t	 ireInfos;
		ThreadList_t	 threadInfos;
		AppInfo_t	 appInfo;
		/*
		 * This type is extensible,
		 * possible extensions are below.
		 */
	} choice;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} AlarmInfo_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_AlarmInfo;
extern asn_CHOICE_specifics_t asn_SPC_AlarmInfo_specs_1;
extern asn_TYPE_member_t asn_MBR_AlarmInfo_1[7];
extern asn_per_constraints_t asn_PER_type_AlarmInfo_constr_1;

#ifdef __cplusplus
}
#endif

#endif	/* _AlarmInfo_H_ */
#include <asn_internal.h>
