/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "SensorSharing"
 * 	found in "./SensorSharing.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#ifndef	_MotorDataExtension_H_
#define	_MotorDataExtension_H_


#include <asn_application.h>

/* Including external dependencies */
#include "ExteriorLights.h"
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Forward declarations */
struct Attitude;
struct AttitudeConfidence;
struct AngularVelocity;
struct AngularVelocityConfidence;

/* MotorDataExtension */
typedef struct MotorDataExtension {
	ExteriorLights_t	*lights	/* OPTIONAL */;
	struct Attitude	*vehAttitude	/* OPTIONAL */;
	struct AttitudeConfidence	*vehAttitudeConfidence	/* OPTIONAL */;
	struct AngularVelocity	*vehAngVel	/* OPTIONAL */;
	struct AngularVelocityConfidence	*vehAngVelConfidence	/* OPTIONAL */;
	/*
	 * This type is extensible,
	 * possible extensions are below.
	 */
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} MotorDataExtension_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_MotorDataExtension;
extern asn_SEQUENCE_specifics_t asn_SPC_MotorDataExtension_specs_1;
extern asn_TYPE_member_t asn_MBR_MotorDataExtension_1[5];

#ifdef __cplusplus
}
#endif

/* Referred external types */
#include "Attitude.h"
#include "AttitudeConfidence.h"
#include "AngularVelocity.h"
#include "AngularVelocityConfidence.h"

#endif	/* _MotorDataExtension_H_ */
#include <asn_internal.h>
