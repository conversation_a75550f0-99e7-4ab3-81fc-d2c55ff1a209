/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "GoEMEC"
 * 	found in "./GoEMEC.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#ifndef	_ErrorCode_H_
#define	_ErrorCode_H_


#include <asn_application.h>

/* Including external dependencies */
#include <NativeEnumerated.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Dependencies */
typedef enum ErrorCode {
	ErrorCode_noError	= 0,
	ErrorCode_paramError	= 1,
	ErrorCode_systemError	= 2,
	ErrorCode_crcError	= 3,
	ErrorCode_updateTypeInvalid	= 4,
	ErrorCode_updateSizeInvalid	= 5,
	ErrorCode_updateMagicErr	= 6,
	ErrorCode_updateFileNull	= 7
	/*
	 * Enumeration is extensible
	 */
} e_ErrorCode;

/* ErrorCode */
typedef long	 ErrorCode_t;

/* Implementation */
extern asn_per_constraints_t asn_PER_type_ErrorCode_constr_1;
extern asn_TYPE_descriptor_t asn_DEF_ErrorCode;
extern const asn_INTEGER_specifics_t asn_SPC_ErrorCode_specs_1;
asn_struct_free_f ErrorCode_free;
asn_struct_print_f ErrorCode_print;
asn_constr_check_f ErrorCode_constraint;
ber_type_decoder_f ErrorCode_decode_ber;
der_type_encoder_f ErrorCode_encode_der;
xer_type_decoder_f ErrorCode_decode_xer;
xer_type_encoder_f ErrorCode_encode_xer;
oer_type_decoder_f ErrorCode_decode_oer;
oer_type_encoder_f ErrorCode_encode_oer;
per_type_decoder_f ErrorCode_decode_uper;
per_type_encoder_f ErrorCode_encode_uper;

#ifdef __cplusplus
}
#endif

#endif	/* _ErrorCode_H_ */
#include <asn_internal.h>
