/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "VPM"
 * 	found in "./VPM.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#ifndef	_GetSecureRq_H_
#define	_GetSecureRq_H_


#include <asn_application.h>

/* Including external dependencies */
#include "RangeOfFile.h"
#include "RandStr8.h"
#include <NativeInteger.h>
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* GetSecureRq */
typedef struct GetSecureRq {
	RangeOfFile_t	 vehicleInfo;
	RandStr8_t	 rndRsuForAuthen;
	long	 keyIdForAuthen;
	long	*keyIdForEncrypt	/* OPTIONAL */;
	/*
	 * This type is extensible,
	 * possible extensions are below.
	 */
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} GetSecureRq_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_GetSecureRq;
extern asn_SEQUENCE_specifics_t asn_SPC_GetSecureRq_specs_1;
extern asn_TYPE_member_t asn_MBR_GetSecureRq_1[4];

#ifdef __cplusplus
}
#endif

#endif	/* _GetSecureRq_H_ */
#include <asn_internal.h>
