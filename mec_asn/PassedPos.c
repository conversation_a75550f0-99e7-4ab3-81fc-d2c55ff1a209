/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "VPM"
 * 	found in "./VPM.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#include "PassedPos.h"

static int
memb_tollingAmount_constraint_1(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	const OCTET_STRING_t *st = (const OCTET_STRING_t *)sptr;
	size_t size;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	size = st->size;
	
	if((size == 4)) {
		/* Constraint check succeeded */
		return 0;
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static asn_oer_constraints_t asn_OER_memb_tollingAmount_constr_4 CC_NOTUSED = {
	{ 0, 0 },
	4	/* (SIZE(4..4)) */};
static asn_per_constraints_t asn_PER_memb_tollingAmount_constr_4 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 0,  0,  4,  4 }	/* (SIZE(4..4)) */,
	0, 0	/* No PER value map */
};
asn_TYPE_member_t asn_MBR_PassedPos_1[] = {
	{ ATF_NOFLAGS, 0, offsetof(struct PassedPos, tollingPos),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_TollingPos,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"tollingPos"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct PassedPos, tollingTime),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_DDateTime,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"tollingTime"
		},
	{ ATF_POINTER, 1, offsetof(struct PassedPos, tollingAmount),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_OCTET_STRING,
		0,
		{ &asn_OER_memb_tollingAmount_constr_4, &asn_PER_memb_tollingAmount_constr_4,  memb_tollingAmount_constraint_1 },
		0, 0, /* No default value */
		"tollingAmount"
		},
};
static const int asn_MAP_PassedPos_oms_1[] = { 2 };
static const ber_tlv_tag_t asn_DEF_PassedPos_tags_1[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_PassedPos_tag2el_1[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* tollingPos */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* tollingTime */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 } /* tollingAmount */
};
asn_SEQUENCE_specifics_t asn_SPC_PassedPos_specs_1 = {
	sizeof(struct PassedPos),
	offsetof(struct PassedPos, _asn_ctx),
	asn_MAP_PassedPos_tag2el_1,
	3,	/* Count of tags in the map */
	asn_MAP_PassedPos_oms_1,	/* Optional members */
	1, 0,	/* Root/Additions */
	3,	/* First extension addition */
};
asn_TYPE_descriptor_t asn_DEF_PassedPos = {
	"PassedPos",
	"PassedPos",
	&asn_OP_SEQUENCE,
	asn_DEF_PassedPos_tags_1,
	sizeof(asn_DEF_PassedPos_tags_1)
		/sizeof(asn_DEF_PassedPos_tags_1[0]), /* 1 */
	asn_DEF_PassedPos_tags_1,	/* Same as above */
	sizeof(asn_DEF_PassedPos_tags_1)
		/sizeof(asn_DEF_PassedPos_tags_1[0]), /* 1 */
	{ 0, 0, SEQUENCE_constraint },
	asn_MBR_PassedPos_1,
	3,	/* Elements count */
	&asn_SPC_PassedPos_specs_1	/* Additional specs */
};

