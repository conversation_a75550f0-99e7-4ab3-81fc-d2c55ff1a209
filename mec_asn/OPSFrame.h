/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "OPS"
 * 	found in "./OPS.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#ifndef	_OPSFrame_H_
#define	_OPSFrame_H_


#include <asn_application.h>

/* Including external dependencies */
#include "Command.h"
#include "DeviceInfo.h"
#include "Network.h"
#include "NTP.h"
#include "SystemStatus.h"
#include "FileTransfer.h"
#include "Alarm.h"
#include "V2XStatus.h"
#include "IREList.h"
#include "AIParamList.h"
#include "EnvParamInfo.h"
#include "ReferenceList.h"
#include "PositionCalStart.h"
#include "PositionConfirm.h"
#include "UwbRangingContrl.h"
#include "PositionSet.h"
#include "DeviceIdSet.h"
#include <constr_CHOICE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Dependencies */
typedef enum OPSFrame_PR {
	OPSFrame_PR_NOTHING,	/* No components present */
	OPSFrame_PR_commandFrame,
	OPSFrame_PR_infoFrame,
	OPSFrame_PR_networkFrame,
	OPSFrame_PR_ntpFrame,
	OPSFrame_PR_systemStatus,
	OPSFrame_PR_fileTransfer,
	OPSFrame_PR_alarmFrame,
	OPSFrame_PR_v2xStatus,
	OPSFrame_PR_ires,
	OPSFrame_PR_aiParams,
	OPSFrame_PR_envParams,
	OPSFrame_PR_referenceList,
	OPSFrame_PR_positionCalStart,
	OPSFrame_PR_positionConfirm,
	OPSFrame_PR_uwbRangingContrl,
	OPSFrame_PR_positionSet,
	OPSFrame_PR_deviceIdSet
	/* Extensions may appear below */
	
} OPSFrame_PR;

/* OPSFrame */
typedef struct OPSFrame {
	OPSFrame_PR present;
	union OPSFrame_u {
		Command_t	 commandFrame;
		DeviceInfo_t	 infoFrame;
		Network_t	 networkFrame;
		NTP_t	 ntpFrame;
		SystemStatus_t	 systemStatus;
		FileTransfer_t	 fileTransfer;
		Alarm_t	 alarmFrame;
		V2XStatus_t	 v2xStatus;
		IREList_t	 ires;
		AIParamList_t	 aiParams;
		EnvParamInfo_t	 envParams;
		ReferenceList_t	 referenceList;
		PositionCalStart_t	 positionCalStart;
		PositionConfirm_t	 positionConfirm;
		UwbRangingContrl_t	 uwbRangingContrl;
		PositionSet_t	 positionSet;
		DeviceIdSet_t	 deviceIdSet;
		/*
		 * This type is extensible,
		 * possible extensions are below.
		 */
	} choice;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} OPSFrame_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_OPSFrame;
extern asn_CHOICE_specifics_t asn_SPC_OPSFrame_specs_1;
extern asn_TYPE_member_t asn_MBR_OPSFrame_1[17];
extern asn_per_constraints_t asn_PER_type_OPSFrame_constr_1;

#ifdef __cplusplus
}
#endif

#endif	/* _OPSFrame_H_ */
#include <asn_internal.h>
