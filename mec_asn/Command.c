/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "OPS"
 * 	found in "./OPS.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#include "Command.h"

/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
static asn_oer_constraints_t asn_OER_type_Command_constr_1 CC_NOTUSED = {
	{ 0, 0 },
	-1};
asn_per_constraints_t asn_PER_type_Command_constr_1 CC_NOTUSED = {
	{ APC_CONSTRAINED | APC_EXTENSIBLE,  4,  4,  0,  9 }	/* (0..9,...) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
static const asn_INTEGER_enum_map_t asn_MAP_Command_value2enum_1[] = {
	{ 0,	13,	"getDeviceInfo" },
	{ 1,	6,	"reboot" },
	{ 2,	15,	"getSystemStatus" },
	{ 3,	12,	"getV2XStatus" },
	{ 4,	6,	"update" },
	{ 5,	10,	"getIREList" },
	{ 6,	6,	"getNTP" },
	{ 7,	14,	"getAIParamInfo" },
	{ 8,	11,	"getEnvParam" },
	{ 9,	16,	"getReferenceList" }
	/* This list is extensible */
};
static const unsigned int asn_MAP_Command_enum2value_1[] = {
	7,	/* getAIParamInfo(7) */
	0,	/* getDeviceInfo(0) */
	8,	/* getEnvParam(8) */
	5,	/* getIREList(5) */
	6,	/* getNTP(6) */
	9,	/* getReferenceList(9) */
	2,	/* getSystemStatus(2) */
	3,	/* getV2XStatus(3) */
	1,	/* reboot(1) */
	4	/* update(4) */
	/* This list is extensible */
};
const asn_INTEGER_specifics_t asn_SPC_Command_specs_1 = {
	asn_MAP_Command_value2enum_1,	/* "tag" => N; sorted by tag */
	asn_MAP_Command_enum2value_1,	/* N => "tag"; sorted by N */
	10,	/* Number of elements in the maps */
	11,	/* Extensions before this member */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_Command_tags_1[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
asn_TYPE_descriptor_t asn_DEF_Command = {
	"Command",
	"Command",
	&asn_OP_NativeEnumerated,
	asn_DEF_Command_tags_1,
	sizeof(asn_DEF_Command_tags_1)
		/sizeof(asn_DEF_Command_tags_1[0]), /* 1 */
	asn_DEF_Command_tags_1,	/* Same as above */
	sizeof(asn_DEF_Command_tags_1)
		/sizeof(asn_DEF_Command_tags_1[0]), /* 1 */
	{ &asn_OER_type_Command_constr_1, &asn_PER_type_Command_constr_1, NativeEnumerated_constraint },
	0, 0,	/* Defined elsewhere */
	&asn_SPC_Command_specs_1	/* Additional specs */
};

