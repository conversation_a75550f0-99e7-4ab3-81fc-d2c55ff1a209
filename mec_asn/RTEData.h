/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "RSI"
 * 	found in "./RSI.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#ifndef	_RTEData_H_
#define	_RTEData_H_


#include <asn_application.h>

/* Including external dependencies */
#include <NativeInteger.h>
#include "EventType.h"
#include "EventSource.h"
#include "Radius.h"
#include "RSIPriority.h"
#include "Confidence.h"
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Forward declarations */
struct PositionOffsetLLV;
struct Description;
struct RSITimeDetails;
struct ReferencePathList;
struct ReferenceLinkList;

/* RTEData */
typedef struct RTEData {
	long	 rteId;
	EventType_t	 eventType;
	EventSource_t	 eventSource;
	struct PositionOffsetLLV	*eventPos	/* OPTIONAL */;
	Radius_t	*eventRadius	/* OPTIONAL */;
	struct Description	*description	/* OPTIONAL */;
	struct RSITimeDetails	*timeDetails	/* OPTIONAL */;
	RSIPriority_t	*priority	/* OPTIONAL */;
	struct ReferencePathList	*referencePaths	/* OPTIONAL */;
	struct ReferenceLinkList	*referenceLinks	/* OPTIONAL */;
	Confidence_t	*eventConfidence	/* OPTIONAL */;
	/*
	 * This type is extensible,
	 * possible extensions are below.
	 */
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} RTEData_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_RTEData;
extern asn_SEQUENCE_specifics_t asn_SPC_RTEData_specs_1;
extern asn_TYPE_member_t asn_MBR_RTEData_1[11];

#ifdef __cplusplus
}
#endif

/* Referred external types */
#include "PositionOffsetLLV.h"
#include "Description.h"
#include "RSITimeDetails.h"
#include "ReferencePathList.h"
#include "ReferenceLinkList.h"

#endif	/* _RTEData_H_ */
#include <asn_internal.h>
