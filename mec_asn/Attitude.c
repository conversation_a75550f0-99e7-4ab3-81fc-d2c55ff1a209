/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "SensorSharing"
 * 	found in "./SensorSharing.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#include "Attitude.h"

asn_TYPE_member_t asn_MBR_Attitude_1[] = {
	{ ATF_NOFLAGS, 0, offsetof(struct Attitude, pitch),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_Pitch,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"pitch"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct Attitude, roll),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_Roll,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"roll"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct Attitude, yaw),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_Yaw,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"yaw"
		},
};
static const ber_tlv_tag_t asn_DEF_Attitude_tags_1[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_Attitude_tag2el_1[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* pitch */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* roll */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 } /* yaw */
};
asn_SEQUENCE_specifics_t asn_SPC_Attitude_specs_1 = {
	sizeof(struct Attitude),
	offsetof(struct Attitude, _asn_ctx),
	asn_MAP_Attitude_tag2el_1,
	3,	/* Count of tags in the map */
	0, 0, 0,	/* Optional elements (not needed) */
	-1,	/* First extension addition */
};
asn_TYPE_descriptor_t asn_DEF_Attitude = {
	"Attitude",
	"Attitude",
	&asn_OP_SEQUENCE,
	asn_DEF_Attitude_tags_1,
	sizeof(asn_DEF_Attitude_tags_1)
		/sizeof(asn_DEF_Attitude_tags_1[0]), /* 1 */
	asn_DEF_Attitude_tags_1,	/* Same as above */
	sizeof(asn_DEF_Attitude_tags_1)
		/sizeof(asn_DEF_Attitude_tags_1[0]), /* 1 */
	{ 0, 0, SEQUENCE_constraint },
	asn_MBR_Attitude_1,
	3,	/* Elements count */
	&asn_SPC_Attitude_specs_1	/* Additional specs */
};

