/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "VehSafetyExt"
 * 	found in "./VehSafetyExt.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#ifndef	_FullPositionVector_H_
#define	_FullPositionVector_H_


#include <asn_application.h>

/* Including external dependencies */
#include "Position3D.h"
#include "Heading.h"
#include "TransmissionState.h"
#include "Speed.h"
#include "TimeConfidence.h"
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Forward declarations */
struct DDateTime;
struct PositionConfidenceSet;
struct MotionConfidenceSet;

/* FullPositionVector */
typedef struct FullPositionVector {
	struct DDateTime	*utcTime	/* OPTIONAL */;
	Position3D_t	 pos;
	Heading_t	*heading	/* OPTIONAL */;
	TransmissionState_t	*transmission	/* OPTIONAL */;
	Speed_t	*speed	/* OPTIONAL */;
	struct PositionConfidenceSet	*posAccuracy	/* OPTIONAL */;
	TimeConfidence_t	*timeConfidence	/* OPTIONAL */;
	struct MotionConfidenceSet	*motionCfd	/* OPTIONAL */;
	/*
	 * This type is extensible,
	 * possible extensions are below.
	 */
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} FullPositionVector_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_FullPositionVector;
extern asn_SEQUENCE_specifics_t asn_SPC_FullPositionVector_specs_1;
extern asn_TYPE_member_t asn_MBR_FullPositionVector_1[8];

#ifdef __cplusplus
}
#endif

/* Referred external types */
#include "DDateTime.h"
#include "PositionConfidenceSet.h"
#include "MotionConfidenceSet.h"

#endif	/* _FullPositionVector_H_ */
#include <asn_internal.h>
