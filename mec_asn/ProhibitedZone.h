/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "Map"
 * 	found in "./Map.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#ifndef	_ProhibitedZone_H_
#define	_ProhibitedZone_H_


#include <asn_application.h>

/* Including external dependencies */
#include <asn_SEQUENCE_OF.h>
#include <constr_SEQUENCE_OF.h>
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Forward declarations */
struct Polygon;

/* ProhibitedZone */
typedef struct ProhibitedZone {
	struct Polygon	*centralCircleProhibitedZone	/* OPTIONAL */;
	struct non_motorVehicleProhibitedZones {
		A_SEQUENCE_OF(struct Polygon) list;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *non_motorVehicleProhibitedZones;
	struct gridLineMarkingProhibitedZones {
		A_SEQUENCE_OF(struct Polygon) list;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *gridLineMarkingProhibitedZones;
	/*
	 * This type is extensible,
	 * possible extensions are below.
	 */
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} ProhibitedZone_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_ProhibitedZone;
extern asn_SEQUENCE_specifics_t asn_SPC_ProhibitedZone_specs_1;
extern asn_TYPE_member_t asn_MBR_ProhibitedZone_1[3];

#ifdef __cplusplus
}
#endif

/* Referred external types */
#include "Polygon.h"

#endif	/* _ProhibitedZone_H_ */
#include <asn_internal.h>
