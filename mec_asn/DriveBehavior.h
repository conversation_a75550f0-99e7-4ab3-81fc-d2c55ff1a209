/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "VIR"
 * 	found in "./VIR.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#ifndef	_DriveBehavior_H_
#define	_DriveBehavior_H_


#include <asn_application.h>

/* Including external dependencies */
#include <BIT_STRING.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Dependencies */
typedef enum DriveBehavior {
	DriveBehavior_goStraightForward	= 0,
	DriveBehavior_laneChangingToLeft	= 1,
	DriveBehavior_laneChangingToRight	= 2,
	DriveBehavior_rampIn	= 3,
	DriveBehavior_rampOut	= 4,
	DriveBehavior_intersectionStraightThrough	= 5,
	DriveBehavior_intersectionTurnLeft	= 6,
	DriveBehavior_intersectionTurnRight	= 7,
	DriveBehavior_intersectionUTurn	= 8,
	DriveBehavior_stop_and_go	= 9,
	DriveBehavior_stop	= 10,
	DriveBehavior_slow_down	= 11,
	DriveBehavior_speed_up	= 12,
	DriveBehavior_parking	= 13
} e_DriveBehavior;

/* DriveBehavior */
typedef BIT_STRING_t	 DriveBehavior_t;

/* Implementation */
extern asn_per_constraints_t asn_PER_type_DriveBehavior_constr_1;
extern asn_TYPE_descriptor_t asn_DEF_DriveBehavior;
asn_struct_free_f DriveBehavior_free;
asn_struct_print_f DriveBehavior_print;
asn_constr_check_f DriveBehavior_constraint;
ber_type_decoder_f DriveBehavior_decode_ber;
der_type_encoder_f DriveBehavior_encode_der;
xer_type_decoder_f DriveBehavior_decode_xer;
xer_type_encoder_f DriveBehavior_encode_xer;
oer_type_decoder_f DriveBehavior_decode_oer;
oer_type_encoder_f DriveBehavior_encode_oer;
per_type_decoder_f DriveBehavior_decode_uper;
per_type_encoder_f DriveBehavior_encode_uper;

#ifdef __cplusplus
}
#endif

#endif	/* _DriveBehavior_H_ */
#include <asn_internal.h>
