/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "GoEMEC"
 * 	found in "./GoEMEC.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#include "ErrorCode.h"

/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
static asn_oer_constraints_t asn_OER_type_ErrorCode_constr_1 CC_NOTUSED = {
	{ 0, 0 },
	-1};
asn_per_constraints_t asn_PER_type_ErrorCode_constr_1 CC_NOTUSED = {
	{ APC_CONSTRAINED | APC_EXTENSIBLE,  3,  3,  0,  7 }	/* (0..7,...) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
static const asn_INTEGER_enum_map_t asn_MAP_ErrorCode_value2enum_1[] = {
	{ 0,	7,	"noError" },
	{ 1,	10,	"paramError" },
	{ 2,	11,	"systemError" },
	{ 3,	8,	"crcError" },
	{ 4,	17,	"updateTypeInvalid" },
	{ 5,	17,	"updateSizeInvalid" },
	{ 6,	14,	"updateMagicErr" },
	{ 7,	14,	"updateFileNull" }
	/* This list is extensible */
};
static const unsigned int asn_MAP_ErrorCode_enum2value_1[] = {
	3,	/* crcError(3) */
	0,	/* noError(0) */
	1,	/* paramError(1) */
	2,	/* systemError(2) */
	7,	/* updateFileNull(7) */
	6,	/* updateMagicErr(6) */
	5,	/* updateSizeInvalid(5) */
	4	/* updateTypeInvalid(4) */
	/* This list is extensible */
};
const asn_INTEGER_specifics_t asn_SPC_ErrorCode_specs_1 = {
	asn_MAP_ErrorCode_value2enum_1,	/* "tag" => N; sorted by tag */
	asn_MAP_ErrorCode_enum2value_1,	/* N => "tag"; sorted by N */
	8,	/* Number of elements in the maps */
	9,	/* Extensions before this member */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_ErrorCode_tags_1[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
asn_TYPE_descriptor_t asn_DEF_ErrorCode = {
	"ErrorCode",
	"ErrorCode",
	&asn_OP_NativeEnumerated,
	asn_DEF_ErrorCode_tags_1,
	sizeof(asn_DEF_ErrorCode_tags_1)
		/sizeof(asn_DEF_ErrorCode_tags_1[0]), /* 1 */
	asn_DEF_ErrorCode_tags_1,	/* Same as above */
	sizeof(asn_DEF_ErrorCode_tags_1)
		/sizeof(asn_DEF_ErrorCode_tags_1[0]), /* 1 */
	{ &asn_OER_type_ErrorCode_constr_1, &asn_PER_type_ErrorCode_constr_1, NativeEnumerated_constraint },
	0, 0,	/* Defined elsewhere */
	&asn_SPC_ErrorCode_specs_1	/* Additional specs */
};

