/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "OPS"
 * 	found in "./OPS.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#ifndef	_FileTransferAck_H_
#define	_FileTransferAck_H_


#include <asn_application.h>

/* Including external dependencies */
#include "Result.h"
#include <NativeInteger.h>
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* FileTransferAck */
typedef struct FileTransferAck {
	Result_t	 result;
	long	 fileId;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} FileTransferAck_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_FileTransferAck;
extern asn_SEQUENCE_specifics_t asn_SPC_FileTransferAck_specs_1;
extern asn_TYPE_member_t asn_MBR_FileTransferAck_1[2];

#ifdef __cplusplus
}
#endif

#endif	/* _FileTransferAck_H_ */
#include <asn_internal.h>
