/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "GoEMEC"
 * 	found in "./GoEMEC.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#ifndef	_Login_H_
#define	_Login_H_


#include <asn_application.h>

/* Including external dependencies */
#include <OCTET_STRING.h>
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Login */
typedef struct Login {
	OCTET_STRING_t	 userName;
	OCTET_STRING_t	 passWord;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} Login_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_Login;
extern asn_SEQUENCE_specifics_t asn_SPC_Login_specs_1;
extern asn_TYPE_member_t asn_MBR_Login_1[2];

#ifdef __cplusplus
}
#endif

#endif	/* _Login_H_ */
#include <asn_internal.h>
