/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "VPM"
 * 	found in "./VPM.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#ifndef	_RST_H_
#define	_RST_H_


#include <asn_application.h>

/* Including external dependencies */
#include "DDateTime.h"
#include <OCTET_STRING.h>
#include "PaymentList.h"
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* RST */
typedef struct RST {
	DDateTime_t	 time;
	OCTET_STRING_t	 id;
	PaymentList_t	 paymentList;
	/*
	 * This type is extensible,
	 * possible extensions are below.
	 */
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} RST_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_RST;
extern asn_SEQUENCE_specifics_t asn_SPC_RST_specs_1;
extern asn_TYPE_member_t asn_MBR_RST_1[3];

#ifdef __cplusplus
}
#endif

#endif	/* _RST_H_ */
#include <asn_internal.h>
