/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "DefPosition"
 * 	found in "./DefPosition.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#ifndef	_Elevation_H_
#define	_Elevation_H_


#include <asn_application.h>

/* Including external dependencies */
#include <NativeInteger.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Elevation */
typedef long	 Elevation_t;

/* Implementation */
extern asn_per_constraints_t asn_PER_type_Elevation_constr_1;
extern asn_TYPE_descriptor_t asn_DEF_Elevation;
asn_struct_free_f Elevation_free;
asn_struct_print_f Elevation_print;
asn_constr_check_f Elevation_constraint;
ber_type_decoder_f Elevation_decode_ber;
der_type_encoder_f Elevation_encode_der;
xer_type_decoder_f Elevation_decode_xer;
xer_type_encoder_f Elevation_encode_xer;
oer_type_decoder_f Elevation_decode_oer;
oer_type_encoder_f Elevation_encode_oer;
per_type_decoder_f Elevation_decode_uper;
per_type_encoder_f Elevation_encode_uper;

#ifdef __cplusplus
}
#endif

#endif	/* _Elevation_H_ */
#include <asn_internal.h>
