/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "VPM"
 * 	found in "./VPM.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#ifndef	_PaymentInfo_H_
#define	_PaymentInfo_H_


#include <asn_application.h>

/* Including external dependencies */
#include "PaymentInfoType1.h"
#include <constr_CHOICE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Dependencies */
typedef enum PaymentInfo_PR {
	PaymentInfo_PR_NOTHING,	/* No components present */
	PaymentInfo_PR_paymentInfoType1
	/* Extensions may appear below */
	
} PaymentInfo_PR;

/* PaymentInfo */
typedef struct PaymentInfo {
	PaymentInfo_PR present;
	union PaymentInfo_u {
		PaymentInfoType1_t	 paymentInfoType1;
		/*
		 * This type is extensible,
		 * possible extensions are below.
		 */
	} choice;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} PaymentInfo_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_PaymentInfo;
extern asn_CHOICE_specifics_t asn_SPC_PaymentInfo_specs_1;
extern asn_TYPE_member_t asn_MBR_PaymentInfo_1[1];
extern asn_per_constraints_t asn_PER_type_PaymentInfo_constr_1;

#ifdef __cplusplus
}
#endif

#endif	/* _PaymentInfo_H_ */
#include <asn_internal.h>
