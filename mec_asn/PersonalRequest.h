/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "PSM"
 * 	found in "./PSM.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#ifndef	_PersonalRequest_H_
#define	_PersonalRequest_H_


#include <asn_application.h>

/* Including external dependencies */
#include "PersonalCrossing.h"
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* PersonalRequest */
typedef struct PersonalRequest {
	PersonalCrossing_t	*crossing	/* OPTIONAL */;
	/*
	 * This type is extensible,
	 * possible extensions are below.
	 */
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} PersonalRequest_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_PersonalRequest;
extern asn_SEQUENCE_specifics_t asn_SPC_PersonalRequest_specs_1;
extern asn_TYPE_member_t asn_MBR_PersonalRequest_1[1];

#ifdef __cplusplus
}
#endif

#endif	/* _PersonalRequest_H_ */
#include <asn_internal.h>
