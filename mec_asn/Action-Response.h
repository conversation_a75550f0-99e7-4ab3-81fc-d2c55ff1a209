/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "VPM"
 * 	found in "./VPM.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#ifndef	_Action_Response_H_
#define	_Action_Response_H_


#include <asn_application.h>

/* Including external dependencies */
#include "DDateTime.h"
#include <OCTET_STRING.h>
#include "PaymentEntityID.h"
#include "ReturnStatus.h"
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Forward declarations */
struct TransInfo;

/* Action-Response */
typedef struct Action_Response {
	DDateTime_t	 time;
	OCTET_STRING_t	 sourceId;
	OCTET_STRING_t	*targetId	/* OPTIONAL */;
	PaymentEntityID_t	 paymentEntityId;
	struct TransInfo	*responseParameter	/* OPTIONAL */;
	ReturnStatus_t	 ret;
	/*
	 * This type is extensible,
	 * possible extensions are below.
	 */
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} Action_Response_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_Action_Response;
extern asn_SEQUENCE_specifics_t asn_SPC_Action_Response_specs_1;
extern asn_TYPE_member_t asn_MBR_Action_Response_1[6];

#ifdef __cplusplus
}
#endif

/* Referred external types */
#include "TransInfo.h"

#endif	/* _Action_Response_H_ */
#include <asn_internal.h>
