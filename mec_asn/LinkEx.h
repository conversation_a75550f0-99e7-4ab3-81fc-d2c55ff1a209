/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "Map"
 * 	found in "./Map.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#ifndef	_LinkEx_H_
#define	_LinkEx_H_


#include <asn_application.h>

/* Including external dependencies */
#include "DescriptiveName.h"
#include "NodeReferenceID.h"
#include "LaneWidth.h"
#include "SectionList.h"
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Forward declarations */
struct SpeedLimitList;
struct PointList;
struct MovementExList;

/* LinkEx */
typedef struct LinkEx {
	DescriptiveName_t	*name	/* OPTIONAL */;
	NodeReferenceID_t	 upstreamNodeId;
	struct SpeedLimitList	*speedLimits	/* OPTIONAL */;
	LaneWidth_t	*linkWidth	/* OPTIONAL */;
	struct PointList	*refLine	/* OPTIONAL */;
	struct MovementExList	*movements_ex	/* OPTIONAL */;
	SectionList_t	 sections;
	/*
	 * This type is extensible,
	 * possible extensions are below.
	 */
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} LinkEx_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_LinkEx;
extern asn_SEQUENCE_specifics_t asn_SPC_LinkEx_specs_1;
extern asn_TYPE_member_t asn_MBR_LinkEx_1[7];

#ifdef __cplusplus
}
#endif

/* Referred external types */
#include "SpeedLimitList.h"
#include "PointList.h"
#include "MovementExList.h"

#endif	/* _LinkEx_H_ */
#include <asn_internal.h>
