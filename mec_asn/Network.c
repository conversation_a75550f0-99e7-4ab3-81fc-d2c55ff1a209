/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "OPS"
 * 	found in "./OPS.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#include "Network.h"

static int check_permitted_alphabet_2(const void *sptr) {
	/* The underlying type is IA5String */
	const IA5String_t *st = (const IA5String_t *)sptr;
	const uint8_t *ch = st->buf;
	const uint8_t *end = ch + st->size;
	
	for(; ch < end; ch++) {
		uint8_t cv = *ch;
		if(!(cv <= 127)) return -1;
	}
	return 0;
}

static int
memb_name_constraint_1(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	const IA5String_t *st = (const IA5String_t *)sptr;
	size_t size;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	size = st->size;
	
	if((size >= 1 && size <= 32)
		 && !check_permitted_alphabet_2(st)) {
		/* Constraint check succeeded */
		return 0;
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static asn_oer_constraints_t asn_OER_memb_name_constr_2 CC_NOTUSED = {
	{ 0, 0 },
	-1	/* (SIZE(1..32)) */};
static asn_per_constraints_t asn_PER_memb_name_constr_2 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 7,  7,  0,  127 }	/* (0..127) */,
	{ APC_CONSTRAINED,	 5,  5,  1,  32 }	/* (SIZE(1..32)) */,
	0, 0	/* No PER character map necessary */
};
asn_TYPE_member_t asn_MBR_Network_1[] = {
	{ ATF_NOFLAGS, 0, offsetof(struct Network, name),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_IA5String,
		0,
		{ &asn_OER_memb_name_constr_2, &asn_PER_memb_name_constr_2,  memb_name_constraint_1 },
		0, 0, /* No default value */
		"name"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct Network, isDHCP),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_BOOLEAN,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"isDHCP"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct Network, address),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_IP,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"address"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct Network, netmask),
		(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_IP,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"netmask"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct Network, gateway),
		(ASN_TAG_CLASS_CONTEXT | (4 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_IP,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"gateway"
		},
};
static const ber_tlv_tag_t asn_DEF_Network_tags_1[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_Network_tag2el_1[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* name */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* isDHCP */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 }, /* address */
    { (ASN_TAG_CLASS_CONTEXT | (3 << 2)), 3, 0, 0 }, /* netmask */
    { (ASN_TAG_CLASS_CONTEXT | (4 << 2)), 4, 0, 0 } /* gateway */
};
asn_SEQUENCE_specifics_t asn_SPC_Network_specs_1 = {
	sizeof(struct Network),
	offsetof(struct Network, _asn_ctx),
	asn_MAP_Network_tag2el_1,
	5,	/* Count of tags in the map */
	0, 0, 0,	/* Optional elements (not needed) */
	-1,	/* First extension addition */
};
asn_TYPE_descriptor_t asn_DEF_Network = {
	"Network",
	"Network",
	&asn_OP_SEQUENCE,
	asn_DEF_Network_tags_1,
	sizeof(asn_DEF_Network_tags_1)
		/sizeof(asn_DEF_Network_tags_1[0]), /* 1 */
	asn_DEF_Network_tags_1,	/* Same as above */
	sizeof(asn_DEF_Network_tags_1)
		/sizeof(asn_DEF_Network_tags_1[0]), /* 1 */
	{ 0, 0, SEQUENCE_constraint },
	asn_MBR_Network_1,
	5,	/* Elements count */
	&asn_SPC_Network_specs_1	/* Additional specs */
};

