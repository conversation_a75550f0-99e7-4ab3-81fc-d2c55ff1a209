/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "SensorSharing"
 * 	found in "./SensorSharing.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#ifndef	_PlanningList_H_
#define	_PlanningList_H_


#include <asn_application.h>

/* Including external dependencies */
#include <asn_SEQUENCE_OF.h>
#include <constr_SEQUENCE_OF.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Forward declarations */
struct Planning;

/* PlanningList */
typedef struct PlanningList {
	A_SEQUENCE_OF(struct Planning) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} PlanningList_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_PlanningList;
extern asn_SET_OF_specifics_t asn_SPC_PlanningList_specs_1;
extern asn_TYPE_member_t asn_MBR_PlanningList_1[1];
extern asn_per_constraints_t asn_PER_type_PlanningList_constr_1;

#ifdef __cplusplus
}
#endif

/* Referred external types */
#include "Planning.h"

#endif	/* _PlanningList_H_ */
#include <asn_internal.h>
