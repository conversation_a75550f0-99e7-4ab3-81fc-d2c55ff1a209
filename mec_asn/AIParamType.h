/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "OPS"
 * 	found in "./OPS.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#ifndef	_AIParamType_H_
#define	_AIParamType_H_


#include <asn_application.h>

/* Including external dependencies */
#include <NativeEnumerated.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Dependencies */
typedef enum AIParamType {
	AIParamType_inParam	= 0,
	AIParamType_outParam	= 1
} e_AIParamType;

/* AIParamType */
typedef long	 AIParamType_t;

/* Implementation */
extern asn_per_constraints_t asn_PER_type_AIParamType_constr_1;
extern asn_TYPE_descriptor_t asn_DEF_AIParamType;
extern const asn_INTEGER_specifics_t asn_SPC_AIParamType_specs_1;
asn_struct_free_f AIParamType_free;
asn_struct_print_f AIParamType_print;
asn_constr_check_f AIParamType_constraint;
ber_type_decoder_f AIParamType_decode_ber;
der_type_encoder_f AIParamType_encode_der;
xer_type_decoder_f AIParamType_decode_xer;
xer_type_encoder_f AIParamType_encode_xer;
oer_type_decoder_f AIParamType_decode_oer;
oer_type_encoder_f AIParamType_encode_oer;
per_type_decoder_f AIParamType_decode_uper;
per_type_encoder_f AIParamType_encode_uper;

#ifdef __cplusplus
}
#endif

#endif	/* _AIParamType_H_ */
#include <asn_internal.h>
