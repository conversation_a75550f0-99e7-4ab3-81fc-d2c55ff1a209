/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "GoEMEC"
 * 	found in "./GoEMEC.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#include "GoEMECMessageFrame.h"

static asn_oer_constraints_t asn_OER_type_GoEMECMessageFrame_constr_1 CC_NOTUSED = {
	{ 0, 0 },
	-1};
static asn_per_constraints_t asn_PER_type_GoEMECMessageFrame_constr_1 CC_NOTUSED = {
	{ APC_CONSTRAINED | APC_EXTENSIBLE,  2,  2,  0,  2 }	/* (0..2,...) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
static asn_TYPE_member_t asn_MBR_GoEMECMessageFrame_1[] = {
	{ ATF_NOFLAGS, 0, offsetof(struct GoEMECMessageFrame, choice.linkFrame),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		+1,	/* EXPLICIT tag at current level */
		&asn_DEF_LinkFrame,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"linkFrame"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct GoEMECMessageFrame, choice.cv2xFrame),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		+1,	/* EXPLICIT tag at current level */
		&asn_DEF_MessageFrame,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"cv2xFrame"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct GoEMECMessageFrame, choice.opsFrame),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		+1,	/* EXPLICIT tag at current level */
		&asn_DEF_OPSFrame,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"opsFrame"
		},
};
static const asn_TYPE_tag2member_t asn_MAP_GoEMECMessageFrame_tag2el_1[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* linkFrame */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* cv2xFrame */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 } /* opsFrame */
};
static asn_CHOICE_specifics_t asn_SPC_GoEMECMessageFrame_specs_1 = {
	sizeof(struct GoEMECMessageFrame),
	offsetof(struct GoEMECMessageFrame, _asn_ctx),
	offsetof(struct GoEMECMessageFrame, present),
	sizeof(((struct GoEMECMessageFrame *)0)->present),
	asn_MAP_GoEMECMessageFrame_tag2el_1,
	3,	/* Count of tags in the map */
	0, 0,
	3	/* Extensions start */
};
asn_TYPE_descriptor_t asn_DEF_GoEMECMessageFrame = {
	"GoEMECMessageFrame",
	"GoEMECMessageFrame",
	&asn_OP_CHOICE,
	0,	/* No effective tags (pointer) */
	0,	/* No effective tags (count) */
	0,	/* No tags (pointer) */
	0,	/* No tags (count) */
	{ &asn_OER_type_GoEMECMessageFrame_constr_1, &asn_PER_type_GoEMECMessageFrame_constr_1, CHOICE_constraint },
	asn_MBR_GoEMECMessageFrame_1,
	3,	/* Elements count */
	&asn_SPC_GoEMECMessageFrame_specs_1	/* Additional specs */
};

