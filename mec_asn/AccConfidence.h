/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "SensorSharing"
 * 	found in "./SensorSharing.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#ifndef	_AccConfidence_H_
#define	_AccConfidence_H_


#include <asn_application.h>

/* Including external dependencies */
#include <NativeEnumerated.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Dependencies */
typedef enum AccConfidence {
	AccConfidence_unavailable	= 0,
	AccConfidence_prec100deg	= 1,
	AccConfidence_prec10deg	= 2,
	AccConfidence_prec5deg	= 3,
	AccConfidence_prec1deg	= 4,
	AccConfidence_prec0_1deg	= 5,
	AccConfidence_prec0_05deg	= 6,
	AccConfidence_prec0_01deg	= 7
} e_AccConfidence;

/* AccConfidence */
typedef long	 AccConfidence_t;

/* Implementation */
extern asn_per_constraints_t asn_PER_type_AccConfidence_constr_1;
extern asn_TYPE_descriptor_t asn_DEF_AccConfidence;
extern const asn_INTEGER_specifics_t asn_SPC_AccConfidence_specs_1;
asn_struct_free_f AccConfidence_free;
asn_struct_print_f AccConfidence_print;
asn_constr_check_f AccConfidence_constraint;
ber_type_decoder_f AccConfidence_decode_ber;
der_type_encoder_f AccConfidence_encode_der;
xer_type_decoder_f AccConfidence_decode_xer;
xer_type_encoder_f AccConfidence_encode_xer;
oer_type_decoder_f AccConfidence_decode_oer;
oer_type_encoder_f AccConfidence_encode_oer;
per_type_decoder_f AccConfidence_decode_uper;
per_type_encoder_f AccConfidence_encode_uper;

#ifdef __cplusplus
}
#endif

#endif	/* _AccConfidence_H_ */
#include <asn_internal.h>
