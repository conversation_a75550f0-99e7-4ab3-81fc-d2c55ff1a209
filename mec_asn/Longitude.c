/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "DefPosition"
 * 	found in "./DefPosition.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#include "Longitude.h"

int
Longitude_constraint(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	long value;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	value = *(const long *)sptr;
	
	if((value >= -1799999999 && value <= 1800000001)) {
		/* Constraint check succeeded */
		return 0;
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

/*
 * This type is implemented using NativeInteger,
 * so here we adjust the DEF accordingly.
 */
static asn_oer_constraints_t asn_OER_type_Longitude_constr_1 CC_NOTUSED = {
	{ 4, 0 }	/* (-1799999999..1800000001) */,
	-1};
asn_per_constraints_t asn_PER_type_Longitude_constr_1 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 32, -1, -1799999999,  1800000001 }	/* (-1799999999..1800000001) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
static const ber_tlv_tag_t asn_DEF_Longitude_tags_1[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (2 << 2))
};
asn_TYPE_descriptor_t asn_DEF_Longitude = {
	"Longitude",
	"Longitude",
	&asn_OP_NativeInteger,
	asn_DEF_Longitude_tags_1,
	sizeof(asn_DEF_Longitude_tags_1)
		/sizeof(asn_DEF_Longitude_tags_1[0]), /* 1 */
	asn_DEF_Longitude_tags_1,	/* Same as above */
	sizeof(asn_DEF_Longitude_tags_1)
		/sizeof(asn_DEF_Longitude_tags_1[0]), /* 1 */
	{ &asn_OER_type_Longitude_constr_1, &asn_PER_type_Longitude_constr_1, Longitude_constraint },
	0, 0,	/* No members */
	0	/* No specifics */
};

