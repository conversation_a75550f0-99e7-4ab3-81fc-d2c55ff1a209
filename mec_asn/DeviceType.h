/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "OPS"
 * 	found in "./OPS.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#ifndef	_DeviceType_H_
#define	_DeviceType_H_


#include <asn_application.h>

/* Including external dependencies */
#include <NativeEnumerated.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Dependencies */
typedef enum DeviceType {
	DeviceType_mec	= 0,
	DeviceType_rsu	= 1,
	DeviceType_obu	= 2,
	DeviceType_scaler	= 3
	/*
	 * Enumeration is extensible
	 */
} e_DeviceType;

/* DeviceType */
typedef long	 DeviceType_t;

/* Implementation */
extern asn_per_constraints_t asn_PER_type_DeviceType_constr_1;
extern asn_TYPE_descriptor_t asn_DEF_DeviceType;
extern const asn_INTEGER_specifics_t asn_SPC_DeviceType_specs_1;
asn_struct_free_f DeviceType_free;
asn_struct_print_f DeviceType_print;
asn_constr_check_f DeviceType_constraint;
ber_type_decoder_f DeviceType_decode_ber;
der_type_encoder_f DeviceType_encode_der;
xer_type_decoder_f DeviceType_decode_xer;
xer_type_encoder_f DeviceType_encode_xer;
oer_type_decoder_f DeviceType_decode_oer;
oer_type_encoder_f DeviceType_encode_oer;
per_type_decoder_f DeviceType_decode_uper;
per_type_encoder_f DeviceType_encode_uper;

#ifdef __cplusplus
}
#endif

#endif	/* _DeviceType_H_ */
#include <asn_internal.h>
