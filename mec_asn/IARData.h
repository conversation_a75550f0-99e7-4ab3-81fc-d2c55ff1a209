/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "VIR"
 * 	found in "./VIR.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#ifndef	_IARData_H_
#define	_IARData_H_


#include <asn_application.h>

/* Including external dependencies */
#include "DriveBehavior.h"
#include <asn_SEQUENCE_OF.h>
#include <constr_SEQUENCE_OF.h>
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Forward declarations */
struct PathPlanningPoint;
struct PathPlanning;
struct DriveRequest;

/* IARData */
typedef struct IARData {
	struct PathPlanningPoint	*currentPos	/* OPTIONAL */;
	struct PathPlanning	*path_Planning	/* OPTIONAL */;
	DriveBehavior_t	*currentBehavior	/* OPTIONAL */;
	struct reqs {
		A_SEQUENCE_OF(struct DriveRequest) list;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *reqs;
	/*
	 * This type is extensible,
	 * possible extensions are below.
	 */
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} IARData_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_IARData;
extern asn_SEQUENCE_specifics_t asn_SPC_IARData_specs_1;
extern asn_TYPE_member_t asn_MBR_IARData_1[4];

#ifdef __cplusplus
}
#endif

/* Referred external types */
#include "PathPlanningPoint.h"
#include "PathPlanning.h"
#include "DriveRequest.h"

#endif	/* _IARData_H_ */
#include <asn_internal.h>
