/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "RSC"
 * 	found in "./RSC.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#ifndef	_DriveSuggestion_H_
#define	_DriveSuggestion_H_


#include <asn_application.h>

/* Including external dependencies */
#include "DriveBehavior.h"
#include "TimeOffset.h"
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Forward declarations */
struct ReferenceLink;
struct ReferencePath;

/* DriveSuggestion */
typedef struct DriveSuggestion {
	DriveBehavior_t	 suggestion;
	TimeOffset_t	*lifeTime	/* OPTIONAL */;
	struct ReferenceLink	*relatedLink	/* OPTIONAL */;
	struct ReferencePath	*relatedPath	/* OPTIONAL */;
	/*
	 * This type is extensible,
	 * possible extensions are below.
	 */
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} DriveSuggestion_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_DriveSuggestion;
extern asn_SEQUENCE_specifics_t asn_SPC_DriveSuggestion_specs_1;
extern asn_TYPE_member_t asn_MBR_DriveSuggestion_1[4];

#ifdef __cplusplus
}
#endif

/* Referred external types */
#include "ReferenceLink.h"
#include "ReferencePath.h"

#endif	/* _DriveSuggestion_H_ */
#include <asn_internal.h>
