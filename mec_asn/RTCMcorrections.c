/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "RTCM"
 * 	found in "./RTCM.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#include "RTCMcorrections.h"

static int
memb_corrections_constraint_1(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	size_t size;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	/* Determine the number of elements */
	size = _A_CSEQUENCE_FROM_VOID(sptr)->count;
	
	if((size >= 1 && size <= 5)) {
		/* Perform validation of the inner elements */
		return SEQUENCE_OF_constraint(td, sptr, ctfailcb, app_key);
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static asn_oer_constraints_t asn_OER_type_corrections_constr_3 CC_NOTUSED = {
	{ 0, 0 },
	-1	/* (SIZE(1..5)) */};
static asn_per_constraints_t asn_PER_type_corrections_constr_3 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 3,  3,  1,  5 }	/* (SIZE(1..5)) */,
	0, 0	/* No PER value map */
};
static asn_oer_constraints_t asn_OER_memb_corrections_constr_3 CC_NOTUSED = {
	{ 0, 0 },
	-1	/* (SIZE(1..5)) */};
static asn_per_constraints_t asn_PER_memb_corrections_constr_3 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 3,  3,  1,  5 }	/* (SIZE(1..5)) */,
	0, 0	/* No PER value map */
};
static asn_TYPE_member_t asn_MBR_corrections_3[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_RTCMmsg,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_corrections_tags_3[] = {
	(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static asn_SET_OF_specifics_t asn_SPC_corrections_specs_3 = {
	sizeof(struct corrections),
	offsetof(struct corrections, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_corrections_3 = {
	"corrections",
	"corrections",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_corrections_tags_3,
	sizeof(asn_DEF_corrections_tags_3)
		/sizeof(asn_DEF_corrections_tags_3[0]) - 1, /* 1 */
	asn_DEF_corrections_tags_3,	/* Same as above */
	sizeof(asn_DEF_corrections_tags_3)
		/sizeof(asn_DEF_corrections_tags_3[0]), /* 2 */
	{ &asn_OER_type_corrections_constr_3, &asn_PER_type_corrections_constr_3, SEQUENCE_OF_constraint },
	asn_MBR_corrections_3,
	1,	/* Single element */
	&asn_SPC_corrections_specs_3	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_RTCMcorrections_1[] = {
	{ ATF_NOFLAGS, 0, offsetof(struct RTCMcorrections, msgCnt),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_MsgCount,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"msgCnt"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct RTCMcorrections, corrections),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		0,
		&asn_DEF_corrections_3,
		0,
		{ &asn_OER_memb_corrections_constr_3, &asn_PER_memb_corrections_constr_3,  memb_corrections_constraint_1 },
		0, 0, /* No default value */
		"corrections"
		},
};
static const ber_tlv_tag_t asn_DEF_RTCMcorrections_tags_1[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_RTCMcorrections_tag2el_1[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* msgCnt */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 } /* corrections */
};
asn_SEQUENCE_specifics_t asn_SPC_RTCMcorrections_specs_1 = {
	sizeof(struct RTCMcorrections),
	offsetof(struct RTCMcorrections, _asn_ctx),
	asn_MAP_RTCMcorrections_tag2el_1,
	2,	/* Count of tags in the map */
	0, 0, 0,	/* Optional elements (not needed) */
	2,	/* First extension addition */
};
asn_TYPE_descriptor_t asn_DEF_RTCMcorrections = {
	"RTCMcorrections",
	"RTCMcorrections",
	&asn_OP_SEQUENCE,
	asn_DEF_RTCMcorrections_tags_1,
	sizeof(asn_DEF_RTCMcorrections_tags_1)
		/sizeof(asn_DEF_RTCMcorrections_tags_1[0]), /* 1 */
	asn_DEF_RTCMcorrections_tags_1,	/* Same as above */
	sizeof(asn_DEF_RTCMcorrections_tags_1)
		/sizeof(asn_DEF_RTCMcorrections_tags_1[0]), /* 1 */
	{ 0, 0, SEQUENCE_constraint },
	asn_MBR_RTCMcorrections_1,
	2,	/* Elements count */
	&asn_SPC_RTCMcorrections_specs_1	/* Additional specs */
};

