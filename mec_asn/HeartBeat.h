/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "GoEMEC"
 * 	found in "./GoEMEC.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#ifndef	_HeartBeat_H_
#define	_HeartBeat_H_


#include <asn_application.h>

/* Including external dependencies */
#include <NativeInteger.h>

#ifdef __cplusplus
extern "C" {
#endif

/* HeartBeat */
typedef unsigned long	 HeartBeat_t;

/* Implementation */
extern asn_per_constraints_t asn_PER_type_HeartBeat_constr_1;
extern asn_TYPE_descriptor_t asn_DEF_HeartBeat;
extern const asn_INTEGER_specifics_t asn_SPC_HeartBeat_specs_1;
asn_struct_free_f HeartBeat_free;
asn_struct_print_f HeartBeat_print;
asn_constr_check_f HeartBeat_constraint;
ber_type_decoder_f HeartBeat_decode_ber;
der_type_encoder_f HeartBeat_encode_der;
xer_type_decoder_f HeartBeat_decode_xer;
xer_type_encoder_f HeartBeat_encode_xer;
oer_type_decoder_f HeartBeat_decode_oer;
oer_type_encoder_f HeartBeat_encode_oer;
per_type_decoder_f HeartBeat_decode_uper;
per_type_encoder_f HeartBeat_encode_uper;

#ifdef __cplusplus
}
#endif

#endif	/* _HeartBeat_H_ */
#include <asn_internal.h>
