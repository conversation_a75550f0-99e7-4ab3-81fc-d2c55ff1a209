/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "VPM"
 * 	found in "./VPM.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#include "ChannelRq.h"

asn_TYPE_member_t asn_MBR_ChannelRq_1[] = {
	{ ATF_NOFLAGS, 0, offsetof(struct ChannelRq, channelid),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_ChannelID,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"channelid"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct ChannelRq, apdu),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_ApduList,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"apdu"
		},
};
static const ber_tlv_tag_t asn_DEF_ChannelRq_tags_1[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_ChannelRq_tag2el_1[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* channelid */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 } /* apdu */
};
asn_SEQUENCE_specifics_t asn_SPC_ChannelRq_specs_1 = {
	sizeof(struct ChannelRq),
	offsetof(struct ChannelRq, _asn_ctx),
	asn_MAP_ChannelRq_tag2el_1,
	2,	/* Count of tags in the map */
	0, 0, 0,	/* Optional elements (not needed) */
	2,	/* First extension addition */
};
asn_TYPE_descriptor_t asn_DEF_ChannelRq = {
	"ChannelRq",
	"ChannelRq",
	&asn_OP_SEQUENCE,
	asn_DEF_ChannelRq_tags_1,
	sizeof(asn_DEF_ChannelRq_tags_1)
		/sizeof(asn_DEF_ChannelRq_tags_1[0]), /* 1 */
	asn_DEF_ChannelRq_tags_1,	/* Same as above */
	sizeof(asn_DEF_ChannelRq_tags_1)
		/sizeof(asn_DEF_ChannelRq_tags_1[0]), /* 1 */
	{ 0, 0, SEQUENCE_constraint },
	asn_MBR_ChannelRq_1,
	2,	/* Elements count */
	&asn_SPC_ChannelRq_specs_1	/* Additional specs */
};

