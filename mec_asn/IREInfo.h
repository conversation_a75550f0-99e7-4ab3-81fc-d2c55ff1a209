/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "OPS"
 * 	found in "./OPS.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#ifndef	_IREInfo_H_
#define	_IREInfo_H_


#include <asn_application.h>

/* Including external dependencies */
#include <NativeInteger.h>
#include "IREType.h"
#include "Esn.h"
#include <IA5String.h>
#include "Version.h"
#include "IP.h"
#include <BOOLEAN.h>
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* IREInfo */
typedef struct IREInfo {
	long	 id;
	long	 intersectionId;
	IREType_t	 type;
	Esn_t	 esn;
	IA5String_t	 vendor;
	IA5String_t	 model;
	Version_t	 version;
	IP_t	 address;
	BOOLEAN_t	 status;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} IREInfo_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_IREInfo;
extern asn_SEQUENCE_specifics_t asn_SPC_IREInfo_specs_1;
extern asn_TYPE_member_t asn_MBR_IREInfo_1[9];

#ifdef __cplusplus
}
#endif

#endif	/* _IREInfo_H_ */
#include <asn_internal.h>
