/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "SensorSharing"
 * 	found in "./SensorSharing.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#ifndef	_DetectedPTCType_H_
#define	_DetectedPTCType_H_


#include <asn_application.h>

/* Including external dependencies */
#include <NativeEnumerated.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Dependencies */
typedef enum DetectedPTCType {
	DetectedPTCType_unknown	= 0,
	DetectedPTCType_unknown_movable	= 1,
	DetectedPTCType_unknown_unmovable	= 2,
	DetectedPTCType_car	= 3,
	DetectedPTCType_van	= 4,
	DetectedPTCType_truck	= 5,
	DetectedPTCType_bus	= 6,
	DetectedPTCType_cyclist	= 7,
	DetectedPTCType_motorcyclist	= 8,
	DetectedPTCType_tricyclist	= 9,
	DetectedPTCType_pedestrian	= 10
	/*
	 * Enumeration is extensible
	 */
} e_DetectedPTCType;

/* DetectedPTCType */
typedef long	 DetectedPTCType_t;

/* Implementation */
extern asn_per_constraints_t asn_PER_type_DetectedPTCType_constr_1;
extern asn_TYPE_descriptor_t asn_DEF_DetectedPTCType;
extern const asn_INTEGER_specifics_t asn_SPC_DetectedPTCType_specs_1;
asn_struct_free_f DetectedPTCType_free;
asn_struct_print_f DetectedPTCType_print;
asn_constr_check_f DetectedPTCType_constraint;
ber_type_decoder_f DetectedPTCType_decode_ber;
der_type_encoder_f DetectedPTCType_encode_der;
xer_type_decoder_f DetectedPTCType_decode_xer;
xer_type_encoder_f DetectedPTCType_encode_xer;
oer_type_decoder_f DetectedPTCType_decode_oer;
oer_type_encoder_f DetectedPTCType_encode_oer;
per_type_decoder_f DetectedPTCType_decode_uper;
per_type_encoder_f DetectedPTCType_encode_uper;

#ifdef __cplusplus
}
#endif

#endif	/* _DetectedPTCType_H_ */
#include <asn_internal.h>
