/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "DefAcceleration"
 * 	found in "./DefAcceleration.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#ifndef	_Acceleration_H_
#define	_Acceleration_H_


#include <asn_application.h>

/* Including external dependencies */
#include <NativeInteger.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Acceleration */
typedef long	 Acceleration_t;

/* Implementation */
extern asn_per_constraints_t asn_PER_type_Acceleration_constr_1;
extern asn_TYPE_descriptor_t asn_DEF_Acceleration;
asn_struct_free_f Acceleration_free;
asn_struct_print_f Acceleration_print;
asn_constr_check_f Acceleration_constraint;
ber_type_decoder_f Acceleration_decode_ber;
der_type_encoder_f Acceleration_encode_der;
xer_type_decoder_f Acceleration_decode_xer;
xer_type_encoder_f Acceleration_encode_xer;
oer_type_decoder_f Acceleration_decode_oer;
oer_type_encoder_f Acceleration_encode_oer;
per_type_decoder_f Acceleration_decode_uper;
per_type_encoder_f Acceleration_encode_uper;

#ifdef __cplusplus
}
#endif

#endif	/* _Acceleration_H_ */
#include <asn_internal.h>
