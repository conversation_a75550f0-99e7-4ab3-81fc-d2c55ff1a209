/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "OPS"
 * 	found in "./OPS.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#ifndef	_AIParamInfo_H_
#define	_AIParamInfo_H_


#include <asn_application.h>

/* Including external dependencies */
#include <NativeInteger.h>
#include "AIType.h"
#include <BOOLEAN.h>
#include "AIParamType.h"
#include <OCTET_STRING.h>
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* AIParamInfo */
typedef struct AIParamInfo {
	long	 chID;
	AIType_t	 type;
	BOOLEAN_t	 bEnable;
	AIParamType_t	 paramtype;
	OCTET_STRING_t	 info;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} AIParamInfo_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_AIParamInfo;
extern asn_SEQUENCE_specifics_t asn_SPC_AIParamInfo_specs_1;
extern asn_TYPE_member_t asn_MBR_AIParamInfo_1[5];

#ifdef __cplusplus
}
#endif

#endif	/* _AIParamInfo_H_ */
#include <asn_internal.h>
