/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "PAM"
 * 	found in "./PAM.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#ifndef	_ParkingSlot_H_
#define	_ParkingSlot_H_


#include <asn_application.h>

/* Including external dependencies */
#include <NativeInteger.h>
#include "DescriptiveName.h"
#include "ParkingType.h"
#include "SlotStatus.h"
#include "ParkingSpaceTheta.h"
#include "ParkingLock.h"
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Forward declarations */
struct ParkingSlotPosition;

/* ParkingSlot */
typedef struct ParkingSlot {
	long	 slotID;
	struct ParkingSlotPosition	*position	/* OPTIONAL */;
	DescriptiveName_t	*sign	/* OPTIONAL */;
	ParkingType_t	 parkingType;
	SlotStatus_t	 status;
	ParkingSpaceTheta_t	 parkingSpaceTheta;
	ParkingLock_t	 parkingLock;
	/*
	 * This type is extensible,
	 * possible extensions are below.
	 */
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} ParkingSlot_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_ParkingSlot;
extern asn_SEQUENCE_specifics_t asn_SPC_ParkingSlot_specs_1;
extern asn_TYPE_member_t asn_MBR_ParkingSlot_1[7];

#ifdef __cplusplus
}
#endif

/* Referred external types */
#include "ParkingSlotPosition.h"

#endif	/* _ParkingSlot_H_ */
#include <asn_internal.h>
