/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "VPM"
 * 	found in "./VPM.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#ifndef	_ApplicationParameter_H_
#define	_ApplicationParameter_H_


#include <asn_application.h>

/* Including external dependencies */
#include "PaymentEntityID.h"
#include "PaymentInfo.h"
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* ApplicationParameter */
typedef struct ApplicationParameter {
	PaymentEntityID_t	 pid;
	PaymentInfo_t	 paymentInfo;
	/*
	 * This type is extensible,
	 * possible extensions are below.
	 */
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} ApplicationParameter_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_ApplicationParameter;
extern asn_SEQUENCE_specifics_t asn_SPC_ApplicationParameter_specs_1;
extern asn_TYPE_member_t asn_MBR_ApplicationParameter_1[2];

#ifdef __cplusplus
}
#endif

#endif	/* _ApplicationParameter_H_ */
#include <asn_internal.h>
