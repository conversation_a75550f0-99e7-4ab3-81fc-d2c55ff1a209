/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "OPS"
 * 	found in "./OPS.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#ifndef	_IREType_H_
#define	_IREType_H_


#include <asn_application.h>

/* Including external dependencies */
#include <NativeEnumerated.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Dependencies */
typedef enum IREType {
	IREType_radar	= 0,
	IREType_signal	= 1,
	IREType_ipc	= 2,
	IREType_rsu	= 3,
	IREType_scaler	= 4
	/*
	 * Enumeration is extensible
	 */
} e_IREType;

/* IREType */
typedef long	 IREType_t;

/* Implementation */
extern asn_per_constraints_t asn_PER_type_IREType_constr_1;
extern asn_TYPE_descriptor_t asn_DEF_IREType;
extern const asn_INTEGER_specifics_t asn_SPC_IREType_specs_1;
asn_struct_free_f IREType_free;
asn_struct_print_f IREType_print;
asn_constr_check_f IREType_constraint;
ber_type_decoder_f IREType_decode_ber;
der_type_encoder_f IREType_encode_der;
xer_type_decoder_f IREType_decode_xer;
xer_type_encoder_f IREType_encode_xer;
oer_type_decoder_f IREType_decode_oer;
oer_type_encoder_f IREType_encode_oer;
per_type_decoder_f IREType_decode_uper;
per_type_encoder_f IREType_encode_uper;

#ifdef __cplusplus
}
#endif

#endif	/* _IREType_H_ */
#include <asn_internal.h>
