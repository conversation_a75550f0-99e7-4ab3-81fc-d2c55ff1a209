/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "RSI"
 * 	found in "./RSI.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#ifndef	_AuxiliarySignDirection_H_
#define	_AuxiliarySignDirection_H_


#include <asn_application.h>

/* Including external dependencies */
#include <NativeEnumerated.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Dependencies */
typedef enum AuxiliarySignDirection {
	AuxiliarySignDirection_straight	= 0,
	AuxiliarySignDirection_leftAndRight	= 1,
	AuxiliarySignDirection_right	= 2,
	AuxiliarySignDirection_left	= 3,
	AuxiliarySignDirection_leftFrontTurn	= 4,
	AuxiliarySignDirection_rightFronTurn	= 5,
	AuxiliarySignDirection_rightRearTurn	= 6,
	AuxiliarySignDirection_leftRearTurn	= 7
	/*
	 * Enumeration is extensible
	 */
} e_AuxiliarySignDirection;

/* AuxiliarySignDirection */
typedef long	 AuxiliarySignDirection_t;

/* Implementation */
extern asn_per_constraints_t asn_PER_type_AuxiliarySignDirection_constr_1;
extern asn_TYPE_descriptor_t asn_DEF_AuxiliarySignDirection;
extern const asn_INTEGER_specifics_t asn_SPC_AuxiliarySignDirection_specs_1;
asn_struct_free_f AuxiliarySignDirection_free;
asn_struct_print_f AuxiliarySignDirection_print;
asn_constr_check_f AuxiliarySignDirection_constraint;
ber_type_decoder_f AuxiliarySignDirection_decode_ber;
der_type_encoder_f AuxiliarySignDirection_encode_der;
xer_type_decoder_f AuxiliarySignDirection_decode_xer;
xer_type_encoder_f AuxiliarySignDirection_encode_xer;
oer_type_decoder_f AuxiliarySignDirection_decode_oer;
oer_type_encoder_f AuxiliarySignDirection_encode_oer;
per_type_decoder_f AuxiliarySignDirection_decode_uper;
per_type_encoder_f AuxiliarySignDirection_encode_uper;

#ifdef __cplusplus
}
#endif

#endif	/* _AuxiliarySignDirection_H_ */
#include <asn_internal.h>
