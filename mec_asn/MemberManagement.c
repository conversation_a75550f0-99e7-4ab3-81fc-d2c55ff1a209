/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "Platooning"
 * 	found in "./Platooning.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#include "MemberManagement.h"

static int
memb_capacity_constraint_1(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	long value;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	value = *(const long *)sptr;
	
	if((value >= 1 && value <= 32)) {
		/* Constraint check succeeded */
		return 0;
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static asn_oer_constraints_t asn_OER_memb_capacity_constr_5 CC_NOTUSED = {
	{ 1, 1 }	/* (1..32) */,
	-1};
static asn_per_constraints_t asn_PER_memb_capacity_constr_5 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 5,  5,  1,  32 }	/* (1..32) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
asn_TYPE_member_t asn_MBR_MemberManagement_1[] = {
	{ ATF_NOFLAGS, 0, offsetof(struct MemberManagement, memberList),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_MemberList,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"memberList"
		},
	{ ATF_POINTER, 2, offsetof(struct MemberManagement, joiningList),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_MemberList,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"joiningList"
		},
	{ ATF_POINTER, 1, offsetof(struct MemberManagement, leavingList),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_MemberList,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"leavingList"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct MemberManagement, capacity),
		(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NativeInteger,
		0,
		{ &asn_OER_memb_capacity_constr_5, &asn_PER_memb_capacity_constr_5,  memb_capacity_constraint_1 },
		0, 0, /* No default value */
		"capacity"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct MemberManagement, openToJoin),
		(ASN_TAG_CLASS_CONTEXT | (4 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_BOOLEAN,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"openToJoin"
		},
};
static const int asn_MAP_MemberManagement_oms_1[] = { 1, 2 };
static const ber_tlv_tag_t asn_DEF_MemberManagement_tags_1[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_MemberManagement_tag2el_1[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* memberList */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* joiningList */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 }, /* leavingList */
    { (ASN_TAG_CLASS_CONTEXT | (3 << 2)), 3, 0, 0 }, /* capacity */
    { (ASN_TAG_CLASS_CONTEXT | (4 << 2)), 4, 0, 0 } /* openToJoin */
};
asn_SEQUENCE_specifics_t asn_SPC_MemberManagement_specs_1 = {
	sizeof(struct MemberManagement),
	offsetof(struct MemberManagement, _asn_ctx),
	asn_MAP_MemberManagement_tag2el_1,
	5,	/* Count of tags in the map */
	asn_MAP_MemberManagement_oms_1,	/* Optional members */
	2, 0,	/* Root/Additions */
	5,	/* First extension addition */
};
asn_TYPE_descriptor_t asn_DEF_MemberManagement = {
	"MemberManagement",
	"MemberManagement",
	&asn_OP_SEQUENCE,
	asn_DEF_MemberManagement_tags_1,
	sizeof(asn_DEF_MemberManagement_tags_1)
		/sizeof(asn_DEF_MemberManagement_tags_1[0]), /* 1 */
	asn_DEF_MemberManagement_tags_1,	/* Same as above */
	sizeof(asn_DEF_MemberManagement_tags_1)
		/sizeof(asn_DEF_MemberManagement_tags_1[0]), /* 1 */
	{ 0, 0, SEQUENCE_constraint },
	asn_MBR_MemberManagement_1,
	5,	/* Elements count */
	&asn_SPC_MemberManagement_specs_1	/* Additional specs */
};

