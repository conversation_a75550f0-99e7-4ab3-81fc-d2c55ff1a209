/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "GoEMEC"
 * 	found in "./GoEMEC.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#ifndef	_LoginAck_H_
#define	_LoginAck_H_


#include <asn_application.h>

/* Including external dependencies */
#include "ErrorCode.h"
#include <OCTET_STRING.h>
#include <NativeInteger.h>
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* LoginAck */
typedef struct LoginAck {
	ErrorCode_t	 errorCode;
	OCTET_STRING_t	 userName;
	long	 id;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} LoginAck_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_LoginAck;
extern asn_SEQUENCE_specifics_t asn_SPC_LoginAck_specs_1;
extern asn_TYPE_member_t asn_MBR_LoginAck_1[3];

#ifdef __cplusplus
}
#endif

#endif	/* _LoginAck_H_ */
#include <asn_internal.h>
