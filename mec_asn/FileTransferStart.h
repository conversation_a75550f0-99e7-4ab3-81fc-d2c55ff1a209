/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "OPS"
 * 	found in "./OPS.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#ifndef	_FileTransferStart_H_
#define	_FileTransferStart_H_


#include <asn_application.h>

/* Including external dependencies */
#include "FileTransferType.h"
#include <NativeInteger.h>
#include "FileType.h"
#include <IA5String.h>
#include "Name.h"
#include "TextUTF8.h"
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* FileTransferStart */
typedef struct FileTransferStart {
	FileTransferType_t	 transType;
	long	 fileId;
	FileType_t	 fileType;
	IA5String_t	 url;
	Name_t	 name;
	TextUTF8_t	 describe;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} FileTransferStart_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_FileTransferStart;
extern asn_SEQUENCE_specifics_t asn_SPC_FileTransferStart_specs_1;
extern asn_TYPE_member_t asn_MBR_FileTransferStart_1[6];

#ifdef __cplusplus
}
#endif

#endif	/* _FileTransferStart_H_ */
#include <asn_internal.h>
