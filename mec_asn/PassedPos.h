/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "VPM"
 * 	found in "./VPM.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#ifndef	_PassedPos_H_
#define	_PassedPos_H_


#include <asn_application.h>

/* Including external dependencies */
#include "TollingPos.h"
#include "DDateTime.h"
#include <OCTET_STRING.h>
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* PassedPos */
typedef struct PassedPos {
	TollingPos_t	 tollingPos;
	DDateTime_t	 tollingTime;
	OCTET_STRING_t	*tollingAmount	/* OPTIONAL */;
	/*
	 * This type is extensible,
	 * possible extensions are below.
	 */
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} PassedPos_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_PassedPos;
extern asn_SEQUENCE_specifics_t asn_SPC_PassedPos_specs_1;
extern asn_TYPE_member_t asn_MBR_PassedPos_1[3];

#ifdef __cplusplus
}
#endif

#endif	/* _PassedPos_H_ */
#include <asn_internal.h>
