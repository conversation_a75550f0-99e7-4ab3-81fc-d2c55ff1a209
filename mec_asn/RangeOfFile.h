/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "VPM"
 * 	found in "./VPM.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#ifndef	_RangeOfFile_H_
#define	_RangeOfFile_H_


#include <asn_application.h>

/* Including external dependencies */
#include <NativeInteger.h>
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* RangeOfFile */
typedef struct RangeOfFile {
	long	 offset;
	long	 length;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} RangeOfFile_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_RangeOfFile;
extern asn_SEQUENCE_specifics_t asn_SPC_RangeOfFile_specs_1;
extern asn_TYPE_member_t asn_MBR_RangeOfFile_1[2];

#ifdef __cplusplus
}
#endif

#endif	/* _RangeOfFile_H_ */
#include <asn_internal.h>
