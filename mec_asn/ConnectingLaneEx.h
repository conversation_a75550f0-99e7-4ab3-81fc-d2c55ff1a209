/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "Map"
 * 	found in "./Map.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#ifndef	_ConnectingLaneEx_H_
#define	_ConnectingLaneEx_H_


#include <asn_application.h>

/* Including external dependencies */
#include "SectionId.h"
#include "LaneRefID.h"
#include "LaneWidth.h"
#include <BOOLEAN.h>
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Forward declarations */
struct PointList;

/* ConnectingLaneEx */
typedef struct ConnectingLaneEx {
	SectionId_t	 target_section;
	LaneRefID_t	 target_lane;
	LaneWidth_t	*connectingLaneWidth	/* OPTIONAL */;
	struct PointList	*connectingLanePoints	/* OPTIONAL */;
	BOOLEAN_t	*isolatedConnectingLane	/* OPTIONAL */;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} ConnectingLaneEx_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_ConnectingLaneEx;
extern asn_SEQUENCE_specifics_t asn_SPC_ConnectingLaneEx_specs_1;
extern asn_TYPE_member_t asn_MBR_ConnectingLaneEx_1[5];

#ifdef __cplusplus
}
#endif

/* Referred external types */
#include "PointList.h"

#endif	/* _ConnectingLaneEx_H_ */
#include <asn_internal.h>
