/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "OPS"
 * 	found in "./OPS.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#include "AlarmInfo.h"

static asn_oer_constraints_t asn_OER_type_AlarmInfo_constr_1 CC_NOTUSED = {
	{ 0, 0 },
	-1};
asn_per_constraints_t asn_PER_type_AlarmInfo_constr_1 CC_NOTUSED = {
	{ APC_CONSTRAINED | APC_EXTENSIBLE,  3,  3,  0,  6 }	/* (0..6,...) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
asn_TYPE_member_t asn_MBR_AlarmInfo_1[] = {
	{ ATF_NOFLAGS, 0, offsetof(struct AlarmInfo, choice.cpuinfo),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_CpuInfo,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"cpuinfo"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct AlarmInfo, choice.meminfo),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_MemInfo,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"meminfo"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct AlarmInfo, choice.diskInfo),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_DiskInfo,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"diskInfo"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct AlarmInfo, choice.netInfo),
		(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NetInfo,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"netInfo"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct AlarmInfo, choice.ireInfos),
		(ASN_TAG_CLASS_CONTEXT | (4 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_IREList,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"ireInfos"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct AlarmInfo, choice.threadInfos),
		(ASN_TAG_CLASS_CONTEXT | (5 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_ThreadList,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"threadInfos"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct AlarmInfo, choice.appInfo),
		(ASN_TAG_CLASS_CONTEXT | (6 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_AppInfo,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"appInfo"
		},
};
static const asn_TYPE_tag2member_t asn_MAP_AlarmInfo_tag2el_1[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* cpuinfo */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* meminfo */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 }, /* diskInfo */
    { (ASN_TAG_CLASS_CONTEXT | (3 << 2)), 3, 0, 0 }, /* netInfo */
    { (ASN_TAG_CLASS_CONTEXT | (4 << 2)), 4, 0, 0 }, /* ireInfos */
    { (ASN_TAG_CLASS_CONTEXT | (5 << 2)), 5, 0, 0 }, /* threadInfos */
    { (ASN_TAG_CLASS_CONTEXT | (6 << 2)), 6, 0, 0 } /* appInfo */
};
asn_CHOICE_specifics_t asn_SPC_AlarmInfo_specs_1 = {
	sizeof(struct AlarmInfo),
	offsetof(struct AlarmInfo, _asn_ctx),
	offsetof(struct AlarmInfo, present),
	sizeof(((struct AlarmInfo *)0)->present),
	asn_MAP_AlarmInfo_tag2el_1,
	7,	/* Count of tags in the map */
	0, 0,
	7	/* Extensions start */
};
asn_TYPE_descriptor_t asn_DEF_AlarmInfo = {
	"AlarmInfo",
	"AlarmInfo",
	&asn_OP_CHOICE,
	0,	/* No effective tags (pointer) */
	0,	/* No effective tags (count) */
	0,	/* No tags (pointer) */
	0,	/* No tags (count) */
	{ &asn_OER_type_AlarmInfo_constr_1, &asn_PER_type_AlarmInfo_constr_1, CHOICE_constraint },
	asn_MBR_AlarmInfo_1,
	7,	/* Elements count */
	&asn_SPC_AlarmInfo_specs_1	/* Additional specs */
};

