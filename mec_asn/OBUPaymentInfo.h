/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "VPM"
 * 	found in "./VPM.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#ifndef	_OBUPaymentInfo_H_
#define	_OBUPaymentInfo_H_


#include <asn_application.h>

/* Including external dependencies */
#include "OBUPaymentInfoType1.h"
#include <constr_CHOICE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Dependencies */
typedef enum OBUPaymentInfo_PR {
	OBUPaymentInfo_PR_NOTHING,	/* No components present */
	OBUPaymentInfo_PR_obuPaymentInfoType1
	/* Extensions may appear below */
	
} OBUPaymentInfo_PR;

/* OBUPaymentInfo */
typedef struct OBUPaymentInfo {
	OBUPaymentInfo_PR present;
	union OBUPaymentInfo_u {
		OBUPaymentInfoType1_t	 obuPaymentInfoType1;
		/*
		 * This type is extensible,
		 * possible extensions are below.
		 */
	} choice;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} OBUPaymentInfo_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_OBUPaymentInfo;
extern asn_CHOICE_specifics_t asn_SPC_OBUPaymentInfo_specs_1;
extern asn_TYPE_member_t asn_MBR_OBUPaymentInfo_1[1];
extern asn_per_constraints_t asn_PER_type_OBUPaymentInfo_constr_1;

#ifdef __cplusplus
}
#endif

#endif	/* _OBUPaymentInfo_H_ */
#include <asn_internal.h>
