/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "PSM"
 * 	found in "./PSM.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#ifndef	_PropelledInformation_H_
#define	_PropelledInformation_H_


#include <asn_application.h>

/* Including external dependencies */
#include "HumanPropelledType.h"
#include "AnimalPropelledType.h"
#include "MotorizedPropelledType.h"
#include <constr_CHOICE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Dependencies */
typedef enum PropelledInformation_PR {
	PropelledInformation_PR_NOTHING,	/* No components present */
	PropelledInformation_PR_human,
	PropelledInformation_PR_animal,
	PropelledInformation_PR_motor
	/* Extensions may appear below */
	
} PropelledInformation_PR;

/* PropelledInformation */
typedef struct PropelledInformation {
	PropelledInformation_PR present;
	union PropelledInformation_u {
		HumanPropelledType_t	 human;
		AnimalPropelledType_t	 animal;
		MotorizedPropelledType_t	 motor;
		/*
		 * This type is extensible,
		 * possible extensions are below.
		 */
	} choice;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} PropelledInformation_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_PropelledInformation;
extern asn_CHOICE_specifics_t asn_SPC_PropelledInformation_specs_1;
extern asn_TYPE_member_t asn_MBR_PropelledInformation_1[3];
extern asn_per_constraints_t asn_PER_type_PropelledInformation_constr_1;

#ifdef __cplusplus
}
#endif

#endif	/* _PropelledInformation_H_ */
#include <asn_internal.h>
