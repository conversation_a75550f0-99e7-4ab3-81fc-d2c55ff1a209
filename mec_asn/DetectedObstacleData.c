/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "SensorSharing"
 * 	found in "./SensorSharing.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#include "DetectedObstacleData.h"

static int
memb_obsId_constraint_1(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	long value;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	value = *(const long *)sptr;
	
	if((value >= 0 && value <= 65535)) {
		/* Constraint check succeeded */
		return 0;
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_tracking_constraint_1(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	long value;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	value = *(const long *)sptr;
	
	if((value >= 1 && value <= 65535)) {
		/* Constraint check succeeded */
		return 0;
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static asn_oer_constraints_t asn_OER_memb_obsId_constr_4 CC_NOTUSED = {
	{ 2, 1 }	/* (0..65535) */,
	-1};
static asn_per_constraints_t asn_PER_memb_obsId_constr_4 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 16,  16,  0,  65535 }	/* (0..65535) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
static asn_oer_constraints_t asn_OER_memb_tracking_constr_18 CC_NOTUSED = {
	{ 2, 1 }	/* (1..65535) */,
	-1};
static asn_per_constraints_t asn_PER_memb_tracking_constr_18 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (1..65535) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
asn_TYPE_member_t asn_MBR_DetectedObstacleData_1[] = {
	{ ATF_NOFLAGS, 0, offsetof(struct DetectedObstacleData, obsType),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_ObstacleType,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"obsType"
		},
	{ ATF_POINTER, 1, offsetof(struct DetectedObstacleData, objTypeConfidence),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_Confidence,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"objTypeConfidence"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct DetectedObstacleData, obsId),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NativeInteger,
		0,
		{ &asn_OER_memb_obsId_constr_4, &asn_PER_memb_obsId_constr_4,  memb_obsId_constraint_1 },
		0, 0, /* No default value */
		"obsId"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct DetectedObstacleData, source),
		(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_SourceType,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"source"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct DetectedObstacleData, secMark),
		(ASN_TAG_CLASS_CONTEXT | (4 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_DSecond,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"secMark"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct DetectedObstacleData, pos),
		(ASN_TAG_CLASS_CONTEXT | (5 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_PositionOffsetLLV,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"pos"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct DetectedObstacleData, posConfidence),
		(ASN_TAG_CLASS_CONTEXT | (6 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_PositionConfidenceSet,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"posConfidence"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct DetectedObstacleData, speed),
		(ASN_TAG_CLASS_CONTEXT | (7 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_Speed,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"speed"
		},
	{ ATF_POINTER, 1, offsetof(struct DetectedObstacleData, speedCfd),
		(ASN_TAG_CLASS_CONTEXT | (8 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_SpeedConfidence,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"speedCfd"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct DetectedObstacleData, heading),
		(ASN_TAG_CLASS_CONTEXT | (9 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_Heading,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"heading"
		},
	{ ATF_POINTER, 4, offsetof(struct DetectedObstacleData, headingCfd),
		(ASN_TAG_CLASS_CONTEXT | (10 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_HeadingConfidence,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"headingCfd"
		},
	{ ATF_POINTER, 3, offsetof(struct DetectedObstacleData, verSpeed),
		(ASN_TAG_CLASS_CONTEXT | (11 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_Speed,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"verSpeed"
		},
	{ ATF_POINTER, 2, offsetof(struct DetectedObstacleData, verSpeedConfidence),
		(ASN_TAG_CLASS_CONTEXT | (12 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_SpeedConfidence,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"verSpeedConfidence"
		},
	{ ATF_POINTER, 1, offsetof(struct DetectedObstacleData, accelSet),
		(ASN_TAG_CLASS_CONTEXT | (13 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_AccelerationSet4Way,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"accelSet"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct DetectedObstacleData, size),
		(ASN_TAG_CLASS_CONTEXT | (14 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_ObjectSize,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"size"
		},
	{ ATF_POINTER, 3, offsetof(struct DetectedObstacleData, objSizeConfidence),
		(ASN_TAG_CLASS_CONTEXT | (15 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_ObjectSizeConfidence,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"objSizeConfidence"
		},
	{ ATF_POINTER, 2, offsetof(struct DetectedObstacleData, tracking),
		(ASN_TAG_CLASS_CONTEXT | (16 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NativeInteger,
		0,
		{ &asn_OER_memb_tracking_constr_18, &asn_PER_memb_tracking_constr_18,  memb_tracking_constraint_1 },
		0, 0, /* No default value */
		"tracking"
		},
	{ ATF_POINTER, 1, offsetof(struct DetectedObstacleData, polygon),
		(ASN_TAG_CLASS_CONTEXT | (17 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_Polygon,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"polygon"
		},
};
static const int asn_MAP_DetectedObstacleData_oms_1[] = { 1, 8, 10, 11, 12, 13, 15, 16, 17 };
static const ber_tlv_tag_t asn_DEF_DetectedObstacleData_tags_1[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_DetectedObstacleData_tag2el_1[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* obsType */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* objTypeConfidence */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 }, /* obsId */
    { (ASN_TAG_CLASS_CONTEXT | (3 << 2)), 3, 0, 0 }, /* source */
    { (ASN_TAG_CLASS_CONTEXT | (4 << 2)), 4, 0, 0 }, /* secMark */
    { (ASN_TAG_CLASS_CONTEXT | (5 << 2)), 5, 0, 0 }, /* pos */
    { (ASN_TAG_CLASS_CONTEXT | (6 << 2)), 6, 0, 0 }, /* posConfidence */
    { (ASN_TAG_CLASS_CONTEXT | (7 << 2)), 7, 0, 0 }, /* speed */
    { (ASN_TAG_CLASS_CONTEXT | (8 << 2)), 8, 0, 0 }, /* speedCfd */
    { (ASN_TAG_CLASS_CONTEXT | (9 << 2)), 9, 0, 0 }, /* heading */
    { (ASN_TAG_CLASS_CONTEXT | (10 << 2)), 10, 0, 0 }, /* headingCfd */
    { (ASN_TAG_CLASS_CONTEXT | (11 << 2)), 11, 0, 0 }, /* verSpeed */
    { (ASN_TAG_CLASS_CONTEXT | (12 << 2)), 12, 0, 0 }, /* verSpeedConfidence */
    { (ASN_TAG_CLASS_CONTEXT | (13 << 2)), 13, 0, 0 }, /* accelSet */
    { (ASN_TAG_CLASS_CONTEXT | (14 << 2)), 14, 0, 0 }, /* size */
    { (ASN_TAG_CLASS_CONTEXT | (15 << 2)), 15, 0, 0 }, /* objSizeConfidence */
    { (ASN_TAG_CLASS_CONTEXT | (16 << 2)), 16, 0, 0 }, /* tracking */
    { (ASN_TAG_CLASS_CONTEXT | (17 << 2)), 17, 0, 0 } /* polygon */
};
asn_SEQUENCE_specifics_t asn_SPC_DetectedObstacleData_specs_1 = {
	sizeof(struct DetectedObstacleData),
	offsetof(struct DetectedObstacleData, _asn_ctx),
	asn_MAP_DetectedObstacleData_tag2el_1,
	18,	/* Count of tags in the map */
	asn_MAP_DetectedObstacleData_oms_1,	/* Optional members */
	9, 0,	/* Root/Additions */
	18,	/* First extension addition */
};
asn_TYPE_descriptor_t asn_DEF_DetectedObstacleData = {
	"DetectedObstacleData",
	"DetectedObstacleData",
	&asn_OP_SEQUENCE,
	asn_DEF_DetectedObstacleData_tags_1,
	sizeof(asn_DEF_DetectedObstacleData_tags_1)
		/sizeof(asn_DEF_DetectedObstacleData_tags_1[0]), /* 1 */
	asn_DEF_DetectedObstacleData_tags_1,	/* Same as above */
	sizeof(asn_DEF_DetectedObstacleData_tags_1)
		/sizeof(asn_DEF_DetectedObstacleData_tags_1[0]), /* 1 */
	{ 0, 0, SEQUENCE_constraint },
	asn_MBR_DetectedObstacleData_1,
	18,	/* Elements count */
	&asn_SPC_DetectedObstacleData_specs_1	/* Additional specs */
};

