/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "SignalPhaseAndTiming"
 * 	found in "./SignalPhaseAndTiming.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#include "LightState.h"

/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
static asn_oer_constraints_t asn_OER_type_LightState_constr_1 CC_NOTUSED = {
	{ 0, 0 },
	-1};
asn_per_constraints_t asn_PER_type_LightState_constr_1 CC_NOTUSED = {
	{ APC_CONSTRAINED | APC_EXTENSIBLE,  4,  4,  0,  8 }	/* (0..8,...) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
static const asn_INTEGER_enum_map_t asn_MAP_LightState_value2enum_1[] = {
	{ 0,	11,	"unavailable" },
	{ 1,	4,	"dark" },
	{ 2,	12,	"flashing-red" },
	{ 3,	3,	"red" },
	{ 4,	14,	"flashing-green" },
	{ 5,	16,	"permissive-green" },
	{ 6,	15,	"protected-green" },
	{ 7,	6,	"yellow" },
	{ 8,	15,	"flashing-yellow" }
	/* This list is extensible */
};
static const unsigned int asn_MAP_LightState_enum2value_1[] = {
	1,	/* dark(1) */
	4,	/* flashing-green(4) */
	2,	/* flashing-red(2) */
	8,	/* flashing-yellow(8) */
	5,	/* permissive-green(5) */
	6,	/* protected-green(6) */
	3,	/* red(3) */
	0,	/* unavailable(0) */
	7	/* yellow(7) */
	/* This list is extensible */
};
const asn_INTEGER_specifics_t asn_SPC_LightState_specs_1 = {
	asn_MAP_LightState_value2enum_1,	/* "tag" => N; sorted by tag */
	asn_MAP_LightState_enum2value_1,	/* N => "tag"; sorted by N */
	9,	/* Number of elements in the maps */
	10,	/* Extensions before this member */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_LightState_tags_1[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
asn_TYPE_descriptor_t asn_DEF_LightState = {
	"LightState",
	"LightState",
	&asn_OP_NativeEnumerated,
	asn_DEF_LightState_tags_1,
	sizeof(asn_DEF_LightState_tags_1)
		/sizeof(asn_DEF_LightState_tags_1[0]), /* 1 */
	asn_DEF_LightState_tags_1,	/* Same as above */
	sizeof(asn_DEF_LightState_tags_1)
		/sizeof(asn_DEF_LightState_tags_1[0]), /* 1 */
	{ &asn_OER_type_LightState_constr_1, &asn_PER_type_LightState_constr_1, NativeEnumerated_constraint },
	0, 0,	/* Defined elsewhere */
	&asn_SPC_LightState_specs_1	/* Additional specs */
};

