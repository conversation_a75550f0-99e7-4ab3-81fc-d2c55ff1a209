/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "MsgFrame"
 * 	found in "./MsgFrame.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#ifndef	_MsgCount_H_
#define	_MsgCount_H_


#include <asn_application.h>

/* Including external dependencies */
#include <NativeInteger.h>

#ifdef __cplusplus
extern "C" {
#endif

/* MsgCount */
typedef long	 MsgCount_t;

/* Implementation */
extern asn_per_constraints_t asn_PER_type_MsgCount_constr_1;
extern asn_TYPE_descriptor_t asn_DEF_MsgCount;
asn_struct_free_f MsgCount_free;
asn_struct_print_f MsgCount_print;
asn_constr_check_f MsgCount_constraint;
ber_type_decoder_f MsgCount_decode_ber;
der_type_encoder_f MsgCount_encode_der;
xer_type_decoder_f MsgCount_decode_xer;
xer_type_encoder_f MsgCount_encode_xer;
oer_type_decoder_f MsgCount_decode_oer;
oer_type_encoder_f MsgCount_encode_oer;
per_type_decoder_f MsgCount_decode_uper;
per_type_encoder_f MsgCount_encode_uper;

#ifdef __cplusplus
}
#endif

#endif	/* _MsgCount_H_ */
#include <asn_internal.h>
