/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "DefTime"
 * 	found in "./DefTime.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#ifndef	_DMinute_H_
#define	_DMinute_H_


#include <asn_application.h>

/* Including external dependencies */
#include <NativeInteger.h>

#ifdef __cplusplus
extern "C" {
#endif

/* DMinute */
typedef long	 DMinute_t;

/* Implementation */
extern asn_per_constraints_t asn_PER_type_DMinute_constr_1;
extern asn_TYPE_descriptor_t asn_DEF_DMinute;
asn_struct_free_f DMinute_free;
asn_struct_print_f DMinute_print;
asn_constr_check_f DMinute_constraint;
ber_type_decoder_f DMinute_decode_ber;
der_type_encoder_f DMinute_encode_der;
xer_type_decoder_f DMinute_decode_xer;
xer_type_encoder_f DMinute_encode_xer;
oer_type_decoder_f DMinute_decode_oer;
oer_type_encoder_f DMinute_encode_oer;
per_type_decoder_f DMinute_decode_uper;
per_type_encoder_f DMinute_encode_uper;

#ifdef __cplusplus
}
#endif

#endif	/* _DMinute_H_ */
#include <asn_internal.h>
