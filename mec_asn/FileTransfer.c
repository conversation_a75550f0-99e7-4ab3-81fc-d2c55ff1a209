/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "OPS"
 * 	found in "./OPS.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#include "FileTransfer.h"

static asn_oer_constraints_t asn_OER_type_FileTransfer_constr_1 CC_NOTUSED = {
	{ 0, 0 },
	-1};
asn_per_constraints_t asn_PER_type_FileTransfer_constr_1 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 1,  1,  0,  1 }	/* (0..1) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
asn_TYPE_member_t asn_MBR_FileTransfer_1[] = {
	{ ATF_NOFLAGS, 0, offsetof(struct FileTransfer, choice.start),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_FileTransferStart,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"start"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct FileTransfer, choice.ack),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_FileTransferAck,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"ack"
		},
};
static const asn_TYPE_tag2member_t asn_MAP_FileTransfer_tag2el_1[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* start */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 } /* ack */
};
asn_CHOICE_specifics_t asn_SPC_FileTransfer_specs_1 = {
	sizeof(struct FileTransfer),
	offsetof(struct FileTransfer, _asn_ctx),
	offsetof(struct FileTransfer, present),
	sizeof(((struct FileTransfer *)0)->present),
	asn_MAP_FileTransfer_tag2el_1,
	2,	/* Count of tags in the map */
	0, 0,
	-1	/* Extensions start */
};
asn_TYPE_descriptor_t asn_DEF_FileTransfer = {
	"FileTransfer",
	"FileTransfer",
	&asn_OP_CHOICE,
	0,	/* No effective tags (pointer) */
	0,	/* No effective tags (count) */
	0,	/* No tags (pointer) */
	0,	/* No tags (count) */
	{ &asn_OER_type_FileTransfer_constr_1, &asn_PER_type_FileTransfer_constr_1, CHOICE_constraint },
	asn_MBR_FileTransfer_1,
	2,	/* Elements count */
	&asn_SPC_FileTransfer_specs_1	/* Additional specs */
};

