/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "OPS"
 * 	found in "./OPS.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#ifndef	_AIType_H_
#define	_AIType_H_


#include <asn_application.h>

/* Including external dependencies */
#include <NativeEnumerated.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Dependencies */
typedef enum AIType {
	AIType_rayVideo	= 0,
	AIType_lpr	= 1
} e_AIType;

/* AIType */
typedef long	 AIType_t;

/* Implementation */
extern asn_per_constraints_t asn_PER_type_AIType_constr_1;
extern asn_TYPE_descriptor_t asn_DEF_AIType;
extern const asn_INTEGER_specifics_t asn_SPC_AIType_specs_1;
asn_struct_free_f AIType_free;
asn_struct_print_f AIType_print;
asn_constr_check_f AIType_constraint;
ber_type_decoder_f AIType_decode_ber;
der_type_encoder_f AIType_encode_der;
xer_type_decoder_f AIType_decode_xer;
xer_type_encoder_f AIType_encode_xer;
oer_type_decoder_f AIType_decode_oer;
oer_type_encoder_f AIType_encode_oer;
per_type_decoder_f AIType_decode_uper;
per_type_encoder_f AIType_encode_uper;

#ifdef __cplusplus
}
#endif

#endif	/* _AIType_H_ */
#include <asn_internal.h>
