/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "VIR"
 * 	found in "./VIR.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#include "PathPlanningPoint.h"

asn_TYPE_member_t asn_MBR_PathPlanningPoint_1[] = {
	{ ATF_POINTER, 1, offsetof(struct PathPlanningPoint, posInMap),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_ReferenceLink,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"posInMap"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct PathPlanningPoint, pos),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_PositionOffsetLLV,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"pos"
		},
	{ ATF_POINTER, 9, offsetof(struct PathPlanningPoint, posAccuracy),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_PositionConfidenceSet,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"posAccuracy"
		},
	{ ATF_POINTER, 8, offsetof(struct PathPlanningPoint, speed),
		(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_Speed,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"speed"
		},
	{ ATF_POINTER, 7, offsetof(struct PathPlanningPoint, speedCfd),
		(ASN_TAG_CLASS_CONTEXT | (4 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_SpeedConfidence,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"speedCfd"
		},
	{ ATF_POINTER, 6, offsetof(struct PathPlanningPoint, heading),
		(ASN_TAG_CLASS_CONTEXT | (5 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_Heading,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"heading"
		},
	{ ATF_POINTER, 5, offsetof(struct PathPlanningPoint, headingCfd),
		(ASN_TAG_CLASS_CONTEXT | (6 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_HeadingConfidence,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"headingCfd"
		},
	{ ATF_POINTER, 4, offsetof(struct PathPlanningPoint, accelSet),
		(ASN_TAG_CLASS_CONTEXT | (7 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_AccelerationSet4Way,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"accelSet"
		},
	{ ATF_POINTER, 3, offsetof(struct PathPlanningPoint, acc4WayConfidence),
		(ASN_TAG_CLASS_CONTEXT | (8 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_AccSet4WayConfidence,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"acc4WayConfidence"
		},
	{ ATF_POINTER, 2, offsetof(struct PathPlanningPoint, estimatedTime),
		(ASN_TAG_CLASS_CONTEXT | (9 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_TimeOffset,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"estimatedTime"
		},
	{ ATF_POINTER, 1, offsetof(struct PathPlanningPoint, timeConfidence),
		(ASN_TAG_CLASS_CONTEXT | (10 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_Confidence,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"timeConfidence"
		},
};
static const int asn_MAP_PathPlanningPoint_oms_1[] = { 0, 2, 3, 4, 5, 6, 7, 8, 9, 10 };
static const ber_tlv_tag_t asn_DEF_PathPlanningPoint_tags_1[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_PathPlanningPoint_tag2el_1[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* posInMap */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* pos */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 }, /* posAccuracy */
    { (ASN_TAG_CLASS_CONTEXT | (3 << 2)), 3, 0, 0 }, /* speed */
    { (ASN_TAG_CLASS_CONTEXT | (4 << 2)), 4, 0, 0 }, /* speedCfd */
    { (ASN_TAG_CLASS_CONTEXT | (5 << 2)), 5, 0, 0 }, /* heading */
    { (ASN_TAG_CLASS_CONTEXT | (6 << 2)), 6, 0, 0 }, /* headingCfd */
    { (ASN_TAG_CLASS_CONTEXT | (7 << 2)), 7, 0, 0 }, /* accelSet */
    { (ASN_TAG_CLASS_CONTEXT | (8 << 2)), 8, 0, 0 }, /* acc4WayConfidence */
    { (ASN_TAG_CLASS_CONTEXT | (9 << 2)), 9, 0, 0 }, /* estimatedTime */
    { (ASN_TAG_CLASS_CONTEXT | (10 << 2)), 10, 0, 0 } /* timeConfidence */
};
asn_SEQUENCE_specifics_t asn_SPC_PathPlanningPoint_specs_1 = {
	sizeof(struct PathPlanningPoint),
	offsetof(struct PathPlanningPoint, _asn_ctx),
	asn_MAP_PathPlanningPoint_tag2el_1,
	11,	/* Count of tags in the map */
	asn_MAP_PathPlanningPoint_oms_1,	/* Optional members */
	10, 0,	/* Root/Additions */
	11,	/* First extension addition */
};
asn_TYPE_descriptor_t asn_DEF_PathPlanningPoint = {
	"PathPlanningPoint",
	"PathPlanningPoint",
	&asn_OP_SEQUENCE,
	asn_DEF_PathPlanningPoint_tags_1,
	sizeof(asn_DEF_PathPlanningPoint_tags_1)
		/sizeof(asn_DEF_PathPlanningPoint_tags_1[0]), /* 1 */
	asn_DEF_PathPlanningPoint_tags_1,	/* Same as above */
	sizeof(asn_DEF_PathPlanningPoint_tags_1)
		/sizeof(asn_DEF_PathPlanningPoint_tags_1[0]), /* 1 */
	{ 0, 0, SEQUENCE_constraint },
	asn_MBR_PathPlanningPoint_1,
	11,	/* Elements count */
	&asn_SPC_PathPlanningPoint_specs_1	/* Additional specs */
};

