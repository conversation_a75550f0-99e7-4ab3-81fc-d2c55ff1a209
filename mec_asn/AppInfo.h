/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "OPS"
 * 	found in "./OPS.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#ifndef	_AppInfo_H_
#define	_AppInfo_H_


#include <asn_application.h>

/* Including external dependencies */
#include "AppType.h"
#include "TextUTF8.h"
#include "Status.h"
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* AppInfo */
typedef struct AppInfo {
	AppType_t	 appType;
	TextUTF8_t	 appName;
	Status_t	 appStatus;
	/*
	 * This type is extensible,
	 * possible extensions are below.
	 */
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} AppInfo_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_AppInfo;
extern asn_SEQUENCE_specifics_t asn_SPC_AppInfo_specs_1;
extern asn_TYPE_member_t asn_MBR_AppInfo_1[3];

#ifdef __cplusplus
}
#endif

#endif	/* _AppInfo_H_ */
#include <asn_internal.h>
