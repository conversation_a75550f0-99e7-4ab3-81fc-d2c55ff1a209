/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "VPM"
 * 	found in "./VPM.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#ifndef	_GetTollDataRq_H_
#define	_GetTollDataRq_H_


#include <asn_application.h>

/* Including external dependencies */
#include "RangeOfFile.h"
#include <NativeInteger.h>
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Forward declarations */
struct RangeOfFile;

/* GetTollDataRq */
typedef struct GetTollDataRq {
	RangeOfFile_t	 vehicleInfo;
	struct RangeOfFile	*tollInfo	/* OPTIONAL */;
	long	*keyIdForAC	/* OPTIONAL */;
	/*
	 * This type is extensible,
	 * possible extensions are below.
	 */
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} GetTollDataRq_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_GetTollDataRq;
extern asn_SEQUENCE_specifics_t asn_SPC_GetTollDataRq_specs_1;
extern asn_TYPE_member_t asn_MBR_GetTollDataRq_1[3];

#ifdef __cplusplus
}
#endif

/* Referred external types */
#include "RangeOfFile.h"

#endif	/* _GetTollDataRq_H_ */
#include <asn_internal.h>
