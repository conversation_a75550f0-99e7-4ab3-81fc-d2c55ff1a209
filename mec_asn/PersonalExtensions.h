/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "PSM"
 * 	found in "./PSM.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#ifndef	_PersonalExtensions_H_
#define	_PersonalExtensions_H_


#include <asn_application.h>

/* Including external dependencies */
#include "PersonalDeviceUsageState.h"
#include "PersonalAssistive.h"
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* PersonalExtensions */
typedef struct PersonalExtensions {
	PersonalDeviceUsageState_t	*useState	/* OPTIONAL */;
	PersonalAssistive_t	*assistType	/* OPTIONAL */;
	/*
	 * This type is extensible,
	 * possible extensions are below.
	 */
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} PersonalExtensions_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_PersonalExtensions;
extern asn_SEQUENCE_specifics_t asn_SPC_PersonalExtensions_specs_1;
extern asn_TYPE_member_t asn_MBR_PersonalExtensions_1[2];

#ifdef __cplusplus
}
#endif

#endif	/* _PersonalExtensions_H_ */
#include <asn_internal.h>
