/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "Map"
 * 	found in "./Map.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#include "ProhibitedZone.h"

static int
memb_non_motorVehicleProhibitedZones_constraint_1(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	size_t size;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	/* Determine the number of elements */
	size = _A_CSEQUENCE_FROM_VOID(sptr)->count;
	
	if((size >= 1 && size <= 16)) {
		/* Perform validation of the inner elements */
		return SEQUENCE_OF_constraint(td, sptr, ctfailcb, app_key);
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_gridLineMarkingProhibitedZones_constraint_1(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	size_t size;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	/* Determine the number of elements */
	size = _A_CSEQUENCE_FROM_VOID(sptr)->count;
	
	if((size >= 2 && size <= 16)) {
		/* Perform validation of the inner elements */
		return SEQUENCE_OF_constraint(td, sptr, ctfailcb, app_key);
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static asn_oer_constraints_t asn_OER_type_non_motorVehicleProhibitedZones_constr_3 CC_NOTUSED = {
	{ 0, 0 },
	-1	/* (SIZE(1..16)) */};
static asn_per_constraints_t asn_PER_type_non_motorVehicleProhibitedZones_constr_3 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 4,  4,  1,  16 }	/* (SIZE(1..16)) */,
	0, 0	/* No PER value map */
};
static asn_oer_constraints_t asn_OER_type_gridLineMarkingProhibitedZones_constr_5 CC_NOTUSED = {
	{ 0, 0 },
	-1	/* (SIZE(2..16)) */};
static asn_per_constraints_t asn_PER_type_gridLineMarkingProhibitedZones_constr_5 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 4,  4,  2,  16 }	/* (SIZE(2..16)) */,
	0, 0	/* No PER value map */
};
static asn_oer_constraints_t asn_OER_memb_non_motorVehicleProhibitedZones_constr_3 CC_NOTUSED = {
	{ 0, 0 },
	-1	/* (SIZE(1..16)) */};
static asn_per_constraints_t asn_PER_memb_non_motorVehicleProhibitedZones_constr_3 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 4,  4,  1,  16 }	/* (SIZE(1..16)) */,
	0, 0	/* No PER value map */
};
static asn_oer_constraints_t asn_OER_memb_gridLineMarkingProhibitedZones_constr_5 CC_NOTUSED = {
	{ 0, 0 },
	-1	/* (SIZE(2..16)) */};
static asn_per_constraints_t asn_PER_memb_gridLineMarkingProhibitedZones_constr_5 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 4,  4,  2,  16 }	/* (SIZE(2..16)) */,
	0, 0	/* No PER value map */
};
static asn_TYPE_member_t asn_MBR_non_motorVehicleProhibitedZones_3[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_Polygon,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_non_motorVehicleProhibitedZones_tags_3[] = {
	(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static asn_SET_OF_specifics_t asn_SPC_non_motorVehicleProhibitedZones_specs_3 = {
	sizeof(struct non_motorVehicleProhibitedZones),
	offsetof(struct non_motorVehicleProhibitedZones, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_non_motorVehicleProhibitedZones_3 = {
	"non-motorVehicleProhibitedZones",
	"non-motorVehicleProhibitedZones",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_non_motorVehicleProhibitedZones_tags_3,
	sizeof(asn_DEF_non_motorVehicleProhibitedZones_tags_3)
		/sizeof(asn_DEF_non_motorVehicleProhibitedZones_tags_3[0]) - 1, /* 1 */
	asn_DEF_non_motorVehicleProhibitedZones_tags_3,	/* Same as above */
	sizeof(asn_DEF_non_motorVehicleProhibitedZones_tags_3)
		/sizeof(asn_DEF_non_motorVehicleProhibitedZones_tags_3[0]), /* 2 */
	{ &asn_OER_type_non_motorVehicleProhibitedZones_constr_3, &asn_PER_type_non_motorVehicleProhibitedZones_constr_3, SEQUENCE_OF_constraint },
	asn_MBR_non_motorVehicleProhibitedZones_3,
	1,	/* Single element */
	&asn_SPC_non_motorVehicleProhibitedZones_specs_3	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_gridLineMarkingProhibitedZones_5[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_Polygon,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_gridLineMarkingProhibitedZones_tags_5[] = {
	(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static asn_SET_OF_specifics_t asn_SPC_gridLineMarkingProhibitedZones_specs_5 = {
	sizeof(struct gridLineMarkingProhibitedZones),
	offsetof(struct gridLineMarkingProhibitedZones, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_gridLineMarkingProhibitedZones_5 = {
	"gridLineMarkingProhibitedZones",
	"gridLineMarkingProhibitedZones",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_gridLineMarkingProhibitedZones_tags_5,
	sizeof(asn_DEF_gridLineMarkingProhibitedZones_tags_5)
		/sizeof(asn_DEF_gridLineMarkingProhibitedZones_tags_5[0]) - 1, /* 1 */
	asn_DEF_gridLineMarkingProhibitedZones_tags_5,	/* Same as above */
	sizeof(asn_DEF_gridLineMarkingProhibitedZones_tags_5)
		/sizeof(asn_DEF_gridLineMarkingProhibitedZones_tags_5[0]), /* 2 */
	{ &asn_OER_type_gridLineMarkingProhibitedZones_constr_5, &asn_PER_type_gridLineMarkingProhibitedZones_constr_5, SEQUENCE_OF_constraint },
	asn_MBR_gridLineMarkingProhibitedZones_5,
	1,	/* Single element */
	&asn_SPC_gridLineMarkingProhibitedZones_specs_5	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_ProhibitedZone_1[] = {
	{ ATF_POINTER, 3, offsetof(struct ProhibitedZone, centralCircleProhibitedZone),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_Polygon,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"centralCircleProhibitedZone"
		},
	{ ATF_POINTER, 2, offsetof(struct ProhibitedZone, non_motorVehicleProhibitedZones),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		0,
		&asn_DEF_non_motorVehicleProhibitedZones_3,
		0,
		{ &asn_OER_memb_non_motorVehicleProhibitedZones_constr_3, &asn_PER_memb_non_motorVehicleProhibitedZones_constr_3,  memb_non_motorVehicleProhibitedZones_constraint_1 },
		0, 0, /* No default value */
		"non-motorVehicleProhibitedZones"
		},
	{ ATF_POINTER, 1, offsetof(struct ProhibitedZone, gridLineMarkingProhibitedZones),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		0,
		&asn_DEF_gridLineMarkingProhibitedZones_5,
		0,
		{ &asn_OER_memb_gridLineMarkingProhibitedZones_constr_5, &asn_PER_memb_gridLineMarkingProhibitedZones_constr_5,  memb_gridLineMarkingProhibitedZones_constraint_1 },
		0, 0, /* No default value */
		"gridLineMarkingProhibitedZones"
		},
};
static const int asn_MAP_ProhibitedZone_oms_1[] = { 0, 1, 2 };
static const ber_tlv_tag_t asn_DEF_ProhibitedZone_tags_1[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_ProhibitedZone_tag2el_1[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* centralCircleProhibitedZone */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* non-motorVehicleProhibitedZones */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 } /* gridLineMarkingProhibitedZones */
};
asn_SEQUENCE_specifics_t asn_SPC_ProhibitedZone_specs_1 = {
	sizeof(struct ProhibitedZone),
	offsetof(struct ProhibitedZone, _asn_ctx),
	asn_MAP_ProhibitedZone_tag2el_1,
	3,	/* Count of tags in the map */
	asn_MAP_ProhibitedZone_oms_1,	/* Optional members */
	3, 0,	/* Root/Additions */
	3,	/* First extension addition */
};
asn_TYPE_descriptor_t asn_DEF_ProhibitedZone = {
	"ProhibitedZone",
	"ProhibitedZone",
	&asn_OP_SEQUENCE,
	asn_DEF_ProhibitedZone_tags_1,
	sizeof(asn_DEF_ProhibitedZone_tags_1)
		/sizeof(asn_DEF_ProhibitedZone_tags_1[0]), /* 1 */
	asn_DEF_ProhibitedZone_tags_1,	/* Same as above */
	sizeof(asn_DEF_ProhibitedZone_tags_1)
		/sizeof(asn_DEF_ProhibitedZone_tags_1[0]), /* 1 */
	{ 0, 0, SEQUENCE_constraint },
	asn_MBR_ProhibitedZone_1,
	3,	/* Elements count */
	&asn_SPC_ProhibitedZone_specs_1	/* Additional specs */
};

