/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "OPS"
 * 	found in "./OPS.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#ifndef	_NetInfo_H_
#define	_NetInfo_H_


#include <asn_application.h>

/* Including external dependencies */
#include <NativeInteger.h>
#include "DataRateByte.h"
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* NetInfo */
typedef struct NetInfo {
	long	 rx;
	long	 tx;
	DataRateByte_t	 rxByte;
	DataRateByte_t	 txByte;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} NetInfo_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_NetInfo;
extern asn_SEQUENCE_specifics_t asn_SPC_NetInfo_specs_1;
extern asn_TYPE_member_t asn_MBR_NetInfo_1[4];

#ifdef __cplusplus
}
#endif

#endif	/* _NetInfo_H_ */
#include <asn_internal.h>
