/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "OPS"
 * 	found in "./OPS.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#include "MemInfo.h"

asn_TYPE_member_t asn_MBR_MemInfo_1[] = {
	{ ATF_NOFLAGS, 0, offsetof(struct MemInfo, total),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_MemSize,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"total"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct MemInfo, used),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_MemSize,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"used"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct MemInfo, free),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_MemSize,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"free"
		},
};
static const ber_tlv_tag_t asn_DEF_MemInfo_tags_1[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_MemInfo_tag2el_1[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* total */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* used */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 } /* free */
};
asn_SEQUENCE_specifics_t asn_SPC_MemInfo_specs_1 = {
	sizeof(struct MemInfo),
	offsetof(struct MemInfo, _asn_ctx),
	asn_MAP_MemInfo_tag2el_1,
	3,	/* Count of tags in the map */
	0, 0, 0,	/* Optional elements (not needed) */
	-1,	/* First extension addition */
};
asn_TYPE_descriptor_t asn_DEF_MemInfo = {
	"MemInfo",
	"MemInfo",
	&asn_OP_SEQUENCE,
	asn_DEF_MemInfo_tags_1,
	sizeof(asn_DEF_MemInfo_tags_1)
		/sizeof(asn_DEF_MemInfo_tags_1[0]), /* 1 */
	asn_DEF_MemInfo_tags_1,	/* Same as above */
	sizeof(asn_DEF_MemInfo_tags_1)
		/sizeof(asn_DEF_MemInfo_tags_1[0]), /* 1 */
	{ 0, 0, SEQUENCE_constraint },
	asn_MBR_MemInfo_1,
	3,	/* Elements count */
	&asn_SPC_MemInfo_specs_1	/* Additional specs */
};

