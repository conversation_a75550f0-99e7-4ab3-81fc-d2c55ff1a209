/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "SensorSharing"
 * 	found in "./SensorSharing.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#include "DetectedPTCType.h"

/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
static asn_oer_constraints_t asn_OER_type_DetectedPTCType_constr_1 CC_NOTUSED = {
	{ 0, 0 },
	-1};
asn_per_constraints_t asn_PER_type_DetectedPTCType_constr_1 CC_NOTUSED = {
	{ APC_CONSTRAINED | APC_EXTENSIBLE,  4,  4,  0,  10 }	/* (0..10,...) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
static const asn_INTEGER_enum_map_t asn_MAP_DetectedPTCType_value2enum_1[] = {
	{ 0,	7,	"unknown" },
	{ 1,	15,	"unknown-movable" },
	{ 2,	17,	"unknown-unmovable" },
	{ 3,	3,	"car" },
	{ 4,	3,	"van" },
	{ 5,	5,	"truck" },
	{ 6,	3,	"bus" },
	{ 7,	7,	"cyclist" },
	{ 8,	12,	"motorcyclist" },
	{ 9,	10,	"tricyclist" },
	{ 10,	10,	"pedestrian" }
	/* This list is extensible */
};
static const unsigned int asn_MAP_DetectedPTCType_enum2value_1[] = {
	6,	/* bus(6) */
	3,	/* car(3) */
	7,	/* cyclist(7) */
	8,	/* motorcyclist(8) */
	10,	/* pedestrian(10) */
	9,	/* tricyclist(9) */
	5,	/* truck(5) */
	0,	/* unknown(0) */
	1,	/* unknown-movable(1) */
	2,	/* unknown-unmovable(2) */
	4	/* van(4) */
	/* This list is extensible */
};
const asn_INTEGER_specifics_t asn_SPC_DetectedPTCType_specs_1 = {
	asn_MAP_DetectedPTCType_value2enum_1,	/* "tag" => N; sorted by tag */
	asn_MAP_DetectedPTCType_enum2value_1,	/* N => "tag"; sorted by N */
	11,	/* Number of elements in the maps */
	12,	/* Extensions before this member */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_DetectedPTCType_tags_1[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
asn_TYPE_descriptor_t asn_DEF_DetectedPTCType = {
	"DetectedPTCType",
	"DetectedPTCType",
	&asn_OP_NativeEnumerated,
	asn_DEF_DetectedPTCType_tags_1,
	sizeof(asn_DEF_DetectedPTCType_tags_1)
		/sizeof(asn_DEF_DetectedPTCType_tags_1[0]), /* 1 */
	asn_DEF_DetectedPTCType_tags_1,	/* Same as above */
	sizeof(asn_DEF_DetectedPTCType_tags_1)
		/sizeof(asn_DEF_DetectedPTCType_tags_1[0]), /* 1 */
	{ &asn_OER_type_DetectedPTCType_constr_1, &asn_PER_type_DetectedPTCType_constr_1, NativeEnumerated_constraint },
	0, 0,	/* Defined elsewhere */
	&asn_SPC_DetectedPTCType_specs_1	/* Additional specs */
};

