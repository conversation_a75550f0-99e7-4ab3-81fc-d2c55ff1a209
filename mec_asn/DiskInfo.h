/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "OPS"
 * 	found in "./OPS.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#ifndef	_DiskInfo_H_
#define	_DiskInfo_H_


#include <asn_application.h>

/* Including external dependencies */
#include "MemSize.h"
#include <NativeInteger.h>
#include "DataRateKByte.h"
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* DiskInfo */
typedef struct DiskInfo {
	MemSize_t	 total;
	MemSize_t	 used;
	MemSize_t	 free;
	long	 tps;
	DataRateKByte_t	 write;
	DataRateKByte_t	 read;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} DiskInfo_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_DiskInfo;
extern asn_SEQUENCE_specifics_t asn_SPC_DiskInfo_specs_1;
extern asn_TYPE_member_t asn_MBR_DiskInfo_1[6];

#ifdef __cplusplus
}
#endif

#endif	/* _DiskInfo_H_ */
#include <asn_internal.h>
