/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "VPM"
 * 	found in "./VPM.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#ifndef	_ApduList_H_
#define	_ApduList_H_


#include <asn_application.h>

/* Including external dependencies */
#include <OCTET_STRING.h>
#include <asn_SEQUENCE_OF.h>
#include <constr_SEQUENCE_OF.h>

#ifdef __cplusplus
extern "C" {
#endif

/* ApduList */
typedef struct ApduList {
	A_SEQUENCE_OF(OCTET_STRING_t) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} ApduList_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_ApduList;
extern asn_SET_OF_specifics_t asn_SPC_ApduList_specs_1;
extern asn_TYPE_member_t asn_MBR_ApduList_1[1];

#ifdef __cplusplus
}
#endif

#endif	/* _ApduList_H_ */
#include <asn_internal.h>
