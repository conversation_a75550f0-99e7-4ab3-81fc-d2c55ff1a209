/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "OPS"
 * 	found in "./OPS.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#ifndef	_FileTransfer_H_
#define	_FileTransfer_H_


#include <asn_application.h>

/* Including external dependencies */
#include "FileTransferStart.h"
#include "FileTransferAck.h"
#include <constr_CHOICE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Dependencies */
typedef enum FileTransfer_PR {
	FileTransfer_PR_NOTHING,	/* No components present */
	FileTransfer_PR_start,
	FileTransfer_PR_ack
} FileTransfer_PR;

/* FileTransfer */
typedef struct FileTransfer {
	FileTransfer_PR present;
	union FileTransfer_u {
		FileTransferStart_t	 start;
		FileTransferAck_t	 ack;
	} choice;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} FileTransfer_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_FileTransfer;
extern asn_CHOICE_specifics_t asn_SPC_FileTransfer_specs_1;
extern asn_TYPE_member_t asn_MBR_FileTransfer_1[2];
extern asn_per_constraints_t asn_PER_type_FileTransfer_constr_1;

#ifdef __cplusplus
}
#endif

#endif	/* _FileTransfer_H_ */
#include <asn_internal.h>
