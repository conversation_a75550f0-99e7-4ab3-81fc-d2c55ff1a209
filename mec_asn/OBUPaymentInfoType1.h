/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "VPM"
 * 	found in "./VPM.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#ifndef	_OBUPaymentInfoType1_H_
#define	_OBUPaymentInfoType1_H_


#include <asn_application.h>

/* Including external dependencies */
#include "EquipmentClass.h"
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Forward declarations */
struct GBICCInfo;
struct SysInfo;
struct VehicleInfo;
struct PassedSitesInfo;

/* OBUPaymentInfoType1 */
typedef struct OBUPaymentInfoType1 {
	EquipmentClass_t	*equipmentClass	/* OPTIONAL */;
	struct GBICCInfo	*gbiCCInfo	/* OPTIONAL */;
	struct SysInfo	*sysInfo	/* OPTIONAL */;
	struct VehicleInfo	*vehicleInfo	/* OPTIONAL */;
	struct PassedSitesInfo	*passedSitesInfo	/* OPTIONAL */;
	/*
	 * This type is extensible,
	 * possible extensions are below.
	 */
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} OBUPaymentInfoType1_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_OBUPaymentInfoType1;
extern asn_SEQUENCE_specifics_t asn_SPC_OBUPaymentInfoType1_specs_1;
extern asn_TYPE_member_t asn_MBR_OBUPaymentInfoType1_1[5];

#ifdef __cplusplus
}
#endif

/* Referred external types */
#include "GBICCInfo.h"
#include "SysInfo.h"
#include "VehicleInfo.h"
#include "PassedSitesInfo.h"

#endif	/* _OBUPaymentInfoType1_H_ */
#include <asn_internal.h>
