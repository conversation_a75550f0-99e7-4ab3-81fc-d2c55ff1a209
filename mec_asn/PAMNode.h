/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "PAM"
 * 	found in "./PAM.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#ifndef	_PAMNode_H_
#define	_PAMNode_H_


#include <asn_application.h>

/* Including external dependencies */
#include "PAMNodeID.h"
#include "Position3D.h"
#include <NativeInteger.h>
#include "PAMNodeAttributes.h"
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Forward declarations */
struct PAMDriveList;

/* PAMNode */
typedef struct PAMNode {
	PAMNodeID_t	 id;
	Position3D_t	 refPos;
	long	*floor	/* OPTIONAL */;
	PAMNodeAttributes_t	*attributes	/* OPTIONAL */;
	struct PAMDriveList	*inDrives	/* OPTIONAL */;
	/*
	 * This type is extensible,
	 * possible extensions are below.
	 */
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} PAMNode_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_PAMNode;
extern asn_SEQUENCE_specifics_t asn_SPC_PAMNode_specs_1;
extern asn_TYPE_member_t asn_MBR_PAMNode_1[5];

#ifdef __cplusplus
}
#endif

/* Referred external types */
#include "PAMDriveList.h"

#endif	/* _PAMNode_H_ */
#include <asn_internal.h>
