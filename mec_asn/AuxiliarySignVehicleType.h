/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "RSI"
 * 	found in "./RSI.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#ifndef	_AuxiliarySignVehicleType_H_
#define	_AuxiliarySignVehicleType_H_


#include <asn_application.h>

/* Including external dependencies */
#include <BIT_STRING.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Dependencies */
typedef enum AuxiliarySignVehicleType {
	AuxiliarySignVehicleType_restrictedFromBus	= 0,
	AuxiliarySignVehicleType_vehicle	= 1,
	AuxiliarySignVehicleType_truck	= 2,
	AuxiliarySignVehicleType_tractor	= 3,
	AuxiliarySignVehicleType_private	= 4
} e_AuxiliarySignVehicleType;

/* AuxiliarySignVehicleType */
typedef BIT_STRING_t	 AuxiliarySignVehicleType_t;

/* Implementation */
extern asn_per_constraints_t asn_PER_type_AuxiliarySignVehicleType_constr_1;
extern asn_TYPE_descriptor_t asn_DEF_AuxiliarySignVehicleType;
asn_struct_free_f AuxiliarySignVehicleType_free;
asn_struct_print_f AuxiliarySignVehicleType_print;
asn_constr_check_f AuxiliarySignVehicleType_constraint;
ber_type_decoder_f AuxiliarySignVehicleType_decode_ber;
der_type_encoder_f AuxiliarySignVehicleType_encode_der;
xer_type_decoder_f AuxiliarySignVehicleType_decode_xer;
xer_type_encoder_f AuxiliarySignVehicleType_encode_xer;
oer_type_decoder_f AuxiliarySignVehicleType_decode_oer;
oer_type_encoder_f AuxiliarySignVehicleType_encode_oer;
per_type_decoder_f AuxiliarySignVehicleType_decode_uper;
per_type_encoder_f AuxiliarySignVehicleType_encode_uper;

#ifdef __cplusplus
}
#endif

#endif	/* _AuxiliarySignVehicleType_H_ */
#include <asn_internal.h>
