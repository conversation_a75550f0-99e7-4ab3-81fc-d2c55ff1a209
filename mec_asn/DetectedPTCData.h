/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "SensorSharing"
 * 	found in "./SensorSharing.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#ifndef	_DetectedPTCData_H_
#define	_DetectedPTCData_H_


#include <asn_application.h>

/* Including external dependencies */
#include "ParticipantData.h"
#include "DetectedPTCType.h"
#include "Confidence.h"
#include "TimeOffset.h"
#include <NativeInteger.h>
#include "MotorDataExtension.h"
#include "Non-motorDataExtension.h"
#include <constr_CHOICE.h>
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Dependencies */
typedef enum type_relatedExt_PR {
	type_relatedExt_PR_NOTHING,	/* No components present */
	type_relatedExt_PR_motorExt,
	type_relatedExt_PR_non_motorExt
	/* Extensions may appear below */
	
} type_relatedExt_PR;

/* Forward declarations */
struct ObjectSizeConfidence;
struct AccSet4WayConfidence;
struct PathHistory;
struct PlanningList;
struct Polygon;

/* DetectedPTCData */
typedef struct DetectedPTCData {
	ParticipantData_t	 ptc;
	struct ObjectSizeConfidence	*objSizeConfidence	/* OPTIONAL */;
	DetectedPTCType_t	*detectedPTCType	/* OPTIONAL */;
	Confidence_t	*typeConfidence	/* OPTIONAL */;
	struct AccSet4WayConfidence	*acc4WayConfidence	/* OPTIONAL */;
	TimeOffset_t	*statusDuration	/* OPTIONAL */;
	struct PathHistory	*pathHistory	/* OPTIONAL */;
	struct PlanningList	*planningList	/* OPTIONAL */;
	long	*tracking	/* OPTIONAL */;
	struct Polygon	*polygon	/* OPTIONAL */;
	struct type_relatedExt {
		type_relatedExt_PR present;
		union DetectedPTCData__type_relatedExt_u {
			MotorDataExtension_t	 motorExt;
			Non_motorDataExtension_t	 non_motorExt;
			/*
			 * This type is extensible,
			 * possible extensions are below.
			 */
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *type_relatedExt;
	/*
	 * This type is extensible,
	 * possible extensions are below.
	 */
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} DetectedPTCData_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_DetectedPTCData;
extern asn_SEQUENCE_specifics_t asn_SPC_DetectedPTCData_specs_1;
extern asn_TYPE_member_t asn_MBR_DetectedPTCData_1[11];

#ifdef __cplusplus
}
#endif

/* Referred external types */
#include "ObjectSizeConfidence.h"
#include "AccSet4WayConfidence.h"
#include "PathHistory.h"
#include "PlanningList.h"
#include "Polygon.h"

#endif	/* _DetectedPTCData_H_ */
#include <asn_internal.h>
