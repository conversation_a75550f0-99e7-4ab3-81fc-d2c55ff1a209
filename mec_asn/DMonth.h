/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "DefTime"
 * 	found in "./DefTime.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#ifndef	_DMonth_H_
#define	_DMonth_H_


#include <asn_application.h>

/* Including external dependencies */
#include <NativeInteger.h>

#ifdef __cplusplus
extern "C" {
#endif

/* DMonth */
typedef long	 DMonth_t;

/* Implementation */
extern asn_per_constraints_t asn_PER_type_DMonth_constr_1;
extern asn_TYPE_descriptor_t asn_DEF_DMonth;
asn_struct_free_f DMonth_free;
asn_struct_print_f DMonth_print;
asn_constr_check_f DMonth_constraint;
ber_type_decoder_f DMonth_decode_ber;
der_type_encoder_f DMonth_encode_der;
xer_type_decoder_f DMonth_decode_xer;
xer_type_encoder_f DMonth_encode_xer;
oer_type_decoder_f DMonth_decode_oer;
oer_type_encoder_f DMonth_encode_oer;
per_type_decoder_f DMonth_decode_uper;
per_type_encoder_f DMonth_encode_uper;

#ifdef __cplusplus
}
#endif

#endif	/* _DMonth_H_ */
#include <asn_internal.h>
