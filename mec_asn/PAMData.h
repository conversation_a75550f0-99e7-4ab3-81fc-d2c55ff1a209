/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "PAM"
 * 	found in "./PAM.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#ifndef	_PAMData_H_
#define	_PAMData_H_


#include <asn_application.h>

/* Including external dependencies */
#include "MsgCount.h"
#include "MinuteOfTheYear.h"
#include "ParkingLotInfo.h"
#include "PAMNodeList.h"
#include <asn_SEQUENCE_OF.h>
#include <constr_SEQUENCE_OF.h>
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Forward declarations */
struct ParkingGuide;

/* PAMData */
typedef struct PAMData {
	MsgCount_t	 msgCnt;
	MinuteOfTheYear_t	*timeStamp	/* OPTIONAL */;
	ParkingLotInfo_t	 parkingLotInfo;
	PAMNodeList_t	 pamNodes;
	struct parkingAreaGuidance {
		A_SEQUENCE_OF(struct ParkingGuide) list;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *parkingAreaGuidance;
	/*
	 * This type is extensible,
	 * possible extensions are below.
	 */
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} PAMData_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_PAMData;
extern asn_SEQUENCE_specifics_t asn_SPC_PAMData_specs_1;
extern asn_TYPE_member_t asn_MBR_PAMData_1[5];

#ifdef __cplusplus
}
#endif

/* Referred external types */
#include "ParkingGuide.h"

#endif	/* _PAMData_H_ */
#include <asn_internal.h>
