/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "SensorSharing"
 * 	found in "./SensorSharing.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#ifndef	_AccSet4WayConfidence_H_
#define	_AccSet4WayConfidence_H_


#include <asn_application.h>

/* Including external dependencies */
#include "AccConfidence.h"
#include "AngularVConfidence.h"
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* AccSet4WayConfidence */
typedef struct AccSet4WayConfidence {
	AccConfidence_t	 lonAccConfidence;
	AccConfidence_t	 latAccConfidence;
	AccConfidence_t	 vertAccConfidence;
	AngularVConfidence_t	 yawRateCon;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} AccSet4WayConfidence_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_AccSet4WayConfidence;
extern asn_SEQUENCE_specifics_t asn_SPC_AccSet4WayConfidence_specs_1;
extern asn_TYPE_member_t asn_MBR_AccSet4WayConfidence_1[4];

#ifdef __cplusplus
}
#endif

#endif	/* _AccSet4WayConfidence_H_ */
#include <asn_internal.h>
