/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "OPS"
 * 	found in "./OPS.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#ifndef	_Alarm_H_
#define	_Alarm_H_


#include <asn_application.h>

/* Including external dependencies */
#include "AlarmType.h"
#include "AlarmState.h"
#include <NativeInteger.h>
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Forward declarations */
struct AlarmInfo;

/* Alarm */
typedef struct Alarm {
	AlarmType_t	 type;
	AlarmState_t	 status;
	unsigned long	 timestamp;
	struct AlarmInfo	*info	/* OPTIONAL */;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} Alarm_t;

/* Implementation */
/* extern asn_TYPE_descriptor_t asn_DEF_timestamp_4;	// (Use -fall-defs-global to expose) */
extern asn_TYPE_descriptor_t asn_DEF_Alarm;
extern asn_SEQUENCE_specifics_t asn_SPC_Alarm_specs_1;
extern asn_TYPE_member_t asn_MBR_Alarm_1[4];

#ifdef __cplusplus
}
#endif

/* Referred external types */
#include "AlarmInfo.h"

#endif	/* _Alarm_H_ */
#include <asn_internal.h>
