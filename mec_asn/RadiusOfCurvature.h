/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "VehSafetyExt"
 * 	found in "./VehSafetyExt.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#ifndef	_RadiusOfCurvature_H_
#define	_RadiusOfCurvature_H_


#include <asn_application.h>

/* Including external dependencies */
#include <NativeInteger.h>

#ifdef __cplusplus
extern "C" {
#endif

/* RadiusOfCurvature */
typedef long	 RadiusOfCurvature_t;

/* Implementation */
extern asn_per_constraints_t asn_PER_type_RadiusOfCurvature_constr_1;
extern asn_TYPE_descriptor_t asn_DEF_RadiusOfCurvature;
asn_struct_free_f RadiusOfCurvature_free;
asn_struct_print_f RadiusOfCurvature_print;
asn_constr_check_f RadiusOfCurvature_constraint;
ber_type_decoder_f RadiusOfCurvature_decode_ber;
der_type_encoder_f RadiusOfCurvature_encode_der;
xer_type_decoder_f RadiusOfCurvature_decode_xer;
xer_type_encoder_f RadiusOfCurvature_encode_xer;
oer_type_decoder_f RadiusOfCurvature_decode_oer;
oer_type_encoder_f RadiusOfCurvature_encode_oer;
per_type_decoder_f RadiusOfCurvature_decode_uper;
per_type_encoder_f RadiusOfCurvature_encode_uper;

#ifdef __cplusplus
}
#endif

#endif	/* _RadiusOfCurvature_H_ */
#include <asn_internal.h>
