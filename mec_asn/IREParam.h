/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "OPS"
 * 	found in "./OPS.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#ifndef	_IREParam_H_
#define	_IREParam_H_


#include <asn_application.h>

/* Including external dependencies */
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* IREParam */
typedef struct IREParam {
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} IREParam_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_IREParam;
extern asn_SEQUENCE_specifics_t asn_SPC_IREParam_specs_1;

#ifdef __cplusplus
}
#endif

#endif	/* _IREParam_H_ */
#include <asn_internal.h>
