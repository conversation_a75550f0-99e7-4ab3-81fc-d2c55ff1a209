/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "OPS"
 * 	found in "./OPS.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#ifndef	_IREList_H_
#define	_IREList_H_


#include <asn_application.h>

/* Including external dependencies */
#include <asn_SEQUENCE_OF.h>
#include <constr_SEQUENCE_OF.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Forward declarations */
struct IREInfo;

/* IREList */
typedef struct IREList {
	A_SEQUENCE_OF(struct IREInfo) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} IREList_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_IREList;
extern asn_SET_OF_specifics_t asn_SPC_IREList_specs_1;
extern asn_TYPE_member_t asn_MBR_IREList_1[1];
extern asn_per_constraints_t asn_PER_type_IREList_constr_1;

#ifdef __cplusplus
}
#endif

/* Referred external types */
#include "IREInfo.h"

#endif	/* _IREList_H_ */
#include <asn_internal.h>
