/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "Platooning"
 * 	found in "./Platooning.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#ifndef	_MemberNode_H_
#define	_MemberNode_H_


#include <asn_application.h>

/* Including external dependencies */
#include <OCTET_STRING.h>
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* MemberNode */
typedef struct MemberNode {
	OCTET_STRING_t	 vid;
	/*
	 * This type is extensible,
	 * possible extensions are below.
	 */
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} MemberNode_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_MemberNode;
extern asn_SEQUENCE_specifics_t asn_SPC_MemberNode_specs_1;
extern asn_TYPE_member_t asn_MBR_MemberNode_1[1];

#ifdef __cplusplus
}
#endif

#endif	/* _MemberNode_H_ */
#include <asn_internal.h>
