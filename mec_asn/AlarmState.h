/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "OPS"
 * 	found in "./OPS.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#ifndef	_AlarmState_H_
#define	_AlarmState_H_


#include <asn_application.h>

/* Including external dependencies */
#include <NativeEnumerated.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Dependencies */
typedef enum AlarmState {
	AlarmState_off	= 0,
	AlarmState_on	= 1
} e_AlarmState;

/* AlarmState */
typedef long	 AlarmState_t;

/* Implementation */
extern asn_per_constraints_t asn_PER_type_AlarmState_constr_1;
extern asn_TYPE_descriptor_t asn_DEF_AlarmState;
extern const asn_INTEGER_specifics_t asn_SPC_AlarmState_specs_1;
asn_struct_free_f AlarmState_free;
asn_struct_print_f AlarmState_print;
asn_constr_check_f AlarmState_constraint;
ber_type_decoder_f AlarmState_decode_ber;
der_type_encoder_f AlarmState_encode_der;
xer_type_decoder_f AlarmState_decode_xer;
xer_type_encoder_f AlarmState_encode_xer;
oer_type_decoder_f AlarmState_decode_oer;
oer_type_encoder_f AlarmState_encode_oer;
per_type_decoder_f AlarmState_decode_uper;
per_type_encoder_f AlarmState_encode_uper;

#ifdef __cplusplus
}
#endif

#endif	/* _AlarmState_H_ */
#include <asn_internal.h>
