/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "Map"
 * 	found in "./Map.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#include "Dotted-SolidMarkingLineType.h"

/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
static asn_oer_constraints_t asn_OER_type_Dotted_SolidMarkingLineType_constr_1 CC_NOTUSED = {
	{ 0, 0 },
	-1};
asn_per_constraints_t asn_PER_type_Dotted_SolidMarkingLineType_constr_1 CC_NOTUSED = {
	{ APC_CONSTRAINED | APC_EXTENSIBLE,  3,  3,  0,  7 }	/* (0..7,...) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
static const asn_INTEGER_enum_map_t asn_MAP_Dotted_SolidMarkingLineType_value2enum_1[] = {
	{ 0,	15,	"whiteDottedLine" },
	{ 1,	14,	"whiteSolidLine" },
	{ 2,	16,	"yellowDottedLine" },
	{ 3,	15,	"yellowSolidLine" },
	{ 4,	21,	"whiteDotted-solidLine" },
	{ 5,	22,	"whiteSotted-dottedLine" },
	{ 6,	22,	"yellowDotted-solidLine" },
	{ 7,	23,	"yellowSotted-dottedLine" }
	/* This list is extensible */
};
static const unsigned int asn_MAP_Dotted_SolidMarkingLineType_enum2value_1[] = {
	4,	/* whiteDotted-solidLine(4) */
	0,	/* whiteDottedLine(0) */
	1,	/* whiteSolidLine(1) */
	5,	/* whiteSotted-dottedLine(5) */
	6,	/* yellowDotted-solidLine(6) */
	2,	/* yellowDottedLine(2) */
	3,	/* yellowSolidLine(3) */
	7	/* yellowSotted-dottedLine(7) */
	/* This list is extensible */
};
const asn_INTEGER_specifics_t asn_SPC_Dotted_SolidMarkingLineType_specs_1 = {
	asn_MAP_Dotted_SolidMarkingLineType_value2enum_1,	/* "tag" => N; sorted by tag */
	asn_MAP_Dotted_SolidMarkingLineType_enum2value_1,	/* N => "tag"; sorted by N */
	8,	/* Number of elements in the maps */
	9,	/* Extensions before this member */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_Dotted_SolidMarkingLineType_tags_1[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
asn_TYPE_descriptor_t asn_DEF_Dotted_SolidMarkingLineType = {
	"Dotted-SolidMarkingLineType",
	"Dotted-SolidMarkingLineType",
	&asn_OP_NativeEnumerated,
	asn_DEF_Dotted_SolidMarkingLineType_tags_1,
	sizeof(asn_DEF_Dotted_SolidMarkingLineType_tags_1)
		/sizeof(asn_DEF_Dotted_SolidMarkingLineType_tags_1[0]), /* 1 */
	asn_DEF_Dotted_SolidMarkingLineType_tags_1,	/* Same as above */
	sizeof(asn_DEF_Dotted_SolidMarkingLineType_tags_1)
		/sizeof(asn_DEF_Dotted_SolidMarkingLineType_tags_1[0]), /* 1 */
	{ &asn_OER_type_Dotted_SolidMarkingLineType_constr_1, &asn_PER_type_Dotted_SolidMarkingLineType_constr_1, NativeEnumerated_constraint },
	0, 0,	/* Defined elsewhere */
	&asn_SPC_Dotted_SolidMarkingLineType_specs_1	/* Additional specs */
};

