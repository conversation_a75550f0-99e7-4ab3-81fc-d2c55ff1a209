/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "OPS"
 * 	found in "./OPS.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#ifndef	_IREParamList_H_
#define	_IREParamList_H_


#include <asn_application.h>

/* Including external dependencies */
#include <asn_SEQUENCE_OF.h>
#include <constr_SEQUENCE_OF.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Forward declarations */
struct IREParam;

/* IREParamList */
typedef struct IREParamList {
	A_SEQUENCE_OF(struct IREParam) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} IREParamList_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_IREParamList;

#ifdef __cplusplus
}
#endif

/* Referred external types */
#include "IREParam.h"

#endif	/* _IREParamList_H_ */
#include <asn_internal.h>
