/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "RSI"
 * 	found in "./RSI.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#ifndef	_ReferenceLink_H_
#define	_ReferenceLink_H_


#include <asn_application.h>

/* Including external dependencies */
#include "NodeReferenceID.h"
#include "ReferenceLanes.h"
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* ReferenceLink */
typedef struct ReferenceLink {
	NodeReferenceID_t	 upstreamNodeId;
	NodeReferenceID_t	 downstreamNodeId;
	ReferenceLanes_t	*referenceLanes	/* OPTIONAL */;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} ReferenceLink_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_ReferenceLink;
extern asn_SEQUENCE_specifics_t asn_SPC_ReferenceLink_specs_1;
extern asn_TYPE_member_t asn_MBR_ReferenceLink_1[3];

#ifdef __cplusplus
}
#endif

#endif	/* _ReferenceLink_H_ */
#include <asn_internal.h>
