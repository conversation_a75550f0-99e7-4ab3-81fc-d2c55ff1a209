/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "OPS"
 * 	found in "./OPS.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#include "DiskInfo.h"

static int
memb_tps_constraint_1(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	long value;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	value = *(const long *)sptr;
	
	if((value >= 0 && value <= 65535)) {
		/* Constraint check succeeded */
		return 0;
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static asn_oer_constraints_t asn_OER_memb_tps_constr_5 CC_NOTUSED = {
	{ 2, 1 }	/* (0..65535) */,
	-1};
static asn_per_constraints_t asn_PER_memb_tps_constr_5 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 16,  16,  0,  65535 }	/* (0..65535) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
asn_TYPE_member_t asn_MBR_DiskInfo_1[] = {
	{ ATF_NOFLAGS, 0, offsetof(struct DiskInfo, total),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_MemSize,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"total"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct DiskInfo, used),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_MemSize,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"used"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct DiskInfo, free),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_MemSize,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"free"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct DiskInfo, tps),
		(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NativeInteger,
		0,
		{ &asn_OER_memb_tps_constr_5, &asn_PER_memb_tps_constr_5,  memb_tps_constraint_1 },
		0, 0, /* No default value */
		"tps"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct DiskInfo, write),
		(ASN_TAG_CLASS_CONTEXT | (4 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_DataRateKByte,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"write"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct DiskInfo, read),
		(ASN_TAG_CLASS_CONTEXT | (5 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_DataRateKByte,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"read"
		},
};
static const ber_tlv_tag_t asn_DEF_DiskInfo_tags_1[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_DiskInfo_tag2el_1[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* total */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* used */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 }, /* free */
    { (ASN_TAG_CLASS_CONTEXT | (3 << 2)), 3, 0, 0 }, /* tps */
    { (ASN_TAG_CLASS_CONTEXT | (4 << 2)), 4, 0, 0 }, /* write */
    { (ASN_TAG_CLASS_CONTEXT | (5 << 2)), 5, 0, 0 } /* read */
};
asn_SEQUENCE_specifics_t asn_SPC_DiskInfo_specs_1 = {
	sizeof(struct DiskInfo),
	offsetof(struct DiskInfo, _asn_ctx),
	asn_MAP_DiskInfo_tag2el_1,
	6,	/* Count of tags in the map */
	0, 0, 0,	/* Optional elements (not needed) */
	-1,	/* First extension addition */
};
asn_TYPE_descriptor_t asn_DEF_DiskInfo = {
	"DiskInfo",
	"DiskInfo",
	&asn_OP_SEQUENCE,
	asn_DEF_DiskInfo_tags_1,
	sizeof(asn_DEF_DiskInfo_tags_1)
		/sizeof(asn_DEF_DiskInfo_tags_1[0]), /* 1 */
	asn_DEF_DiskInfo_tags_1,	/* Same as above */
	sizeof(asn_DEF_DiskInfo_tags_1)
		/sizeof(asn_DEF_DiskInfo_tags_1[0]), /* 1 */
	{ 0, 0, SEQUENCE_constraint },
	asn_MBR_DiskInfo_1,
	6,	/* Elements count */
	&asn_SPC_DiskInfo_specs_1	/* Additional specs */
};

