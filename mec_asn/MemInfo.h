/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "OPS"
 * 	found in "./OPS.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#ifndef	_MemInfo_H_
#define	_MemInfo_H_


#include <asn_application.h>

/* Including external dependencies */
#include "MemSize.h"
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* MemInfo */
typedef struct MemInfo {
	MemSize_t	 total;
	MemSize_t	 used;
	MemSize_t	 free;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} MemInfo_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_MemInfo;
extern asn_SEQUENCE_specifics_t asn_SPC_MemInfo_specs_1;
extern asn_TYPE_member_t asn_MBR_MemInfo_1[3];

#ifdef __cplusplus
}
#endif

#endif	/* _MemInfo_H_ */
#include <asn_internal.h>
