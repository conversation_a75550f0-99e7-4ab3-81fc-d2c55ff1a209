/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "SensorSharing"
 * 	found in "./SensorSharing.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#ifndef	_Non_motorDataExtension_H_
#define	_Non_motorDataExtension_H_


#include <asn_application.h>

/* Including external dependencies */
#include <NativeInteger.h>
#include "Non-motorData.h"
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Non-motorDataExtension */
typedef struct Non_motorDataExtension {
	long	 overallRadius;
	Non_motorData_t	 non_motorData;
	/*
	 * This type is extensible,
	 * possible extensions are below.
	 */
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} Non_motorDataExtension_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_Non_motorDataExtension;
extern asn_SEQUENCE_specifics_t asn_SPC_Non_motorDataExtension_specs_1;
extern asn_TYPE_member_t asn_MBR_Non_motorDataExtension_1[2];

#ifdef __cplusplus
}
#endif

#endif	/* _Non_motorDataExtension_H_ */
#include <asn_internal.h>
