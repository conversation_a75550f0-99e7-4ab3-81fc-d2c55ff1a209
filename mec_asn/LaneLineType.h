/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "Map"
 * 	found in "./Map.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#ifndef	_LaneLineType_H_
#define	_LaneLineType_H_


#include <asn_application.h>

/* Including external dependencies */
#include "Dotted-SolidMarkingLineType.h"
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* LaneLineType */
typedef struct LaneLineType {
	Dotted_SolidMarkingLineType_t	 leftLaneLine;
	Dotted_SolidMarkingLineType_t	 rightLaneLine;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} LaneLineType_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_LaneLineType;
extern asn_SEQUENCE_specifics_t asn_SPC_LaneLineType_specs_1;
extern asn_TYPE_member_t asn_MBR_LaneLineType_1[2];

#ifdef __cplusplus
}
#endif

#endif	/* _LaneLineType_H_ */
#include <asn_internal.h>
