/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "VPM"
 * 	found in "./VPM.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#include "PaymentInfoType1.h"

static int
memb_serviceInfo_constraint_1(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	const OCTET_STRING_t *st = (const OCTET_STRING_t *)sptr;
	size_t size;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	size = st->size;
	
	if((size >= 1 && size <= 512)) {
		/* Constraint check succeeded */
		return 0;
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static asn_oer_constraints_t asn_OER_memb_serviceInfo_constr_4 CC_NOTUSED = {
	{ 0, 0 },
	-1	/* (SIZE(1..512)) */};
static asn_per_constraints_t asn_PER_memb_serviceInfo_constr_4 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 9,  9,  1,  512 }	/* (SIZE(1..512)) */,
	0, 0	/* No PER value map */
};
asn_TYPE_member_t asn_MBR_PaymentInfoType1_1[] = {
	{ ATF_POINTER, 3, offsetof(struct PaymentInfoType1, tollingNodeInfo),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_TollingNodeInfo,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"tollingNodeInfo"
		},
	{ ATF_POINTER, 2, offsetof(struct PaymentInfoType1, tollInfo),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_TollInfo,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"tollInfo"
		},
	{ ATF_POINTER, 1, offsetof(struct PaymentInfoType1, serviceInfo),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_OCTET_STRING,
		0,
		{ &asn_OER_memb_serviceInfo_constr_4, &asn_PER_memb_serviceInfo_constr_4,  memb_serviceInfo_constraint_1 },
		0, 0, /* No default value */
		"serviceInfo"
		},
};
static const int asn_MAP_PaymentInfoType1_oms_1[] = { 0, 1, 2 };
static const ber_tlv_tag_t asn_DEF_PaymentInfoType1_tags_1[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_PaymentInfoType1_tag2el_1[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* tollingNodeInfo */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* tollInfo */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 } /* serviceInfo */
};
asn_SEQUENCE_specifics_t asn_SPC_PaymentInfoType1_specs_1 = {
	sizeof(struct PaymentInfoType1),
	offsetof(struct PaymentInfoType1, _asn_ctx),
	asn_MAP_PaymentInfoType1_tag2el_1,
	3,	/* Count of tags in the map */
	asn_MAP_PaymentInfoType1_oms_1,	/* Optional members */
	3, 0,	/* Root/Additions */
	3,	/* First extension addition */
};
asn_TYPE_descriptor_t asn_DEF_PaymentInfoType1 = {
	"PaymentInfoType1",
	"PaymentInfoType1",
	&asn_OP_SEQUENCE,
	asn_DEF_PaymentInfoType1_tags_1,
	sizeof(asn_DEF_PaymentInfoType1_tags_1)
		/sizeof(asn_DEF_PaymentInfoType1_tags_1[0]), /* 1 */
	asn_DEF_PaymentInfoType1_tags_1,	/* Same as above */
	sizeof(asn_DEF_PaymentInfoType1_tags_1)
		/sizeof(asn_DEF_PaymentInfoType1_tags_1[0]), /* 1 */
	{ 0, 0, SEQUENCE_constraint },
	asn_MBR_PaymentInfoType1_1,
	3,	/* Elements count */
	&asn_SPC_PaymentInfoType1_specs_1	/* Additional specs */
};

