/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "OPS"
 * 	found in "./OPS.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#ifndef	_AppType_H_
#define	_AppType_H_


#include <asn_application.h>

/* Including external dependencies */
#include <NativeEnumerated.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Dependencies */
typedef enum AppType {
	AppType_v2xApp	= 0,
	AppType_v2xCenter	= 1,
	AppType_uuMEC	= 2,
	AppType_rsu	= 3,
	AppType_scaler	= 4
	/*
	 * Enumeration is extensible
	 */
} e_AppType;

/* AppType */
typedef long	 AppType_t;

/* Implementation */
extern asn_per_constraints_t asn_PER_type_AppType_constr_1;
extern asn_TYPE_descriptor_t asn_DEF_AppType;
extern const asn_INTEGER_specifics_t asn_SPC_AppType_specs_1;
asn_struct_free_f AppType_free;
asn_struct_print_f AppType_print;
asn_constr_check_f AppType_constraint;
ber_type_decoder_f AppType_decode_ber;
der_type_encoder_f AppType_encode_der;
xer_type_decoder_f AppType_decode_xer;
xer_type_encoder_f AppType_encode_xer;
oer_type_decoder_f AppType_decode_oer;
oer_type_encoder_f AppType_encode_oer;
per_type_decoder_f AppType_decode_uper;
per_type_encoder_f AppType_encode_uper;

#ifdef __cplusplus
}
#endif

#endif	/* _AppType_H_ */
#include <asn_internal.h>
