/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "VPM"
 * 	found in "./VPM.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#include "OBUPaymentInfoType1.h"

asn_TYPE_member_t asn_MBR_OBUPaymentInfoType1_1[] = {
	{ ATF_POINTER, 5, offsetof(struct OBUPaymentInfoType1, equipmentClass),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_EquipmentClass,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"equipmentClass"
		},
	{ ATF_POINTER, 4, offsetof(struct OBUPaymentInfoType1, gbiCCInfo),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_GBICCInfo,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"gbiCCInfo"
		},
	{ ATF_POINTER, 3, offsetof(struct OBUPaymentInfoType1, sysInfo),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_SysInfo,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"sysInfo"
		},
	{ ATF_POINTER, 2, offsetof(struct OBUPaymentInfoType1, vehicleInfo),
		(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_VehicleInfo,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"vehicleInfo"
		},
	{ ATF_POINTER, 1, offsetof(struct OBUPaymentInfoType1, passedSitesInfo),
		(ASN_TAG_CLASS_CONTEXT | (4 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_PassedSitesInfo,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"passedSitesInfo"
		},
};
static const int asn_MAP_OBUPaymentInfoType1_oms_1[] = { 0, 1, 2, 3, 4 };
static const ber_tlv_tag_t asn_DEF_OBUPaymentInfoType1_tags_1[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_OBUPaymentInfoType1_tag2el_1[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* equipmentClass */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* gbiCCInfo */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 }, /* sysInfo */
    { (ASN_TAG_CLASS_CONTEXT | (3 << 2)), 3, 0, 0 }, /* vehicleInfo */
    { (ASN_TAG_CLASS_CONTEXT | (4 << 2)), 4, 0, 0 } /* passedSitesInfo */
};
asn_SEQUENCE_specifics_t asn_SPC_OBUPaymentInfoType1_specs_1 = {
	sizeof(struct OBUPaymentInfoType1),
	offsetof(struct OBUPaymentInfoType1, _asn_ctx),
	asn_MAP_OBUPaymentInfoType1_tag2el_1,
	5,	/* Count of tags in the map */
	asn_MAP_OBUPaymentInfoType1_oms_1,	/* Optional members */
	5, 0,	/* Root/Additions */
	5,	/* First extension addition */
};
asn_TYPE_descriptor_t asn_DEF_OBUPaymentInfoType1 = {
	"OBUPaymentInfoType1",
	"OBUPaymentInfoType1",
	&asn_OP_SEQUENCE,
	asn_DEF_OBUPaymentInfoType1_tags_1,
	sizeof(asn_DEF_OBUPaymentInfoType1_tags_1)
		/sizeof(asn_DEF_OBUPaymentInfoType1_tags_1[0]), /* 1 */
	asn_DEF_OBUPaymentInfoType1_tags_1,	/* Same as above */
	sizeof(asn_DEF_OBUPaymentInfoType1_tags_1)
		/sizeof(asn_DEF_OBUPaymentInfoType1_tags_1[0]), /* 1 */
	{ 0, 0, SEQUENCE_constraint },
	asn_MBR_OBUPaymentInfoType1_1,
	5,	/* Elements count */
	&asn_SPC_OBUPaymentInfoType1_specs_1	/* Additional specs */
};

