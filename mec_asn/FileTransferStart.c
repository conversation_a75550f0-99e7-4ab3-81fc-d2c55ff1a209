/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "OPS"
 * 	found in "./OPS.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#include "FileTransferStart.h"

static int check_permitted_alphabet_5(const void *sptr) {
	/* The underlying type is IA5String */
	const IA5String_t *st = (const IA5String_t *)sptr;
	const uint8_t *ch = st->buf;
	const uint8_t *end = ch + st->size;
	
	for(; ch < end; ch++) {
		uint8_t cv = *ch;
		if(!(cv <= 127)) return -1;
	}
	return 0;
}

static int
memb_fileId_constraint_1(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	long value;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	value = *(const long *)sptr;
	
	if((value >= 1 && value <= 65536)) {
		/* Constraint check succeeded */
		return 0;
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_url_constraint_1(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	const IA5String_t *st = (const IA5String_t *)sptr;
	size_t size;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	size = st->size;
	
	if((size >= 1 && size <= 128)
		 && !check_permitted_alphabet_5(st)) {
		/* Constraint check succeeded */
		return 0;
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static asn_oer_constraints_t asn_OER_memb_fileId_constr_3 CC_NOTUSED = {
	{ 4, 1 }	/* (1..65536) */,
	-1};
static asn_per_constraints_t asn_PER_memb_fileId_constr_3 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 16, -1,  1,  65536 }	/* (1..65536) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
static asn_oer_constraints_t asn_OER_memb_url_constr_5 CC_NOTUSED = {
	{ 0, 0 },
	-1	/* (SIZE(1..128)) */};
static asn_per_constraints_t asn_PER_memb_url_constr_5 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 7,  7,  0,  127 }	/* (0..127) */,
	{ APC_CONSTRAINED,	 7,  7,  1,  128 }	/* (SIZE(1..128)) */,
	0, 0	/* No PER character map necessary */
};
asn_TYPE_member_t asn_MBR_FileTransferStart_1[] = {
	{ ATF_NOFLAGS, 0, offsetof(struct FileTransferStart, transType),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_FileTransferType,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"transType"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct FileTransferStart, fileId),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NativeInteger,
		0,
		{ &asn_OER_memb_fileId_constr_3, &asn_PER_memb_fileId_constr_3,  memb_fileId_constraint_1 },
		0, 0, /* No default value */
		"fileId"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct FileTransferStart, fileType),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_FileType,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"fileType"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct FileTransferStart, url),
		(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_IA5String,
		0,
		{ &asn_OER_memb_url_constr_5, &asn_PER_memb_url_constr_5,  memb_url_constraint_1 },
		0, 0, /* No default value */
		"url"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct FileTransferStart, name),
		(ASN_TAG_CLASS_CONTEXT | (4 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_Name,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"name"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct FileTransferStart, describe),
		(ASN_TAG_CLASS_CONTEXT | (5 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_TextUTF8,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"describe"
		},
};
static const ber_tlv_tag_t asn_DEF_FileTransferStart_tags_1[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_FileTransferStart_tag2el_1[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* transType */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* fileId */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 }, /* fileType */
    { (ASN_TAG_CLASS_CONTEXT | (3 << 2)), 3, 0, 0 }, /* url */
    { (ASN_TAG_CLASS_CONTEXT | (4 << 2)), 4, 0, 0 }, /* name */
    { (ASN_TAG_CLASS_CONTEXT | (5 << 2)), 5, 0, 0 } /* describe */
};
asn_SEQUENCE_specifics_t asn_SPC_FileTransferStart_specs_1 = {
	sizeof(struct FileTransferStart),
	offsetof(struct FileTransferStart, _asn_ctx),
	asn_MAP_FileTransferStart_tag2el_1,
	6,	/* Count of tags in the map */
	0, 0, 0,	/* Optional elements (not needed) */
	-1,	/* First extension addition */
};
asn_TYPE_descriptor_t asn_DEF_FileTransferStart = {
	"FileTransferStart",
	"FileTransferStart",
	&asn_OP_SEQUENCE,
	asn_DEF_FileTransferStart_tags_1,
	sizeof(asn_DEF_FileTransferStart_tags_1)
		/sizeof(asn_DEF_FileTransferStart_tags_1[0]), /* 1 */
	asn_DEF_FileTransferStart_tags_1,	/* Same as above */
	sizeof(asn_DEF_FileTransferStart_tags_1)
		/sizeof(asn_DEF_FileTransferStart_tags_1[0]), /* 1 */
	{ 0, 0, SEQUENCE_constraint },
	asn_MBR_FileTransferStart_1,
	6,	/* Elements count */
	&asn_SPC_FileTransferStart_specs_1	/* Additional specs */
};

