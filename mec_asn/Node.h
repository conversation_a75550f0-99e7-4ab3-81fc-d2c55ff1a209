/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "Map"
 * 	found in "./Map.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#ifndef	_Node_H_
#define	_Node_H_


#include <asn_application.h>

/* Including external dependencies */
#include "DescriptiveName.h"
#include "NodeReferenceID.h"
#include "Position3D.h"
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Forward declarations */
struct LinkList;
struct LinkExList;
struct ProhibitedZone;

/* Node */
typedef struct Node {
	DescriptiveName_t	*name	/* OPTIONAL */;
	NodeReferenceID_t	 id;
	Position3D_t	 refPos;
	struct LinkList	*inLinks	/* OPTIONAL */;
	/*
	 * This type is extensible,
	 * possible extensions are below.
	 */
	struct LinkExList	*inLinks_ex	/* OPTIONAL */;
	struct ProhibitedZone	*prohibitedzone	/* OPTIONAL */;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} Node_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_Node;
extern asn_SEQUENCE_specifics_t asn_SPC_Node_specs_1;
extern asn_TYPE_member_t asn_MBR_Node_1[6];

#ifdef __cplusplus
}
#endif

/* Referred external types */
#include "LinkList.h"
#include "LinkExList.h"
#include "ProhibitedZone.h"

#endif	/* _Node_H_ */
#include <asn_internal.h>
