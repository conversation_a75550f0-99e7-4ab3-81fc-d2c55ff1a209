/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "DefMotion"
 * 	found in "./DefMotion.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#ifndef	_Heading_H_
#define	_Heading_H_


#include <asn_application.h>

/* Including external dependencies */
#include <NativeInteger.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Heading */
typedef long	 Heading_t;

/* Implementation */
extern asn_per_constraints_t asn_PER_type_Heading_constr_1;
extern asn_TYPE_descriptor_t asn_DEF_Heading;
asn_struct_free_f Heading_free;
asn_struct_print_f Heading_print;
asn_constr_check_f Heading_constraint;
ber_type_decoder_f Heading_decode_ber;
der_type_encoder_f Heading_encode_der;
xer_type_decoder_f Heading_decode_xer;
xer_type_encoder_f Heading_encode_xer;
oer_type_decoder_f Heading_decode_oer;
oer_type_encoder_f Heading_encode_oer;
per_type_decoder_f Heading_decode_uper;
per_type_encoder_f Heading_encode_uper;

#ifdef __cplusplus
}
#endif

#endif	/* _Heading_H_ */
#include <asn_internal.h>
