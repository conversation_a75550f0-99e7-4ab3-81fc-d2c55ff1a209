/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "OPS"
 * 	found in "./OPS.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#ifndef	_PositionSet_H_
#define	_PositionSet_H_


#include <asn_application.h>

/* Including external dependencies */
#include "Position3D.h"
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* PositionSet */
typedef struct PositionSet {
	Position3D_t	 location;
	/*
	 * This type is extensible,
	 * possible extensions are below.
	 */
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} PositionSet_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_PositionSet;
extern asn_SEQUENCE_specifics_t asn_SPC_PositionSet_specs_1;
extern asn_TYPE_member_t asn_MBR_PositionSet_1[1];

#ifdef __cplusplus
}
#endif

#endif	/* _PositionSet_H_ */
#include <asn_internal.h>
