/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "OPS"
 * 	found in "./OPS.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#ifndef	_Network_H_
#define	_Network_H_


#include <asn_application.h>

/* Including external dependencies */
#include <IA5String.h>
#include <BOOLEAN.h>
#include "IP.h"
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Network */
typedef struct Network {
	IA5String_t	 name;
	BOOLEAN_t	 isDHCP;
	IP_t	 address;
	IP_t	 netmask;
	IP_t	 gateway;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} Network_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_Network;
extern asn_SEQUENCE_specifics_t asn_SPC_Network_specs_1;
extern asn_TYPE_member_t asn_MBR_Network_1[5];

#ifdef __cplusplus
}
#endif

#endif	/* _Network_H_ */
#include <asn_internal.h>
