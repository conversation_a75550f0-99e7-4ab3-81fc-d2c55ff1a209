/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "VPM"
 * 	found in "./VPM.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#ifndef	_OBUType_H_
#define	_OBUType_H_


#include <asn_application.h>

/* Including external dependencies */
#include <NativeEnumerated.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Dependencies */
typedef enum OBUType {
	OBUType_oemObu	= 0,
	OBUType_aftermarketObu	= 1,
	OBUType_mobilePhone	= 2
	/*
	 * Enumeration is extensible
	 */
} e_OBUType;

/* OBUType */
typedef long	 OBUType_t;

/* Implementation */
extern asn_per_constraints_t asn_PER_type_OBUType_constr_1;
extern asn_TYPE_descriptor_t asn_DEF_OBUType;
extern const asn_INTEGER_specifics_t asn_SPC_OBUType_specs_1;
asn_struct_free_f OBUType_free;
asn_struct_print_f OBUType_print;
asn_constr_check_f OBUType_constraint;
ber_type_decoder_f OBUType_decode_ber;
der_type_encoder_f OBUType_encode_der;
xer_type_decoder_f OBUType_decode_xer;
xer_type_encoder_f OBUType_encode_xer;
oer_type_decoder_f OBUType_decode_oer;
oer_type_encoder_f OBUType_encode_oer;
per_type_decoder_f OBUType_decode_uper;
per_type_encoder_f OBUType_encode_uper;

#ifdef __cplusplus
}
#endif

#endif	/* _OBUType_H_ */
#include <asn_internal.h>
