/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "VPM"
 * 	found in "./VPM.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#include "GetTollDataRq.h"

static int
memb_keyIdForAC_constraint_1(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	long value;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	value = *(const long *)sptr;
	
	if((value >= 0 && value <= 255)) {
		/* Constraint check succeeded */
		return 0;
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static asn_oer_constraints_t asn_OER_memb_keyIdForAC_constr_4 CC_NOTUSED = {
	{ 1, 1 }	/* (0..255) */,
	-1};
static asn_per_constraints_t asn_PER_memb_keyIdForAC_constr_4 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 8,  8,  0,  255 }	/* (0..255) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
asn_TYPE_member_t asn_MBR_GetTollDataRq_1[] = {
	{ ATF_NOFLAGS, 0, offsetof(struct GetTollDataRq, vehicleInfo),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_RangeOfFile,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"vehicleInfo"
		},
	{ ATF_POINTER, 2, offsetof(struct GetTollDataRq, tollInfo),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_RangeOfFile,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"tollInfo"
		},
	{ ATF_POINTER, 1, offsetof(struct GetTollDataRq, keyIdForAC),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NativeInteger,
		0,
		{ &asn_OER_memb_keyIdForAC_constr_4, &asn_PER_memb_keyIdForAC_constr_4,  memb_keyIdForAC_constraint_1 },
		0, 0, /* No default value */
		"keyIdForAC"
		},
};
static const int asn_MAP_GetTollDataRq_oms_1[] = { 1, 2 };
static const ber_tlv_tag_t asn_DEF_GetTollDataRq_tags_1[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_GetTollDataRq_tag2el_1[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* vehicleInfo */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* tollInfo */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 } /* keyIdForAC */
};
asn_SEQUENCE_specifics_t asn_SPC_GetTollDataRq_specs_1 = {
	sizeof(struct GetTollDataRq),
	offsetof(struct GetTollDataRq, _asn_ctx),
	asn_MAP_GetTollDataRq_tag2el_1,
	3,	/* Count of tags in the map */
	asn_MAP_GetTollDataRq_oms_1,	/* Optional members */
	2, 0,	/* Root/Additions */
	3,	/* First extension addition */
};
asn_TYPE_descriptor_t asn_DEF_GetTollDataRq = {
	"GetTollDataRq",
	"GetTollDataRq",
	&asn_OP_SEQUENCE,
	asn_DEF_GetTollDataRq_tags_1,
	sizeof(asn_DEF_GetTollDataRq_tags_1)
		/sizeof(asn_DEF_GetTollDataRq_tags_1[0]), /* 1 */
	asn_DEF_GetTollDataRq_tags_1,	/* Same as above */
	sizeof(asn_DEF_GetTollDataRq_tags_1)
		/sizeof(asn_DEF_GetTollDataRq_tags_1[0]), /* 1 */
	{ 0, 0, SEQUENCE_constraint },
	asn_MBR_GetTollDataRq_1,
	3,	/* Elements count */
	&asn_SPC_GetTollDataRq_specs_1	/* Additional specs */
};

