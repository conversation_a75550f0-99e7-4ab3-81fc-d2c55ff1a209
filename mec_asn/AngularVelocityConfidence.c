/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "SensorSharing"
 * 	found in "./SensorSharing.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#include "AngularVelocityConfidence.h"

asn_TYPE_member_t asn_MBR_AngularVelocityConfidence_1[] = {
	{ ATF_NOFLAGS, 0, offsetof(struct AngularVelocityConfidence, pitchRate),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_AngularVConfidence,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"pitchRate"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct AngularVelocityConfidence, rollRate),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_AngularVConfidence,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"rollRate"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct AngularVelocityConfidence, yawRate),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_AngularVConfidence,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"yawRate"
		},
};
static const ber_tlv_tag_t asn_DEF_AngularVelocityConfidence_tags_1[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_AngularVelocityConfidence_tag2el_1[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* pitchRate */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* rollRate */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 } /* yawRate */
};
asn_SEQUENCE_specifics_t asn_SPC_AngularVelocityConfidence_specs_1 = {
	sizeof(struct AngularVelocityConfidence),
	offsetof(struct AngularVelocityConfidence, _asn_ctx),
	asn_MAP_AngularVelocityConfidence_tag2el_1,
	3,	/* Count of tags in the map */
	0, 0, 0,	/* Optional elements (not needed) */
	-1,	/* First extension addition */
};
asn_TYPE_descriptor_t asn_DEF_AngularVelocityConfidence = {
	"AngularVelocityConfidence",
	"AngularVelocityConfidence",
	&asn_OP_SEQUENCE,
	asn_DEF_AngularVelocityConfidence_tags_1,
	sizeof(asn_DEF_AngularVelocityConfidence_tags_1)
		/sizeof(asn_DEF_AngularVelocityConfidence_tags_1[0]), /* 1 */
	asn_DEF_AngularVelocityConfidence_tags_1,	/* Same as above */
	sizeof(asn_DEF_AngularVelocityConfidence_tags_1)
		/sizeof(asn_DEF_AngularVelocityConfidence_tags_1[0]), /* 1 */
	{ 0, 0, SEQUENCE_constraint },
	asn_MBR_AngularVelocityConfidence_1,
	3,	/* Elements count */
	&asn_SPC_AngularVelocityConfidence_specs_1	/* Additional specs */
};

