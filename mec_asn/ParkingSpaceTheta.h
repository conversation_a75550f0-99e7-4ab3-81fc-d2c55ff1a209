/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "PAM"
 * 	found in "./PAM.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#ifndef	_ParkingSpaceTheta_H_
#define	_ParkingSpaceTheta_H_


#include <asn_application.h>

/* Including external dependencies */
#include <NativeEnumerated.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Dependencies */
typedef enum ParkingSpaceTheta {
	ParkingSpaceTheta_unknown	= 0,
	ParkingSpaceTheta_vertical	= 1,
	ParkingSpaceTheta_side	= 2,
	ParkingSpaceTheta_oblique	= 3
	/*
	 * Enumeration is extensible
	 */
} e_ParkingSpaceTheta;

/* ParkingSpaceTheta */
typedef long	 ParkingSpaceTheta_t;

/* Implementation */
extern asn_per_constraints_t asn_PER_type_ParkingSpaceTheta_constr_1;
extern asn_TYPE_descriptor_t asn_DEF_ParkingSpaceTheta;
extern const asn_INTEGER_specifics_t asn_SPC_ParkingSpaceTheta_specs_1;
asn_struct_free_f ParkingSpaceTheta_free;
asn_struct_print_f ParkingSpaceTheta_print;
asn_constr_check_f ParkingSpaceTheta_constraint;
ber_type_decoder_f ParkingSpaceTheta_decode_ber;
der_type_encoder_f ParkingSpaceTheta_encode_der;
xer_type_decoder_f ParkingSpaceTheta_decode_xer;
xer_type_encoder_f ParkingSpaceTheta_encode_xer;
oer_type_decoder_f ParkingSpaceTheta_decode_oer;
oer_type_encoder_f ParkingSpaceTheta_encode_oer;
per_type_decoder_f ParkingSpaceTheta_decode_uper;
per_type_encoder_f ParkingSpaceTheta_encode_uper;

#ifdef __cplusplus
}
#endif

#endif	/* _ParkingSpaceTheta_H_ */
#include <asn_internal.h>
