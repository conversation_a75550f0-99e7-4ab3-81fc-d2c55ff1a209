/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "MsgDayII"
 * 	found in "./MsgDayII.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#ifndef	_ExtMsgID_H_
#define	_ExtMsgID_H_


#include <asn_application.h>

/* Including external dependencies */
#include <NativeInteger.h>

#ifdef __cplusplus
extern "C" {
#endif

/* ExtMsgID */
typedef long	 ExtMsgID_t;

/* Implementation */
extern asn_per_constraints_t asn_PER_type_ExtMsgID_constr_1;
extern asn_TYPE_descriptor_t asn_DEF_ExtMsgID;
asn_struct_free_f ExtMsgID_free;
asn_struct_print_f ExtMsgID_print;
asn_constr_check_f ExtMsgID_constraint;
ber_type_decoder_f ExtMsgID_decode_ber;
der_type_encoder_f ExtMsgID_encode_der;
xer_type_decoder_f ExtMsgID_decode_xer;
xer_type_encoder_f ExtMsgID_encode_xer;
oer_type_decoder_f ExtMsgID_decode_oer;
oer_type_encoder_f ExtMsgID_encode_oer;
per_type_decoder_f ExtMsgID_decode_uper;
per_type_encoder_f ExtMsgID_encode_uper;

#ifdef __cplusplus
}
#endif

#endif	/* _ExtMsgID_H_ */
#include <asn_internal.h>
