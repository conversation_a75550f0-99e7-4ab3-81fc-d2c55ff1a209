/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "Map"
 * 	found in "./Map.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#ifndef	_Maneuver_H_
#define	_Maneuver_H_


#include <asn_application.h>

/* Including external dependencies */
#include <NativeEnumerated.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Dependencies */
typedef enum Maneuver {
	Maneuver_maneuverStraight	= 0,
	Maneuver_maneuverLeftTurn	= 1,
	Maneuver_maneuverRightTurn	= 2,
	Maneuver_maneuverUTurn	= 3
	/*
	 * Enumeration is extensible
	 */
} e_Maneuver;

/* Maneuver */
typedef long	 Maneuver_t;

/* Implementation */
extern asn_per_constraints_t asn_PER_type_Maneuver_constr_1;
extern asn_TYPE_descriptor_t asn_DEF_Maneuver;
extern const asn_INTEGER_specifics_t asn_SPC_Maneuver_specs_1;
asn_struct_free_f Maneuver_free;
asn_struct_print_f Maneuver_print;
asn_constr_check_f Maneuver_constraint;
ber_type_decoder_f Maneuver_decode_ber;
der_type_encoder_f Maneuver_encode_der;
xer_type_decoder_f Maneuver_decode_xer;
xer_type_encoder_f Maneuver_encode_xer;
oer_type_decoder_f Maneuver_decode_oer;
oer_type_encoder_f Maneuver_encode_oer;
per_type_decoder_f Maneuver_decode_uper;
per_type_encoder_f Maneuver_encode_uper;

#ifdef __cplusplus
}
#endif

#endif	/* _Maneuver_H_ */
#include <asn_internal.h>
