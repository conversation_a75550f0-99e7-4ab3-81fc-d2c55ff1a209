/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "OPS"
 * 	found in "./OPS.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#include "AlarmType.h"

/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
static asn_oer_constraints_t asn_OER_type_AlarmType_constr_1 CC_NOTUSED = {
	{ 0, 0 },
	-1};
asn_per_constraints_t asn_PER_type_AlarmType_constr_1 CC_NOTUSED = {
	{ APC_CONSTRAINED | APC_EXTENSIBLE,  4,  4,  0,  12 }	/* (0..12,...) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
static const asn_INTEGER_enum_map_t asn_MAP_AlarmType_value2enum_1[] = {
	{ 0,	7,	"nothing" },
	{ 1,	7,	"highCPU" },
	{ 2,	11,	"outOfMemory" },
	{ 3,	13,	"netDisconnect" },
	{ 4,	11,	"badFirmware" },
	{ 5,	15,	"storageBadBlock" },
	{ 6,	14,	"outOfDiskSpace" },
	{ 7,	10,	"ireOffLine" },
	{ 8,	10,	"threadLock" },
	{ 9,	8,	"appBlock" },
	{ 10,	8,	"pc5Error" },
	{ 11,	9,	"gnssError" },
	{ 12,	8,	"uwbError" }
	/* This list is extensible */
};
static const unsigned int asn_MAP_AlarmType_enum2value_1[] = {
	9,	/* appBlock(9) */
	4,	/* badFirmware(4) */
	11,	/* gnssError(11) */
	1,	/* highCPU(1) */
	7,	/* ireOffLine(7) */
	3,	/* netDisconnect(3) */
	0,	/* nothing(0) */
	6,	/* outOfDiskSpace(6) */
	2,	/* outOfMemory(2) */
	10,	/* pc5Error(10) */
	5,	/* storageBadBlock(5) */
	8,	/* threadLock(8) */
	12	/* uwbError(12) */
	/* This list is extensible */
};
const asn_INTEGER_specifics_t asn_SPC_AlarmType_specs_1 = {
	asn_MAP_AlarmType_value2enum_1,	/* "tag" => N; sorted by tag */
	asn_MAP_AlarmType_enum2value_1,	/* N => "tag"; sorted by N */
	13,	/* Number of elements in the maps */
	14,	/* Extensions before this member */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_AlarmType_tags_1[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
asn_TYPE_descriptor_t asn_DEF_AlarmType = {
	"AlarmType",
	"AlarmType",
	&asn_OP_NativeEnumerated,
	asn_DEF_AlarmType_tags_1,
	sizeof(asn_DEF_AlarmType_tags_1)
		/sizeof(asn_DEF_AlarmType_tags_1[0]), /* 1 */
	asn_DEF_AlarmType_tags_1,	/* Same as above */
	sizeof(asn_DEF_AlarmType_tags_1)
		/sizeof(asn_DEF_AlarmType_tags_1[0]), /* 1 */
	{ &asn_OER_type_AlarmType_constr_1, &asn_PER_type_AlarmType_constr_1, NativeEnumerated_constraint },
	0, 0,	/* Defined elsewhere */
	&asn_SPC_AlarmType_specs_1	/* Additional specs */
};

