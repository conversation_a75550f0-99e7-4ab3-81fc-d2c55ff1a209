/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "OPS"
 * 	found in "./OPS.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#ifndef	_Esn_H_
#define	_Esn_H_


#include <asn_application.h>

/* Including external dependencies */
#include <IA5String.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Esn */
typedef IA5String_t	 Esn_t;

/* Implementation */
extern asn_per_constraints_t asn_PER_type_Esn_constr_1;
extern asn_TYPE_descriptor_t asn_DEF_Esn;
asn_struct_free_f Esn_free;
asn_struct_print_f Esn_print;
asn_constr_check_f Esn_constraint;
ber_type_decoder_f Esn_decode_ber;
der_type_encoder_f Esn_encode_der;
xer_type_decoder_f Esn_decode_xer;
xer_type_encoder_f Esn_encode_xer;
oer_type_decoder_f Esn_decode_oer;
oer_type_encoder_f Esn_encode_oer;
per_type_decoder_f Esn_decode_uper;
per_type_encoder_f Esn_encode_uper;

#ifdef __cplusplus
}
#endif

#endif	/* _Esn_H_ */
#include <asn_internal.h>
