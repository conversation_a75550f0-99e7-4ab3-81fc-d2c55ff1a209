/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "VPM"
 * 	found in "./VPM.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#include "ApplicationParameter.h"

asn_TYPE_member_t asn_MBR_ApplicationParameter_1[] = {
	{ ATF_NOFLAGS, 0, offsetof(struct ApplicationParameter, pid),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_PaymentEntityID,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"pid"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct ApplicationParameter, paymentInfo),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		+1,	/* EXPLICIT tag at current level */
		&asn_DEF_PaymentInfo,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"paymentInfo"
		},
};
static const ber_tlv_tag_t asn_DEF_ApplicationParameter_tags_1[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_ApplicationParameter_tag2el_1[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* pid */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 } /* paymentInfo */
};
asn_SEQUENCE_specifics_t asn_SPC_ApplicationParameter_specs_1 = {
	sizeof(struct ApplicationParameter),
	offsetof(struct ApplicationParameter, _asn_ctx),
	asn_MAP_ApplicationParameter_tag2el_1,
	2,	/* Count of tags in the map */
	0, 0, 0,	/* Optional elements (not needed) */
	2,	/* First extension addition */
};
asn_TYPE_descriptor_t asn_DEF_ApplicationParameter = {
	"ApplicationParameter",
	"ApplicationParameter",
	&asn_OP_SEQUENCE,
	asn_DEF_ApplicationParameter_tags_1,
	sizeof(asn_DEF_ApplicationParameter_tags_1)
		/sizeof(asn_DEF_ApplicationParameter_tags_1[0]), /* 1 */
	asn_DEF_ApplicationParameter_tags_1,	/* Same as above */
	sizeof(asn_DEF_ApplicationParameter_tags_1)
		/sizeof(asn_DEF_ApplicationParameter_tags_1[0]), /* 1 */
	{ 0, 0, SEQUENCE_constraint },
	asn_MBR_ApplicationParameter_1,
	2,	/* Elements count */
	&asn_SPC_ApplicationParameter_specs_1	/* Additional specs */
};

