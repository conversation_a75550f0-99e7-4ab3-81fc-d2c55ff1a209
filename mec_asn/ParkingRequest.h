/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "VIR"
 * 	found in "./VIR.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#ifndef	_ParkingRequest_H_
#define	_ParkingRequest_H_


#include <asn_application.h>

/* Including external dependencies */
#include <BIT_STRING.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Dependencies */
typedef enum ParkingRequest {
	ParkingRequest_enter	= 0,
	ParkingRequest_exit	= 1,
	ParkingRequest_park	= 2,
	ParkingRequest_pay	= 3,
	ParkingRequest_unloadPassenger	= 4,
	ParkingRequest_pickupPassenger	= 5,
	ParkingRequest_unloadCargo	= 6,
	ParkingRequest_loadCargo	= 7,
	ParkingRequest_reserved1	= 8,
	ParkingRequest_reverved2	= 9,
	ParkingRequest_reserved3	= 10,
	ParkingRequest_reverved4	= 11
} e_ParkingRequest;

/* ParkingRequest */
typedef BIT_STRING_t	 ParkingRequest_t;

/* Implementation */
extern asn_per_constraints_t asn_PER_type_ParkingRequest_constr_1;
extern asn_TYPE_descriptor_t asn_DEF_ParkingRequest;
asn_struct_free_f ParkingRequest_free;
asn_struct_print_f ParkingRequest_print;
asn_constr_check_f ParkingRequest_constraint;
ber_type_decoder_f ParkingRequest_decode_ber;
der_type_encoder_f ParkingRequest_encode_der;
xer_type_decoder_f ParkingRequest_decode_xer;
xer_type_encoder_f ParkingRequest_encode_xer;
oer_type_decoder_f ParkingRequest_decode_oer;
oer_type_encoder_f ParkingRequest_encode_oer;
per_type_decoder_f ParkingRequest_decode_uper;
per_type_encoder_f ParkingRequest_encode_uper;

#ifdef __cplusplus
}
#endif

#endif	/* _ParkingRequest_H_ */
#include <asn_internal.h>
