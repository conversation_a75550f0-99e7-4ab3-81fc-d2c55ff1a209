/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "PAM"
 * 	found in "./PAM.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#ifndef	_PAMNodeID_H_
#define	_PAMNodeID_H_


#include <asn_application.h>

/* Including external dependencies */
#include <NativeInteger.h>

#ifdef __cplusplus
extern "C" {
#endif

/* PAMNodeID */
typedef long	 PAMNodeID_t;

/* Implementation */
extern asn_per_constraints_t asn_PER_type_PAMNodeID_constr_1;
extern asn_TYPE_descriptor_t asn_DEF_PAMNodeID;
asn_struct_free_f PAMNodeID_free;
asn_struct_print_f PAMNodeID_print;
asn_constr_check_f PAMNodeID_constraint;
ber_type_decoder_f PAMNodeID_decode_ber;
der_type_encoder_f PAMNodeID_encode_der;
xer_type_decoder_f PAMNodeID_decode_xer;
xer_type_encoder_f PAMNodeID_encode_xer;
oer_type_decoder_f PAMNodeID_decode_oer;
oer_type_encoder_f PAMNodeID_encode_oer;
per_type_decoder_f PAMNodeID_decode_uper;
per_type_encoder_f PAMNodeID_encode_uper;

#ifdef __cplusplus
}
#endif

#endif	/* _PAMNodeID_H_ */
#include <asn_internal.h>
