/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "VPM"
 * 	found in "./VPM.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#ifndef	_GetTollDataRs_H_
#define	_GetTollDataRs_H_


#include <asn_application.h>

/* Including external dependencies */
#include "File.h"
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* GetTollDataRs */
typedef struct GetTollDataRs {
	File_t	 vehicleInfo;
	File_t	*tollInfo	/* OPTIONAL */;
	/*
	 * This type is extensible,
	 * possible extensions are below.
	 */
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} GetTollDataRs_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_GetTollDataRs;
extern asn_SEQUENCE_specifics_t asn_SPC_GetTollDataRs_specs_1;
extern asn_TYPE_member_t asn_MBR_GetTollDataRs_1[2];

#ifdef __cplusplus
}
#endif

#endif	/* _GetTollDataRs_H_ */
#include <asn_internal.h>
