/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "GoEMEC"
 * 	found in "./GoEMEC.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#include "LinkFrame.h"

static asn_oer_constraints_t asn_OER_type_LinkFrame_constr_1 CC_NOTUSED = {
	{ 0, 0 },
	-1};
asn_per_constraints_t asn_PER_type_LinkFrame_constr_1 CC_NOTUSED = {
	{ APC_CONSTRAINED | APC_EXTENSIBLE,  3,  3,  0,  4 }	/* (0..4,...) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
asn_TYPE_member_t asn_MBR_LinkFrame_1[] = {
	{ ATF_NOFLAGS, 0, offsetof(struct LinkFrame, choice.login),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_Login,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"login"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct LinkFrame, choice.loginAck),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_LoginAck,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"loginAck"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct LinkFrame, choice.logout),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_Logout,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"logout"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct LinkFrame, choice.ack),
		(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_ACK,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"ack"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct LinkFrame, choice.heartbeat),
		(ASN_TAG_CLASS_CONTEXT | (4 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_HeartBeat,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"heartbeat"
		},
};
static const asn_TYPE_tag2member_t asn_MAP_LinkFrame_tag2el_1[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* login */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* loginAck */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 }, /* logout */
    { (ASN_TAG_CLASS_CONTEXT | (3 << 2)), 3, 0, 0 }, /* ack */
    { (ASN_TAG_CLASS_CONTEXT | (4 << 2)), 4, 0, 0 } /* heartbeat */
};
asn_CHOICE_specifics_t asn_SPC_LinkFrame_specs_1 = {
	sizeof(struct LinkFrame),
	offsetof(struct LinkFrame, _asn_ctx),
	offsetof(struct LinkFrame, present),
	sizeof(((struct LinkFrame *)0)->present),
	asn_MAP_LinkFrame_tag2el_1,
	5,	/* Count of tags in the map */
	0, 0,
	5	/* Extensions start */
};
asn_TYPE_descriptor_t asn_DEF_LinkFrame = {
	"LinkFrame",
	"LinkFrame",
	&asn_OP_CHOICE,
	0,	/* No effective tags (pointer) */
	0,	/* No effective tags (count) */
	0,	/* No tags (pointer) */
	0,	/* No tags (count) */
	{ &asn_OER_type_LinkFrame_constr_1, &asn_PER_type_LinkFrame_constr_1, CHOICE_constraint },
	asn_MBR_LinkFrame_1,
	5,	/* Elements count */
	&asn_SPC_LinkFrame_specs_1	/* Additional specs */
};

