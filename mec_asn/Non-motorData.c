/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "PSM"
 * 	found in "./PSM.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#include "Non-motorData.h"

asn_TYPE_member_t asn_MBR_Non_motorData_1[] = {
	{ ATF_NOFLAGS, 0, offsetof(struct Non_motorData, basicType),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_PersonalDeviceUserType,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"basicType"
		},
	{ ATF_POINTER, 6, offsetof(struct Non_motorData, propulsion),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		+1,	/* EXPLICIT tag at current level */
		&asn_DEF_PropelledInformation,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"propulsion"
		},
	{ ATF_POINTER, 5, offsetof(struct Non_motorData, clusterSize),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NumberOfParticipantsInCluster,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"clusterSize"
		},
	{ ATF_POINTER, 4, offsetof(struct Non_motorData, attachment),
		(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_Attachment,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"attachment"
		},
	{ ATF_POINTER, 3, offsetof(struct Non_motorData, personalExt),
		(ASN_TAG_CLASS_CONTEXT | (4 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_PersonalExtensions,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"personalExt"
		},
	{ ATF_POINTER, 2, offsetof(struct Non_motorData, roadWorkerExt),
		(ASN_TAG_CLASS_CONTEXT | (5 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_RoadWorkerExtensions,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"roadWorkerExt"
		},
	{ ATF_POINTER, 1, offsetof(struct Non_motorData, personalReq),
		(ASN_TAG_CLASS_CONTEXT | (6 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_PersonalRequest,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"personalReq"
		},
};
static const int asn_MAP_Non_motorData_oms_1[] = { 1, 2, 3, 4, 5, 6 };
static const ber_tlv_tag_t asn_DEF_Non_motorData_tags_1[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_Non_motorData_tag2el_1[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* basicType */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* propulsion */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 }, /* clusterSize */
    { (ASN_TAG_CLASS_CONTEXT | (3 << 2)), 3, 0, 0 }, /* attachment */
    { (ASN_TAG_CLASS_CONTEXT | (4 << 2)), 4, 0, 0 }, /* personalExt */
    { (ASN_TAG_CLASS_CONTEXT | (5 << 2)), 5, 0, 0 }, /* roadWorkerExt */
    { (ASN_TAG_CLASS_CONTEXT | (6 << 2)), 6, 0, 0 } /* personalReq */
};
asn_SEQUENCE_specifics_t asn_SPC_Non_motorData_specs_1 = {
	sizeof(struct Non_motorData),
	offsetof(struct Non_motorData, _asn_ctx),
	asn_MAP_Non_motorData_tag2el_1,
	7,	/* Count of tags in the map */
	asn_MAP_Non_motorData_oms_1,	/* Optional members */
	6, 0,	/* Root/Additions */
	7,	/* First extension addition */
};
asn_TYPE_descriptor_t asn_DEF_Non_motorData = {
	"Non-motorData",
	"Non-motorData",
	&asn_OP_SEQUENCE,
	asn_DEF_Non_motorData_tags_1,
	sizeof(asn_DEF_Non_motorData_tags_1)
		/sizeof(asn_DEF_Non_motorData_tags_1[0]), /* 1 */
	asn_DEF_Non_motorData_tags_1,	/* Same as above */
	sizeof(asn_DEF_Non_motorData_tags_1)
		/sizeof(asn_DEF_Non_motorData_tags_1[0]), /* 1 */
	{ 0, 0, SEQUENCE_constraint },
	asn_MBR_Non_motorData_1,
	7,	/* Elements count */
	&asn_SPC_Non_motorData_specs_1	/* Additional specs */
};

