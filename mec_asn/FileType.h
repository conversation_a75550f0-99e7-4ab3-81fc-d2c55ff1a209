/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "OPS"
 * 	found in "./OPS.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#ifndef	_FileType_H_
#define	_FileType_H_


#include <asn_application.h>

/* Including external dependencies */
#include <NativeEnumerated.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Dependencies */
typedef enum FileType {
	FileType_upgradeFile	= 0,
	FileType_configFile	= 1,
	FileType_logFile	= 2
} e_FileType;

/* FileType */
typedef long	 FileType_t;

/* Implementation */
extern asn_per_constraints_t asn_PER_type_FileType_constr_1;
extern asn_TYPE_descriptor_t asn_DEF_FileType;
extern const asn_INTEGER_specifics_t asn_SPC_FileType_specs_1;
asn_struct_free_f FileType_free;
asn_struct_print_f FileType_print;
asn_constr_check_f FileType_constraint;
ber_type_decoder_f FileType_decode_ber;
der_type_encoder_f FileType_encode_der;
xer_type_decoder_f FileType_decode_xer;
xer_type_encoder_f FileType_encode_xer;
oer_type_decoder_f FileType_decode_oer;
oer_type_encoder_f FileType_encode_oer;
per_type_decoder_f FileType_decode_uper;
per_type_encoder_f FileType_encode_uper;

#ifdef __cplusplus
}
#endif

#endif	/* _FileType_H_ */
#include <asn_internal.h>
