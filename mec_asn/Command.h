/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "OPS"
 * 	found in "./OPS.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#ifndef	_Command_H_
#define	_Command_H_


#include <asn_application.h>

/* Including external dependencies */
#include <NativeEnumerated.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Dependencies */
typedef enum Command {
	Command_getDeviceInfo	= 0,
	Command_reboot	= 1,
	Command_getSystemStatus	= 2,
	Command_getV2XStatus	= 3,
	Command_update	= 4,
	Command_getIREList	= 5,
	Command_getNTP	= 6,
	Command_getAIParamInfo	= 7,
	Command_getEnvParam	= 8,
	Command_getReferenceList	= 9
	/*
	 * Enumeration is extensible
	 */
} e_Command;

/* Command */
typedef long	 Command_t;

/* Implementation */
extern asn_per_constraints_t asn_PER_type_Command_constr_1;
extern asn_TYPE_descriptor_t asn_DEF_Command;
extern const asn_INTEGER_specifics_t asn_SPC_Command_specs_1;
asn_struct_free_f Command_free;
asn_struct_print_f Command_print;
asn_constr_check_f Command_constraint;
ber_type_decoder_f Command_decode_ber;
der_type_encoder_f Command_encode_der;
xer_type_decoder_f Command_decode_xer;
xer_type_encoder_f Command_encode_xer;
oer_type_decoder_f Command_decode_oer;
oer_type_encoder_f Command_encode_oer;
per_type_decoder_f Command_decode_uper;
per_type_encoder_f Command_encode_uper;

#ifdef __cplusplus
}
#endif

#endif	/* _Command_H_ */
#include <asn_internal.h>
