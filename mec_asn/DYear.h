/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "DefTime"
 * 	found in "./DefTime.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#ifndef	_DYear_H_
#define	_DYear_H_


#include <asn_application.h>

/* Including external dependencies */
#include <NativeInteger.h>

#ifdef __cplusplus
extern "C" {
#endif

/* DYear */
typedef long	 DYear_t;

/* Implementation */
extern asn_per_constraints_t asn_PER_type_DYear_constr_1;
extern asn_TYPE_descriptor_t asn_DEF_DYear;
asn_struct_free_f DYear_free;
asn_struct_print_f DYear_print;
asn_constr_check_f DYear_constraint;
ber_type_decoder_f DYear_decode_ber;
der_type_encoder_f DYear_encode_der;
xer_type_decoder_f DYear_decode_xer;
xer_type_encoder_f DYear_encode_xer;
oer_type_decoder_f DYear_decode_oer;
oer_type_encoder_f DYear_encode_oer;
per_type_decoder_f DYear_decode_uper;
per_type_encoder_f DYear_encode_uper;

#ifdef __cplusplus
}
#endif

#endif	/* _DYear_H_ */
#include <asn_internal.h>
