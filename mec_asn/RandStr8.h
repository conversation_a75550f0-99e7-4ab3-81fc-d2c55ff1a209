/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "VPM"
 * 	found in "./VPM.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#ifndef	_RandStr8_H_
#define	_RandStr8_H_


#include <asn_application.h>

/* Including external dependencies */
#include <OCTET_STRING.h>

#ifdef __cplusplus
extern "C" {
#endif

/* RandStr8 */
typedef OCTET_STRING_t	 RandStr8_t;

/* Implementation */
extern asn_per_constraints_t asn_PER_type_RandStr8_constr_1;
extern asn_TYPE_descriptor_t asn_DEF_RandStr8;
asn_struct_free_f RandStr8_free;
asn_struct_print_f RandStr8_print;
asn_constr_check_f RandStr8_constraint;
ber_type_decoder_f RandStr8_decode_ber;
der_type_encoder_f RandStr8_encode_der;
xer_type_decoder_f RandStr8_decode_xer;
xer_type_encoder_f RandStr8_encode_xer;
oer_type_decoder_f RandStr8_decode_oer;
oer_type_encoder_f RandStr8_encode_oer;
per_type_decoder_f RandStr8_decode_uper;
per_type_encoder_f RandStr8_encode_uper;

#ifdef __cplusplus
}
#endif

#endif	/* _RandStr8_H_ */
#include <asn_internal.h>
