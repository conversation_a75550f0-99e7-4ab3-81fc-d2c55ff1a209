/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "PAM"
 * 	found in "./PAM.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#include "PAMNode.h"

static int
memb_floor_constraint_1(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	long value;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	value = *(const long *)sptr;
	
	if((value >= -128 && value <= 128)) {
		/* Constraint check succeeded */
		return 0;
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static asn_oer_constraints_t asn_OER_memb_floor_constr_4 CC_NOTUSED = {
	{ 2, 0 }	/* (-128..128) */,
	-1};
static asn_per_constraints_t asn_PER_memb_floor_constr_4 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 9,  9, -128,  128 }	/* (-128..128) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
asn_TYPE_member_t asn_MBR_PAMNode_1[] = {
	{ ATF_NOFLAGS, 0, offsetof(struct PAMNode, id),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_PAMNodeID,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"id"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct PAMNode, refPos),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_Position3D,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"refPos"
		},
	{ ATF_POINTER, 3, offsetof(struct PAMNode, floor),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NativeInteger,
		0,
		{ &asn_OER_memb_floor_constr_4, &asn_PER_memb_floor_constr_4,  memb_floor_constraint_1 },
		0, 0, /* No default value */
		"floor"
		},
	{ ATF_POINTER, 2, offsetof(struct PAMNode, attributes),
		(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_PAMNodeAttributes,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"attributes"
		},
	{ ATF_POINTER, 1, offsetof(struct PAMNode, inDrives),
		(ASN_TAG_CLASS_CONTEXT | (4 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_PAMDriveList,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"inDrives"
		},
};
static const int asn_MAP_PAMNode_oms_1[] = { 2, 3, 4 };
static const ber_tlv_tag_t asn_DEF_PAMNode_tags_1[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_PAMNode_tag2el_1[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* id */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* refPos */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 }, /* floor */
    { (ASN_TAG_CLASS_CONTEXT | (3 << 2)), 3, 0, 0 }, /* attributes */
    { (ASN_TAG_CLASS_CONTEXT | (4 << 2)), 4, 0, 0 } /* inDrives */
};
asn_SEQUENCE_specifics_t asn_SPC_PAMNode_specs_1 = {
	sizeof(struct PAMNode),
	offsetof(struct PAMNode, _asn_ctx),
	asn_MAP_PAMNode_tag2el_1,
	5,	/* Count of tags in the map */
	asn_MAP_PAMNode_oms_1,	/* Optional members */
	3, 0,	/* Root/Additions */
	5,	/* First extension addition */
};
asn_TYPE_descriptor_t asn_DEF_PAMNode = {
	"PAMNode",
	"PAMNode",
	&asn_OP_SEQUENCE,
	asn_DEF_PAMNode_tags_1,
	sizeof(asn_DEF_PAMNode_tags_1)
		/sizeof(asn_DEF_PAMNode_tags_1[0]), /* 1 */
	asn_DEF_PAMNode_tags_1,	/* Same as above */
	sizeof(asn_DEF_PAMNode_tags_1)
		/sizeof(asn_DEF_PAMNode_tags_1[0]), /* 1 */
	{ 0, 0, SEQUENCE_constraint },
	asn_MBR_PAMNode_1,
	5,	/* Elements count */
	&asn_SPC_PAMNode_specs_1	/* Additional specs */
};

