/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "OPS"
 * 	found in "./OPS.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#ifndef	_AlarmType_H_
#define	_AlarmType_H_


#include <asn_application.h>

/* Including external dependencies */
#include <NativeEnumerated.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Dependencies */
typedef enum AlarmType {
	AlarmType_nothing	= 0,
	AlarmType_highCPU	= 1,
	AlarmType_outOfMemory	= 2,
	AlarmType_netDisconnect	= 3,
	AlarmType_badFirmware	= 4,
	AlarmType_storageBadBlock	= 5,
	AlarmType_outOfDiskSpace	= 6,
	AlarmType_ireOffLine	= 7,
	AlarmType_threadLock	= 8,
	AlarmType_appBlock	= 9,
	AlarmType_pc5Error	= 10,
	AlarmType_gnssError	= 11,
	AlarmType_uwbError	= 12
	/*
	 * Enumeration is extensible
	 */
} e_AlarmType;

/* AlarmType */
typedef long	 AlarmType_t;

/* Implementation */
extern asn_per_constraints_t asn_PER_type_AlarmType_constr_1;
extern asn_TYPE_descriptor_t asn_DEF_AlarmType;
extern const asn_INTEGER_specifics_t asn_SPC_AlarmType_specs_1;
asn_struct_free_f AlarmType_free;
asn_struct_print_f AlarmType_print;
asn_constr_check_f AlarmType_constraint;
ber_type_decoder_f AlarmType_decode_ber;
der_type_encoder_f AlarmType_encode_der;
xer_type_decoder_f AlarmType_decode_xer;
xer_type_encoder_f AlarmType_encode_xer;
oer_type_decoder_f AlarmType_decode_oer;
oer_type_encoder_f AlarmType_encode_oer;
per_type_decoder_f AlarmType_decode_uper;
per_type_encoder_f AlarmType_encode_uper;

#ifdef __cplusplus
}
#endif

#endif	/* _AlarmType_H_ */
#include <asn_internal.h>
