/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "SignalPhaseAndTiming"
 * 	found in "./SignalPhaseAndTiming.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#ifndef	_PhaseID_H_
#define	_PhaseID_H_


#include <asn_application.h>

/* Including external dependencies */
#include <NativeInteger.h>

#ifdef __cplusplus
extern "C" {
#endif

/* PhaseID */
typedef long	 PhaseID_t;

/* Implementation */
extern asn_per_constraints_t asn_PER_type_PhaseID_constr_1;
extern asn_TYPE_descriptor_t asn_DEF_PhaseID;
asn_struct_free_f PhaseID_free;
asn_struct_print_f PhaseID_print;
asn_constr_check_f PhaseID_constraint;
ber_type_decoder_f PhaseID_decode_ber;
der_type_encoder_f PhaseID_encode_der;
xer_type_decoder_f PhaseID_decode_xer;
xer_type_encoder_f PhaseID_encode_xer;
oer_type_decoder_f PhaseID_decode_oer;
oer_type_encoder_f PhaseID_encode_oer;
per_type_decoder_f PhaseID_decode_uper;
per_type_encoder_f PhaseID_encode_uper;

#ifdef __cplusplus
}
#endif

#endif	/* _PhaseID_H_ */
#include <asn_internal.h>
