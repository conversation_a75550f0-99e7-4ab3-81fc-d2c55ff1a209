/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "PSM"
 * 	found in "./PSM.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#include "PersonalRequest.h"

asn_TYPE_member_t asn_MBR_PersonalRequest_1[] = {
	{ ATF_POINTER, 1, offsetof(struct PersonalRequest, crossing),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_PersonalCrossing,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"crossing"
		},
};
static const int asn_MAP_PersonalRequest_oms_1[] = { 0 };
static const ber_tlv_tag_t asn_DEF_PersonalRequest_tags_1[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_PersonalRequest_tag2el_1[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 } /* crossing */
};
asn_SEQUENCE_specifics_t asn_SPC_PersonalRequest_specs_1 = {
	sizeof(struct PersonalRequest),
	offsetof(struct PersonalRequest, _asn_ctx),
	asn_MAP_PersonalRequest_tag2el_1,
	1,	/* Count of tags in the map */
	asn_MAP_PersonalRequest_oms_1,	/* Optional members */
	1, 0,	/* Root/Additions */
	1,	/* First extension addition */
};
asn_TYPE_descriptor_t asn_DEF_PersonalRequest = {
	"PersonalRequest",
	"PersonalRequest",
	&asn_OP_SEQUENCE,
	asn_DEF_PersonalRequest_tags_1,
	sizeof(asn_DEF_PersonalRequest_tags_1)
		/sizeof(asn_DEF_PersonalRequest_tags_1[0]), /* 1 */
	asn_DEF_PersonalRequest_tags_1,	/* Same as above */
	sizeof(asn_DEF_PersonalRequest_tags_1)
		/sizeof(asn_DEF_PersonalRequest_tags_1[0]), /* 1 */
	{ 0, 0, SEQUENCE_constraint },
	asn_MBR_PersonalRequest_1,
	1,	/* Elements count */
	&asn_SPC_PersonalRequest_specs_1	/* Additional specs */
};

