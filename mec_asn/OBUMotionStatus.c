/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "VPM"
 * 	found in "./VPM.asn"
 * 	`asn1c -pdu=auto -no-gen-example -gen-PER`
 */

#include "OBUMotionStatus.h"

asn_TYPE_member_t asn_MBR_OBUMotionStatus_1[] = {
	{ ATF_POINTER, 3, offsetof(struct OBUMotionStatus, pos),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_Position3D,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"pos"
		},
	{ ATF_POINTER, 2, offsetof(struct OBUMotionStatus, heading),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_Heading,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"heading"
		},
	{ ATF_POINTER, 1, offsetof(struct OBUMotionStatus, speed),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_Speed,
		0,
		{ 0, 0, 0 },
		0, 0, /* No default value */
		"speed"
		},
};
static const int asn_MAP_OBUMotionStatus_oms_1[] = { 0, 1, 2 };
static const ber_tlv_tag_t asn_DEF_OBUMotionStatus_tags_1[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_OBUMotionStatus_tag2el_1[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* pos */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* heading */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 } /* speed */
};
asn_SEQUENCE_specifics_t asn_SPC_OBUMotionStatus_specs_1 = {
	sizeof(struct OBUMotionStatus),
	offsetof(struct OBUMotionStatus, _asn_ctx),
	asn_MAP_OBUMotionStatus_tag2el_1,
	3,	/* Count of tags in the map */
	asn_MAP_OBUMotionStatus_oms_1,	/* Optional members */
	3, 0,	/* Root/Additions */
	3,	/* First extension addition */
};
asn_TYPE_descriptor_t asn_DEF_OBUMotionStatus = {
	"OBUMotionStatus",
	"OBUMotionStatus",
	&asn_OP_SEQUENCE,
	asn_DEF_OBUMotionStatus_tags_1,
	sizeof(asn_DEF_OBUMotionStatus_tags_1)
		/sizeof(asn_DEF_OBUMotionStatus_tags_1[0]), /* 1 */
	asn_DEF_OBUMotionStatus_tags_1,	/* Same as above */
	sizeof(asn_DEF_OBUMotionStatus_tags_1)
		/sizeof(asn_DEF_OBUMotionStatus_tags_1[0]), /* 1 */
	{ 0, 0, SEQUENCE_constraint },
	asn_MBR_OBUMotionStatus_1,
	3,	/* Elements count */
	&asn_SPC_OBUMotionStatus_specs_1	/* Additional specs */
};

