#!/bin/bash
git_commit=`git log -1 |grep 'commit'`
git_Author=`git log -1 |grep 'Author'`
git_Date=`git log -1 |grep 'Date'`
file_name=./app/git_version.h
#echo ${file_name}

echo "// create by getgitver.sh do not edit this file !!! " > ${file_name}
echo " " >> ${file_name}
echo "#ifndef _GIT_VERSION_H_ " >>  ${file_name}
echo "#define _GIT_VERSION_H_ " >> ${file_name}
echo " " >> ${file_name}
echo " " >> ${file_name}
echo "#define GIT_COMMIT \"$git_commit\"" >> ${file_name}
echo "#define GIT_Author \"$git_Author\"" >> ${file_name}
echo "#define GIT_Date \"$git_Date\"" >> ${file_name}
echo " " >> ${file_name}
echo "#endif //_GIT_VERSION_H_  " >> ${file_name}



